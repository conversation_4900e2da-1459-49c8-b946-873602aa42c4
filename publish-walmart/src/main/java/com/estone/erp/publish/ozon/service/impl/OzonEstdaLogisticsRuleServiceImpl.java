package com.estone.erp.publish.ozon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.common.OzonAccountCacheManager;
import com.estone.erp.publish.ozon.mapper.OzonEstdaLogisticsRuleMapper;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRule;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRuleCriteria;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRuleExample;
import com.estone.erp.publish.ozon.service.OzonEstdaLogisticsRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-10-25 15:24:10
 */
@Service("ozonEstdaLogisticsRuleService")
@Slf4j
public class OzonEstdaLogisticsRuleServiceImpl implements OzonEstdaLogisticsRuleService {
    @Resource
    private OzonEstdaLogisticsRuleMapper ozonEstdaLogisticsRuleMapper;

    @Override
    public int countByExample(OzonEstdaLogisticsRuleExample example) {
        Assert.notNull(example, "example is null!");
        return ozonEstdaLogisticsRuleMapper.countByExample(example);
    }

    @Override
    public CQueryResult<OzonEstdaLogisticsRule> search(CQuery<OzonEstdaLogisticsRuleCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        OzonEstdaLogisticsRuleCriteria query = cquery.getSearch();
        OzonEstdaLogisticsRuleExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ozonEstdaLogisticsRuleMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<OzonEstdaLogisticsRule> ozonEstdaLogisticsRules = ozonEstdaLogisticsRuleMapper.selectByExample(example);
        // 组装结果
        CQueryResult<OzonEstdaLogisticsRule> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ozonEstdaLogisticsRules);
        return result;
    }

    @Override
    public OzonEstdaLogisticsRule selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return ozonEstdaLogisticsRuleMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<OzonEstdaLogisticsRule> selectByExample(OzonEstdaLogisticsRuleExample example) {
        Assert.notNull(example, "example is null!");
        return ozonEstdaLogisticsRuleMapper.selectByExample(example);
    }

    @Override
    public int insert(OzonEstdaLogisticsRule record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return ozonEstdaLogisticsRuleMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(OzonEstdaLogisticsRule record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ozonEstdaLogisticsRuleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(OzonEstdaLogisticsRule record, OzonEstdaLogisticsRuleExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ozonEstdaLogisticsRuleMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return ozonEstdaLogisticsRuleMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<OzonEstdaLogisticsRule> getAllRulesByCache() {
        List<OzonEstdaLogisticsRule> estdaLogisticsRule = OzonAccountCacheManager.getEstdaLogisticsRule();
        if (CollectionUtils.isEmpty(estdaLogisticsRule)) {
            List<OzonEstdaLogisticsRule> ozonEstdaLogisticsRules = selectByExample(new OzonEstdaLogisticsRuleExample());
            OzonAccountCacheManager.putEstdaLogisticsRule(ozonEstdaLogisticsRules);
            return ozonEstdaLogisticsRules;
        }
        return estdaLogisticsRule;
    }
}