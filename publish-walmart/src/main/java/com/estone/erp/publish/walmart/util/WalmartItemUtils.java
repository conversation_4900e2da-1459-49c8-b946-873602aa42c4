package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.model.*;
import com.estone.erp.publish.walmart.model.dto.UploadInventoryBean;
import com.estone.erp.publish.walmart.service.WalmartAccountConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2020/11/21
 */
@Slf4j
public class WalmartItemUtils {

    /**
     * 分隔符
     */
    private static final String DELIMITER = "_";

    /**
     * 冠通产品尾缀
     */
    private static final String GT_SUFFIX = "YYL";


    /**
     * 冠通产品前缀
     */
    private static final String GT_PREFIX = "GT";

    // 特供标签列表：AMZ特供、TG-IP、TG-KF、TG-MZ
    public static final List<Integer> SPECIAL_TAG_CODES = Arrays.asList(SpecialTagEnum.s_2028.getCode(),
            SpecialTagEnum.s_2037.getCode(), SpecialTagEnum.s_2038.getCode(), SpecialTagEnum.s_2039.getCode());


    private static WalmartAccountConfigService walmartAccountConfigService = SpringUtils.getBean(WalmartAccountConfigService.class);

    /**
     * 检查产品是否包含特供标签
     *
     * @param specialGoodsCodeList 产品的特殊标签code列表
     * @param specialTagCodes  特供标签code列表
     * @return 是否包含特供标签
     */
    public static boolean containsSpecialTagCode(List<Integer> specialGoodsCodeList, List<Integer> specialTagCodes) {

        if (CollectionUtils.isEmpty(specialGoodsCodeList)) {
            return false;
        }

        if (CollectionUtils.isEmpty(specialTagCodes)) {
            return false;
        }

        for (Integer tagCode : specialTagCodes) {
            if (specialGoodsCodeList.contains(tagCode)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 判断是否应该下架产品
     * 1. 如果产品没有特殊标签，不下架
     * 2. 如果产品的特殊标签不在店铺配置的特供标签中且需包含差集标签中任何一种，应该下架
     */
    public static boolean shouldRetireItem(List<Integer> specialGoodsCodeList, List<Integer> specialSupplyCodesList) {
        if (CollectionUtils.isEmpty(specialSupplyCodesList)) {
            throw new BusinessException("数据非法,特供店铺配置未设置好特供标签");
        }

        // 如果产品没有特殊标签，不下架
        if (CollectionUtils.isEmpty(specialGoodsCodeList)) {
            return false;
        }

        // 差集标签：如店铺配置的特殊标签为AMZ，则差集为TG-IP、TG-KF、TG-MZ
        List<Integer> difference = SPECIAL_TAG_CODES.stream()
                .filter(s -> !specialSupplyCodesList.contains(s))
                .collect(Collectors.toList());


        boolean isContainsDifference = specialGoodsCodeList.stream().anyMatch(difference::contains);

        // 特殊标签包含不是店铺配置选择的特供标签的数据进行下架
        for (Integer goodsCode : specialGoodsCodeList) {
            if (!specialSupplyCodesList.contains(goodsCode) && isContainsDifference) {
                return true;
            }
        }


        return false;
    }


    /**
     * 特供店铺：判断是否包含该店铺特殊标签
     */
    public static boolean isContainsShopSpecialTag(List<Integer> specialGoodsCodeList, List<Integer> specialSupplyCodesList) {
        if (CollectionUtils.isEmpty(specialSupplyCodesList)) {
            throw new BusinessException("数据非法,特供店铺配置未设置好特供标签");
        }

        // 如果产品没有特殊标签
        if (CollectionUtils.isEmpty(specialGoodsCodeList)) {
            return false;
        }

        return specialGoodsCodeList.stream().anyMatch(specialSupplyCodesList::contains);
    }


    /**
     * 判断是否属于特供店铺上架店铺配置选择的特供标签产品
     *
     * @param accountNumber
     * @param skuList
     * @return
     */
    public static boolean isBelongSpecialSupplyPublish(String accountNumber, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return false;
        }

        // 获取特殊商品代码映射
        Map<String, ForbiddenAndSpecical> specialGoodsCodeMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, 250);


        // 获取账号配置
        WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null) {
            throw new BusinessException(String.format("[%s]店铺配置不存在", accountNumber));
        }


        if (!BooleanUtils.isTrue(accountConfig.getSpecialSupplyShop())) {
            return false;
        }

        // 获取店铺配置的特供标签code
        List<Integer> specialSupplyCodesList = CommonUtils.splitList(accountConfig.getSpecialSupplyCodes(), ",")
                .stream().map(s -> Integer.valueOf(s.trim())).collect(Collectors.toList());


        return skuList.stream().allMatch(sku -> {
            List<Integer> specialGoodsCodeList = Optional.ofNullable(specialGoodsCodeMap.get(sku))
                    .map(ForbiddenAndSpecical::getSpecialGoodsTypes).orElse(Collections.emptyList());
            return !shouldRetireItem(specialGoodsCodeList, specialSupplyCodesList);
        });
    }


    /**
     * 根据店铺配置上架拦截
     * 1. 如果店铺为特供账号，过滤掉非店铺配置选择的特供标签的产品
     * 2. 如果店铺为非特供账号，过滤掉特殊标签为AMZ特供或TG-IP或TG-KF或TG-MZ的数据
     *
     * @param accountNumber 账号
     * @param skuList       SKU列表
     * @return 过滤后的SKU列表
     * @throws BusinessException 如果特供店铺过滤后的SKU列表为空，抛出异常
     */
    public static List<String> filterSkuListByAccountConfig(String accountNumber, List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }

        // 获取特殊商品代码映射
        Map<String, ForbiddenAndSpecical> specialGoodsCodeMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, 250);

        // 调用已有方法
        return filterSkuListByAccountConfig(accountNumber, skuList, specialGoodsCodeMap);
    }


    /**
     * 根据账号和SKU列表过滤SKU
     * 1. 如果店铺为特供账号，过滤掉非店铺配置选择的特供标签需包含差集标签中任何一种的产品
     * 2. 如果店铺为非特供账号，过滤掉特殊标签为AMZ特供或TG-IP或TG-KF或TG-MZ的数据
     *
     * @param accountNumber       账号
     * @param skuList             SKU列表
     * @param specialGoodsCodeMap 特殊商品代码映射
     * @return 过滤后的SKU列表
     * @throws BusinessException 如果特供店铺过滤后的SKU列表为空，抛出异常
     */
    public static List<String> filterSkuListByAccountConfig(String accountNumber, List<String> skuList,
                                                      Map<String, ForbiddenAndSpecical> specialGoodsCodeMap) {
        if (CollectionUtils.isEmpty(skuList)) {
            throw new BusinessException("根据店铺配置上架拦截:sku为空");
        }

        // 获取账号配置
        WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null) {
            throw new BusinessException(String.format("[%s]店铺配置不存在", accountNumber));
        }

        // 过滤SKU
        List<String> filteredSkuList = new ArrayList<>();

        // 判断是否为特供账号
        if (Boolean.TRUE.equals(accountConfig.getSpecialSupplyShop())) {
            // 特供账号
            // 获取店铺配置的特供标签code
            List<Integer> specialSupplyCodesList = CommonUtils.splitList(accountConfig.getSpecialSupplyCodes(), ",")
                    .stream().map(s -> Integer.valueOf(s.trim())).collect(Collectors.toList());

            // 过滤SKU
            for (String sku : skuList) {
                List<Integer> specialGoodsCodeList = Optional.ofNullable(specialGoodsCodeMap.get(sku))
                        .map(ForbiddenAndSpecical::getSpecialGoodsTypes)
                        .orElse(Collections.emptyList());

                if (!shouldRetireItem(specialGoodsCodeList, specialSupplyCodesList)) {
                    filteredSkuList.add(sku);
                }
            }

            // 如果过滤后的SKU列表为空，抛出异常
            if (CollectionUtils.isEmpty(filteredSkuList)) {
                throw new BusinessException("特供店铺仅允许上架店铺配置的特供标签产品");
            }
        } else {
            // 非特供账号
            // 过滤掉特殊标签为AMZ特供或TG-IP或TG-KF或TG-MZ的数据
            for (String sku : skuList) {
                List<Integer> specialGoodsCodeList = Optional.ofNullable(specialGoodsCodeMap.get(sku))
                        .map(ForbiddenAndSpecical::getSpecialGoodsTypes)
                        .orElse(Collections.emptyList());

                if (!containsSpecialTagCode(specialGoodsCodeList, SPECIAL_TAG_CODES)) {
                    filteredSkuList.add(sku);
                }
            }
            // 如果过滤后的SKU列表为空，抛出异常
            if (CollectionUtils.isEmpty(filteredSkuList)) {
                throw new BusinessException("非特供店铺不允许刊登特供产品");
            }
        }

        return filteredSkuList;
    }


    /**
     * 转化Walmart产品列表
     * @param body
     * @return
     */
    public static List<WalmartItem> toWalmartItemList(String body, String accountNumber) {
        JSONObject jsonObject = JSON.parseObject(body);
        if(null == jsonObject) {
            return null;
        }

        JSONArray itemJsonArray = jsonObject.getJSONArray("ItemResponse");
        if(null == itemJsonArray) {
            return null;
        }

        List<WalmartItem> walmartItems = new ArrayList<>();

        for (int i = 0; i < itemJsonArray.size(); i++) {
            try {
                JSONObject itemJsonObjct = itemJsonArray.getJSONObject(i);
                WalmartItem walmartItem = toWalmartItem(itemJsonObjct, accountNumber);
                if(null != walmartItem) {
                    walmartItems.add(walmartItem);
                }
            } catch (Exception e) {
                log.error("walmart解析结构体报错:" + e.getMessage());
            }
        }

        return walmartItems;
    }

    /**
     * 转化Walmart产品
     * @param itemJsonObjct
     * @return
     */
    public static WalmartItem toWalmartItem (JSONObject itemJsonObjct, String accountNumber) {
        if(null == itemJsonObjct) {
            return null;
        }
        WalmartItem walmartItem = new WalmartItem();

        // 过滤掉itemId为空的数据
        String itemId = itemJsonObjct.getString("wpid");
        if (StringUtils.isNotBlank(itemId)) {
            walmartItem.setItemId(itemId);
        } else {
            return null;
        }

        // 子SKU截取平台SKU下划线前面部分 且处理数据全部转大写
        String sellerSku = itemJsonObjct.getString("sku");
        walmartItem.setSellerSku(sellerSku);
        walmartItem.setSku(analysisSku(sellerSku));
        walmartItem.setAccountNumber(accountNumber);

        walmartItem.setSite(itemJsonObjct.getString("mart"));
        walmartItem.setGtin(itemJsonObjct.getString("gtin"));
        walmartItem.setTitle(itemJsonObjct.getString("productName"));
        walmartItem.setShelf(itemJsonObjct.getString("shelf"));
        walmartItem.setProductType(itemJsonObjct.getString("productType"));

        JSONObject priceJsonObject = itemJsonObjct.getJSONObject("price");
        if(null != priceJsonObject) {
            walmartItem.setPrice(priceJsonObject.getDouble("amount"));
            walmartItem.setCurrency(priceJsonObject.getString("currency"));
        }

        walmartItem.setPublishedStatus(itemJsonObjct.getString("publishedStatus"));
        walmartItem.setLifecycleStatus(itemJsonObjct.getString("lifecycleStatus"));
        walmartItem.setVariantGroupId(itemJsonObjct.getString("variantGroupId"));

        JSONObject unpublishedReasonsJsonObject = itemJsonObjct.getJSONObject("unpublishedReasons");
        if(null != unpublishedReasonsJsonObject) {
            JSONArray reasonJsonArray = unpublishedReasonsJsonObject.getJSONArray("reason");
            if(null != reasonJsonArray) {
                List<String> reasons = JSONObject.parseArray(reasonJsonArray.toJSONString(), String.class);
                walmartItem.setUnpublishedReasons(String.join(";", reasons));
            }
        }

        return walmartItem;
    }

    private static String analysisSku(String sellerSku) {
        if (StringUtils.isBlank(sellerSku)) {
            return null;
        }
        if(StringUtils.contains(sellerSku, DELIMITER)) {
            sellerSku = StringUtils.substringBeforeLast(sellerSku, DELIMITER);
            String suffix = StringUtils.substringAfterLast(sellerSku, DELIMITER);

            // _YYL 结尾 且不是GT开头需要加上GT
            if(StringUtils.equalsIgnoreCase(GT_SUFFIX, suffix) && !StringUtils.startsWith(sellerSku, GT_PREFIX)) {
                sellerSku = "GT" + sellerSku;
            }
        }

        return StringUtils.upperCase(sellerSku);
    }

    public static List<WalmartItem> toWalmartItemListByFile(File file, String accountNumber, long queueExTime) {
        List<WalmartItem> walmartItems = new ArrayList<>();

        // 转成csv格式
        List<CSVRecord> csvRecords = parseFile(file);
        if (CollectionUtils.isEmpty(csvRecords)) {
            return walmartItems;
        }

        // 删除标题行
        csvRecords.remove(0);

        for (CSVRecord csvRecord : csvRecords) {
            try {
                WalmartItem walmartItem = toWalmartItem(csvRecord, accountNumber);
                if (null != walmartItem) {
                    walmartItems.add(walmartItem);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            long current = System.currentTimeMillis();
            if (current - queueExTime > 9 * 60 * 60 * 1000) {
                throw new RuntimeException("执行失败，转换对象时长超过9小时");
            }
        }

        return walmartItems;
    }

    private static WalmartItem toWalmartItem(CSVRecord csvRecord, String accountNumber) {
        WalmartItem walmartItem = new WalmartItem();

        // 过滤掉itemId为空的数据
        if (StringUtils.isNotBlank(csvRecord.get(20))) {
            walmartItem.setItemId(csvRecord.get(20));
        } else {
            return null;
        }

        walmartItem.setSellerSku(csvRecord.get(0));
        walmartItem.setSku(analysisSku(csvRecord.get(0)));
        walmartItem.setAccountNumber(accountNumber);
        walmartItem.setGtin(csvRecord.get(21));
        walmartItem.setTitle(csvRecord.get(2));
        walmartItem.setShelf(csvRecord.get(26));
        walmartItem.setPrice(StringUtils.isBlank(csvRecord.get(7)) ? null : Double.valueOf(csvRecord.get(7)));
        walmartItem.setCurrency(csvRecord.get(8));
        walmartItem.setPublishedStatus(csvRecord.get(4));
        walmartItem.setLifecycleStatus(csvRecord.get(3));
        walmartItem.setVariantGroupId(csvRecord.get(35));
        walmartItem.setBrand(csvRecord.get(27));

        return walmartItem;
    }

    private static List<CSVRecord> parseFile(File file) {
        try (CSVParser csvParser = CSVFormat.DEFAULT.parse(new FileReader(file))) {
            return csvParser.getRecords();
        } catch (IOException e) {
            throw new RuntimeException("解析文件出现错误", e);
        }
    }

    /**
     *
     * 转换修改价格XML
     *
     * <AUTHOR>
     * @param sku
     * @param price
     * @return
     */
    public static String toUpdatePriceXml(String sku, Double price)
    {
        Document doc = DocumentHelper.createDocument();

        Element rootEle = doc.addElement("Price", "http://walmart.com/");
        Element itemIdentifierEle = rootEle.addElement("itemIdentifier");

        Element skuEle = itemIdentifierEle.addElement("sku");
        skuEle.addText(sku);

        Element pricingListEle = rootEle.addElement("pricingList");
        Element pricingEle = pricingListEle.addElement("pricing");
        Element currentPriceEle = pricingEle.addElement("currentPrice");

        Element valueEle = currentPriceEle.addElement("value");
        valueEle.addAttribute("amount", String.valueOf(price));

        return doc.asXML();
    }

    /**
     *
     * 转换修改库存XML
     *
     * <AUTHOR>
     * @param sku
     * @param inventory
     * @return
     */
    public static String toUpdateInventoryXml(String sku, Integer inventory)
    {
        Document doc = DocumentHelper.createDocument();
        Element rootEle = doc.addElement("inventory", "http://walmart.com/");

        Element skuEle = rootEle.addElement("sku");
        skuEle.addText(sku);

        Element quantityEle = rootEle.addElement("quantity");
        quantityEle.addElement("unit").addText("EACH");
        quantityEle.addElement("amount").addText(inventory.toString());

        return doc.getRootElement().asXML();
    }

    /**
     * 转化批量修改库存xml
     *
     * @param walmartItems
     * @return
     */
    public static String toBulkUpdateInventoryXml(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return null;
        }

        Document doc = DocumentHelper.createDocument();
        Element rootEle = doc.addElement("InventoryFeed ", "http://walmart.com/");
        rootEle.addElement("InventoryHeader").addElement("version").addText("1.4");

        for (WalmartItem walmartItem :walmartItems) {
            if(StringUtils.isBlank(walmartItem.getSellerSku()) || null == walmartItem.getInventory()) {
                continue;
            }

            Element inventoryEle = rootEle.addElement("inventory");
            inventoryEle.addElement("sku").addText(walmartItem.getSellerSku());

            Element quantityEle = inventoryEle.addElement("quantity");
            quantityEle.addElement("unit").addText("EACH");
            quantityEle.addElement("amount").addText(walmartItem.getInventory().toString());
        }

        return doc.getRootElement().asXML();
    }

    /**
     * 转化批量上传库存xml
     * @param beanList
     * @return
     */
    public static String toBulkUploadInventoryXml(List<UploadInventoryBean> beanList) {
        if(CollectionUtils.isEmpty(beanList)) {
            return null;
        }

        Document doc = DocumentHelper.createDocument();
        Element rootEle = doc.addElement("InventoryFeed ", "http://walmart.com/");
        rootEle.addElement("InventoryHeader").addElement("version").addText("1.4");

        for (UploadInventoryBean bean :beanList) {
            if(StringUtils.isBlank(bean.getSellerSku()) || null == bean.getInventory()) {
                continue;
            }

            Element inventoryEle = rootEle.addElement("inventory");
            inventoryEle.addElement("sku").addText(bean.getSellerSku());

            Element quantityEle = inventoryEle.addElement("quantity");
            quantityEle.addElement("unit").addText("EACH");
            quantityEle.addElement("amount").addText(bean.getInventory().toString());
        }

        return doc.getRootElement().asXML();
    }

    /**
     * 装换批量修改价格XML
     * @param walmartItems
     * @return
     */
    public static String toBulkUpdatePriceXml(List<WalmartItem> walmartItems) {

        if(CollectionUtils.isEmpty(walmartItems)) {
            return null;
        }

        Document doc = DocumentHelper.createDocument();
        Element rootEle = doc.addElement("PriceFeed ", "http://walmart.com/");
        rootEle.addElement("PriceHeader").addElement("version").addText("1.5.1");

        for (WalmartItem walmartItem :walmartItems) {
            if(StringUtils.isBlank(walmartItem.getSellerSku()) || null == walmartItem.getPrice()) {
                continue;
            }

            Element priceEle = rootEle.addElement("Price");
            priceEle.addElement("itemIdentifier").addElement("sku").addText(walmartItem.getSellerSku());

            Element pricingListEle = priceEle.addElement("pricingList");
            Element pricingEle = pricingListEle.addElement("pricing");
            Element currentPriceEle = pricingEle.addElement("currentPrice");
            Element valueEle = currentPriceEle.addElement("value");
            valueEle.addAttribute("currency", "USD");
            valueEle.addAttribute("amount", walmartItem.getPrice().toString());
        }

        return doc.getRootElement().asXML();
    }

    public static JSONObject toBulkUpdatePriceJson(List<WalmartItem> walmartItems) {
        List<JSONObject> priceList = new ArrayList<>();
        for (WalmartItem walmartItem : walmartItems) {
            if(StringUtils.isBlank(walmartItem.getSellerSku()) || null == walmartItem.getPrice() || StringUtils.isBlank(walmartItem.getCurrency())) {
                continue;
            }

            JSONObject currentPriceObject = new JSONObject();
            currentPriceObject.put("amount", walmartItem.getPrice());
            currentPriceObject.put("currency", walmartItem.getCurrency());

            JSONObject pricingObject = new JSONObject();
            pricingObject.put("currentPrice",currentPriceObject);
            pricingObject.put("currentPriceType", "BASE");

            JSONObject priceObject = new JSONObject();
            priceObject.put("sku", walmartItem.getSellerSku());
            priceObject.put("pricing", Arrays.asList(pricingObject));

            priceList.add(priceObject);
        }

        JSONObject version = new JSONObject();
        version.put("version", "1.7");

        JSONObject json = new JSONObject();
        json.put("PriceHeader", version);
        json.put("Price", priceList);

        return json;
    }

    public static String toBulkUploadItemJson(List<WalmartTemplate> walmartTemplateList) {
        JSONObject jsonObject = new JSONObject();

        // 设置头部信息
        JSONObject headerObject = new JSONObject();
        headerObject.put("businessUnit", "WALMART_US");
        headerObject.put("locale", "en");
        headerObject.put("version", "5.0.20250121-19_24_23-api");
        jsonObject.put("MPItemFeedHeader", headerObject);

        JSONArray mpItemJsonArray = new JSONArray();
        for (WalmartTemplate walmartTemplate : walmartTemplateList) {
            List<JSONObject> mpItemJsonObjects = toMpItemJsonObjectList(walmartTemplate);
            mpItemJsonArray.addAll(mpItemJsonObjects);
        }
        jsonObject.put("MPItem", mpItemJsonArray);

        return jsonObject.toJSONString();
    }

    private static List<JSONObject> toMpItemJsonObjectList(WalmartTemplate walmartTemplate) {
        List<JSONObject> mpItemJsonObjects = new ArrayList<>();

        if (walmartTemplate.getSaleVariant()) {
            // 变体
            List<JSONObject> variantObjects = toVariantObjectList(walmartTemplate);
            mpItemJsonObjects.addAll(variantObjects);
        } else {
            // 单体
            JSONObject singleObject = toSingleObject(walmartTemplate);
            mpItemJsonObjects.add(singleObject);
        }

        return mpItemJsonObjects;
    }

    private static List<JSONObject> toVariantObjectList(WalmartTemplate walmartTemplate) {
        List<JSONObject> variantObjects = new ArrayList<>();
        List<WalmartVariant> variants = JSON.parseArray(walmartTemplate.getVariations(), WalmartVariant.class);
        for (WalmartVariant variant : variants) {
            JSONObject variantObject = new JSONObject();
            variantObject.put("Orderable", toOrderableObject(walmartTemplate, variant));
            variantObject.put("Visible", toVisibleObject(walmartTemplate, variant));
            variantObjects.add(variantObject);
        }
        return variantObjects;
    }

    private static JSONObject toOrderableObject(WalmartTemplate walmartTemplate, WalmartVariant variant) {
        JSONObject orderableObject;
        if (StringUtils.isNotBlank(walmartTemplate.getProductAttribute())) {
            orderableObject = JSONObject.parseObject(walmartTemplate.getProductAttribute());
        } else {
            orderableObject = new JSONObject();
        }

        JSONObject productIdentifiersObject = new JSONObject();
        productIdentifiersObject.put("productId", variant.getProductId());
        productIdentifiersObject.put("productIdType", walmartTemplate.getProductIdType());

        orderableObject.put("productIdentifiers", productIdentifiersObject);
        orderableObject.put("sku", variant.getSellerSku());
//        orderableObject.put("brand", walmartTemplate.getBrand());
//        orderableObject.put("productName", walmartTemplate.getTitle());
        orderableObject.put("ShippingWeight", variant.getShippingWeight());
        orderableObject.put("price", variant.getPrice());

        if (null != walmartTemplate.getFulfillmentLagTime()) {
            orderableObject.put("fulfillmentLagTime", walmartTemplate.getFulfillmentLagTime());
        }

//        if (StringUtils.isNotBlank(walmartTemplate.getPriceUnit()) && null != walmartTemplate.getPricePerUnitQuantity()) {
//            JSONObject pricePerUnitObject = new JSONObject();
//            pricePerUnitObject.put("pricePerUnitQuantity", walmartTemplate.getPricePerUnitQuantity());
//            pricePerUnitObject.put("pricePerUnitUom", walmartTemplate.getPriceUnit());
//            orderableObject.put("pricePerUnit", pricePerUnitObject);
//        }

        if (null != variant.getStartDate()) {
            orderableObject.put("startDate", formatDate(variant.getStartDate()));
        }

        if (null != variant.getEndDate()) {
            orderableObject.put("endDate", formatDate(variant.getEndDate()));
        }

        return orderableObject;
    }

    private static Object toVisibleObject(WalmartTemplate walmartTemplate, WalmartVariant variant) {
        JSONObject visibleObject = new JSONObject();

        JSONObject categoryObject;
        if (StringUtils.isNotBlank(walmartTemplate.getCategoryAttribute())) {
            categoryObject = JSONObject.parseObject(walmartTemplate.getCategoryAttribute());
        } else {
            categoryObject = new JSONObject();
        }
        visibleObject.put(walmartTemplate.getSubCategoryName(), categoryObject);

        // 获取图片映射
        Map<String, String> imageMappingMap = walmartTemplate.getImageMappingMap();

        // 获取变体属性
        Map<String, Object> attributeMap = variant.getAttributeMap();
        List<String> attributeNames = new ArrayList<>(attributeMap.keySet());

        for (String name : attributeMap.keySet()) {
            Object value = attributeMap.get(name);
            categoryObject.put(name, value);
        }

        // 描述换行处理
        String description = walmartTemplate.getDescription();
        if (StringUtils.isNotBlank(description)) {
            categoryObject.put("shortDescription", description.replaceAll("\n","</br>"));
        }
      categoryObject.put("mainImageUrl", imageMappingMap != null && imageMappingMap.get(variant.getMainImageUrl()) != null
                ? imageMappingMap.get(variant.getMainImageUrl())
                : "");
        categoryObject.put("variantAttributeNames", attributeNames);
        categoryObject.put("variantGroupId", variant.getVariantGroupId());
        categoryObject.put("isPrimaryVariant", variant.getIsPrimaryVariant() ? "Yes" : "No");

        if (StringUtils.isNotBlank(variant.getSwatchImageUrl())) {
            JSONObject swatchObject = new JSONObject();
            swatchObject.put("swatchVariantAttribute", attributeNames.get(0));
            swatchObject.put("swatchImageUrl", variant.getSwatchImageUrl());

            JSONArray swatchImagesArray = new JSONArray();
            swatchImagesArray.add(swatchObject);
            categoryObject.put("swatchImages", swatchImagesArray);
        }

        if (StringUtils.isNotBlank(variant.getExtraImageUrls())) {
            List<String> extraImages = JSONObject.parseArray(variant.getExtraImageUrls(), String.class);
            List<String> ossExtraImages = new ArrayList<>();
            for (String extraImage : extraImages) {
                ossExtraImages.add(imageMappingMap.get(extraImage));
            }
            if (CollectionUtils.isNotEmpty(ossExtraImages)) {
                categoryObject.put("productSecondaryImageURL", ossExtraImages);
            }
        }

        if (StringUtils.isNotBlank(walmartTemplate.getKeyFeatures())) {
            List<String> keyFeatures = JSONObject.parseArray(walmartTemplate.getKeyFeatures(), String.class);
            categoryObject.put("keyFeatures", keyFeatures);
        }

        categoryObject.put("brand", walmartTemplate.getBrand());
        categoryObject.put("productName", walmartTemplate.getTitle());

//        if (null != variant.getMsrp()) {
//            categoryObject.put("msrp", variant.getMsrp());
//        }

        // 颜色分类
        if (CollectionUtils.isNotEmpty(variant.getColorCategory())) {
            categoryObject.put("colorCategory", variant.getColorCategory());
        }

        return visibleObject;
    }

    private static JSONObject toSingleObject(WalmartTemplate walmartTemplate) {
        JSONObject singleObject = new JSONObject();
        singleObject.put("Orderable", toOrderableObject(walmartTemplate));
        singleObject.put("Visible", toVisibleObject(walmartTemplate));
        return singleObject;
    }

    private static Object toOrderableObject(WalmartTemplate walmartTemplate) {
        JSONObject orderableObject;
        if (StringUtils.isNotBlank(walmartTemplate.getProductAttribute())) {
            orderableObject = JSONObject.parseObject(walmartTemplate.getProductAttribute());
        } else {
            orderableObject = new JSONObject();
        }

        JSONObject productIdentifiersObject = new JSONObject();
        productIdentifiersObject.put("productId", walmartTemplate.getProductId());
        productIdentifiersObject.put("productIdType", walmartTemplate.getProductIdType());

        orderableObject.put("productIdentifiers", productIdentifiersObject);
        orderableObject.put("sku", walmartTemplate.getSellerSku());
//        orderableObject.put("brand", walmartTemplate.getBrand());
//        orderableObject.put("productName", walmartTemplate.getTitle());
        orderableObject.put("ShippingWeight", walmartTemplate.getShippingWeight());
        orderableObject.put("price", walmartTemplate.getPrice());

        if (null != walmartTemplate.getFulfillmentLagTime()) {
            orderableObject.put("fulfillmentLagTime", walmartTemplate.getFulfillmentLagTime());
        }

//        if (StringUtils.isNotBlank(walmartTemplate.getPriceUnit()) && null != walmartTemplate.getPricePerUnitQuantity()) {
//            JSONObject pricePerUnitObject = new JSONObject();
//            pricePerUnitObject.put("pricePerUnitQuantity", walmartTemplate.getPricePerUnitQuantity());
//            pricePerUnitObject.put("pricePerUnitUom", walmartTemplate.getPriceUnit());
//            orderableObject.put("pricePerUnit", pricePerUnitObject);
//        }

        if (null != walmartTemplate.getStartDate()) {
            orderableObject.put("startDate", formatDate(walmartTemplate.getStartDate()));
        }

        if (null != walmartTemplate.getEndDate()) {
            orderableObject.put("endDate", formatDate(walmartTemplate.getEndDate()));
        }

        return orderableObject;
    }

    private static Object toVisibleObject(WalmartTemplate walmartTemplate) {
        JSONObject visibleObject = new JSONObject();

        JSONObject categoryObject;
        if (StringUtils.isNotBlank(walmartTemplate.getCategoryAttribute())) {
            categoryObject = JSONObject.parseObject(walmartTemplate.getCategoryAttribute());
        } else {
            categoryObject = new JSONObject();
        }
        visibleObject.put(walmartTemplate.getSubCategoryName(), categoryObject);

        // 获取图片映射
        Map<String, String> imageMappingMap = walmartTemplate.getImageMappingMap();

        // 描述换行处理
        String description = walmartTemplate.getDescription();
        if (StringUtils.isNotBlank(description)) {
            categoryObject.put("shortDescription", description.replaceAll("\n","</br>"));
        }

        categoryObject.put("mainImageUrl", imageMappingMap.get(walmartTemplate.getMainImageUrl()));

        if (StringUtils.isNotBlank(walmartTemplate.getExtraImageUrls())) {
            List<String> extraImages = JSONObject.parseArray(walmartTemplate.getExtraImageUrls(), String.class);
            List<String> ossExtraImages = new ArrayList<>();
            for (String extraImage : extraImages) {
                ossExtraImages.add(imageMappingMap.get(extraImage));
            }
            if (CollectionUtils.isNotEmpty(ossExtraImages)) {
                categoryObject.put("productSecondaryImageURL", ossExtraImages);
            }
        }

        if (StringUtils.isNotBlank(walmartTemplate.getKeyFeatures())) {
            List<String> keyFeatures = JSONObject.parseArray(walmartTemplate.getKeyFeatures(), String.class);
            categoryObject.put("keyFeatures", keyFeatures);
        }

        categoryObject.put("brand", walmartTemplate.getBrand());
        categoryObject.put("productName", walmartTemplate.getTitle());

//        if (null != walmartTemplate.getMsrp()) {
//            categoryObject.put("msrp", walmartTemplate.getMsrp());
//        }

        return visibleObject;
    }

    private static String formatDate(Timestamp timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        return sdf.format(timestamp);
    }

    public static String toBulkReplaceItemJson(List<WalmartReplaceItem> walmartReplaceItems) {
        JSONObject jsonObject = new JSONObject();
        // 设置头部信息
        setUpdateHeader(jsonObject);

        JSONArray mpItemJsonArray = new JSONArray();
        for (WalmartReplaceItem replaceItem : walmartReplaceItems) {
            JSONObject orderableObject = new JSONObject();
            JSONObject productIdentifiersObject = new JSONObject();
            productIdentifiersObject.put("productId", replaceItem.getProductId());
            productIdentifiersObject.put("productIdType", replaceItem.getProductIdType());
            orderableObject.put("productIdentifiers", productIdentifiersObject);
            orderableObject.put("sku", replaceItem.getSellerSku());
            if (StringUtils.isNotBlank(replaceItem.getTitle())) {
                orderableObject.put("productName", replaceItem.getTitle());
            }
            if (null != replaceItem.getStartDate()) {
                orderableObject.put("startDate", formatDate(replaceItem.getStartDate()));
            }
            if (null != replaceItem.getEndDate()) {
                orderableObject.put("endDate", formatDate(replaceItem.getEndDate()));
            }

            JSONObject visibleObject = new JSONObject();
            JSONObject categoryObject = new JSONObject();
            visibleObject.put(replaceItem.getSubCategoryName(), categoryObject);
            if (StringUtils.isNotBlank(replaceItem.getDescription())) {
                categoryObject.put("shortDescription", replaceItem.getDescription().replaceAll("\n","</br>"));
            }
            if (StringUtils.isNotBlank(replaceItem.getKeyFeatures())) {
                List<String> keyFeatures = JSONObject.parseArray(replaceItem.getKeyFeatures(), String.class);
                categoryObject.put("keyFeatures", keyFeatures);
            }
            if (StringUtils.isNotBlank(replaceItem.getMainImageUrl())) {
                categoryObject.put("mainImageUrl", replaceItem.getMainImageUrl());
            }
            if (StringUtils.isNotBlank(replaceItem.getExtraImages())) {
                List<String> extraImages = JSONObject.parseArray(replaceItem.getExtraImages(), String.class);
                categoryObject.put("productSecondaryImageURL", extraImages);
            }

            JSONObject mpItemJsonObject = new JSONObject();
            mpItemJsonObject.put("Orderable", orderableObject);
            mpItemJsonObject.put("Visible", visibleObject);
            mpItemJsonArray.add(mpItemJsonObject);
        }
        jsonObject.put("MPItem", mpItemJsonArray);

        return jsonObject.toJSONString();
    }

    private static void setUpdateHeader(JSONObject jsonObject) {
        JSONObject headerObject = new JSONObject();
        headerObject.put("subset", "EXTERNAL");
        headerObject.put("locale", "en");
        // 新增item使用marketplace  更新item使用mpmaintenance
        headerObject.put("sellingChannel", "mpmaintenance");
        headerObject.put("processMode", "REPLACE");
        headerObject.put("version", "4.7");
        jsonObject.put("MPItemFeedHeader", headerObject);
    }

    public static String toBulkUpdateItemJson(List<WalmartItem> walmartItems) {
        JSONObject jsonObject = new JSONObject();
        // 设置头部信息
        setUpdateHeader(jsonObject);

        JSONArray mpItemJsonArray = new JSONArray();
        for (WalmartItem item : walmartItems) {
            JSONObject orderableObject = new JSONObject();
            JSONObject productIdentifiersObject = new JSONObject();
            productIdentifiersObject.put("productId", item.getGtin());
            productIdentifiersObject.put("productIdType", WalmartPublishConstant.DEFAULT_PRODUCT_ID_TYPE);
            orderableObject.put("productIdentifiers", productIdentifiersObject);
            orderableObject.put("sku", item.getSellerSku());

            // 状态限制
            List<StateRestriction> stateRestrictionList = item.getStateRestrictionList();
            if (CollectionUtils.isNotEmpty(stateRestrictionList)) {
                JSONArray stateRestrictionsArray = new JSONArray();
                for (StateRestriction stateRestriction : stateRestrictionList) {
                    JSONObject stateRestrictionsObject = new JSONObject();
                    stateRestrictionsObject.put("stateRestrictionsText", stateRestriction.getStateRestrictionsText());
                    if (StringUtils.isNotBlank(stateRestriction.getStates())) {
                        stateRestrictionsObject.put("states", stateRestriction.getStates());
                    }
                    stateRestrictionsArray.add(stateRestrictionsObject);
                }
                orderableObject.put("stateRestrictions", stateRestrictionsArray);
            }

            JSONObject visibleObject = new JSONObject();
            JSONObject categoryObject = new JSONObject();
            visibleObject.put(WalmartPublishConstant.DEFAULT_CATEGORY, categoryObject);

            JSONObject mpItemJsonObject = new JSONObject();
            mpItemJsonObject.put("Orderable", orderableObject);
            mpItemJsonObject.put("Visible", visibleObject);
            mpItemJsonArray.add(mpItemJsonObject);
        }
        jsonObject.put("MPItem", mpItemJsonArray);

        return jsonObject.toJSONString();
    }
}
