package com.estone.erp.publish.ozon.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.common.util.DynamicLimiter;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.publish.ozon.call.api.OzonApiConstant;
import com.estone.erp.publish.ozon.call.model.*;
import com.estone.erp.publish.ozon.call.model.request.*;
import com.estone.erp.publish.ozon.call.model.response.*;
import com.estone.erp.publish.ozon.common.OzonAccountCacheManager;
import com.estone.erp.publish.ozon.common.OzonConstant;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.google.common.base.Stopwatch;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-03-27 16:07
 */
@Slf4j
@Component
public class OzonApiClient extends AbstractHttpClient {
    @Autowired
    private OzonAccountCacheManager ozonAccountCacheManager;

    static {
        // 初始化
        DynamicLimiter.getInstance("SYNC_SELLER_ANALYSIS_TOTAL", 0.3);
    }

    private List<Header> createDefaultHeader(String clientId, String apiKey) {
        if (StringUtils.isBlank(clientId) || StringUtils.isBlank(apiKey)) {
            throw new IllegalArgumentException("clientId or apiKey cant be empty");
        }
        List<Header> headers = new ArrayList<>();
        headers.add(new BasicHeader("Client-Id", clientId));
        headers.add(new BasicHeader("Api-Key", apiKey));
        return headers;
    }

    /**
     * 获取商品列表
     *
     * @param request 请求
     * @return 商品列表
     */
    public OzonResponseResult<ProductListResponse> productList(ProductListRequest request, String accountNumber) {
        String param = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_LIST;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<ProductListResponse>>() {
        });
    }

    /**
     * 获取商品详情
     *
     * @param request 请求
     * @return 商品列表
     */
    @Deprecated
    public OzonResponseResult<OzonItemInfo> productInfo(ProductInfoRequest request, String accountNumber) {
        String param = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_INFO;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<OzonItemInfo>>() {
        });
    }


    /**
     * 按识别码获取商品详情列表
     *
     * @param request 请求
     * @return 商品列表
     */
    @Deprecated
    public OzonResponseResult<ProductInfoListResponse> productInfoList(ProductInfoListRequest request, String accountNumber) {
        String param = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_INFO_LIST;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<ProductInfoListResponse>>() {
        });
    }

    public OzonResponseResult<ProductInfoListV3Response> productInfoListV3(ProductInfoListRequest request, String accountNumber) {
        String json = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_INFO_LIST_V3;
        return doPostInfo(accountNumber, json, url, new TypeReference<>(){});
    }

    /**
     * 修改库存
     * 单个店铺在一次请求中最多可以改变100个商品。每分钟最多可以发送80个请求。
     */
    public OzonResponseResult<List<UpdateResponse>> updateStock(String accountNumber, UpdateStockRequest updateStockRequest) {
        return EnvironmentSupplierWrapper.execute(() -> {
            String param = JSON.toJSONString(updateStockRequest);
            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.UPDATE_STOCK;
            // 限流
            DynamicLimiter.getInstance(OzonConstant.UPDATE_STOCK_LIMITER + accountNumber, 1.3d).acquire();
            return doPost(accountNumber, param, url, new TypeReference<>() {
            });
        }, () -> {
            log.info("非正式环境, 不执行下架, param:{}", JSON.toJSONString(updateStockRequest));
            return OzonResponseResult.failResult("非正式环境,不执行改价");
        });
    }


    /**
     * 修改价格
     */
    public OzonResponseResult<List<UpdateResponse>> updatePrice(String accountNumber, UpdatePriceRequest updatePriceRequest) {
        return EnvironmentSupplierWrapper.execute(() -> {
            String param = JSON.toJSONString(updatePriceRequest);
            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.UPDATE_PRICE;
            return doPost(accountNumber, param, url, new TypeReference<>() {
            });
        }, () -> {
            log.info("非正式环境, 不执行下架, param:{}", JSON.toJSONString(updatePriceRequest));
            return OzonResponseResult.failResult("非正式环境,不执行改价");
        });
//        String param = JSON.toJSONString(updatePriceRequest);
//        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.UPDATE_PRICE;
//        return doPost(accountNumber, param, url, new TypeReference<>() {
//        });
    }

    /**
     * 将商品归档
     */
    public OzonResponseResult<Boolean> productArchive(String accountNumber, ArchiveRequest archiveRequest) {
//        return EnvironmentSupplierWrapper.execute(() -> {
//            String param = JSON.toJSONString(archiveRequest);
//            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_ARCHIVE;
//            return doPost(accountNumber, param, url, new TypeReference<>() {
//            });
//        }, () -> {
//            log.info("非正式环境, 不将商品归档, param:{}", JSON.toJSONString(archiveRequest));
//            return OzonResponseResult.failResult("非正式环境,不将商品归档");
//        });
        String param = JSON.toJSONString(archiveRequest);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_ARCHIVE;
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 将商品取消归档
     */
    public OzonResponseResult<Boolean> productUnarchive(String accountNumber, ArchiveRequest archiveRequest) {
//        return EnvironmentSupplierWrapper.execute(() -> {
//            String param = JSON.toJSONString(archiveRequest);
//            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_UNARCHIVE;
//            return doPost(accountNumber, param, url, new TypeReference<>() {
//            });
//        }, () -> {
//            log.info("非正式环境, 不将商品取消归档, param:{}", JSON.toJSONString(archiveRequest));
//            return OzonResponseResult.failResult("非正式环境,不将商品取消归档");
//        });
        String param = JSON.toJSONString(archiveRequest);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_UNARCHIVE;
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取类目
     */
    public OzonResponseResult<List<OzonCategoryTreeDO>> getCategory(String accountNumber, CategoryTreeRequest request) {
        String param = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CATEGORY_TREE;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<List<OzonCategoryTreeDO>>>() {
        });
    }

    /**
     * 获取商品属性
     *
     * @return
     */
    public OzonResponseResult<List<ProductAttributesResponse>> getProductAttributes(String accountNumber, ProductListRequest request) {
        String param = JSON.toJSONString(request);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PRODUCT_ATTRIBUTES;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<List<ProductAttributesResponse>>>() {
        });
    }

    /**
     * 获取fbs仓库库存信息
     */
    public OzonResponseResult<List<WareHouseStockInfo>> getFbsWareHouseStock(String accountNumber, List<Long> ozonSkuList) {
        Map<String, List<Long>> paramMap = new HashMap<>();
        // 2023.8.15 参数过期
//        paramMap.put("fbs_sku",fbsSkuList);
        paramMap.put("sku", ozonSkuList);
        String param = JSON.toJSONString(paramMap);
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.FBS_WAREHOUSE_STOCKS;
        return doPost(accountNumber, param, url, new TypeReference<OzonResponseResult<List<WareHouseStockInfo>>>() {
        });
    }

    /**
     * 获取店铺配置仓库信息
     */
    public OzonResponseResult<List<AccountWareHouseInfo>> getAccountWarehouseInfo(String accountNumber) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.WAREHOUSE_LIST;
        return doPost(accountNumber, "", url, new TypeReference<OzonResponseResult<List<AccountWareHouseInfo>>>() {
        });
    }


    /**
     * 获取店铺配置仓库信息
     */
    public OzonResponseResult<String> updateAttributes(String accountNumber, List<UpdateAttributesRequest> attributesRequest) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.ATTRIBUTES_UPDATE;
        Map<String, List<UpdateAttributesRequest>> paramMap = new HashMap<>();
        paramMap.put("items", attributesRequest);
        String param = JSON.toJSONString(paramMap);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取类目属性
     *
     * @param request 类目id
     */
    public OzonResponseResult<List<OzonAttributeInfoDO>> getCategoryAttribute(CategoryAttributeRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CATEGORY_ATTRIBUTE;
        String param = JSON.toJSONString(request);
        return doPost(OzonConstant.DEFAULT_ACCOUNT, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取类目属性项的属性值
     *
     * @param request
     */
    public OzonResponseResult<List<OzonAttributeValue>> getAttributeValue(String accountNumber, CategoryAttributeValueRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CATEGORY_ATTRIBUTE_VALUE;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 创建商品
     *
     * @param requests
     */
    public OzonResponseResult<CreateProductResponse> createProduct(String accountNumber, List<CreateProductRequest> requests) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CREATE_PRODUCT;
        Map<String, List<CreateProductRequest>> paramMap = new HashMap<>();
        paramMap.put("items", requests);
        String param = JSON.toJSONString(paramMap, SerializerFeature.DisableCircularReferenceDetect);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取商品处理状态
     *
     * @param taskId
     */
    public OzonResponseResult<ProductImportInfoResponse> getProductImportInfo(String accountNumber, String taskId) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.TASK_INFO;
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("task_id", taskId);
        String param = JSON.toJSONString(paramMap);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取可参与活动的商品列表
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public OzonResponseResult<ActionItemResponse> getCandidatesActionItems(String accountNumber, ActionItemRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CANDIDATES_ACTION_PRODUCTS;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取参与活动的商品列表
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public OzonResponseResult<ActionItemResponse> getParticipatedActionItems(String accountNumber, ActionItemRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.PARTICIPATED_ACTION_PRODUCTS;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 获取平台的促销活动
     *
     * @param accountNumber
     * @return
     */
    public OzonResponseResult<List<OzonActionDo>> getActionList(String accountNumber) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.ACTIONS;
        return doGet(accountNumber, new HashMap<>(), url, new TypeReference<>() {
        });
    }

    /**
     * 添加商品到活动
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public OzonResponseResult<AddActionItemsResponse> addActionItem(String accountNumber, AddActionItemsRequest request) {
        //        return EnvironmentSupplierWrapper.execute(() -> {
//            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.ADD_ACTION_PRODUCTS;
//            String param = JSON.toJSONString(request);
//            return doPost(accountNumber, param, url, new TypeReference<>() {
//            });
//        }, () -> {
//            log.info("非正式环境, 不执行改价, param:{}", JSON.toJSONString(request.getProducts()));
//            return OzonResponseResult.failResult("非正式环境,不执行："+ JSON.toJSONString(request.getProducts()));
//        });
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.ADD_ACTION_PRODUCTS;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    /**
     * 将商品从活动中移除
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public OzonResponseResult<DeleteActionItemsResponse> deleteActionItem(String accountNumber, DeleteActionItemsRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.DELETE_ACTION_PRODUCTS;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
//        return EnvironmentSupplierWrapper.execute(() -> {
//            String url = OzonApiConstant.BASE_HOST + OzonApiConstant.DELETE_ACTION_PRODUCTS;
//            String param = JSON.toJSONString(request);
//            return doPost(accountNumber, param, url, new TypeReference<>() {
//            });
//        }, () -> {
//            log.info("非正式环境, 不执行改价, param:{}", JSON.toJSONString(request.getProductIds()));
//            return OzonResponseResult.failResult("非正式环境,不执行："+ JSON.toJSONString(request.getProductIds()));
//        });
    }

    /**
     * 获取商品的库存信息
     *
     * @param accountNumber
     * @param request
     * @return
     */
    public OzonResponseResult<GetProductInfoStocksResponse> getProductInfoStocks(String accountNumber, GetProductInfoStocksRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.GET_PRODUCT_INFO_STOCKS;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }


    /**
     * 创建商品报告
     *
     * @param account 店铺
     * @param request 创建商品报告请求
     * @return 商品报告Code
     */
    public OzonResponseResult<String> createProductReport(SaleAccountAndBusinessResponse account, CreateProductReportRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.CREATE_PRODUCT_REPORT;
        String param = JSON.toJSONString(request);
        OzonResponseResult<String> responseResult = doPost(account.getAccountNumber(), param, url, new TypeReference<>() {
        });
        if (responseResult.isSuccess()) {
            String result = responseResult.getResult();
            JSONObject resultObject = JSON.parseObject(result);
            responseResult.setResult(resultObject.getString("code"));
            return responseResult;
        }
        return responseResult;
    }

    /**
     * 获取报告信息
     *
     * @param accountNumber 店铺
     * @param code          报告Code
     * @return 商品报告Code
     */
    public OzonResponseResult<ReportInfoResponse> getReportInfo(String accountNumber, String code) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.GET_REPORT_INFO;
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        String param = JSON.toJSONString(paramMap);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    public OzonResponseResult<OzonRatingResponse> getRatingBySku(String accountNumber, List<Long> skuIdList) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.GET_RATING_BY_SKU;
        Map<String, List<Long>> paramMap = new HashMap<>();
        paramMap.put("skus", skuIdList);
        String param = JSON.toJSONString(paramMap);
        OzonResponseResult<OzonRatingResponse> ozonResponseResult = doPost(accountNumber, param, url, new TypeReference<>() {
        });
        String products = ozonResponseResult.getProducts();
        JSONArray jsonArray = JSON.parseArray(products);
        OzonRatingResponse ratingResponse = new OzonRatingResponse();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Long sku = jsonObject.getLong("sku");
            Double rating = jsonObject.getDouble("rating");
            ratingResponse.addRating(sku, rating);
        }
        ozonResponseResult.setResult(ratingResponse);
        return ozonResponseResult;

    }

    public OzonResponseResult<AnalyticsDataResponse> postAnalyticsData(String accountNumber, AnalyticsDataRequest request) {
        RateLimiter allRateLimiter = DynamicLimiter.getInstance("SYNC_SELLER_ANALYSIS_TOTAL", 0.3);
        allRateLimiter.acquire();
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_ANALYTICS_DATA;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        }, 120000);
    }

    public OzonResponseResult<List<ReturnsRfbsListResponse>> postRfbsReturnsList(String accountNumber, ReturnsRfbsListRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_RETURNS_RFBS_LIST;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    public OzonResponseResult<ReturnsRfbsGetResponse> postRfbsReturnsGet(String accountNumber, ReturnsRfbsGetRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_RETURNS_RFBS_GET;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    public OzonResponseResult<PostingFbsGetResponse> postPostingFbsGet(String accountNumber, PostingFbsGetRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_POSTING_FBS_GET;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }


    private <T> OzonResponseResult<T> doPost(String accountNumber, String json, String url, TypeReference<OzonResponseResult<T>> reference) {
        return doPost(accountNumber, json, url, reference, 6000);
    }
    private <T> OzonResponseResult<T> doPost(String accountNumber, String json, String url, TypeReference<OzonResponseResult<T>> reference, Integer timeOut) {
        SaleAccountAndBusinessResponse account = ozonAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getClientId(), account.getClientSecret());
        Stopwatch watch = Stopwatch.createStarted();
        Exception ex = null;
        String result = null;
        HttpPost httpPost = new HttpPost();
        try {
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if (CollectionUtils.isNotEmpty(defaultHeader)) {
                httpPost.setHeaders(defaultHeader.toArray(new Header[]{}));
            }
            httpPost.setEntity(entity);
            httpPost.setURI(new URI(url));
            return getResultString(getHttpClient(timeOut).execute(httpPost), reference);
        } catch (Exception e) {
            ex = new IllegalStateException("请求失败", e);
            return OzonResponseResult.failResult(e.getMessage());
        } finally {
            httpPost.releaseConnection();
            String template = "[http-client][POST][{}][requestData][{}][result][{}][error][{}][costTime][{} ms]";
            if (ex != null) {
                log.error(template, url, json, null, ex.getMessage(), watch.elapsed(TimeUnit.MILLISECONDS), ex);
            } else {
                log.debug(template, url, json, null, org.apache.commons.lang3.StringUtils.EMPTY, watch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }

    private <T> OzonResponseResult<T> doGet(String accountNumber, Map<String, Object> paramMap, String url, TypeReference<OzonResponseResult<T>> reference) {
        SaleAccountAndBusinessResponse account = ozonAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getClientId(), account.getClientSecret());
        OzonResponseResult<T> responseResult = get(paramMap, url, reference, defaultHeader);
        if (responseResult.getResult() != null) {
            responseResult.setCode(200);
        }
        return responseResult;
    }

    private <T> OzonResponseResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<OzonResponseResult<T>> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        String result = null;
        try {
            try {
                result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    OzonResponseResult<T> responseResult = JSON.parseObject(result, reference);
                    responseResult.setCode(Optional.ofNullable(responseResult.getCode()).orElseGet(() -> statusCode));
                    return responseResult;
                }
                OzonResponseResult<T> responseResult = JSON.parseObject(result, reference);
                responseResult.setCode(200);
                return responseResult;
            } catch (Exception e) {
                UUID uuid = UUID.randomUUID();
                log.error("[{}],statusCode:{},error:{}", uuid, statusCode, e.getMessage(), e);
                throw new RuntimeException(String.format("[%s],%s", uuid, e.getMessage()), e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }

    public  OzonResponseResult<ProductInfoLimitResponse> postProductInfoLimit(String accountNumber) {
       String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_PRODUCT_INFO_LIMIT;
       return doPostInfo(accountNumber, "{}", url, new TypeReference<>() {
       });
    }

    public OzonResponseResult<ProductPicturesImportResponse> postProductPicturesImport(String accountNumber, ProductPicturesImportRequest request) {
        String url = OzonApiConstant.BASE_HOST + OzonApiConstant.POST_PRODUCT_PICTURES_IMPORT;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<>() {
        });
    }

    public <T> OzonResponseResult<T> doPostInfo(String accountNumber, String json, String url, TypeReference<T> reference) {
        SaleAccountAndBusinessResponse account = ozonAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getClientId(), account.getClientSecret());
        Stopwatch watch = Stopwatch.createStarted();
        Exception ex = null;
        String result = null;
        HttpPost httpPost = new HttpPost();
        try {
            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if (CollectionUtils.isNotEmpty(defaultHeader)) {
                httpPost.setHeaders(defaultHeader.toArray(new Header[]{}));
            }
            httpPost.setEntity(entity);
            httpPost.setURI(new URI(url));
            CloseableHttpResponse httpResponse = getHttpClient(6000).execute(httpPost);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            try {
                try {
                    result = EntityUtils.toString(httpResponse.getEntity());
                    if (statusCode != 200) {
                        OzonResponseResult<T> responseResult = new OzonResponseResult<>();
                        T t = JSON.parseObject(result, reference);
                        responseResult.setResult(t);
                        responseResult.setCode(Optional.ofNullable(responseResult.getCode()).orElseGet(() -> statusCode));
                        return responseResult;
                    }
                    OzonResponseResult<T> responseResult = new OzonResponseResult<>();
                    T t = JSON.parseObject(result, reference);
                    responseResult.setResult(t);
                    responseResult.setCode(200);
                    return responseResult;
                } catch (Exception e) {
                    UUID uuid = UUID.randomUUID();
                    log.error("[{}],statusCode:{},error:{}", uuid, statusCode, e.getMessage(), e);
                    throw new RuntimeException(String.format("[%s],%s", uuid, e.getMessage()), e);
                }
            } finally {
                IOUtils.closeQuietly(httpResponse);
            }
        } catch (Exception e) {
            ex = new IllegalStateException("请求失败", e);
            return OzonResponseResult.failResult(e.getMessage());
        } finally {
            httpPost.releaseConnection();
            String template = "[http-client][POST][{}][requestData][{}][result][{}][error][{}][costTime][{} ms]";
            if (ex != null) {
                log.error(template, url, json, null, ex.getMessage(), watch.elapsed(TimeUnit.MILLISECONDS), ex);
            } else {
                log.debug(template, url, json, null, org.apache.commons.lang3.StringUtils.EMPTY, watch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }
}
