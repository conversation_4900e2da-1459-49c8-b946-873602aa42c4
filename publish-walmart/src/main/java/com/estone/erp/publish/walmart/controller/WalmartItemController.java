package com.estone.erp.publish.walmart.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.common.model.dto.ResultListDTO;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.walmart.call.items.WalmartGetItemsCall;
import com.estone.erp.publish.walmart.enums.WalmartExcelDownloadTypeEnum;
import com.estone.erp.publish.walmart.enums.WalmartExcelStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartExecutionTypeEnum;
import com.estone.erp.publish.walmart.model.WalmartExcelDownloadLog;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemCriteria;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.model.dto.WalmartItemCalcPriceRequest;
import com.estone.erp.publish.walmart.model.vo.WalmartReplaceItemVO;
import com.estone.erp.publish.walmart.model.vo.WalmartRetireItemVO;
import com.estone.erp.publish.walmart.model.vo.WalmartSyncItemVO;
import com.estone.erp.publish.walmart.mq.sender.WalmartExcelDownloadMqSender;
import com.estone.erp.publish.walmart.service.WalmartExcelDownloadLogService;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.estone.erp.publish.walmart.util.WalmartCalcPriceUtil;
import com.estone.erp.publish.walmart.util.WalmartItemLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> walmart_item
 * 2021-03-04 10:32:52
 */
@Slf4j
@RestController
@RequestMapping("walmartItem")
public class WalmartItemController {
    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private DrainageSkuService drainageSkuService;

    @Resource
    private WalmartExcelDownloadLogService walmartExcelDownloadLogService;

    @Resource
    private WalmartExcelDownloadMqSender walmartExcelDownloadMqSender;

    @Resource
    private WalmartTemplateService walmartTemplateService;

    @PostMapping
    public ApiResult<?> postWalmartItem(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchWalmartItem": // 查询列表
                    CQuery<WalmartItemCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<WalmartItemCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<WalmartItem> results = walmartItemService.search(cquery);
                    return results;
                case "addWalmartItem": // 添加
                    WalmartItem walmartItem = requestParam.getArgsValue(new TypeReference<WalmartItem>() {});
                    walmartItemService.insert(walmartItem);
                    return ApiResult.newSuccess(walmartItem);
                case "batchUpdateInventory": // 批量修改库存
                    List<WalmartItem> updateInventoryWalmartItems = requestParam.getArgsValue(new TypeReference<List<WalmartItem>>() {});
                    Asserts.isTrue(CollectionUtils.isNotEmpty(updateInventoryWalmartItems), ErrorCode.PARAM_EMPTY_ERROR);
                    String updateInventoryErrorMessage = walmartItemService.batchUpdateInventory(updateInventoryWalmartItems);
                    if(StringUtils.isNotBlank(updateInventoryErrorMessage)) {
                        return ApiResult.newError(updateInventoryErrorMessage);
                    }
                    return ApiResult.newSuccess("修改库存请求成功，请查看处理报告！");
                case "batchUpdatePrice": // 批量修改价格
                    List<WalmartItem> updatePriceWalmartItems = requestParam.getArgsValue(new TypeReference<List<WalmartItem>>() {});
                    Asserts.isTrue(CollectionUtils.isNotEmpty(updatePriceWalmartItems), ErrorCode.PARAM_EMPTY_ERROR);
                    String updatePriceErrorMessage = walmartItemService.batchUpdatePrice(updatePriceWalmartItems);
                    if(StringUtils.isNotBlank(updatePriceErrorMessage)) {
                        return ApiResult.newError(updatePriceErrorMessage);
                    }
                    return ApiResult.newSuccess("修改价格请求成功，请查看处理报告！");
            }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/syncByAccountNumber")
    public ApiResult<?> syncByAccountNumber(@RequestParam("accountNumber") String accountNumber, @RequestParam(value = "sellerSku", required = false) String sellerSku) {
        if (StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("请求参数错误：账号不能为空");
        }
        SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        // 查询不到账号
        if (walmartAccount == null) {
            return ApiResult.newError("未查询到该账号：" + accountNumber);
        }

        WalmartExecutors.syncItem(() -> {
            try {
                walmartItemService.syncByAccountNumber(walmartAccount, sellerSku, null);
//            walmartItemService.syncOffsetItemByAccountNumber(walmartAccount, sellerSku);
//                walmartItemService.syncItemByReport(walmartAccount);
            } catch (Exception e) {
                log.error(String.format("同步账号%s报错：%s", accountNumber, e.getMessage()));
            }
        });

        return ApiResult.newSuccess();
    }

    @PostMapping("/syncItemByItemId")
    public ApiResult<String> syncItemByItemId(@RequestBody WalmartSyncItemVO walmartSyncItemVO) {
        try {
            walmartItemService.syncItemByItemId(walmartSyncItemVO);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        return ApiResult.newSuccess("开始同步，同步结果请查看处理报告");
    }

    @PostMapping(value = "/searchItemByIds")
    public ApiResult<?> searchItemByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("请求参数错误：ids不能为空");
        }
        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria().andIdIn(ids);
        List<WalmartItem> walmartItems = walmartItemService.selectByExample(example);
        return ApiResult.newSuccess(walmartItems);
    }

    @PostMapping(value = "/batchRetire")
    public ApiResult<?> batchUnPublish(@RequestBody WalmartRetireItemVO walmartRetireItemVO) {
        if (null == walmartRetireItemVO
                || CollectionUtils.isEmpty(walmartRetireItemVO.getIds())
                || StringUtils.isBlank(walmartRetireItemVO.getRetireRemark())) {
            return ApiResult.newError("参数为空");
        }

        String batchUnPublishErrorMessage = walmartItemService
                .batchRetire(walmartRetireItemVO.getIds(), walmartRetireItemVO.getRetireRemark());
        if(StringUtils.isNotBlank(batchUnPublishErrorMessage)) {
            return ApiResult.newError(batchUnPublishErrorMessage);
        }
        return ApiResult.newSuccess("下架请求成功，等待平台执行！");
    }

    /**
     * 根据毛利计算价格
     *
     * @return
     */
    @PostMapping(value = "/batchCalcPrice")
    public ApiResult<?> batchCalcPrice(@RequestBody WalmartItemCalcPriceRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getWalmartItems()) || null == request.getGrossMargin()) {
            return ApiResult.newError("缺少必填参数");
        }

        return WalmartCalcPriceUtil.batchCalcWalmartItemPrice(request);
    }

    @GetMapping(value = "/getWalmartItem")
    public ApiResult<?> getWalmartItem(@RequestParam("accountNumber") String accountNumber, @RequestParam("sellerSku") String sellerSku) {

        SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        WalmartAccountUtils.refreshAccountToken(walmartAccount);

        ResultListDTO<WalmartItem> resultListDTO = new WalmartGetItemsCall(walmartAccount).execute( null, sellerSku, null, null);
        return ApiResult.newSuccess(resultListDTO);
    }

    @GetMapping(value = "/getWalmartItemOffset")
    public ApiResult<?> getWalmartItemOffset(@RequestParam("accountNumber") String accountNumber, @RequestParam("sellerSku") String sellerSku) {

        SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        WalmartAccountUtils.refreshAccountToken(walmartAccount);

        List<WalmartItem> walmartItemList = new WalmartGetItemsCall(walmartAccount).execute(100, 0, sellerSku);
        return ApiResult.newSuccess(walmartItemList);
    }

    @PostMapping(value = "/download")
    public ApiResult<?> downloadItemMethod(@RequestBody WalmartItemCriteria criteria) {
        try {
            // 权限控制
            walmartItemService.handleAuth(criteria);

            // 查询账号状态
            walmartItemService.handleAccountStatus(criteria);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        // 查询下载量
        WalmartItemExample example = criteria.getExample();
        int count = walmartItemService.countByExample(example);
        if (count > 500000) {
            return ApiResult.newError("数据大于50万，超过最大可导出数量");
        }

        // 记录下载日志
        WalmartExcelDownloadLog titleDownloadLog = new WalmartExcelDownloadLog();
        titleDownloadLog.setQueryCondition(JSON.toJSONString(criteria));
        titleDownloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        titleDownloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        titleDownloadLog.setType(WalmartExcelDownloadTypeEnum.ITEM_DOWNLOAD.getCode());
        titleDownloadLog.setStatus(WalmartExcelStatusEnum.WAIT.getCode());
        walmartExcelDownloadLogService.insert(titleDownloadLog);

        // 发送消息
        walmartExcelDownloadMqSender.excelDownloadSend(titleDownloadLog.getId(), WalmartExcelDownloadTypeEnum.ITEM_DOWNLOAD.getCode());

        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/getDrainageSkuByAccount")
    public ApiResult<?> getDrainageSku(@RequestParam("accountNumber") String accountNumber) {
        DrainageSkuExample example = new DrainageSkuExample();
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andPlatformEqualTo(SaleChannel.CHANNEL_WALMART)
                .andIsDrainageEqualTo(true);
        List<DrainageSku> drainageSkus = drainageSkuService.selectByExample(example);

        List<String> skus = drainageSkus.stream().map(DrainageSku::getSku).collect(Collectors.toList());

        // 查询单前引流SKU 不存active对应的sku 并建议设置为非引流SKU
        List<String> notActiveSkus = walmartItemService.filterNotActiveDrainageSku(accountNumber, skus);
        if(CollectionUtils.isNotEmpty(notActiveSkus)) {
            for (DrainageSku drainageSku : drainageSkus) {
                String sku = drainageSku.getSku();
                if(notActiveSkus.contains(sku)) {
                    drainageSku.setIsDrainage(false);
                }
            }
        }
        return ApiResult.newSuccess(drainageSkus);
    }

    @PostMapping(value = "/saveDrainageSku")
    public ApiResult<?> saveDrainageSku(@RequestBody List<DrainageSku> drainageSkus) {
        if(CollectionUtils.isEmpty(drainageSkus)) {
            return ApiResult.newError("保存数据为空！");
        }

        drainageSkus = drainageSkus.stream()
                .filter(o -> {
                    if(StringUtils.isNotBlank(o.getSku())){
                        o.setSku(o.getSku().toUpperCase());
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

        String accountNumber = drainageSkus.get(0).getAccountNumber();
        List<String> isDrainageskus = drainageSkus.stream()
                .filter(o -> BooleanUtils.isTrue(o.getIsDrainage()))
                .map(o -> o.getSku())
                .collect(Collectors.toList());

        // 查询单前引流SKU 不存active对应的sku 存在则报错
        List<String> notActiveSkus = walmartItemService.filterNotActiveDrainageSku(accountNumber, isDrainageskus);
        if(CollectionUtils.isNotEmpty(notActiveSkus)){
            return ApiResult.newError(String.format("sku[%s] 不存在或者不是ACTIVE，请删除或取消选中！", JSON.toJSONString(notActiveSkus)));
        }

        drainageSkus.stream().forEach(o -> {
            o.setPlatform(Platform.Walmart.name());
        });

        return drainageSkuService.updateOrInsert(drainageSkus);
    }

    /**
     * 校验清仓甩卖sku改库存限制
     * @param idList
     * @return
     */
    @PostMapping(value = "/checkClearanceReductionListing")
    public ApiResult<?> checkClearanceReductionListing(@RequestBody List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("参数为空");
        }

        WalmartItemExample example = new WalmartItemExample();
        String fields = "id, sku_status, forbid_channel, sku";
        example.setFiledColumns(fields);
        example.createCriteria().andIdIn(idList);
        List<WalmartItem> walmartItemList = walmartItemService.selectFiledColumnsByExample(example);

        Map<String, Integer> resultMap = new HashMap<>();
        for (WalmartItem item : walmartItemList) {
            try {
                Integer skuStock = SkuStockUtils.getSkuSystemStock(item.getSku());
                Boolean isTrue = WalmartItemLocalUtils.checkClearanceReductionListing(item, skuStock);
                if (isTrue) {
                    resultMap.put(item.getSku(), skuStock);
                }
            } catch (Exception e) {
                log.error("清仓甩卖SKU改库存限制报错：" + e.getMessage());
            }
        }

        if (MapUtils.isNotEmpty(resultMap)) {
            ApiResult<Map<String, Integer>> result = new ApiResult<>();
            result.setResult(resultMap);
            result.setSuccess(true);
            result.setErrorMsg("存在SKU单品状态为清仓、甩卖，且SKU在Walmart不禁售，不允许修改库存为0，只允许修改库存为可用库存，请选择修改为可用库存提交，或过滤清仓甩卖SKU后提交。");
            return result;
        }

        return ApiResult.newSuccess();
    }

    /**
     * 更新item标题描述五点描述
     * @param request
     * @return
     */
    @PostMapping("/replaceItem")
    public ApiResult<String> updateItemTitleDescription(@RequestBody WalmartReplaceItemVO request) {
        WalmartExecutors.executeReplaceItem(() -> {
            try {
                walmartItemService.updateItemTitleDescription(request);
            } catch (Exception e) {
                log.error(String.format("更新item标题描述五点描述报错：%s", e.getMessage()));
            }
        });
        return ApiResult.newSuccess("提交成功，请稍后到处理报告查看修改结果");
    }

    /**
     * 修改item图片
     *
     * @param walmartReplaceItemVOList item
     * @return msg
     */
    @PostMapping("/updateItemImage")
    public ApiResult<String> updateItemImage(@RequestBody List<WalmartReplaceItemVO> walmartReplaceItemVOList) {
        WalmartExecutors.executeUpdateImage(() -> {
            try {
                walmartItemService.replaceItemImage(walmartReplaceItemVOList);
            } catch (Exception e) {
                log.error(String.format("报错：%s", e.getMessage()));
            }
        });
        return ApiResult.newSuccess("提交成功，请稍后到处理报告查看修改结果");
    }

    /**
     * 确认下架无销量产品
     */
    @GetMapping("/confirmRetireNoSales")
    public ApiResult<String> confirmRetireNoSales(@RequestParam Integer daysBefore) {
        if (null == daysBefore) {
            return ApiResult.newError("参数不可为空");
        }

        //TODO Mark:test 待删除
        // 高风险的正常账号
//        List<SaleAccountAndBusinessResponse> accountList = WalmartAccountUtils.getRiskAccount(null);

        List<SaleAccountAndBusinessResponse> accountList = WalmartAccountUtils.getRiskAccount(List.of("Coosia"));
        if (CollectionUtils.isEmpty(accountList)) {
            return ApiResult.newError("没有非低风险的正常账号");
        }


        try {
            walmartItemService.retireNoSalesSku(accountList, daysBefore, WalmartExecutionTypeEnum.DELETE_DATA.getCode(), 10);
            return ApiResult.newSuccess();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return ApiResult.newError(e.getMessage());
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 导出无销量下架产品
     */
    @GetMapping("/downloadNoSalesRetireData")
    public ApiResult<String> downloadNoSalesRetireData(@RequestParam Integer daysBefore) {
        if (null == daysBefore) {
            return ApiResult.newError("参数不可为空");
        }

        try {
            walmartItemService.downloadNoSalesRetireData(daysBefore);
            return ApiResult.newSuccess("请前往Walmart导出日志页面查看下载结果");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping("/getImages")
    public ApiResult<Map<String, List<String>>> getImages(@RequestBody List<String> mainSkuList) {
        if (CollectionUtils.isEmpty(mainSkuList)) {
            return ApiResult.newError("参数不可为空");
        }
        mainSkuList = mainSkuList.stream().distinct().collect(Collectors.toList());
        Map<String, List<String>> spuToImageMap = new HashMap<>();
        mainSkuList.stream().forEach(o ->{
            try {
                List<String> images = walmartTemplateService.getImagesByMainSku(o);
                if (CollectionUtils.isNotEmpty(images)) {
                    spuToImageMap.put(o, images);
                }
            }catch (Exception e){
                log.error(e.getMessage());
            }
        });
        return ApiResult.newSuccess(spuToImageMap);
    }
}