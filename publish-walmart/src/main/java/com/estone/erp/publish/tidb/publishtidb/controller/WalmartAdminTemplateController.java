package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelDO;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.product.response.PageResult;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplate;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartAdminTemplateLog;
import com.estone.erp.publish.tidb.publishtidb.model.dto.WalmartAdminTemplateLogPageDto;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateLogService;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartAdminTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description Walmart Admin 范本控制器
 * @createDate 2025-06-27 15:30:00
 */
@Slf4j
@RestController
@RequestMapping("walmartAdminTemplate")
public class WalmartAdminTemplateController {
    @Resource
    private WalmartAdminTemplateService walmartAdminTemplateService;

    @Resource
    private WalmartAdminTemplateLogService walmartAdminTemplateLogService;

    /**
     * 查询Walmart Admin范本列表
     * 支持多条件筛选：范本编号、货号、标题、店铺、数据来源、刊登类型、状态、售卖形式
     *
     * @param cquery 请求参数
     * @return 分页查询结果
     */
    @PostMapping("/search")
    public ApiResult<?> postWalmartAdminTemplate(@RequestBody(required = true) WalmartAdminTemplateCriteria cquery) {
        try {
            CQueryResult<WalmartAdminTemplate> results = walmartAdminTemplateService.search(cquery);
            return results;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据ID查询Walmart Admin范本
     *
     * @param id 范本ID
     * @return 范本详情
     */
    @GetMapping(value = "/{id}")
    public ApiResult<WalmartAdminTemplate> getWalmartAdminTemplate(@PathVariable(value = "id", required = true) Long id) {
        WalmartAdminTemplate template = walmartAdminTemplateService.getById(id);
        return ApiResult.newSuccess(template);
    }

    /**
     * 批量更新范本状态（启用/禁用）
     *
     * @param request 请求参数，包含ids和status
     * @return 操作结果
     */
    @PostMapping(value = "/updateStatus")
    public ApiResult<?> updateStatus(@RequestBody WalmartAdminTemplateCriteria request) {
        if (null == request || null == request.getStatus() || CollectionUtils.isEmpty(request.getIds())) {
            return ApiResult.newError("存在必填参数为空！");
        }

        try {
            String userName = WebUtils.getUserName();
            if (StringUtils.isBlank(userName)) {
                throw new IllegalArgumentException("当前登录人为获取到请重新登录");
            }

            walmartAdminTemplateService.updateStatus(request.getIds(), request.getStatus(), userName);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            log.error("更新范本状态失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 查看范本操作日志
     *
     * @return 日志列表
     */
    @PostMapping(value = "/logs")
    public ApiResult<?> getTemplateLogs(@RequestBody WalmartAdminTemplateLogPageDto dto) {
        try {
            CQueryResult<WalmartAdminTemplateLog> logs = walmartAdminTemplateLogService.getLogsByTemplateId(dto);
            return ApiResult.newSuccess(logs);
        } catch (Exception e) {
            log.error("查询范本日志失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 获取刊登SPU
     * 各个平台存在范本spu查询接口
     * @param request      request
     * @return PageResult<PublishSpuModelDO>
     */
    @PostMapping(value = "/getPublishSpuModelPage")
    public ApiResult<PageResult<PublishSpuModelDO>> getPublishSpuModelPage(@RequestBody PublishSpuModelRequest request) {
//        if (request == null) {
//            return ApiResult.newSuccess();
//        }

        PageResult<PublishSpuModelDO> result = new PageResult<>();

        try {
            // 构建查询条件
            LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper = new LambdaQueryWrapper<>();


            // 根据启用状态过滤
            if (request.getIsEnable() != null) {
                queryWrapper.eq(WalmartAdminTemplate::getStatus, request.getIsEnable());
            }

            // 根据更新时间范围过滤
            if (StringUtils.isNotBlank(request.getStarUpdateTime())) {
                queryWrapper.ge(WalmartAdminTemplate::getUpdateDate, request.getStarUpdateTime());
            }
            if (StringUtils.isNotBlank(request.getEndUpdateTime())) {
                queryWrapper.le(WalmartAdminTemplate::getUpdateDate, request.getEndUpdateTime());
            }

            // 查询总数
            long total = walmartAdminTemplateService.count(queryWrapper);
            int totalPages = (int) Math.ceil((double) total / request.getPageSize());

            result.setTotal((int) total);
            result.setCurrent(request.getPageIndex());
            result.setPages(totalPages);

            if (request.getPageIndex() > totalPages) {
                result.setSize(0);
                result.setRecords(Collections.emptyList());
                return ApiResult.newSuccess(result);
            }

            // 分页查询
            Page<WalmartAdminTemplate> page = new Page<>(request.getPageIndex(), request.getPageSize());
            walmartAdminTemplateService.page(page, queryWrapper);

            // 转换结果
            List<PublishSpuModelDO> spuModelDOS = page.getRecords().stream().map(template -> {
                PublishSpuModelDO modelDO = new PublishSpuModelDO();
                modelDO.setSpu(template.getArticleNumber());
                modelDO.setPlatform(SaleChannelEnum.WALMART.getChannelName());
                modelDO.setSite(request.getSite());
                modelDO.setSkuDataSource(template.getSkuDataSource());
                modelDO.setIsEnable(template.getStatus() != null && template.getStatus() == 1);
                return modelDO;
            }).collect(Collectors.toList());

            result.setSize(spuModelDOS.size());
            result.setRecords(spuModelDOS);

            return ApiResult.newSuccess(result);
        } catch (Exception e) {
            log.error("获取刊登SPU失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }
}
