package com.estone.erp.publish.walmart.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.WalmartTemplateCriteria;
import com.estone.erp.publish.walmart.model.WalmartTemplateExample;
import com.estone.erp.publish.walmart.model.dto.*;
import com.estone.erp.publish.walmart.model.vo.WalmartSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> walmart_template
 * 2022-08-12 15:32:22
 */
public interface WalmartTemplateService {
    int countByExample(WalmartTemplateExample example);

    CQueryResult<WalmartTemplate> search(CQuery<WalmartTemplateCriteria> cquery);

    List<WalmartTemplate> selectByExample(WalmartTemplateExample example);

    /**
     * 根据ID查询
     */
    List<WalmartTemplate> selectByPrimaryKey(List<Integer> ids);
    /**
     * 查询自定义字段
     * @param example
     * @return
     */
    List<WalmartTemplate> selectFiledColumnsByExample(WalmartTemplateExample example);

    List<WalmartTemplate> selectByArticleNumber(String articleNumber, String table);

    WalmartTemplate selectByPrimaryKey(Integer id, String table);

    int insert(WalmartTemplate record);

    void batchInsert(List<WalmartTemplate> walmartTemplates, String table);

    int updateByPrimaryKeySelective(WalmartTemplate record);

    int updateByPrimaryKey(WalmartTemplate record);

    int batchUpdateByPrimaryKeySelective(List<WalmartTemplate> walmartTemplates, String table);

    int updateByExampleSelective(WalmartTemplate record, WalmartTemplateExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    /**
     * 批量删除
     * @param idList
     * @param table
     */
    void batchDelete(List<Integer> idList, String table);

    /**
     * 根据id查询模板范本
     * @param id
     * @param table
     * @return
     */
    WalmartTemplateVO findById(Integer id, String table);

    /**
     * 添加或修改范本模板
     * @param walmartTemplateVO
     * @return
     */
    WalmartTemplateVO insertEditTemplate(WalmartTemplateVO walmartTemplateVO) throws Exception;

    /**
     * 获取范本模板信息
     *
     * @param articleNumber
     * @param categoryId
     * @return
     */
    WalmartTemplateSkuInfoVO getTemplateData(String articleNumber, String categoryId);


    /**
     * 根据主sku获取图片
     * @param mainSku
     * @return
     */
    List<String> getImagesByMainSku(String mainSku);

    /**
     * 复制到模板
     * @param copyTemplateRequestList
     * @return
     */
    String copyTemplate(List<CopyTemplateRequest> copyTemplateRequestList);

    /**
     * 批量另存模板
     * @param idList
     */
    void batchSaveAs(List<Integer> idList);

    /**
     * 另存模板
     * @param walmartTemplateVO
     * @return
     */
    String saveAsTemplate(WalmartTemplateVO walmartTemplateVO);

    /**
     * 根据货号查询产品信息
     * @param articleNumber
     * @return
     */
    List<WalmartSkuInfoVO> getProductInfo(String articleNumber);

    /**
     * 刊登
     * @param walmartTemplateList
     */
    void publish(List<WalmartTemplate> walmartTemplateList);

    /**
     * 批量更新模板的刊登状态
     * @param templateIdList
     * @param templateStatus
     */
    void batchUpdateTemplateStatus(List<Integer> templateIdList, int templateStatus);

    /**
     * 批量更新是否刊登状态
     */
    void batchUpdateIsPublishStatus(List<Integer> templateIdList, int isPublish);

    /**
     * 批量更新模板的库存状态
     * @param templateIdList
     * @param inventoryStatus
     */
    void batchUpdateInventoryStatus(List<Integer> templateIdList, int inventoryStatus);

    /**
     * 处理失败报告
     * @param templateIdList
     * @param taskType
     * @param msg
     */
    void handleFailFeedTaskStatus(List<String> templateIdList, String taskType, String msg);

    /**
     * 刊登模板
     * @param id
     * @param isRetry 是否重试
     */
    void publishTemplate(Integer id,Boolean isRetry) throws Exception;

    /**
     * 保存并刊登
     * @param walmartTemplateVO
     */
    void saveAndPublish(WalmartTemplateVO walmartTemplateVO) throws Exception;

    /**
     * 批量刊登
     * @param request
     */
    void batchPublish(BatchPublishRequest request) throws Exception;

    /**
     * 获取店铺后缀
     * @param accountNumber
     * @return
     */
    String getAccountSuffix(String accountNumber) throws Exception;

    /**
     * 过滤侵权词
     * @param walmartTemplate
     */
    void filterInfringingWords(WalmartTemplate walmartTemplate);

    /**
     * 上传库存
     * @param templateIdList
     */
    void uploadInventory(List<Integer> templateIdList) throws Exception;

    /**
     * 上传库存
     * @param accountNumber
     * @param beanList
     */
    void uploadInventory(String accountNumber, List<UploadInventoryBean> beanList);

    String getLastProductId(Integer skuDataSource);

    /**
     * 构建temu模板
     * @param accountNumber
     * @param itemId
     * @return
     */
    WalmartTemplate buildTemuTemplate(String accountNumber, String itemId);


    /**
     * 获取模板相关信息
     */
    Map<String, Object> getTemplateInfo(BuilderTemplateDTO builderTemplateDTO);


    /**
     * 校验模板数据
     */
    Map<String, Object> checkTemplateData(WalmartTemplateDTO templateDTO);


    /**
     * 模板-保存模板及范本-保存范本
     */
    WalmartTemplateDTO saveTemplate(WalmartTemplateDTO templateDTO);

    /**
     * 模板-刊登模板及范本-保存并刊登
     */
    WalmartTemplate saveAndPublishNew(WalmartTemplateDTO templateDTO);


    /**
     * 批量生成商品ID
     * @param accountEanRequest
     * @return
     */
    List<String> generateProductId(AccountEanRequest accountEanRequest);

    /**
     * 批量更新运费模板状态
     * @param templateIds 模板ID列表
     * @param status 状态
     */
    void batchUpdateShippingTemplateStatus(List<Integer> templateIds, Integer status);

    /**
     * 上传运费模板
     * @param accountNumber 账号
     * @param beanList 数据列表
     */
    void uploadShippingTemplate(String accountNumber, List<ShippingTemplateBean> beanList);
}
