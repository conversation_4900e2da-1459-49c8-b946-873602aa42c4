package com.estone.erp.publish.walmart.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.base.pms.service.StockKeepingUnitService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsTemuItem;
import com.estone.erp.publish.elasticsearch.model.EsTemuItemIncrementInfo;
import com.estone.erp.publish.elasticsearch.model.EsTemuItemSkuInfo;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import com.estone.erp.publish.walmart.call.inventory.WalmartBulkUpdateInventoryCall;
import com.estone.erp.publish.walmart.call.items.WalmartBulkItemCall;
import com.estone.erp.publish.walmart.call.shipping.WalmartUploadShippingTemplateCall;
import com.estone.erp.publish.walmart.componet.WalmartTemplateBuilderHelper;
import com.estone.erp.publish.walmart.componet.WalmartTemplateNewBuilderHelper;
import com.estone.erp.publish.walmart.componet.template.context.WalmartTemplateValidationContext;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.enums.*;
import com.estone.erp.publish.walmart.handler.publish.WalmartTemplatePublishExecutor;
import com.estone.erp.publish.walmart.handler.publish.param.TemplatePublishParam;
import com.estone.erp.publish.walmart.mapper.WalmartTemplateMapper;
import com.estone.erp.publish.walmart.model.*;
import com.estone.erp.publish.walmart.model.dto.*;
import com.estone.erp.publish.walmart.model.vo.WalmartSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateSkuInfoVO;
import com.estone.erp.publish.walmart.model.vo.WalmartTemplateVO;
import com.estone.erp.publish.walmart.mq.bean.FeedMessage;
import com.estone.erp.publish.walmart.service.*;
import com.estone.erp.publish.walmart.util.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.estone.erp.publish.walmart.util.WalmartTemplateUtils.getSkuReferImages;

/**
 * <AUTHOR> walmart_template
 * 2022-08-12 15:32:22
 */
@Service("walmartTemplateService")
@Slf4j
public class WalmartTemplateServiceImpl implements WalmartTemplateService {
    @Resource
    private WalmartTemplateMapper walmartTemplateMapper;

    @Resource
    private StockKeepingUnitService stockKeepingUnitService;

    @Resource
    private WalmartCategoryAttributeService walmartCategoryAttributeService;


    @Resource
    private InfringementWordService infringementWordService;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private TemuItemService temuItemService;

    @Resource
    private WalmartAccountConfigService walmartAccountConfigService;

    @Resource
    private TemuItemWamlartPublishRecordService temuItemWamlartPublishRecordService;

    @Resource
    private WalmartTemplateBuilderHelper walmartTemplateBuilderHelper;

    @Resource
    private WalmartTemplateNewBuilderHelper walmartTemplateNewBuilderHelper;

    @Resource
    private WalmartTemplatePublishExecutor walmartTemplatePublishExecutor;
    @Autowired
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private WalmartTimePublishQueueService walmartTimePublishQueueService;

    @Override
    public List<WalmartTemplate> selectByPrimaryKey(List<Integer> ids) {
        WalmartTemplateExample example = new WalmartTemplateExample();
        example.createCriteria().andIdIn(ids);
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        return walmartTemplateMapper.selectByExample(example);
    }

    @Override
    public int countByExample(WalmartTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return walmartTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<WalmartTemplate> search(CQuery<WalmartTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        WalmartTemplateCriteria query = cquery.getSearch();

        if (BooleanUtils.isNotTrue(query.getIsParent())) {
            String platform = SaleChannel.CHANNEL_WALMART;
            // 人员权限
            Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, query.getCreateBy(), query.getCreateByList(),
                    query.getAccountNumber(), query.getAccountNumberList());
            if (BooleanUtils.isTrue(employeePair.getLeft())) {
                query.setCreateByList(employeePair.getRight());
            }
        }


        WalmartTemplateExample example = query.getExample();

        String table = WalmartTemplateUtils.getTemplateTable(query.getIsParent());
        example.setTable(table);

        // 排序
        example.setOrderByClause("create_date desc ");

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = walmartTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<WalmartTemplate> walmartTemplates = walmartTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<WalmartTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(walmartTemplates);
        return result;
    }

    @Override
    public WalmartTemplate selectByPrimaryKey(Integer id, String table) {
        Assert.notNull(id, "id is null!");
        Assert.notNull(table, "table is null!");
        return walmartTemplateMapper.selectByPrimaryKey(id, table);
    }

    @Override
    public List<WalmartTemplate> selectByExample(WalmartTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return walmartTemplateMapper.selectByExample(example);
    }

    @Override
    public List<WalmartTemplate> selectFiledColumnsByExample(WalmartTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return walmartTemplateMapper.selectFiledColumnsByExample(example);
    }

    @Override
    public List<WalmartTemplate> selectByArticleNumber(String articleNumber, String table) {
        Assert.notNull(articleNumber, "articleNumber is null!");
        Assert.notNull(table, "table is null!");
        WalmartTemplateExample example = new WalmartTemplateExample();
        example.setTable(table);
        example.createCriteria().andArticleNumberEqualTo(articleNumber);
        return walmartTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(WalmartTemplate record) {
        Assert.notNull(record, "record is null!");
        // 设置哪张表
        record.initTable();
        return walmartTemplateMapper.insert(record);
    }

    @Override
    public void batchInsert(List<WalmartTemplate> walmartTemplates, String table) {
        Assert.notNull(walmartTemplates, "record is null!");
        Assert.notNull(table, "table is null!");
        walmartTemplateMapper.batchInsert(walmartTemplates, table);
    }

    @Override
    public int updateByPrimaryKeySelective(WalmartTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return walmartTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(WalmartTemplate record) {
        Assert.notNull(record, "record is null!");
        return walmartTemplateMapper.updateByPrimaryKey(record);
    }

    @Override
    public int batchUpdateByPrimaryKeySelective(List<WalmartTemplate> walmartTemplates, String table) {
        Assert.notNull(walmartTemplates, "walmartTemplates is null!");
        Assert.notNull(table, "table is null!");
        return walmartTemplateMapper.batchUpdateByPrimaryKeySelective(walmartTemplates, table);
    }

    @Override
    public int updateByExampleSelective(WalmartTemplate record, WalmartTemplateExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return walmartTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public void batchUpdateTemplateStatus(List<Integer> templateIdList, int templateStatus) {
        Assert.notNull(templateIdList, "templateIdList is null!");
        walmartTemplateMapper.batchUpdateTemplateStatus(templateIdList, templateStatus);
    }

    @Override
    public void batchUpdateIsPublishStatus(List<Integer> templateIdList, int isPublish) {
        Assert.notNull(templateIdList, "templateIdList is null!");
        walmartTemplateMapper.batchUpdateIsPublishStatus(templateIdList, isPublish);
    }

    @Override
    public void batchUpdateInventoryStatus(List<Integer> templateIdList, int inventoryStatus) {
        Assert.notNull(templateIdList, "templateIdList is null!");
        walmartTemplateMapper.batchUpdateInventoryStatus(templateIdList, inventoryStatus);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return walmartTemplateMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchDelete(List<Integer> idList, String table) {
        Assert.notNull(idList, "idList is null!");
        walmartTemplateMapper.batchDelete(idList, table);
    }

    @Override
    public WalmartTemplateVO findById(Integer id, String table) {
        return walmartTemplateMapper.selectVoByPrimaryKey(id, table);
    }

    @Override
    public WalmartTemplateVO insertEditTemplate(WalmartTemplateVO walmartTemplateVO) throws Exception {
        if (null == walmartTemplateVO) {
            throw new Exception("参数为空");
        }

        // 过滤侵权词
        filterInfringWord(walmartTemplateVO);


        // 处理描述信息
        WalmartTemplateUtils.handleTitleDescription(walmartTemplateVO);

        Integer id = walmartTemplateVO.getId();
        if (null == id) {
            // 如果该货号已存在范本，不允许添加
            String articleNumber = walmartTemplateVO.getArticleNumber();
            String table = WalmartTemplateTableEnum.WALMART_TEMPLATE_MODEL.getCode();
            List<WalmartTemplate> walmartTemplates = selectByArticleNumber(articleNumber, table);
            if (CollectionUtils.isNotEmpty(walmartTemplates)) {
                throw new Exception("此货号已存在范本");
            }

            walmartTemplateVO.setTable(table);
            walmartTemplateVO.setCreateBy(WebUtils.getUserName());
            walmartTemplateVO.setCreateDate(new Timestamp(System.currentTimeMillis()));
            insert(walmartTemplateVO);
        } else {
            if (walmartTemplateVO.getIsParent()) {
                // 如果该货号查出的范本的id和参数的id不一致，不允许修改
                String articleNumber = walmartTemplateVO.getArticleNumber();
                String table = WalmartTemplateTableEnum.WALMART_TEMPLATE_MODEL.getCode();
                List<WalmartTemplate> walmartTemplates = selectByArticleNumber(articleNumber, table);
                if (CollectionUtils.isNotEmpty(walmartTemplates)) {
                    if (walmartTemplates.size() > 1 || !ObjectUtils.nullSafeEquals(walmartTemplateVO.getId(), walmartTemplates.get(0).getId())) {
                        throw new Exception("此货号已存在范本");
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(walmartTemplateVO.getWalmartVariantList())) {
                walmartTemplateVO.setVariations(JSON.toJSONString(walmartTemplateVO.getWalmartVariantList()));
            }
            walmartTemplateVO.initTable();
            walmartTemplateVO.setUpdateBy(WebUtils.getUserName());
            walmartTemplateVO.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            //图片上传服务器标识
            String uploadImageServer = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "UPLOAD_IMAGE_SERVER", 10);
            walmartTemplateVO.setImageServer(StringUtils.isNotBlank(uploadImageServer) ? Integer.parseInt(uploadImageServer) : 0);
            updateByPrimaryKey(walmartTemplateVO);
        }

        return walmartTemplateVO;
    }

    private void filterInfringWord(WalmartTemplateVO walmartTemplateVO) throws Exception {

        //属性和品牌存在侵权词无法保存
        String attributeStr = null;
        if (walmartTemplateVO.getSaleVariant() && StringUtils.isNotBlank(walmartTemplateVO.getVariations())) {
            List<WalmartVariant> walmartVariantList = JSON.parseObject(walmartTemplateVO.getVariations(), new TypeReference<List<WalmartVariant>>() {
            });
            if (CollectionUtils.isNotEmpty(walmartVariantList)) {
                attributeStr = WalmartTemplateUtils.getAttributeStr(walmartVariantList);
            }
        }

        Map<String, String> resultMap = WalmartTemplateUtils.checkInfringementWords(walmartTemplateVO.getTitle(), walmartTemplateVO.getDescription(), walmartTemplateVO.getBrand(), walmartTemplateVO.getKeyFeatures(), attributeStr);
        if (MapUtils.isNotEmpty(resultMap) && (resultMap.containsKey("属性") || resultMap.containsKey("品牌"))) {
            throw new Exception("属性或品牌存在侵权词，无法保存");
        }

        if (BooleanUtils.isTrue(walmartTemplateVO.getFilterInfringingWord())) {
            filterInfringingWords(walmartTemplateVO);
        }


    }

    @Override
    public WalmartTemplateSkuInfoVO getTemplateData(String articleNumber, String categoryId) {
        // 查询产品信息，并过滤停产存档废弃，以及禁售的产品
        List<ProductInfo> productInfoList = WalmartTemplateUtils.getTemplateSkuInfo(articleNumber);

        List<StockKeepingUnitWithBLOBs> skuInfoList = stockKeepingUnitService.handleProductInfo(productInfoList);
        if (CollectionUtils.isEmpty(skuInfoList)) {
            return null;
        }
        WalmartTemplateSkuInfoVO walmartTemplateSkuInfoVO = new WalmartTemplateSkuInfoVO();
        ProductInfo productInfo = productInfoList.get(0);
        walmartTemplateSkuInfoVO.setSubCategoryId(categoryId);
        // 货号
        walmartTemplateSkuInfoVO.setArticleNumber(articleNumber);

        // 数据来源
        walmartTemplateSkuInfoVO.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());

        // 标签
        walmartTemplateSkuInfoVO.setTag(productInfo.getTag());

        // 上贴备注
        walmartTemplateSkuInfoVO.setPostRemark(productInfo.getPostRemark());

        // 图片
        walmartTemplateSkuInfoVO.setImageList(getImagesByMainSku(productInfo.getMainSku()));


        // 是否是变体
        Boolean saleVariant = articleNumber.equals(productInfo.getMainSku())
                && !ObjectUtils.nullSafeEquals(productInfo.getMainSku(), productInfo.getSonSku());
        walmartTemplateSkuInfoVO.setSaleVariant(saleVariant);

        if (!saleVariant) {
            // 单品状态
            walmartTemplateSkuInfoVO.setItemStatus(productInfo.getItemStatus());

            // 禁售平台
            walmartTemplateSkuInfoVO.setSaleForbiddenPlatform(StringUtils.join(productInfo.getSaleForbiddenList(), ","));

            // 重量
            walmartTemplateSkuInfoVO.setShippingWeight(getSkuShippingWeight(skuInfoList.get(0)));

            //附图
            List<String> skuReferImages = getSkuReferImages(articleNumber, null, walmartTemplateSkuInfoVO.getImageList());
            walmartTemplateSkuInfoVO.setExtraImageUrls(JSON.toJSONString(skuReferImages));
        } else {
            // 获取分类属性属性
            Map<String, Map<String, Object>> attributeMap = walmartCategoryAttributeService.matchPlatformAttribute(categoryId, productInfoList);

            List<WalmartVariant> walmartVariants = new ArrayList<>(skuInfoList.size());
            for (StockKeepingUnitWithBLOBs item : skuInfoList) {
                String itemArticleNumber = item.getArticleNumber();
                if (StringUtils.isBlank(itemArticleNumber)) {
                    continue;
                }
                WalmartVariant walmartVariant = new WalmartVariant();
                walmartVariants.add(walmartVariant);
                walmartVariant.setSku(itemArticleNumber);
                walmartVariant.setItemStatus(item.getSkulifecyclephase());
                walmartVariant.setSaleForbiddenPlatform(item.getForbiddensalechannel());
                walmartVariant.setSpu(productInfo.getMainSku());

                WalmartTemplateUtils.setTempalterImage(walmartVariant, walmartTemplateSkuInfoVO.getImageList());

                // 重量
                walmartVariant.setShippingWeight(getSkuShippingWeight(item));

                // 分类属性
                walmartVariant.setAttributeMap(attributeMap.get(itemArticleNumber));
            }
            walmartTemplateSkuInfoVO.setWalmartVariantList(walmartVariants);
        }

        // 匹配标题、描述、五点描述
        WalmartTemplateUtils.matchingTemplateInfo(productInfo.getMainSku(), walmartTemplateSkuInfoVO);

        return walmartTemplateSkuInfoVO;
    }


    /**
     * 计算重量（自身净重+包材重量+搭配包材重量+面单重量3g）平台计量单位是磅  1克=0.002204623磅
     *
     * @param item
     * @return
     */
    private Double getSkuShippingWeight(StockKeepingUnitWithBLOBs item) {
        // 自身净重
        double skuWeight = item.getWeight() == null ? 0.0 : item.getWeight();

        // 包材重量 + 搭配包材重量
        double pmWeight = item.getPmWeight() == null ? 0.0 : item.getPmWeight();

        return NumberUtils.format((skuWeight + pmWeight + 3) * 0.002204623, "0.###");
    }

    /**
     * 根据主sku获取图片
     * walmart用 1600*1600 图片
     *
     * @param mainSku
     * @return
     */
    @Override
    public List<String> getImagesByMainSku(String mainSku) {
        if (StringUtils.isBlank(mainSku)) {
            return Collections.emptyList();
        }
        String type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
        return FmsUtils.getPictureUrlBySkuAndType(mainSku, type);
    }

    @Override
    public String copyTemplate(List<CopyTemplateRequest> copyTemplateRequestList) {
        List<String> errorList = new ArrayList<>();
        for (CopyTemplateRequest copyTemplateRequest : copyTemplateRequestList) {
            Integer id = copyTemplateRequest.getId();
            Integer quantity = copyTemplateRequest.getQuantity();
            String error = copyTemplate(id, quantity);
            if (StringUtils.isNotBlank(error)) {
                errorList.add(error);
            }
        }

        if (CollectionUtils.isNotEmpty(errorList)) {
            return JSON.toJSONString(errorList);
        }
        return null;
    }

    private String copyTemplate(Integer id, Integer quantity) {
        if (null == id || null == quantity) {
            return "id或数量不可为空";
        }

        WalmartTemplate walmartTemplate = selectByPrimaryKey(id, WalmartTemplateTableEnum.WALMART_TEMPLATE_MODEL.getCode());
        if (null == walmartTemplate) {
            return String.format("范本编号%s，查询不到该范本", id);
        }

        List<WalmartTemplate> copyList = new ArrayList<>();
        for (int i = 0; i < quantity; i++) {
            WalmartTemplate copyTemplate = new WalmartTemplate();
            BeanCopier beanCopier = BeanCopier.create(WalmartTemplate.class, WalmartTemplate.class, false);
            beanCopier.copy(walmartTemplate, copyTemplate, null);

            // 重新从产品系统取标题，描述，五点描述
//            String articleNumber = copyTemplate.getArticleNumber();
//            String mainSku = ProductUtils.getMainSku(articleNumber);
//            WalmartTemplateSkuInfoVO templateSkuInfoVO = new WalmartTemplateSkuInfoVO();
//            WalmartTemplateUtils.matchingTemplateInfo(mainSku, templateSkuInfoVO);
//            copyTemplate.setTitle(templateSkuInfoVO.getTitle());
//            copyTemplate.setDescription(templateSkuInfoVO.getDescription());
//            copyTemplate.setKeyFeatures(templateSkuInfoVO.getKeyFeatures());

            // 重新获取图片信息
//            reacquireImages(mainSku, copyTemplate);

            copyTemplate.setId(null);
            copyTemplate.setAccountNumber(null);
            copyTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
            copyTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
            copyTemplate.setCreateBy(WebUtils.getUserName());
            copyTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
            copyTemplate.setUpdateBy(null);
            copyTemplate.setUpdateDate(null);

            copyList.add(copyTemplate);
        }

        // 批量保存
        if (CollectionUtils.isNotEmpty(copyList)) {
            batchInsert(copyList, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        }

        return null;
    }

//    private void reacquireImages(String mainSku, WalmartTemplate copyTemplate) {
//        if (StringUtils.isBlank(mainSku)) {
//            return;
//        }
//
//        // 获取所有图片
//        List<String> allImages = getImagesByMainSku(mainSku);
//        if (CollectionUtils.isEmpty(allImages)) {
//            return;
//        }
//        if (copyTemplate.getSaleVariant()) {
//            List<WalmartVariant> variants = JSON.parseObject(copyTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
//            });
//            for (WalmartVariant variant : variants) {
//                String articleNumber = variant.getSku();
//                List<String> mainImage = allImages.stream().filter(o -> o.contains(articleNumber + ".")).collect(Collectors.toList());
//                variant.setMainImageUrl(mainImage.get(0));
//                variant.setSwatchImageUrl(mainImage.get(0));
//                allImages.removeAll(mainImage);
//                if (allImages.size() > 20) {
//                    allImages = allImages.subList(0, 20);
//                }
//                variant.setExtraImageUrls(JSON.toJSONString(allImages));
//            }
//            copyTemplate.setVariations(JSON.toJSONString(variants));
//        } else {
//            String articleNumber = copyTemplate.getArticleNumber();
//            List<String> mainImage = allImages.stream().filter(o -> o.contains(articleNumber + ".")).collect(Collectors.toList());
//            copyTemplate.setMainImageUrl(mainImage.get(0));
//            allImages.removeAll(mainImage);
//            if (allImages.size() > 20) {
//                allImages = allImages.subList(0, 20);
//            }
//            copyTemplate.setExtraImageUrls(JSON.toJSONString(allImages));
//        }
//    }

    @Override
    public void batchSaveAs(List<Integer> idList) {
        WalmartTemplateExample example = new WalmartTemplateExample();
        String table = WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode();
        example.setTable(table);
        example.createCriteria().andIdIn(idList);
        List<WalmartTemplate> walmartTemplates = selectByExample(example);

        List<WalmartTemplate> insertList = new ArrayList<>();
        for (WalmartTemplate walmartTemplate : walmartTemplates) {
            walmartTemplate.setId(null);
            walmartTemplate.setUpdateBy(null);
            walmartTemplate.setUpdateDate(null);
            walmartTemplate.setCreateBy(WebUtils.getUserName());
            walmartTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
            walmartTemplate.setPublishType(WalmartPublishTypeEnum.PUBLISH.getCode());
            walmartTemplate.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
            walmartTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
            walmartTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
            walmartTemplate.setUploadShippingTemplateStatus(WalmartUploadShippingTemplateEnum.NOT_UPLOAD.getCode());
            insertList.add(walmartTemplate);
        }

        batchInsert(insertList, table);
    }

    @Override
    public String saveAsTemplate(WalmartTemplateVO walmartTemplateVO) {
        walmartTemplateVO.setId(null);
        if (CollectionUtils.isNotEmpty(walmartTemplateVO.getWalmartVariantList())) {
            walmartTemplateVO.setVariations(JSON.toJSONString(walmartTemplateVO.getWalmartVariantList()));
        }
        walmartTemplateVO.setUpdateBy(null);
        walmartTemplateVO.setUpdateDate(null);
        walmartTemplateVO.setCreateBy(WebUtils.getUserName());
        walmartTemplateVO.setCreateDate(new Timestamp(System.currentTimeMillis()));
        walmartTemplateVO.setPublishType(WalmartPublishTypeEnum.PUBLISH.getCode());
        walmartTemplateVO.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
        walmartTemplateVO.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
        walmartTemplateVO.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
        walmartTemplateVO.setUploadShippingTemplateStatus(WalmartUploadShippingTemplateEnum.NOT_UPLOAD.getCode());
        walmartTemplateVO.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());

        int count = insert(walmartTemplateVO);
        if (count > 0) {
            return null;
        } else {
            return "保存失败";
        }
    }

    @Override
    public String getAccountSuffix(String accountNumber) {
        if (StringUtils.isBlank(accountNumber)) {
            throw new RuntimeException("参数为空");
        }

        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        if (null == account) {
            throw new RuntimeException("获取不到账号信息");
        }

        String accountSuffix = account.getSellerSkuSuffix();
        if (StringUtils.isBlank(accountSuffix)) {
            throw new RuntimeException("获取不到店铺后缀");
        }

        return accountSuffix;
    }

    @Override
    public List<WalmartSkuInfoVO> getProductInfo(String articleNumber) {
        if (StringUtils.isBlank(articleNumber)) {
            throw new IllegalArgumentException("参数为空");
        }

        String[] field = {"mainSku", "sonSku", "itemStatus", "postRemark", "tag", "salesProhibition"};
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber), field);
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new NoSuchElementException("查询不到产品信息");
        }

        // 如果参数货号是子sku，则以单体形式返回
        if (!articleNumber.equalsIgnoreCase(productInfoList.get(0).getMainSku())) {
            productInfoList = productInfoList
                    .stream().filter(o -> articleNumber.equalsIgnoreCase(o.getSonSku())).collect(Collectors.toList());
        }

        List<WalmartSkuInfoVO> walmartSkuInfoVOList = new ArrayList<>();
        for (ProductInfo productInfo : productInfoList) {
            WalmartSkuInfoVO walmartSkuInfoVO = new WalmartSkuInfoVO();
            walmartSkuInfoVO.setMainSku(productInfo.getMainSku());
            walmartSkuInfoVO.setSonSku(productInfo.getSonSku());
            walmartSkuInfoVO.setItemStatus(productInfo.getItemStatus());
            walmartSkuInfoVO.setPostRemark(productInfo.getPostRemark());
            walmartSkuInfoVO.setSaleForbiddenPlatform(StringUtils.join(productInfo.getSaleForbiddenList(), ","));
            walmartSkuInfoVO.setTag(productInfo.getTag());

            walmartSkuInfoVOList.add(walmartSkuInfoVO);
        }

        return walmartSkuInfoVOList;
    }

    @Override
    public void publish(List<WalmartTemplate> walmartTemplateList) {
        if (CollectionUtils.isEmpty(walmartTemplateList)) {
            return;
        }

//        // 拦截禁售sku 停产存档
//        try {
//            interceptForbiddenSku(walmartTemplateList);
//            if (CollectionUtils.isEmpty(walmartTemplateList)) {
//                return;
//            }
//        } catch (Exception e) {
//            log.error("过滤禁售sku报错：" + e.getMessage());
//        }

        try {
            // 校验模板数据并拦截
            checkTemplateDataAndIntercept(walmartTemplateList);
            if (CollectionUtils.isEmpty(walmartTemplateList)) {
                return;
            }
        } catch (Exception e) {
            log.error("检查报错：" + e.getMessage());
        }


        // 获取模板id
        List<String> templateIdList = walmartTemplateList.stream().map(o -> o.getId().toString()).collect(Collectors.toList());

        // 更新模板的刊登状态
        List<Integer> templateIds = walmartTemplateList.stream().map(WalmartTemplate::getId).collect(Collectors.toList());
        batchUpdateTemplateStatus(templateIds, WalmartTemplateStatusEnum.PUBLISHING.getCode());
        batchUpdateIsPublishStatus(templateIds, 1);

        try {
            // 获取图片映射路径
            WalmartTemplateUtils.setOSSUrl(walmartTemplateList);

            SaleAccountAndBusinessResponse account =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartTemplateList.get(0).getAccountNumber());
            WalmartAccountUtils.refreshAccountToken(account);

            // 执行刊登
            String feedId = new WalmartBulkItemCall(account).uploadItem(walmartTemplateList);

            // 处理报告添加feedId
            FeedTask update = new FeedTask();
            update.setPlatform(Platform.Walmart.name());
            update.setTableIndex();
            update.setAssociationId(feedId);
            FeedTaskExample example = new FeedTaskExample();
            example.createCriteria()
                    .andTaskTypeEqualTo(WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn())
                    .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                    .andAttribute4In(templateIdList);
            feedTaskService.updateByExampleSelective(update, example);

            // 发送消息
            FeedMessage feedMessage = new FeedMessage();
            feedMessage.setFeedId(feedId);
            feedMessage.setAccountNumber(walmartTemplateList.get(0).getAccountNumber());
            feedMessage.setRelationIdList(templateIds);
            feedMessage.setType(WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());
            rabbitTemplate.convertAndSend(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                    PublishQueues.WALMART_PUBLISH_DELAY_QUEUE_KEY, feedMessage, (message) -> {
                        message.getMessageProperties().setExpiration(WalmartPublishConstant.FIRST_TTL_TIME);
                        return message;
                    });
        } catch (Exception e) {
            log.error("提交刊登时报错：" + e.getMessage(), e);
            // 更新模板的刊登状态
            batchUpdateTemplateStatus(templateIds, WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
            // 更新处理报告状态
            handleFailFeedTaskStatus(templateIdList, WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), e.getMessage());
            // _TE账号移除刊登中记录
            if (WalmartAccountUtils.isPublishTemuAccount(walmartTemplateList.get(0).getAccountNumber())) {
                List<String> mainSkus = walmartTemplateList.stream().map(WalmartTemplate::getArticleNumber).distinct().collect(Collectors.toList());
                temuItemWamlartPublishRecordService.batchRemovePublishing(mainSkus, walmartTemplateList.get(0).getAccountNumber());
            }
            //处理定时刊登状态
            updateQueueFailStatus(templateIds, e.getMessage());

        }
    }

    private void updateQueueFailStatus(List<Integer> templateIds, String message) {
        List<WalmartTemplate> walmartTemplates = this.selectByPrimaryKey(templateIds);
        if (CollectionUtils.isEmpty(walmartTemplates)) {
            return;
        }
        walmartTemplates.forEach(walmartTemplate -> {
            if (!ObjectUtils.isEmpty(walmartTemplate.getQueueId())) {
                WalmartTimePublishQueue queue = new WalmartTimePublishQueue();
                queue.setId(walmartTemplate.getQueueId());
                queue.setStatus(WalmartTimePublishEnums.END.getCode());
                queue.setPublishStatus(WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                queue.setExtra(message);
                queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
                walmartTimePublishQueueService.updateById(queue);
            }

        });

    }

    private void interceptForbiddenSku(List<WalmartTemplate> walmartTemplateList) {
        // 校验失败的模板
        List<WalmartTemplate> failTemplateList = new ArrayList<>();

        Iterator<WalmartTemplate> it = walmartTemplateList.iterator();
        while (it.hasNext()) {
            WalmartTemplate walmartTemplate = it.next();
            try {
                walmartTemplateBuilderHelper.filterIllegalSku(walmartTemplate);
            } catch (Exception e) {
                failTemplateList.add(walmartTemplate);
                it.remove();
            }
        }

        if (CollectionUtils.isNotEmpty(failTemplateList)) {
            // 更新处理报告
            List<String> idList = failTemplateList.stream().map(o -> o.getId().toString()).collect(Collectors.toList());
            String msg = "SKU均为停产存档废弃或者在walmart禁售";
            handleFailFeedTaskStatus(idList, WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), msg);

            // 更新模板状态
            List<Integer> templateIdList = failTemplateList.stream().map(WalmartTemplate::getId).collect(Collectors.toList());
            batchUpdateTemplateStatus(templateIdList, WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
        }
    }


    /**
     * 校验模板数据并拦截
     *
     * @param walmartTemplates
     */
    private void checkTemplateDataAndIntercept(List<WalmartTemplate> walmartTemplates) {
        Iterator<WalmartTemplate> it = walmartTemplates.iterator();
        while (it.hasNext()) {
            WalmartTemplate template = it.next();
            String accountNumber = template.getAccountNumber();
            Integer templateId = template.getId();
            try {
                WalmartTemplateDTO templateDTO = new WalmartTemplateDTO();
                BeanCopier beanCopier = BeanCopier.create(WalmartTemplate.class, WalmartTemplateDTO.class, false);
                beanCopier.copy(template, templateDTO, null);

                WalmartTemplateValidationContext validationContext = new WalmartTemplateValidationContext(templateDTO);
                // 产品信息校验, 校验规则中若存在一条未通过则直接返回提示
                BuilderTemplateDTO builderDTO = walmartTemplateNewBuilderHelper.validationProductData(validationContext);
                if (!validationContext.isAllSuccess()) {
                    String errorMsg = validationContext.getValidations().stream().filter(validation -> BooleanUtils.isFalse(validation.getSuccess()))
                            .map(WalmartTemplateValidationDTO::getErrorMsg).collect(Collectors.joining(","));
                    throw new RuntimeException(errorMsg);
                }

                List<String> existSkuList = Optional.ofNullable(builderDTO.getProductInfos()).orElse(Collections.emptyList())
                        .stream().map(ProductInfo::getSonSku).distinct().collect(Collectors.toList());

                if (template.getSaleVariant()) {
                    List<WalmartVariant> variants = JSON.parseObject(template.getVariations(), new TypeReference<List<WalmartVariant>>() {
                    });
                    // 仅保留产品信息校验后的数据
                    variants.removeIf(skuInfo -> !existSkuList.contains(skuInfo.getSku()));
                    List<String> skuList = variants.stream().map(WalmartVariant::getSku).distinct().collect(Collectors.toList());
                    List<String> filteredSkuList = WalmartItemUtils.filterSkuListByAccountConfig(accountNumber, skuList);
                    variants.removeIf(skuInfo -> !filteredSkuList.contains(skuInfo.getSku()));
                    if (CollectionUtils.isEmpty(variants)) {
                        throw new RuntimeException("根据店铺配置上架拦截:无符合的sku");
                    }
                    List<String> sellerSkuList = variants.stream().map(WalmartVariant::getSellerSku).collect(Collectors.toList());

                    //检验变体属性不能为空
                    if (variants.stream().anyMatch(skuInfo -> MapUtils.isEmpty(skuInfo.getAttributeMap()))) {
                        throw new RuntimeException("变体属性不能为空");
                    }

                    WalmartTemplate update = new WalmartTemplate();
                    update.setId(templateId);
                    update.setVariations(JSON.toJSONString(variants));
                    update.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
                    walmartTemplateService.updateByPrimaryKeySelective(update);
                    WalmartFeedTaskUtil.updateTemplateFeedTaskSellerSku(String.valueOf(templateId), sellerSkuList, WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());
                } else {
                    List<String> skuList = Collections.singletonList(template.getArticleNumber());
                    WalmartItemUtils.filterSkuListByAccountConfig(accountNumber, skuList);
                }


            } catch (Exception e) {
                // 更新处理报告
                handleFailFeedTaskStatus(Collections.singletonList(String.valueOf(templateId)), WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), e.getMessage());

                // 更新模板状态
                batchUpdateTemplateStatus(Collections.singletonList(templateId), WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
                updateQueueFailStatus(Collections.singletonList(templateId), e.getMessage());
                it.remove();
            }
        }
    }


    @Override
    public void handleFailFeedTaskStatus(List<String> templateIdList, String taskType, String msg) {
        FeedTask update = new FeedTask();
        update.setPlatform(Platform.Walmart.name());
        update.setTableIndex();
        update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
        update.setFinishTime(new Timestamp(System.currentTimeMillis()));
        update.setResultMsg(msg);

        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andTaskTypeEqualTo(taskType)
                .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                .andAttribute4In(templateIdList);

        feedTaskService.updateByExampleSelective(update, example);
    }

    @Override
    public void publishTemplate(Integer id, Boolean isRetry) throws Exception {
        if (null == id) {
            throw new Exception("id不能为空");
        }

        WalmartTemplate walmartTemplate = selectByPrimaryKey(id, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        if (null == walmartTemplate) {
            throw new Exception("获取不到模板信息");
        }

        // 校验重复刊登
        List<Integer> templateIds = checkRepeatPublication(Lists.newArrayList(walmartTemplate));
        if (CollectionUtils.isNotEmpty(templateIds)) {
            throw new Exception("当前店铺中该货号已刊登或已提交刊登，不允许重复刊登");
        }

        // 创建处理报告
        WalmartFeedTaskUtil.generateFeedTask(Lists.newArrayList(walmartTemplate), WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn(), isRetry);

        // 更新模板状态
        WalmartTemplate updateTemplate = new WalmartTemplate();
        updateTemplate.setId(walmartTemplate.getId());
        if (null != walmartTemplate.getPublishRole()) {
            updateTemplate.setPublishRole(walmartTemplate.getPublishRole());
        } else {
            updateTemplate.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
        }
        if (null != walmartTemplate.getPublishType()) {
            updateTemplate.setPublishType(walmartTemplate.getPublishType());
        } else {
            updateTemplate.setPublishType(WalmartPublishTypeEnum.PUBLISH.getCode());
        }

        // 是否重试,重试将云服务器调换
        if (isRetry) {
            String uploadImageServer = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "UPLOAD_IMAGE_SERVER", 10);
            updateTemplate.setImageServer("0".equals(uploadImageServer) ? 1 : 0);
        }
        updateTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_RELEASE.getCode());
        updateTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
        updateTemplate.setUpdateBy(StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : StrConstant.ADMIN);
        updateTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        updateTemplate.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        updateByPrimaryKeySelective(updateTemplate);
    }

    @Override
    public void saveAndPublish(WalmartTemplateVO walmartTemplateVO) throws Exception {

        //过滤侵权词
        filterInfringWord(walmartTemplateVO);

        // 先保存范本
        WalmartTemplateVO walmartTemplate = insertEditTemplate(walmartTemplateVO);
        if (null == walmartTemplate) {
            throw new Exception("获取不到范本");
        }

        // 校验重复刊登
        List<Integer> templateIds = checkRepeatPublication(Lists.newArrayList(walmartTemplateVO));
        if (CollectionUtils.isNotEmpty(templateIds)) {
            throw new Exception("保存并刊登失败！当前店铺中该货号已刊登或已提交刊登，不允许重复刊登");
        }

        // 复制一份模板并刊登
        WalmartTemplate copyTemplate = new WalmartTemplate();
        BeanCopier beanCopier = BeanCopier.create(WalmartTemplate.class, WalmartTemplate.class, false);
        beanCopier.copy(walmartTemplate, copyTemplate, null);

        copyTemplate.setId(null);
        copyTemplate.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
        copyTemplate.setPublishType(WalmartPublishTypeEnum.PUBLISH.getCode());
        copyTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_RELEASE.getCode());
        copyTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
        copyTemplate.setCreateBy(WebUtils.getUserName());
        copyTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
        copyTemplate.setUpdateBy(WebUtils.getUserName());
        copyTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        copyTemplate.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        insert(copyTemplate);

        // 创建处理报告
        WalmartFeedTaskUtil.generateFeedTask(Lists.newArrayList(copyTemplate), WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());
    }

    @Override
    public void batchPublish(BatchPublishRequest request) throws Exception {
        if (CollectionUtils.isEmpty(request.getBatchPublishBeanList())) {
            throw new Exception("参数为空");
        }

        // 报错的原因
        List<String> filterList = new ArrayList<>();

        // 刊登的模板
        List<WalmartTemplate> walmartTemplates = new ArrayList<>();

        List<BatchPublishBean> batchPublishBeans = request.getBatchPublishBeanList();
        Map<Integer, BatchPublishBean> requestMap =
                batchPublishBeans.stream().collect(Collectors.toMap(BatchPublishBean::getId, o -> o));

        // 根据id查询模板
        List<Integer> idList = batchPublishBeans.stream().map(BatchPublishBean::getId).collect(Collectors.toList());
        WalmartTemplateExample example = new WalmartTemplateExample();
        example.createCriteria().andIdIn(idList);
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        List<WalmartTemplate> walmartTemplateList = selectByExample(example);

        for (WalmartTemplate walmartTemplate : walmartTemplateList) {
            BatchPublishBean bean = requestMap.get(walmartTemplate.getId());

//            // 设置标题
//            walmartTemplate.setTitle(bean.getTitle());

            Map<String, String> resultMap = WalmartTemplateUtils.checkInfringementWords(walmartTemplate.getTitle(),
                    walmartTemplate.getDescription(), walmartTemplate.getBrand(), walmartTemplate.getKeyFeatures(), null);
            if (MapUtils.isNotEmpty(resultMap) && resultMap.containsKey("品牌")) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("模板编号：").append(walmartTemplate.getId()).append("品牌存在侵权词，无法保存：");
                if (resultMap.containsKey("品牌")) {
                    stringBuilder.append("品牌-").append(resultMap.get("品牌"));
                }
                filterList.add(stringBuilder.toString());
                continue;
            }


            // 需要重新生成sellerSku，变体组id，产品id,更换品牌，库存，运费模板物流中心
            try {
                regenerateAccountRelevant(walmartTemplate);
            } catch (Exception e) {
                filterList.add(e.getMessage());
                continue;
            }


            // 处理SKU列表属性替换并过滤侵权词 + 根据店铺配置上架拦截
            try {
                walmartTemplateNewBuilderHelper.processSkuListAndFilterInfringingWords(walmartTemplate);
            } catch (Exception e) {
                filterList.add(String.format("模板编号%s根据店铺配置上架拦截:无符合的sku", walmartTemplate.getId()));
                continue;
            }


            // 算价
            try {
                calcTemplatePrice(walmartTemplate, bean.getGrossProfitRate());
            } catch (Exception e) {
                filterList.add(e.getMessage());
                continue;
            }

            // 校验重复刊登
            List<Integer> templateIds = checkRepeatPublication(List.of(walmartTemplate));
            if (CollectionUtils.isNotEmpty(templateIds)) {
                String id = StringUtils.join(templateIds, ",");
                filterList.add(String.format("模板编号%s当前店铺该货号已刊登或已提交刊登，不允许重复刊登，请重新勾选数据", id));
                continue;
            }

            // 处理标题描述
            WalmartTemplateUtils.handleTitleDescription(walmartTemplate);

//            walmartTemplate.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
//            walmartTemplate.setPublishType(WalmartPublishTypeEnum.PUBLISH.getCode());
            walmartTemplate.setPublishStatus(WalmartTemplateStatusEnum.PUBLISHING.getCode());
            walmartTemplate.setIsPublish(0);
            walmartTemplate.setUpdateBy(WebUtils.getUserName());
            walmartTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            walmartTemplates.add(walmartTemplate);
        }



        if (CollectionUtils.isNotEmpty(walmartTemplates)) {
            // 创建处理报告
            WalmartFeedTaskUtil.generateFeedTask(walmartTemplates, WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());

            // 更新模板状态
            batchUpdateByPrimaryKeySelective(walmartTemplates, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        }

        if (CollectionUtils.isNotEmpty(filterList)) {
            throw new Exception(JSON.toJSONString(filterList));
        }
    }

    @Override
    public void filterInfringingWords(WalmartTemplate walmartTemplate) {
        String title = WalmartInfringementWordUtils.delInfringementWords(walmartTemplate.getTitle(), false);
        walmartTemplate.setTitle(title);

        String description = WalmartInfringementWordUtils.delInfringementWords(walmartTemplate.getDescription(), false);
        walmartTemplate.setDescription(description);

        String keyFeatures = WalmartInfringementWordUtils.delInfringementWords(walmartTemplate.getKeyFeatures(), true);
        walmartTemplate.setKeyFeatures(keyFeatures);
    }

    private List<Integer> checkRepeatPublication(List<WalmartTemplate> walmartTemplates) {
        List<Integer> templateIdList = new ArrayList<>();
        for (WalmartTemplate walmartTemplate : walmartTemplates) {
            String accountNumber = walmartTemplate.getAccountNumber();
            String articleNumber = walmartTemplate.getArticleNumber();

            // 获取主sku
            String mainSku = "";
            if (!WalmartAccountUtils.isPublishTemuAccount(accountNumber)) {
                mainSku = ProductUtils.getMainSku(walmartTemplate.getArticleNumber());
            }

            // 根据店铺和货号查在线的item
            WalmartItemExample itemExample = new WalmartItemExample();
            String filed = "id";
            itemExample.setFiledColumns(filed);
            WalmartItemExample.Criteria criteria = itemExample.createCriteria();
            criteria.andAccountNumberEqualTo(accountNumber);
            criteria.andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode());
            if (articleNumber.equals(mainSku)) {
                criteria.andMainSkuEqualTo(articleNumber);
            } else {
                criteria.andSkuEqualTo(articleNumber);
            }
            List<WalmartItem> walmartItemList = walmartItemService.selectFiledColumnsByExample(itemExample);
            if (CollectionUtils.isNotEmpty(walmartItemList)) {
                templateIdList.add(walmartTemplate.getId());
                continue;
            }

            // 根据店铺和货号查今日待发布，刊登中，刊登成功的模板
            WalmartTemplateExample templateExample = new WalmartTemplateExample();
            String filedColumns = "id";
            templateExample.setFiledColumns(filedColumns);
            templateExample.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
            templateExample.createCriteria()
                    .andAccountNumberEqualTo(accountNumber)
                    .andArticleNumberEqualTo(articleNumber)
                    .andPublishStatusNotEqualTo(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode())
                    .andPublishStatusNotEqualTo(WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode())
                    .andPublishStatusNotEqualTo(WalmartTemplateStatusEnum.PARTIAL_SUCCESS.getCode())
                    .andUpdateDateGreaterThanOrEqualTo(new Timestamp(DateUtils.getToday().getTime()));
            List<WalmartTemplate> walmartTemplateList = selectFiledColumnsByExample(templateExample);
            if (CollectionUtils.isNotEmpty(walmartTemplateList)) {
                templateIdList.add(walmartTemplate.getId());
            }
        }

        return templateIdList;
    }

    private void calcTemplatePrice(WalmartTemplate walmartTemplate, Double grossProfitRate) throws Exception {
        if (null == grossProfitRate) {
            grossProfitRate = 0.25;
        }

        // 获取默认物流方式
        String shippingMethod = systemParamService.queryParamValue(SaleChannel.CHANNEL_WALMART, "WALMART",
                "DEFAULT_SHIPPING_METHOD").getParamValue();

        if (walmartTemplate.getSaleVariant()) {
            List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
            });
            List<WalmartCalcBean> walmartCalcBeans = new ArrayList<>();
            for (WalmartVariant variant : variants) {
                WalmartCalcBean bean = new WalmartCalcBean();
                bean.setArticleNumber(variant.getSku());
                bean.setShippingMethod(shippingMethod);
                bean.setGrossProfitRate(grossProfitRate);
                walmartCalcBeans.add(bean);
            }
            ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = WalmartCalcPriceUtil.batchCalcPrice(walmartCalcBeans);
            if (!listApiResult.isSuccess()) {
                throw new Exception(String.format("模板ID：%s算价失败，试算器提示：%s", walmartTemplate.getId(), listApiResult.getErrorMsg()));
            }
            List<BatchPriceCalculatorResponse> responses = listApiResult.getResult();
            Map<String, BatchPriceCalculatorResponse> responseMap =
                    responses.stream().collect(Collectors.toMap(BatchPriceCalculatorResponse::getArticleNumber, o -> o));

            for (WalmartVariant variant : variants) {
                BatchPriceCalculatorResponse response = responseMap.get(variant.getSku());
                if (null == response || !response.getIsSuccess()) {
                    throw new Exception(String.format("模板ID：%s算价失败", walmartTemplate.getId()));
                }
                variant.setPrice(NumberUtils.format(response.getForeignPrice(), "0.##"));
            }
            walmartTemplate.setVariations(JSON.toJSONString(variants));
        } else {
            WalmartCalcBean bean = new WalmartCalcBean();
            bean.setArticleNumber(walmartTemplate.getArticleNumber());
            bean.setShippingMethod(shippingMethod);
            bean.setGrossProfitRate(grossProfitRate);
            ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = WalmartCalcPriceUtil.batchCalcPrice(Lists.newArrayList(bean));
            if (!listApiResult.isSuccess()) {
                throw new Exception(String.format("模板ID：%s算价失败，试算器提示：%s", walmartTemplate.getId(), listApiResult.getErrorMsg()));
            }
            List<BatchPriceCalculatorResponse> responses = listApiResult.getResult();
            if (null == responses.get(0) || !responses.get(0).getIsSuccess()) {
                throw new Exception(String.format("模板ID：%s算价失败", walmartTemplate.getId()));
            }
            walmartTemplate.setPrice(NumberUtils.format(responses.get(0).getForeignPrice(), "0.##"));
        }
    }

    private void regenerateAccountRelevant(WalmartTemplate walmartTemplate) throws Exception {
        // 获取店铺后缀
        SaleAccountAndBusinessResponse account = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartTemplate.getAccountNumber());
        String accountSuffix = null;
        if (null != account) {
            accountSuffix = account.getSellerSkuSuffix();
        }
        if (StringUtils.isBlank(accountSuffix)) {
            throw new Exception(String.format("模板ID：%s，获取不到店铺后缀;", walmartTemplate.getId()));
        }
        WalmartAccountConfig walmartAccountConfig = walmartAccountConfigService.selectByAccountNumber(walmartTemplate.getAccountNumber());
        if (ObjectUtils.isEmpty(walmartAccountConfig)) {
            throw new Exception(String.format("模板编号%s更换品牌失败，获取不到店铺配置", walmartTemplate.getId()));
        }
        walmartTemplate.setBrand(walmartAccountConfig.getBrand());
        walmartTemplate.setFulfillmentCenter(walmartAccountConfig.getSelectedFulfillmentCenter());
        walmartTemplate.setShippingTemplate(walmartAccountConfig.getSelectedShippingTemplate());

        // 获取ean前缀
        String prefixStr = WalmartAccountUtils.getEanPrefix(account, walmartTemplate.getProductIdType());

        // 生成sellerSku，变体组id，产品id
        AccountEanRequest accountEanRequest = new AccountEanRequest();
        accountEanRequest.setEanPrefix(prefixStr);
        accountEanRequest.setSkuDataSource(walmartTemplate.getSkuDataSource());
        accountEanRequest.setCardCodeType(walmartTemplate.getProductIdType());
        int size;
        if (walmartTemplate.getSaleVariant()) {
            List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
            });
            size = variants.size();
            List<String> skuList = variants.stream().map(WalmartVariant::getSku).collect(Collectors.toList());
            accountEanRequest.setSize(size);
            accountEanRequest.setSkuList(skuList);
            List<String> productIdList = CardCodeUtils.generateCardCodes(accountEanRequest);
            for (int i = 0; i < productIdList.size(); i++) {
                WalmartVariant walmartVariant = variants.get(i);
                walmartVariant.setProductId(productIdList.get(i));
                walmartVariant.setSellerSku(walmartVariant.getSku() + "_" + accountSuffix);
                walmartVariant.setVariantGroupId(walmartVariant.getSku() + "_" + accountSuffix);
                walmartVariant.setInventory(walmartAccountConfig.getDefaultStock());
            }
            walmartTemplate.setVariations(JSON.toJSONString(variants));
        } else {
            size = 1;
            accountEanRequest.setSize(size);
            accountEanRequest.setSkuList(Lists.newArrayList(walmartTemplate.getArticleNumber()));
            List<String> productIdList = CardCodeUtils.generateCardCodes(accountEanRequest);
            walmartTemplate.setProductId(productIdList.get(0));
            walmartTemplate.setSellerSku(walmartTemplate.getArticleNumber() + "_" + accountSuffix);
            walmartTemplate.setInventory(walmartAccountConfig.getDefaultStock());
        }
    }

    @Override
    public void uploadInventory(List<Integer> templateIdList) throws Exception {
        if (CollectionUtils.isEmpty(templateIdList)) {
            throw new Exception("参数为空");
        }

        WalmartTemplateExample example = new WalmartTemplateExample();
        example.createCriteria().andIdIn(templateIdList);
        String filed = "id, account_number, article_number, seller_sku, sale_variant, inventory, variations";
        example.setFiledColumns(filed);
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        List<WalmartTemplate> walmartTemplates = selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(walmartTemplates)) {
            throw new Exception("查询不到模板");
        }

        List<WalmartTemplate> updateList = new ArrayList<>();
        for (Integer templateId : templateIdList) {
            WalmartTemplate walmartTemplate = new WalmartTemplate();
            walmartTemplate.setId(templateId);
            walmartTemplate.setInventoryUpload(WalmartUploadInventoryEnum.WAIT_UPLOAD.getCode());
            walmartTemplate.setUpdateBy(WebUtils.getUserName());
            walmartTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(walmartTemplate);
        }

        // 更新模板上传库存状态
        batchUpdateByPrimaryKeySelective(updateList, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
    }

    @Override
    public void uploadInventory(String accountNumber, List<UploadInventoryBean> beanList) {
        if (StringUtils.isBlank(accountNumber) || CollectionUtils.isEmpty(beanList)) {
            return;
        }

        List<Integer> templateIds = beanList.stream().map(UploadInventoryBean::getTemplateId).collect(Collectors.toList());

        // 更新模板库存状态
        batchUpdateInventoryStatus(templateIds, WalmartUploadInventoryEnum.UPLOADING.getCode());

        // 请求平台上传库存
        try {
            SaleAccountAndBusinessResponse walmartAccount =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
            WalmartAccountUtils.refreshAccountToken(walmartAccount);
            new WalmartBulkUpdateInventoryCall(walmartAccount).uploadInventory(beanList);

            // 记录成功处理报告
            successUploadInventoryFeedTask(beanList);

            // 更新模板库存状态
            batchUpdateInventoryStatus(templateIds, WalmartUploadInventoryEnum.UPLOAD_SUCCESS.getCode());

            if (WalmartAccountUtils.isPublishTemuAccount(accountNumber)) {
                List<String> mainSkus = beanList.stream().map(UploadInventoryBean::getMainSku).distinct().collect(Collectors.toList());
                temuItemWamlartPublishRecordService.batchSaveAddSuccess(mainSkus, accountNumber);
            }
        } catch (Exception e) {
            log.error("批量上传库存请求平台失败！" + e.getMessage());

            // 记录失败处理报告
            List<String> templateIdList = beanList.stream().map(o -> o.getTemplateId().toString()).collect(Collectors.toList());
            handleFailFeedTaskStatus(templateIdList, WalmartTaskTypeEnum.UPLOAD_INVENTORY.getStatusMsgEn(), e.getMessage());

            // 更新模板库存状态
            batchUpdateInventoryStatus(templateIds, WalmartUploadInventoryEnum.UPLOAD_FAILED.getCode());

            if (WalmartAccountUtils.isPublishTemuAccount(accountNumber)) {
                List<String> mainSkus = beanList.stream().map(UploadInventoryBean::getMainSku).distinct().collect(Collectors.toList());
                temuItemWamlartPublishRecordService.batchRemovePublishing(mainSkus, accountNumber);
            }
        }
    }

    @Override
    public void batchUpdateShippingTemplateStatus(List<Integer> templateIds, Integer status) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return;
        }

        List<WalmartTemplate> updateList = new ArrayList<>();
        for (Integer templateId : templateIds) {
            WalmartTemplate walmartTemplate = new WalmartTemplate();
            walmartTemplate.setId(templateId);
            walmartTemplate.setUploadShippingTemplateStatus(status);
            walmartTemplate.setUpdateBy(WebUtils.getUserName());
            walmartTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(walmartTemplate);
        }

        // 更新模板上传运费模板状态
        batchUpdateByPrimaryKeySelective(updateList, WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
    }

    @Override
    public void uploadShippingTemplate(String accountNumber, List<ShippingTemplateBean> beanList) {
        if (StringUtils.isBlank(accountNumber) || CollectionUtils.isEmpty(beanList)) {
            return;
        }

        List<Integer> templateIds = beanList.stream().map(ShippingTemplateBean::getTemplateId).collect(Collectors.toList());

        // 更新模板运费模板状态
        batchUpdateShippingTemplateStatus(templateIds, WalmartUploadShippingTemplateEnum.UPLOADING.getCode());

        // 请求平台上传运费模板
        try {
            SaleAccountAndBusinessResponse walmartAccount =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
            WalmartAccountUtils.refreshAccountToken(walmartAccount);
            new WalmartUploadShippingTemplateCall(walmartAccount).uploadShippingTemplate(beanList);

            // 记录成功处理报告
            successUploadShippingTemplateFeedTask(beanList);

            // 更新模板运费模板状态
            batchUpdateShippingTemplateStatus(templateIds, WalmartUploadShippingTemplateEnum.UPLOAD_SUCCESS.getCode());
        } catch (Exception e) {
            log.error("批量上传运费模板请求平台失败！" + e.getMessage());

            // 记录失败处理报告
            List<String> templateIdList = beanList.stream().map(o -> o.getTemplateId().toString()).collect(Collectors.toList());
            handleFailFeedTaskStatus(templateIdList, WalmartTaskTypeEnum.UPLOAD_SHIPPING_TEMPLATE.getStatusMsgEn(), e.getMessage());

            // 更新模板运费模板状态
            batchUpdateShippingTemplateStatus(templateIds, WalmartUploadShippingTemplateEnum.UPLOAD_FAILED.getCode());
        }
    }

    private void successUploadShippingTemplateFeedTask(List<ShippingTemplateBean> beanList) {
        List<String> templateIdList = beanList.stream().map(o -> o.getTemplateId().toString()).collect(Collectors.toList());
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andTaskTypeEqualTo(WalmartTaskTypeEnum.UPLOAD_SHIPPING_TEMPLATE.getStatusMsgEn())
                .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                .andAttribute4In(templateIdList);
        List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Walmart.name());

//        Map<Integer, ShippingTemplateBean> beanMap = beanList.stream()
//                .collect(Collectors.toMap(ShippingTemplateBean::getTemplateId, o -> o, (o1, o2) -> o1));

        List<FeedTask> updateTaskList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            FeedTask update = new FeedTask();
            update.setId(feedTask.getId());
            update.setPlatform(Platform.Walmart.name());
            update.setTableIndex();
            update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            update.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
            update.setFinishTime(new Timestamp(System.currentTimeMillis()));
//            ShippingTemplateBean bean = beanMap.get(Integer.valueOf(feedTask.getAttribute4()));
//            if (null != bean) {
//                update.setAttribute2(bean.getSellerSku());
//            }
            updateTaskList.add(update);
        }

        feedTaskService.batchUpdateFeedTask(updateTaskList);
    }

    @Override
    public String getLastProductId(Integer skuDataSource) {
        WalmartTemplateExample example = new WalmartTemplateExample();
        example.setOrderByClause("id desc");
        example.setLimit(1);
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());

        if (SkuDataSourceEnum.COMPOSE_SYSTEM.getCode().equals(skuDataSource)) {
            example.createCriteria()
                    .andProductIdIsNotNull()
                    .andProductIdTypeEqualTo(CardCodeTypeEnum.GTIN.getCode())
                    .andSkuDataSourceEqualTo(skuDataSource);
        } else {
            example.createCriteria()
                    .andProductIdIsNotNull()
                    .andAccountNumberLike("%" + WalmartAccountUtils.WALMART_TEMU_ACCOUNT_SUFFIX);
        }
        List<WalmartTemplate> walmartTemplates = this.selectByExample(example);
        if (CollectionUtils.isNotEmpty(walmartTemplates)) {
            return walmartTemplates.get(0).getProductId();
        }
        return null;
    }

    private void successUploadInventoryFeedTask(List<UploadInventoryBean> beanList) {
        List<String> templateIdList = beanList.stream().map(o -> o.getTemplateId().toString()).collect(Collectors.toList());
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andTaskTypeEqualTo(WalmartTaskTypeEnum.UPLOAD_INVENTORY.getStatusMsgEn())
                .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                .andAttribute4In(templateIdList);
        List<FeedTask> feedTasks = feedTaskService.selectByExample(example, Platform.Walmart.name());

        Map<Integer, UploadInventoryBean> beanMap = beanList.stream()
                .collect(Collectors.toMap(UploadInventoryBean::getTemplateId, o -> o, (o1, o2) -> o1));

        List<FeedTask> updateTaskList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            FeedTask update = new FeedTask();
            update.setId(feedTask.getId());
            update.setPlatform(Platform.Walmart.name());
            update.setTableIndex();
            update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
            update.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
            update.setFinishTime(new Timestamp(System.currentTimeMillis()));
            UploadInventoryBean bean = beanMap.get(Integer.valueOf(feedTask.getAttribute4()));
            if (null != bean && null != bean.getInventory()) {
                update.setAttribute2(bean.getInventory().toString());
            }
            updateTaskList.add(update);
        }

        feedTaskService.batchUpdateFeedTask(updateTaskList);
    }

    @Override
    public WalmartTemplate buildTemuTemplate(String accountNumber, String itemId) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(itemId)) {
            throw new RuntimeException("账号或itemID为空");
        }
        EsTemuItem esTemuItem = temuItemService.getTemuItemByItemId(itemId);
        if (null == esTemuItem) {
            throw new RuntimeException("es查询不到temuItem, 该itemID:" + itemId);
        }
        List<EsTemuItemIncrementInfo> incrementInfoList = esTemuItem.getIncrementInfo();
        if (CollectionUtils.isEmpty(incrementInfoList)) {
            throw new RuntimeException("EsTemuItemIncrementInfo结构为空, 该itemID:" + itemId);
        }
        EsTemuItemIncrementInfo esTemuItemIncrementInfo = incrementInfoList.get(0);
        List<EsTemuItemSkuInfo> skuInfoList = esTemuItemIncrementInfo.getSkuInfo();
        if (CollectionUtils.isEmpty(skuInfoList)) {
            throw new RuntimeException("EsTemuItemSkuInfo结构为空, 该itemID:" + itemId);
        }
        // 店铺后缀和ean前缀
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        if (null == account) {
            throw new RuntimeException("获取不到账号信息");
        }
        String accountSuffix = account.getSellerSkuSuffix();
        if (StringUtils.isBlank(accountSuffix)) {
            throw new RuntimeException("获取不到店铺后缀");
        }
        String eanPrefix = account.getColStr6();
        if (StringUtils.isBlank(eanPrefix)) {
            throw new RuntimeException("获取不到ean前缀");
        }
        if (eanPrefix.length() > 4) {
            eanPrefix = eanPrefix.substring(0, 4);
        }

        WalmartTemplate walmartTemplate = new WalmartTemplate();
        // 账号
        walmartTemplate.setAccountNumber(accountNumber);
        // 标题
        walmartTemplate.setTitle(esTemuItemIncrementInfo.getTitle());
        // 描述
        if (StringUtils.isNotBlank(esTemuItemIncrementInfo.getDescParam())
                && StringUtils.isNotBlank(esTemuItemIncrementInfo.getDescText())) {
            walmartTemplate.setDescription(esTemuItemIncrementInfo.getDescParam() + esTemuItemIncrementInfo.getDescText());
        } else {
            walmartTemplate.setDescription(esTemuItemIncrementInfo.getDescParam());
        }
        // 描述为空 标题当做描述
        if (StringUtils.isBlank(walmartTemplate.getDescription())) {
            walmartTemplate.setDescription(esTemuItemIncrementInfo.getTitle());
        }
        // 品牌
        WalmartAccountConfig walmartAccountConfig = walmartAccountConfigService.selectByAccountNumber(accountNumber);
        if (null == walmartAccountConfig) {
            throw new RuntimeException("获取不到店铺配置，该店铺：" + accountNumber);
        }
        if (StringUtils.isNotBlank(walmartAccountConfig.getBrand())) {
            List<String> brands = CommonUtils.splitList(walmartAccountConfig.getBrand(), ",");
            walmartTemplate.setBrand(brands.get(0));
        } else {
            walmartTemplate.setBrand(esTemuItemIncrementInfo.getBrand());
        }
        // 分类
        walmartTemplate.setSubCategoryId(WalmartPublishConstant.DEFAULT_CATEGORY_ID);
        walmartTemplate.setSubCategoryName(WalmartPublishConstant.DEFAULT_CATEGORY);
        // 产品id类型
        walmartTemplate.setProductIdType(CardCodeTypeEnum.GTIN.getCode());

        EsTemuItemSkuInfo esTemuItemSkuInfo = skuInfoList.get(0);
        // 货号
        walmartTemplate.setArticleNumber(itemId);
        // sellerSku
        walmartTemplate.setSellerSku(esTemuItemSkuInfo.getSkuId() + "_" + accountSuffix);
        // 公共图片
        List<String> commonImages = esTemuItemIncrementInfo.getImages();

        // 是否变体
        if (skuInfoList.size() == 1) {
            // 是否变体
            walmartTemplate.setSaleVariant(false);
            // 产品id
            walmartTemplate.setProductId(generateTemuProductId(skuInfoList.size(), eanPrefix).get(0));
            // 主图
            List<String> imageUrl = esTemuItemSkuInfo.getImageUrl();
            if (StringUtils.isNotBlank(esTemuItemSkuInfo.getThumbImageUrl())) {
                walmartTemplate.setMainImageUrl(esTemuItemSkuInfo.getThumbImageUrl());
            } else {
                if (CollectionUtils.isEmpty(imageUrl)) {
                    if (CollectionUtils.isEmpty(commonImages)) {
                        throw new RuntimeException("图片信息为空，无法刊登, 该itemID:" + itemId);
                    }
                    walmartTemplate.setMainImageUrl(commonImages.get(0));
                } else {
                    walmartTemplate.setMainImageUrl(imageUrl.get(0));
                }
            }
            // 附图
            if (CollectionUtils.isEmpty(imageUrl)) {
                if (CollectionUtils.isNotEmpty(commonImages)) {
                    walmartTemplate.setExtraImageUrls(JSON.toJSONString(commonImages));
                } else {
                    throw new RuntimeException("图片信息为空，无法刊登, 该itemID:" + itemId);
                }
            } else {
                if (CollectionUtils.isNotEmpty(commonImages)) {
                    imageUrl.addAll(commonImages);
                }
                walmartTemplate.setExtraImageUrls(JSON.toJSONString(imageUrl));
            }
            // 价格
            walmartTemplate.setPrice(calcTemuPrice(esTemuItemSkuInfo));
            // 重量
            walmartTemplate.setShippingWeight(calcTemuWeight(esTemuItemSkuInfo));
            // 库存
            walmartTemplate.setInventory(9999);
        } else {
            List<WalmartVariant> walmartVariants = new ArrayList<>();
            walmartTemplate.setSaleVariant(true);
            // 生成产品id
            List<String> productIdList = generateTemuProductId(skuInfoList.size(), eanPrefix);
            // 分类属性
            Map<String, Map<String, Object>> attributeMap = walmartCategoryAttributeService.matchTemuPlatformAttribute(WalmartPublishConstant.DEFAULT_CATEGORY_ID, skuInfoList);
            for (int i = 0; i < skuInfoList.size(); i++) {
                EsTemuItemSkuInfo skuInfo = skuInfoList.get(i);
                WalmartVariant walmartVariant = new WalmartVariant();
                walmartVariant.setSku(skuInfo.getSkuId());
                walmartVariant.setSellerSku(skuInfo.getSkuId() + "_" + accountSuffix);
                walmartVariant.setVariantGroupId(itemId + "_" + accountSuffix);
                walmartVariant.setProductId(productIdList.get(i));
                walmartVariant.setIsPrimaryVariant(i == 0);
                walmartVariant.setAttributeMap(attributeMap.get(skuInfo.getSkuId()));
                List<String> imageUrl = skuInfo.getImageUrl();
                if (StringUtils.isNotBlank(skuInfo.getThumbImageUrl())) {
                    walmartVariant.setMainImageUrl(skuInfo.getThumbImageUrl());
                } else {
                    if (CollectionUtils.isEmpty(imageUrl)) {
                        if (CollectionUtils.isEmpty(commonImages)) {
                            throw new RuntimeException("图片信息为空，无法刊登, 该itemID:" + itemId);
                        }
                        walmartVariant.setMainImageUrl(commonImages.get(0));
                    } else {
                        walmartVariant.setMainImageUrl(imageUrl.get(0));
                    }
                }
                if (CollectionUtils.isEmpty(imageUrl)) {
                    if (CollectionUtils.isNotEmpty(commonImages)) {
                        walmartVariant.setExtraImageUrls(JSON.toJSONString(commonImages));
                    } else {
                        throw new RuntimeException("图片信息为空，无法刊登, 该itemID:" + itemId);
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(commonImages)) {
                        imageUrl.addAll(commonImages);
                    }
                    walmartVariant.setExtraImageUrls(JSON.toJSONString(imageUrl));
                }
                walmartVariant.setPrice(calcTemuPrice(skuInfo));
                walmartVariant.setShippingWeight(calcTemuWeight(skuInfo));
                walmartVariant.setInventory(9999);
                walmartVariants.add(walmartVariant);
            }
            walmartTemplate.setVariations(JSON.toJSONString(walmartVariants));
        }

        walmartTemplate.setPublishType(WalmartPublishTypeEnum.TRIAL_SALE.getCode());
        walmartTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
        walmartTemplate.setPublishRole(WalmartPublishRoleEnum.SYSTEM.getCode());
        walmartTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
        walmartTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
        walmartTemplate.setIsParent(false);

        return walmartTemplate;
    }

    private Double calcTemuWeight(EsTemuItemSkuInfo esTemuItemSkuInfo) {
        Float discountPrice;
        discountPrice = esTemuItemSkuInfo.getDiscountPrice();
        if (null == discountPrice) {
            discountPrice = esTemuItemSkuInfo.getOriginalPrice();
        }
        if (null == discountPrice) {
            return null;
        }
        return NumberUtils.format((discountPrice * 15 + CommonUtils.random.nextInt(51)) * 0.002204623, "0.###");
    }

    /**
     * <=20美金：+5
     * >20美金：*1.2
     * 不设最低价过滤了，计算出来<7.99的按7.99刊登
     *
     * @param esTemuItemSkuInfo
     * @return
     */
    private Double calcTemuPrice(EsTemuItemSkuInfo esTemuItemSkuInfo) {
        Float discountPrice;
        discountPrice = esTemuItemSkuInfo.getDiscountPrice();
        if (null == discountPrice) {
            discountPrice = esTemuItemSkuInfo.getOriginalPrice();
        }
        if (null == discountPrice) {
            throw new RuntimeException("价格为空！");
        }

        if (discountPrice > 20f) {
            discountPrice = discountPrice * 1.2f;
        } else if (discountPrice <= 20f) {
            discountPrice = discountPrice + 5f;
        }

        if (discountPrice <= 7.99f) {
            discountPrice = 7.99f;
        }

        BigDecimal bigDecimal = new BigDecimal(String.valueOf(discountPrice));
        return NumberUtils.format(bigDecimal.doubleValue(), "0.##");
    }

    private List<String> generateTemuProductId(int size, String eanPrefix) {
        List<String> productIdList;
        try {
            AccountEanRequest accountEanRequest = new AccountEanRequest();
            accountEanRequest.setEanPrefix(eanPrefix);
            accountEanRequest.setCardCodeType(CardCodeTypeEnum.GTIN.getCode());
            accountEanRequest.setPublishType(WalmartPublishTypeEnum.TRIAL_SALE.getCode());
            accountEanRequest.setSize(size);
            productIdList = CardCodeUtils.generateCardCodes(accountEanRequest);
        } catch (Exception e) {
            throw new RuntimeException("生成产品id报错：" + e.getMessage());
        }
        if (CollectionUtils.isEmpty(productIdList)) {
            throw new RuntimeException("无法生成产品id");
        }
        return productIdList;
    }


    @Override
    public Map<String, Object> getTemplateInfo(BuilderTemplateDTO builderTemplateDTO) {
        Map<String, Object> response = new HashMap<>();
        WalmartTemplateValidationContext validationContext = new WalmartTemplateValidationContext(builderTemplateDTO);
        // 产品信息校验, 校验规则中若存在一条未通过则直接返回提示
        BuilderTemplateDTO builderDTO = walmartTemplateNewBuilderHelper.validationProductData(validationContext);
        if (!validationContext.isAllSuccess()) {
            response.put("validation", validationContext.getValidations());
            return response;
        }
        // 构建模板信息
        WalmartTemplateDTO templateDTO = walmartTemplateNewBuilderHelper.builderTemplate(builderDTO);
        // 模板信息校验
        validationContext.setTemplateDTO(templateDTO);
        walmartTemplateNewBuilderHelper.validationTemplateData(validationContext);

        if (null != templateDTO) {
            response.put("template", templateDTO);
        }
        response.put("validation", validationContext.getValidations());
        return response;
    }

    @Override
    public Map<String, Object> checkTemplateData(WalmartTemplateDTO templateDTO) {
        if (null == templateDTO) {
            return null;
        }
        if (StringUtils.isEmpty(templateDTO.getAccountNumber())
                || StringUtils.isEmpty(templateDTO.getArticleNumber())
                || null == templateDTO.getSkuDataSource()) {
            return null;
        }
        Map<String, Object> response = new HashMap<>();
        WalmartTemplateValidationContext validationContext = new WalmartTemplateValidationContext(templateDTO);
        walmartTemplateNewBuilderHelper.validationProductData(validationContext);
        walmartTemplateNewBuilderHelper.validationTemplateData(validationContext);
        response.put("validation", validationContext.getValidations());
        return response;
    }

    @Override
    public WalmartTemplateDTO saveTemplate(WalmartTemplateDTO templateDTO) {
        if (null == templateDTO) {
            throw new BusinessException("参数为空");
        }

        // 处理描述信息
        WalmartTemplateUtils.handleTitleDescription(templateDTO);

        // 处理SKU列表属性替换并过滤侵权词 + 根据店铺配置上架拦截
        walmartTemplateNewBuilderHelper.processSkuListAndFilterInfringingWords(templateDTO);

        Integer id = templateDTO.getId();
        if (null == id) {
            String table = WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode();
            templateDTO.setTable(table);
            templateDTO.setCreateBy(StringUtils.isNotBlank(templateDTO.getCreateBy()) ? templateDTO.getCreateBy() : WebUtils.getUserName());
            templateDTO.setUpdateBy(StringUtils.isNotBlank(templateDTO.getUpdateBy()) ? templateDTO.getUpdateBy() : WebUtils.getUserName());
            templateDTO.setCreateDate(new Timestamp(System.currentTimeMillis()));
            templateDTO.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            templateDTO.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
            templateDTO.setUploadShippingTemplateStatus(WalmartUploadShippingTemplateEnum.NOT_UPLOAD.getCode());
            templateDTO.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
            insert(templateDTO);
        } else {
            if (CollectionUtils.isNotEmpty(templateDTO.getWalmartVariantList())) {
                templateDTO.setVariations(JSON.toJSONString(templateDTO.getWalmartVariantList()));
            }
            templateDTO.initTable();
            templateDTO.setUpdateBy(WebUtils.getUserName());
            templateDTO.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            //图片上传服务器标识
            String uploadImageServer = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "UPLOAD_IMAGE_SERVER", 10);
            templateDTO.setImageServer(StringUtils.isNotBlank(uploadImageServer) ? Integer.parseInt(uploadImageServer) : 0);
            updateByPrimaryKey(templateDTO);
        }
        return templateDTO;
    }

    @Override
    public WalmartTemplate saveAndPublishNew(WalmartTemplateDTO templateDTO) {
        // 单独再校验品牌
        Map<String, String> resultMap = WalmartTemplateUtils.checkInfringementWords(null, null, templateDTO.getBrand(), null, null);
        if (MapUtils.isNotEmpty(resultMap) && resultMap.containsKey("品牌")) {
            throw new BusinessException(String.format("品牌存在侵权词%s，无法保存", resultMap.get("品牌")));
        }

        // 保存模板
        WalmartTemplateDTO walmartTemplate = saveTemplate(templateDTO);

        WalmartTemplate newTemplate = new WalmartTemplate();
        BeanCopier beanCopier = BeanCopier.create(WalmartTemplate.class, WalmartTemplate.class, false);
        beanCopier.copy(walmartTemplate, newTemplate, null);

        // 特殊调整为刊登中
        String userName = StringUtils.isNotBlank(DataContextHolder.getUsername()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        newTemplate.setPublishStatus(WalmartTemplateStatusEnum.WAIT_PUBLISH.getCode());
        newTemplate.setInventoryUpload(WalmartUploadInventoryEnum.NOT_UPLOAD.getCode());
        newTemplate.setUploadShippingTemplateStatus(WalmartUploadShippingTemplateEnum.NOT_UPLOAD.getCode());
        newTemplate.setCreateBy(userName);
        newTemplate.setCreateDate(new Timestamp(System.currentTimeMillis()));
        newTemplate.setUpdateBy(userName);
        newTemplate.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        newTemplate.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        walmartTemplateService.updateByPrimaryKeySelective(newTemplate);


        TemplatePublishParam templatePublishParam = new TemplatePublishParam();
        templatePublishParam.setUser(userName);


        // 传的是模板id
        templatePublishParam.setTemplateId(newTemplate.getId());
        templatePublishParam.setRole(templateDTO.getPublishRole());
        templatePublishParam.setType(templateDTO.getPublishType());

        walmartTemplatePublishExecutor.execute(templatePublishParam);
//        WalmartExecutors.executePublishItem(() -> walmartTemplatePublishExecutor.execute(templatePublishParam));

        return newTemplate;
    }

    @Override
    public List<String> generateProductId(AccountEanRequest accountEanRequest) {
        SaleAccountAndBusinessResponse account = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountEanRequest.getSellerId());
        String eanPrefix = WalmartAccountUtils.getEanPrefix(account, accountEanRequest.getCardCodeType());
        accountEanRequest.setEanPrefix(eanPrefix);
        List<String> idList = CardCodeUtils.generateCardCodes(accountEanRequest);
        return idList;

    }
}
