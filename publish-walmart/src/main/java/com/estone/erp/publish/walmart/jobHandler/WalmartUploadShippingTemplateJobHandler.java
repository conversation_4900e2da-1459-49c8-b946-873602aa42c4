package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateTableEnum;
import com.estone.erp.publish.walmart.enums.WalmartUploadShippingTemplateEnum;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.WalmartTemplateExample;
import com.estone.erp.publish.walmart.model.WalmartVariant;
import com.estone.erp.publish.walmart.model.dto.ShippingTemplateBean;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.WalmartFeedTaskUtil;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 上传运费模板
 * <AUTHOR>
 * @date 2023/5/29
 */
@Slf4j
@Component
public class WalmartUploadShippingTemplateJobHandler extends AbstractJobHandler {

    @Resource
    private WalmartTemplateService walmartTemplateService;

    public WalmartUploadShippingTemplateJobHandler() {
        super("WalmartUploadShippingTemplateJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam {
        // 账号
        private List<String> accountNumberList;

        // 每次请求最大数量
        private Integer uploadQuantity = 500;
    }

    @Override
    @XxlJob("WalmartUploadShippingTemplateJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("执行开始");

        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        // 获取正常状态账号
        List<String> accountNumberList = innerParam.getAccountNumberList();
        if (CollectionUtils.isEmpty(accountNumberList)) {
            accountNumberList = EsAccountUtils.getPlatformNormaLAccountListByEs(SaleChannel.CHANNEL_WALMART);
        }

        Integer publishQuantity = innerParam.getUploadQuantity();

        for (String accountNumber : accountNumberList) {
            WalmartExecutors.executeUploadShippingTemplate(() -> {
                try {
                    uploadShippingTemplate(accountNumber, publishQuantity);
                } catch (Exception e) {
                    XxlJobLogger.log(String.format("账号%s上传运费模板报错：%s", accountNumber, e.getMessage()));
                }
            });
        }

        XxlJobLogger.log("执行结束");
        return ReturnT.SUCCESS;
    }

    private void uploadShippingTemplate(String accountNumber, Integer publishQuantity) {
        // 查询刊登成功，且运费模板状态为未上传、待上传和上传失败的状态
        List<Integer> shippingTemplateStatus =
                Lists.newArrayList(
                    WalmartUploadShippingTemplateEnum.NOT_UPLOAD.getCode(), 
                    WalmartUploadShippingTemplateEnum.WAIT_UPLOAD.getCode(), 
                    WalmartUploadShippingTemplateEnum.UPLOAD_FAILED.getCode()
                );
        List<Integer> publishStatus =
                Lists.newArrayList(WalmartTemplateStatusEnum.PUBLISH_SUCCESS.getCode());
        WalmartTemplateExample example = new WalmartTemplateExample();
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andUploadShippingTemplateStatusIn(shippingTemplateStatus)
                .andPublishStatusIn(publishStatus);

        String filed = "id, article_number, account_number, seller_sku, sale_variant, variations, publish_status, shipping_template, fulfillment_center";
        example.setFiledColumns(filed);
        example.setOrderByClause("create_date ASC");
        example.setOffset(0);
        example.setLimit(publishQuantity);
        example.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());

        List<WalmartTemplate> walmartTemplateList = walmartTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(walmartTemplateList)) {
            return;
        }

        // 创建处理报告
        WalmartFeedTaskUtil.generateFeedTask(walmartTemplateList, WalmartTaskTypeEnum.UPLOAD_SHIPPING_TEMPLATE.getStatusMsgEn());

        // 上传
        upload(accountNumber, walmartTemplateList);
    }

    private void upload(String accountNumber, List<WalmartTemplate> walmartTemplateList) {
        List<ShippingTemplateBean> beanList = new ArrayList<>();

        for (WalmartTemplate walmartTemplate : walmartTemplateList) {
            // 获取运费模板ID和物流中心ID
            String shippingTemplateId = walmartTemplate.getShippingTemplate();
            String fulfillmentCenterId = walmartTemplate.getFulfillmentCenter();

            if (StringUtils.isBlank(shippingTemplateId) || StringUtils.isBlank(fulfillmentCenterId)) {
                continue;
            }

            if (walmartTemplate.getSaleVariant()) {
                // 如果是变体商品
                List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {});
                for (WalmartVariant variant : variants) {
                    ShippingTemplateBean bean = new ShippingTemplateBean();
                    bean.setTemplateId(walmartTemplate.getId());
                    bean.setSellerSku(variant.getSellerSku());
                    bean.setShippingTemplateId(shippingTemplateId);
                    bean.setFulfillmentCenterId(fulfillmentCenterId);
                    bean.setMainSku(walmartTemplate.getArticleNumber());
                    beanList.add(bean);
                }
            } else {
                // 如果是单体商品
                ShippingTemplateBean bean = new ShippingTemplateBean();
                bean.setTemplateId(walmartTemplate.getId());
                bean.setSellerSku(walmartTemplate.getSellerSku());
                bean.setShippingTemplateId(shippingTemplateId);
                bean.setFulfillmentCenterId(fulfillmentCenterId);
                bean.setMainSku(walmartTemplate.getArticleNumber());
                beanList.add(bean);
            }
        }

        walmartTemplateService.uploadShippingTemplate(accountNumber, beanList);
    }
}
