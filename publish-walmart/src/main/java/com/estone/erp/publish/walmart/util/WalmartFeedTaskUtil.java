package com.estone.erp.publish.walmart.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.walmart.enums.WalmartTaskResultStatusEnum;
import com.estone.erp.publish.walmart.model.*;
import com.estone.erp.publish.walmart.service.WalmartAccountConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * walmart处理报告工具类
 * <AUTHOR>
 * @date 2022/4/15 11:23
 */
@Slf4j
public class WalmartFeedTaskUtil {

    private static FeedTaskService feedTaskService = SpringUtils.getBean(FeedTaskService.class);

    private static WalmartAccountConfigService walmartAccountConfigService = SpringUtils.getBean(WalmartAccountConfigService.class);

    /**
     * 初始化处理报告

     * @param accountNumber
     * @param taskType
     * @param sellerSku
     * @param id
     * @param sku
     * @return
     */
    public static FeedTask initFeedTask(String accountNumber, String taskType, String sellerSku, String id, String sku) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(id);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setAttribute3(sellerSku);
        feedTask.setTaskType(taskType);
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setPlatform(Platform.Walmart.name());
        feedTask.setTableIndex();
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    /**
     * 初始化处理报告

     * @param accountNumber
     * @param taskType
     * @param sellerSku
     * @param id
     * @param sku
     * @return
     */
    public static FeedTask initFeedTaskStock(String accountNumber, String taskType, String sellerSku, String id, String sku, StockObj stockObj) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(id);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setAttribute3(sellerSku);
        feedTask.setTaskType(taskType);
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setPlatform(Platform.Walmart.name());
        feedTask.setTableIndex();
        feedTask.setAttribute9(stockObj != null ? JSON.toJSONString(stockObj) : null);
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    /**
     * 成功的报告
     * @param feedTask
     */
    public static void succeedFeedTask(FeedTask feedTask, String oldValue, String newValue) {
        feedTask.setAttribute1(oldValue);
        feedTask.setAttribute2(newValue);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
        if (StringUtils.isBlank(feedTask.getTableIndex())) {
            feedTask.setTableIndex();
        }
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * 成功的报告 记录备注
     * @param feedTask
     */
    public static void succeedFeedTask(FeedTask feedTask, String oldValue, String newValue, String msg) {
        feedTask.setAttribute1(oldValue);
        feedTask.setAttribute2(newValue);
        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
        if (StringUtils.isBlank(feedTask.getTableIndex())) {
            feedTask.setTableIndex();
        }
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * 失败的报告
     * @param feedTask
     * @param msg
     */
    public static void failFeedTask(FeedTask feedTask, String msg) {
        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
        if (StringUtils.isBlank(feedTask.getTableIndex())) {
            feedTask.setTableIndex();
        }
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }


    /**
     * 插入失败的报告
     * @param feedTask
     * @param msg
     */
    public static void insertFailFeedTask(FeedTask feedTask, String msg) {
        feedTask.setResultMsg(msg);
        if(null == feedTask.getCreateTime()) {
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        }
        feedTask.setPlatform(Platform.Walmart.name());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
        if (StringUtils.isBlank(feedTask.getTableIndex())) {
            feedTask.setTableIndex();
        }
        feedTaskService.insert(feedTask);
    }


    public static FeedTask initTemplateFeedTask(WalmartTemplate walmartTemplate, String taskType) {
        // 获取店铺后缀
        String accountSuffix = "";
        SaleAccountAndBusinessResponse account = null;
        try {
            account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartTemplate.getAccountNumber());
            accountSuffix = account.getSellerSkuSuffix();
        } catch (Exception e) {
            log.error("获取店铺后缀报错：" + e.getMessage());
        }

        FeedTask feedTask = new FeedTask();
        feedTask.setAccountNumber(walmartTemplate.getAccountNumber());
        feedTask.setArticleNumber(walmartTemplate.getArticleNumber());
        if (null != account && WalmartAccountUtils.isPublishTemuAccount(account.getAccountNumber())) {
            feedTask.setAttribute3(walmartTemplate.getSellerSku());
        } else {
            feedTask.setAttribute3(walmartTemplate.getArticleNumber() + "_" + accountSuffix);
        }

        if (ObjectUtils.isNotEmpty(walmartTemplate.getId())){
            feedTask.setAttribute4(walmartTemplate.getId().toString());
        }

        if (WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn().equals(taskType)) {
            if (walmartTemplate.getSaleVariant()) {
                List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
                });
                List<String> sellerSkuList = variants.stream().map(WalmartVariant::getSellerSku).collect(Collectors.toList());
                feedTask.setAttribute5(JSON.toJSONString(sellerSkuList));
            } else {
                feedTask.setAttribute5(JSON.toJSONString(Lists.newArrayList(walmartTemplate.getSellerSku())));
            }
        }
        if (WalmartTaskTypeEnum.UPLOAD_SHIPPING_TEMPLATE.getStatusMsgEn().equals(taskType)){
            WalmartAccountConfig walmartAccountConfig = walmartAccountConfigService.selectByAccountNumber(walmartTemplate.getAccountNumber());
            if (ObjectUtils.isNotEmpty(walmartAccountConfig)){
                // 解析运费模板名称
                String shippingTemplateName = "";
                if (StringUtils.isNotBlank(walmartAccountConfig.getShippingTemplateJson()) && StringUtils.isNotBlank(walmartTemplate.getShippingTemplate())) {
                    try {
                        List<Map<String, Object>> shippingTemplates = JSON.parseObject(walmartAccountConfig.getShippingTemplateJson(), new TypeReference<List<Map<String, Object>>>() {});
                        for (Map<String, Object> template : shippingTemplates) {
                            if (walmartTemplate.getShippingTemplate().equals(template.get("id"))) {
                                shippingTemplateName = (String) template.get("name");
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析运费模板JSON出错: " + e.getMessage(), e);
                    }
                }

                // 解析物流中心名称
                String fulfillmentCenterName = "";
                if (StringUtils.isNotBlank(walmartAccountConfig.getFulfillmentCentersJson()) && StringUtils.isNotBlank(walmartTemplate.getFulfillmentCenter())) {
                    try {
                        List<Map<String, Object>> fulfillmentCenters = JSON.parseObject(walmartAccountConfig.getFulfillmentCentersJson(), new TypeReference<List<Map<String, Object>>>() {});
                        for (Map<String, Object> center : fulfillmentCenters) {
                            if (walmartTemplate.getFulfillmentCenter().equals(center.get("shipNode"))) {
                                fulfillmentCenterName = (String) center.get("shipNodeName");
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("解析物流中心JSON出错: " + e.getMessage(), e);
                    }
                }

                // 将名称放入attribute2字段
                feedTask.setAttribute2("运费模板："+shippingTemplateName + " ; " + "物流中心：" +fulfillmentCenterName);
            }
        }

        feedTask.setTaskType(taskType);
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setPlatform(Platform.Walmart.name());
        feedTask.setTableIndex();
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
        return feedTask;
    }
    /**
     * 生成模板刊登处理报告
     * @param walmartTemplates
     */
    public static void generateFeedTask(List<WalmartTemplate> walmartTemplates, String taskType) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (WalmartTemplate walmartTemplate : walmartTemplates) {
            FeedTask feedTask = initTemplateFeedTask(walmartTemplate, taskType);
            feedTasks.add(feedTask);
        }

        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    public static void generateFeedTask(List<WalmartTemplate> walmartTemplates, String taskType,Boolean isRetry) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (WalmartTemplate walmartTemplate : walmartTemplates) {
            // 获取店铺后缀
            String accountSuffix = "";
            SaleAccountAndBusinessResponse account = null;
            try {
                account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartTemplate.getAccountNumber());
                accountSuffix = account.getSellerSkuSuffix();
            } catch (Exception e) {
                log.error("获取店铺后缀报错：" + e.getMessage());
            }

            FeedTask feedTask = new FeedTask();
            feedTask.setAccountNumber(walmartTemplate.getAccountNumber());
            feedTask.setArticleNumber(walmartTemplate.getArticleNumber());
            if(null != account && WalmartAccountUtils.isPublishTemuAccount(account.getAccountNumber())) {
                feedTask.setAttribute3(walmartTemplate.getSellerSku());
            } else {
                feedTask.setAttribute3(walmartTemplate.getArticleNumber() + "_" + accountSuffix);
            }
            feedTask.setAttribute4(walmartTemplate.getId().toString());


            // 是否重试，重试把Attribute6赋值1
            if (isRetry){
                feedTask.setAttribute6("1");
            }

            if (WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn().equals(taskType)) {
                if (walmartTemplate.getSaleVariant()) {
                    List<WalmartVariant> variants = JSON.parseObject(walmartTemplate.getVariations(), new TypeReference<List<WalmartVariant>>() {
                    });
                    List<String> sellerSkuList = variants.stream().map(WalmartVariant::getSellerSku).collect(Collectors.toList());
                    feedTask.setAttribute5(JSON.toJSONString(sellerSkuList));
                } else {
                    feedTask.setAttribute5(JSON.toJSONString(Lists.newArrayList(walmartTemplate.getSellerSku())));
                }
            }

            feedTask.setTaskType(taskType);
            feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setPlatform(Platform.Walmart.name());
            feedTask.setTableIndex();
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
            feedTasks.add(feedTask);
        }

        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    /**
     * 生成修改item属性处理报告
     * @param walmartReplaceItems
     * @param typeList
     */
    public static List<FeedTask> generateFeedTask(List<WalmartReplaceItem> walmartReplaceItems, List<String> typeList) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (WalmartReplaceItem walmartReplaceItem : walmartReplaceItems) {
            for (String type : typeList) {
                FeedTask feedTask = new FeedTask();
                feedTask.setAccountNumber(walmartReplaceItem.getAccountNumber());
                feedTask.setArticleNumber(walmartReplaceItem.getSku());
                feedTask.setAssociationId(walmartReplaceItem.getRelationId().toString());
                feedTask.setAttribute5(walmartReplaceItem.getId().toString());
                feedTask.setAttribute3(walmartReplaceItem.getSellerSku());
                feedTask.setTaskType(type);
                feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
                feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setPlatform(Platform.Walmart.name());
                feedTask.setTableIndex();
                String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
                feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
                feedTasks.add(feedTask);
            }
        }

        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
        return feedTasks;
    }

    /**
     * 生成修改item属性处理报告
     * @param walmartItems item
     * @param type 类型
     * @param platform 平台
     */
    public static List<FeedTask> generateFeedTask(List<WalmartItem> walmartItems, String type, String platform) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (WalmartItem walmartItem : walmartItems) {
            FeedTask feedTask = new FeedTask();
            feedTask.setAccountNumber(walmartItem.getAccountNumber());
            feedTask.setArticleNumber(walmartItem.getSku());
            feedTask.setAssociationId(walmartItem.getId().toString());
            feedTask.setAttribute3(walmartItem.getSellerSku());
            feedTask.setTaskType(type);
            feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setPlatform(platform);
            feedTask.setTableIndex();
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? StrConstant.ADMIN : currentUser);
            feedTasks.add(feedTask);
        }

        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
        return feedTasks;
    }


    public static void updateTemplateFeedTaskSellerSku(String templateId, List<String> sellerSkuList, String taskType) {
        FeedTask update = new FeedTask();
        update.setPlatform(Platform.Walmart.name());
        update.setTableIndex();
        update.setAttribute5(JSON.toJSONString(sellerSkuList));

        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria()
                .andTaskTypeEqualTo(taskType)
                .andAttribute4EqualTo(templateId);
        feedTaskService.updateByExampleSelective(update, example);
    }
}
