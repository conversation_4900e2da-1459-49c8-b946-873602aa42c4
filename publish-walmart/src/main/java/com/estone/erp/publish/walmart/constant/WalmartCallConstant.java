package com.estone.erp.publish.walmart.constant;

/**
 * walmart请求常量类
 * <AUTHOR>
 * @date 2022/9/13 16:07
 */
public class WalmartCallConstant {

    /**
     * 基础地址
     */
    public static final String ROOT_URL = "https://marketplace.walmartapis.com";

    /**
     * 代理地址1
     */
    public static final String ROOT_URL_1 = "http://alicloud.estonapi.top:8001";

    /**
     * 代理地址2
     */
    public static final String ROOT_URL_2 = "http://8.210.64.217:8001";

    /**
     * 代理地址3
     */
    public static final String ROOT_URL_3 = "http://47.244.35.145:8001";

    /**
     * 代理地址4
     */
    public static final String ROOT_URL_4 = "http://47.242.92.226:8001";

    /**
     * 代理地址5
     */
    public static final String ROOT_URL_5 = "http://43.153.108.19:8001";

    /**
     * 版本
     */
    public static final String VERSION = "v3";

    /**
     * 路径分隔符
     */
    public static final String PATH_SPLIT = "/";

    /**
     * 路径参数
     */
    public static final String PARAM = "%s";

    /**
     * 获取token
     */
    public static final String TOKEN_PATH = "/token";

    /**
     * feed相关
     */
    public static final String FEED_PATH = "/feeds";

    /**
     * 批量上传库存
     */
    public static final String BULK_INVENTORY_PATH = "/feeds?feedType=inventory";

    /**
     * 库存相关
     */
    public static final String INVENTORY_PATH = "/inventory";

    /**
     * 批量上传价格
     */
    public static final String BULK_PRICE_PATH = "/feeds?feedType=price";

    /**
     * 价格相关
     */
    public static final String PRICE_PATH = "/price";

    /**
     * item相关
     */
    public static final String ITEM_PATH = "/items";

    /**
     * 批量新增item
     */
    public static final String BULK_ITEM_PATH = "/feeds?feedType=MP_ITEM";

    /**
     * 批量更新已存在的item
     */
    public static final String MAINTENANCE_ITEM_PATH = "/feeds?feedType=MP_MAINTENANCE";

    /**
     * 分类
     */
    public static final String TAXONOMY_PATH = "/taxonomy";

    /**
     * 搜索item
     */
    public static final String SEARCH_ITEM_PATH = "/walmart/search";

    /**
     * 请求报告
     */
    public static final String REPORT_PATH = "/reports/reportRequests";

    /**
     * 下载报告
     */
    public static final String DOWNLOAD_REPORT_PATH = "/reports/downloadReport";

    /**
     * 报告类型和版本
     */
    public static final String REPORT_TYPE_VERSION_PATH = "?reportType=%s&reportVersion=v4";

    /**
     * 获取规格
     */
    public static final String SPEC_PATH = "/spec";

    /**
     * 获取运费模板
     */
    public static final String SHIPPING_TEMPLATE_PATH = "/settings/shipping/templates";

    /**
     * 获取物流中心
     */
    public static final String FULFILLMENT_CENTER_PATH = "/settings/shipping/shipnodes?includeCalendarDayConfiguration=false";

    /**
     * SKU绑定运费模板
     */
    public static final String SKU_TEMPLATE_MAP_PATH = "/feeds?feedType=SKU_TEMPLATE_MAP";

}
