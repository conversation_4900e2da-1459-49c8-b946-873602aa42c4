package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Walmart Admin Template 查询条件类
 */
@Data
public class WalmartAdminTemplateCriteria {
    private static final long serialVersionUID = 1L;



    /**
     * 范本编号逗号分隔
     */
    private String idStr;

    private List<Long> ids;


    /**
     * 货号逗号分隔
     */
    private String articleNumberStr;

    /**
     * 标题模糊查询
     */
    private String title;

    /**
     * 店铺
     */
    private List<String> accountNumberList;

    /**
     *   数据来源
     */
    private Integer skuDataSource;
    /**
     * 刊登类型
     */
    private Integer publishType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 售卖形式,true-变体，false-非变体
     */
    private Boolean saleVariant;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 构建查询条件
     * @return 查询条件
     */
    public LambdaQueryWrapper<WalmartAdminTemplate> buildQueryWrapper() {
       LambdaQueryWrapper<WalmartAdminTemplate> queryWrapper =
            new LambdaQueryWrapper<>();

        // 范本编号
        if (StringUtils.isNotBlank(this.getIdStr())) {
            List<Long> idList = CommonUtils.splitLongList(this.getIdStr(), ",");
            if (CollectionUtils.isNotEmpty(idList)) {
                queryWrapper.in(WalmartAdminTemplate::getId, idList);
            }
        }

        // 货号
        if (StringUtils.isNotBlank(this.getArticleNumberStr())) {
            List<String> articleNumbers = CommonUtils.splitList(this.getArticleNumberStr(), ",");
            if (CollectionUtils.isNotEmpty(articleNumbers)) {
                queryWrapper.in(WalmartAdminTemplate::getArticleNumber, articleNumbers);
            }
        }

        // 标题
        if (StringUtils.isNotBlank(this.getTitle())) {
            queryWrapper.like(WalmartAdminTemplate::getTitle, this.getTitle());
        }

        // 店铺
        if (CollectionUtils.isNotEmpty(this.getAccountNumberList())) {
            queryWrapper.in(WalmartAdminTemplate::getAccountNumber, this.getAccountNumberList());
        }

        // 数据来源
        if (this.getSkuDataSource() != null) {
            queryWrapper.eq(WalmartAdminTemplate::getSkuDataSource, this.getSkuDataSource());
        }

        // 刊登类型
        if (this.getPublishType() != null) {
            queryWrapper.eq(WalmartAdminTemplate::getPublishType, this.getPublishType());
        }

        // 状态
        if (this.getStatus() != null) {
            queryWrapper.eq(WalmartAdminTemplate::getStatus, this.getStatus());
        }

        // 售卖形式
        if (this.getSaleVariant() != null) {
            queryWrapper.eq(WalmartAdminTemplate::getSaleVariant, this.getSaleVariant());
        }

        queryWrapper.orderByDesc(WalmartAdminTemplate::getCreateDate);

        return queryWrapper;
    }
}
