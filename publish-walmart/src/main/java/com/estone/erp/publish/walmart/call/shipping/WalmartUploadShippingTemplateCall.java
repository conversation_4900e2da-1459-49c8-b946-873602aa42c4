package com.estone.erp.publish.walmart.call.shipping;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.walmart.call.AbstractWalmartCall;
import com.estone.erp.publish.walmart.constant.WalmartCallConstant;
import com.estone.erp.publish.walmart.model.dto.ShippingTemplateBean;
import com.estone.erp.publish.walmart.util.WalmartFeedStatusUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SKU绑定运费模板
 * <AUTHOR>
 * @date 2023/5/29
 */
@Slf4j
public class WalmartUploadShippingTemplateCall extends AbstractWalmartCall {

    public WalmartUploadShippingTemplateCall(SaleAccountAndBusinessResponse walmartPmsAccount) {
        super(walmartPmsAccount);
    }

    public String uploadShippingTemplate(List<ShippingTemplateBean> beanList) {
        HttpPost request = this.createPostRequest(WalmartCallConstant.SKU_TEMPLATE_MAP_PATH, null);
        request.setHeader("Content-Type", "multipart/form-data");
        
        // 构建请求体
        String json = buildRequestJson(beanList);
        
        MultipartEntityBuilder meb = MultipartEntityBuilder.create();
        meb.addBinaryBody("file", json.getBytes());
        HttpEntity httpEntity = meb.build();
        request.setEntity(httpEntity);
        
        // 执行请求
        String body = execute(request);
        
        // 解析feedId
        return WalmartFeedStatusUtils.analysisFeedId(body);
    }
    
    private String buildRequestJson(List<ShippingTemplateBean> beanList) {
        // 构建JSON请求体
        Map<String, Object> requestMap = new HashMap<>();
        
        List<Map<String, Object>> items = new ArrayList<>();
        for (ShippingTemplateBean bean : beanList) {
            Map<String, Object> item = new HashMap<>();
            Map<String, Object> preciseDelivery = new HashMap<>();
            
            preciseDelivery.put("shippingTemplateId", bean.getShippingTemplateId());
            preciseDelivery.put("fulfillmentCenterId", bean.getFulfillmentCenterId());
            preciseDelivery.put("actionType", "Add");
            preciseDelivery.put("sku", bean.getSellerSku());
            
            item.put("PreciseDelivery", preciseDelivery);
            items.add(item);
        }
        
        requestMap.put("Item", items);
        
        Map<String, Object> header = new HashMap<>();
        header.put("sellingChannel", "precisedelivery");
        header.put("locale", "en");
        header.put("version", "1.0");
        
        requestMap.put("ItemFeedHeader", header);
        
        return JSON.toJSONString(requestMap);
    }
}