package com.estone.erp.publish.walmart.handler.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.WalmartTimePublishQueue;
import com.estone.erp.publish.tidb.publishtidb.service.WalmartTimePublishQueueService;
import com.estone.erp.publish.walmart.enums.*;
import com.estone.erp.publish.walmart.handler.publish.param.SpuPublishParam;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.dto.BuilderTemplateDTO;
import com.estone.erp.publish.walmart.model.dto.WalmartTemplateDTO;
import com.estone.erp.publish.walmart.model.dto.WalmartTemplateValidationDTO;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.WalmartFeedTaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * SPU自动刊登执行器 - SPU Automatic Publishing Executor
 * 
 * 负责处理SPU自动刊登和定时刊登的业务逻辑
 * Responsible for handling SPU automatic publishing and scheduled publishing business logic
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Component
public class WalmartSpuAutoPublishExecutor extends PublishExecutor<SpuPublishParam> {

    @Resource
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private WalmartTimePublishQueueService walmartTimePublishQueueService;

    @Override
    protected WalmartTemplateDTO getTemplateData(SpuPublishParam param) throws BusinessException {
        try {
            DataContextHolder.setUsername(param.getUser());
            // 1. 获取模板信息 - Get template information
            BuilderTemplateDTO builderTemplateDTO = createBuilderTemplateDTO(param);
            Map<String, Object> templateInfo = walmartTemplateService.getTemplateInfo(builderTemplateDTO);

            // 2. 校验模板 - Validate template
            validateTemplateInfo(templateInfo);

            // 3. 解析模板数据 - Parse template data
            WalmartTemplateDTO walmartTemplateDTO = parseTemplateData(templateInfo);

            // 4. 设置发布角色和类型 - Set publish role and type
            setPublishRoleAndType(walmartTemplateDTO, param);

            walmartTemplateDTO.setCreateBy(param.getUser());
            walmartTemplateDTO.setUpdateBy(param.getUser());

            // 5. 保存并发布模板 - Save and publish template
            WalmartTemplate walmartTemplate = walmartTemplateService.saveAndPublishNew(walmartTemplateDTO);

            // 6. 处理定时刊登 - Handle scheduled publishing
            if (isTimePublish(param)) {
                updateTimePublishQueue(param, walmartTemplate);
            }



            return walmartTemplateDTO;
        } catch (Exception e) {
            handleException(param, e);
            throw new BusinessException("自动刊登失败: " + e.getMessage());
        }
    }

    /**
     * 创建模板构建DTO - Create template builder DTO
     */
    private BuilderTemplateDTO createBuilderTemplateDTO(SpuPublishParam param) {
        return BuilderTemplateDTO.builder()
                .articleNumber(param.getSpu())
                .skuDataSource(param.getSkuDataSource())
                .accountNumber(param.getAccountNumber())
                .build();
    }

    /**
     * 校验模板信息 - Validate template information
     */
    private void validateTemplateInfo(Map<String, Object> templateInfo) throws BusinessException {
     Object validationObj = templateInfo.get("validation");
     if (ObjectUtils.isNotEmpty(validationObj)){
         List<WalmartTemplateValidationDTO> list;
         if (validationObj instanceof List) {
             list = (List<WalmartTemplateValidationDTO>) validationObj;
         } else {
             list = JSON.parseArray(validationObj.toString(), WalmartTemplateValidationDTO.class);
         }
         for (WalmartTemplateValidationDTO validation : list) {
             if (!validation.getSuccess() && !validation.getCode().equals(WalmartTemplateValidationEnum.INFRINGEMENT_TERM.getCode())) {
                 throw new BusinessException(validation.getErrorMsg());
             }
         }
     }
        Object template = templateInfo.get("template");
        if (ObjectUtils.isEmpty(template)) {
            throw new BusinessException("未找到模板信息");
        }
    }

    /**
     * 解析模板数据 - Parse template data
     */
    private WalmartTemplateDTO parseTemplateData(Map<String, Object> templateInfo) {
        WalmartTemplateDTO template = (WalmartTemplateDTO)templateInfo.get("template");
        return template;
    }

    /**
     * 设置发布角色和类型 - Set publish role and type
     */
    private void setPublishRoleAndType(WalmartTemplateDTO walmartTemplateDTO, SpuPublishParam param) {
        if (isSpuPublish(param)) {
            walmartTemplateDTO.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
            walmartTemplateDTO.setPublishType(WalmartPublishTypeEnum.AUTO_PUBLISH.getCode());
        } else if (isTimePublish(param)) {
            walmartTemplateDTO.setPublishRole(WalmartPublishRoleEnum.SALE.getCode());
            walmartTemplateDTO.setPublishType(WalmartPublishTypeEnum.AUTO_PUBLISH.getCode());
        }
        walmartTemplateDTO.setQueueId(param.getTimePublishQueueId());
    }

    /**
     * 更新定时刊登队列 - Update time publish queue
     */
    private void updateTimePublishQueue(SpuPublishParam param, WalmartTemplate walmartTemplate) {
        Long queueId = param.getTimePublishQueueId();
        WalmartTimePublishQueue queue = new WalmartTimePublishQueue();
        queue.setId(queueId);
        queue.setTemplateId(walmartTemplate.getId());
        queue.setTitle(walmartTemplate.getTitle());
        queue.setPublishStatus(WalmartTemplateStatusEnum.PUBLISHING.getCode());
        queue.setStatus(WalmartTimePublishEnums.PROCESSING.getCode());
        queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        walmartTimePublishQueueService.updateById(queue);
    }

    /**
     * 处理异常 - Handle exception
     */
    private void handleException(SpuPublishParam param, Exception e) {
        log.error("保存模板失败", e);
        saveErrorFeedTask(param, e);
        if (isTimePublish(param)) {
            updateQueueStatus(param, e.getMessage());
        }
    }

    /**
     * 是否为SPU直接刊登 - Is SPU direct publishing
     */
    private boolean isSpuPublish(SpuPublishParam param) {
        return param.getPublishType().equals(WalmartPublishModeEnum.SPU_PUBLISH.getCode());
    }

    /**
     * 是否为定时刊登 - Is scheduled publishing
     */
    private boolean isTimePublish(SpuPublishParam param) {
        return param.getPublishType().equals(WalmartPublishModeEnum.TIME_PUBLISH.getCode());
    }

    /**
     * 更新队列状态为失败 - Update queue status to failed
     * 
     * @param param 发布参数 - Publish parameters
     * @param message 错误信息 - Error message
     */
    private void updateQueueStatus(SpuPublishParam param, String message) {
        WalmartTimePublishQueue queue = new WalmartTimePublishQueue();
        queue.setId(param.getTimePublishQueueId());
        queue.setStatus(WalmartTimePublishEnums.END.getCode());
        queue.setPublishStatus(WalmartTemplateStatusEnum.PUBLISH_FAILED.getCode());
        queue.setExtra(message);
        queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        walmartTimePublishQueueService.updateById(queue);
    }

    /**
     * 保存错误的Feed任务 - Save error feed task
     * 
     * @param param 发布参数 - Publish parameters
     * @param e 异常 - Exception
     */
    private void saveErrorFeedTask(SpuPublishParam param, Exception e) {
        FeedTask feedTask = new FeedTask();
        feedTask.setArticleNumber(param.getSpu());
        feedTask.setAccountNumber(param.getAccountNumber());
        feedTask.setTaskType(WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());
        feedTask.setCreatedBy(param.getUser());

        String errorMessage = Optional.ofNullable(e.getMessage())
                .filter(StringUtils::isNotBlank)
                .orElse("Unknown error");

        WalmartFeedTaskUtil.insertFailFeedTask(feedTask, errorMessage);
    }
}
