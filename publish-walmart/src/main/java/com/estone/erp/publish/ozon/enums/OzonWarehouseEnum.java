package com.estone.erp.publish.ozon.enums;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21 18:27
 */
public enum OzonWarehouseEnum {

    ESTD("ESTD", "易邮宝", "OZON-MXEUB"),

    ESTD_K("ESTD K", "CEL陆空联运", "OZON-CELQXJLK"),

    ESTD_L("ESTD L", "CEL陆运", "OZON-CELQXJLY"),

    // 这个名称跟物流现在没有, 下面两个是同一个
    ESTD_A("ESTDA", "", ""),
    ESTD_A_2("ESTD A", "", ""),

    // ESTDB 也是自动
    ESTDB("ESTDB", "", ""),
    ESTD_B("ESTD B", "", "")
    ;

    private static final List<String> ALL_AUTO_WAREHOUSE_LOWERS_ENUMS = new ArrayList<>();
    private static final List<String> ALL_WAREHOUSE_LOWERS_ENUMS = new ArrayList<>();
    private static final List<String> ESTDA_LOWERS_ENUMS = new ArrayList<>();
    private static final List<String> ESTDB_LOWERS_ENUMS = new ArrayList<>();

    static {
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD_L.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD_K.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD_A.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD_B.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTD_A_2.code.toLowerCase());
        ALL_WAREHOUSE_LOWERS_ENUMS.add(ESTDB.code.toLowerCase());

        ALL_AUTO_WAREHOUSE_LOWERS_ENUMS.add(ESTD_A.code.toLowerCase());
        ALL_AUTO_WAREHOUSE_LOWERS_ENUMS.add(ESTD_B.code.toLowerCase());
        ALL_AUTO_WAREHOUSE_LOWERS_ENUMS.add(ESTD_A_2.code.toLowerCase());
        ALL_AUTO_WAREHOUSE_LOWERS_ENUMS.add(ESTDB.code.toLowerCase());

        ESTDA_LOWERS_ENUMS.add(ESTD_A.code.toLowerCase());
        ESTDA_LOWERS_ENUMS.add(ESTD_A_2.code.toLowerCase());

        ESTDB_LOWERS_ENUMS.add(ESTDB.code.toLowerCase());
        ESTDB_LOWERS_ENUMS.add(ESTD_B.code.toLowerCase());
    }

    private String code;

    private String name;

    private String logistics;

    OzonWarehouseEnum(String code, String name, String logistics) {
        this.name = name;
        this.code = code;
        this.logistics = logistics;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getLogistics() {
        return logistics;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLogistics(String logistics) {
        this.logistics = logistics;
    }

    public static boolean allWarehouseLowersContains(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        return ALL_WAREHOUSE_LOWERS_ENUMS.contains(name.toLowerCase());
    }

    public static boolean allAutoWareHouseLowerCaseContains(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        return ALL_AUTO_WAREHOUSE_LOWERS_ENUMS.contains(name.toLowerCase());
    }

    public static boolean estdaWareHouseLowerCaseContains(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        return ESTDA_LOWERS_ENUMS.contains(name.toLowerCase());
    }

    public static boolean estdbWareHouseLowerCaseContains(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        return ESTDB_LOWERS_ENUMS.contains(name.toLowerCase());
    }

    /**
     * 根据code获取物流方式
     */
    public static String getLogisticsByCode(String code) {
        OzonWarehouseEnum[] values = values();
        for (OzonWarehouseEnum value : values) {
            if (value.code.equalsIgnoreCase(code)) {
                return value.logistics;
            }
        }
        return null;
    }

    public static OzonWarehouseEnum getWarehouseByCode(String code) {
        OzonWarehouseEnum[] values = values();
        for (OzonWarehouseEnum value : values) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
