package com.estone.erp.publish.ozon.model;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OzonEstdaLogisticsRuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public OzonEstdaLogisticsRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWeightFromIsNull() {
            addCriterion("weight_from is null");
            return (Criteria) this;
        }

        public Criteria andWeightFromIsNotNull() {
            addCriterion("weight_from is not null");
            return (Criteria) this;
        }

        public Criteria andWeightFromEqualTo(Integer value) {
            addCriterion("weight_from =", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromNotEqualTo(Integer value) {
            addCriterion("weight_from <>", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromGreaterThan(Integer value) {
            addCriterion("weight_from >", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromGreaterThanOrEqualTo(Integer value) {
            addCriterion("weight_from >=", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromLessThan(Integer value) {
            addCriterion("weight_from <", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromLessThanOrEqualTo(Integer value) {
            addCriterion("weight_from <=", value, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromIn(List<Integer> values) {
            addCriterion("weight_from in", values, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromNotIn(List<Integer> values) {
            addCriterion("weight_from not in", values, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromBetween(Integer value1, Integer value2) {
            addCriterion("weight_from between", value1, value2, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightFromNotBetween(Integer value1, Integer value2) {
            addCriterion("weight_from not between", value1, value2, "weightFrom");
            return (Criteria) this;
        }

        public Criteria andWeightToIsNull() {
            addCriterion("weight_to is null");
            return (Criteria) this;
        }

        public Criteria andWeightToIsNotNull() {
            addCriterion("weight_to is not null");
            return (Criteria) this;
        }

        public Criteria andWeightToEqualTo(Integer value) {
            addCriterion("weight_to =", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToNotEqualTo(Integer value) {
            addCriterion("weight_to <>", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToGreaterThan(Integer value) {
            addCriterion("weight_to >", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToGreaterThanOrEqualTo(Integer value) {
            addCriterion("weight_to >=", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToLessThan(Integer value) {
            addCriterion("weight_to <", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToLessThanOrEqualTo(Integer value) {
            addCriterion("weight_to <=", value, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToIn(List<Integer> values) {
            addCriterion("weight_to in", values, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToNotIn(List<Integer> values) {
            addCriterion("weight_to not in", values, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToBetween(Integer value1, Integer value2) {
            addCriterion("weight_to between", value1, value2, "weightTo");
            return (Criteria) this;
        }

        public Criteria andWeightToNotBetween(Integer value1, Integer value2) {
            addCriterion("weight_to not between", value1, value2, "weightTo");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeIsNull() {
            addCriterion("first_logistics_code is null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeIsNotNull() {
            addCriterion("first_logistics_code is not null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeEqualTo(String value) {
            addCriterion("first_logistics_code =", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeNotEqualTo(String value) {
            addCriterion("first_logistics_code <>", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeGreaterThan(String value) {
            addCriterion("first_logistics_code >", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("first_logistics_code >=", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeLessThan(String value) {
            addCriterion("first_logistics_code <", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeLessThanOrEqualTo(String value) {
            addCriterion("first_logistics_code <=", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeLike(String value) {
            addCriterion("first_logistics_code like", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeNotLike(String value) {
            addCriterion("first_logistics_code not like", value, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeIn(List<String> values) {
            addCriterion("first_logistics_code in", values, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeNotIn(List<String> values) {
            addCriterion("first_logistics_code not in", values, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeBetween(String value1, String value2) {
            addCriterion("first_logistics_code between", value1, value2, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsCodeNotBetween(String value1, String value2) {
            addCriterion("first_logistics_code not between", value1, value2, "firstLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameIsNull() {
            addCriterion("first_logistics_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameIsNotNull() {
            addCriterion("first_logistics_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameEqualTo(String value) {
            addCriterion("first_logistics_name =", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameNotEqualTo(String value) {
            addCriterion("first_logistics_name <>", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameGreaterThan(String value) {
            addCriterion("first_logistics_name >", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_logistics_name >=", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameLessThan(String value) {
            addCriterion("first_logistics_name <", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameLessThanOrEqualTo(String value) {
            addCriterion("first_logistics_name <=", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameLike(String value) {
            addCriterion("first_logistics_name like", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameNotLike(String value) {
            addCriterion("first_logistics_name not like", value, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameIn(List<String> values) {
            addCriterion("first_logistics_name in", values, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameNotIn(List<String> values) {
            addCriterion("first_logistics_name not in", values, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameBetween(String value1, String value2) {
            addCriterion("first_logistics_name between", value1, value2, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsNameNotBetween(String value1, String value2) {
            addCriterion("first_logistics_name not between", value1, value2, "firstLogisticsName");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceIsNull() {
            addCriterion("first_logistics_max_price is null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceIsNotNull() {
            addCriterion("first_logistics_max_price is not null");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceEqualTo(BigDecimal value) {
            addCriterion("first_logistics_max_price =", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceNotEqualTo(BigDecimal value) {
            addCriterion("first_logistics_max_price <>", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceGreaterThan(BigDecimal value) {
            addCriterion("first_logistics_max_price >", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("first_logistics_max_price >=", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceLessThan(BigDecimal value) {
            addCriterion("first_logistics_max_price <", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("first_logistics_max_price <=", value, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceIn(List<BigDecimal> values) {
            addCriterion("first_logistics_max_price in", values, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceNotIn(List<BigDecimal> values) {
            addCriterion("first_logistics_max_price not in", values, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_logistics_max_price between", value1, value2, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andFirstLogisticsMaxPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("first_logistics_max_price not between", value1, value2, "firstLogisticsMaxPrice");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsIsNull() {
            addCriterion("enable_second_logistics is null");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsIsNotNull() {
            addCriterion("enable_second_logistics is not null");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsEqualTo(Boolean value) {
            addCriterion("enable_second_logistics =", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsNotEqualTo(Boolean value) {
            addCriterion("enable_second_logistics <>", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsGreaterThan(Boolean value) {
            addCriterion("enable_second_logistics >", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_second_logistics >=", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsLessThan(Boolean value) {
            addCriterion("enable_second_logistics <", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_second_logistics <=", value, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsIn(List<Boolean> values) {
            addCriterion("enable_second_logistics in", values, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsNotIn(List<Boolean> values) {
            addCriterion("enable_second_logistics not in", values, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_second_logistics between", value1, value2, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andEnableSecondLogisticsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_second_logistics not between", value1, value2, "enableSecondLogistics");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeIsNull() {
            addCriterion("second_logistics_code is null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeIsNotNull() {
            addCriterion("second_logistics_code is not null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeEqualTo(String value) {
            addCriterion("second_logistics_code =", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeNotEqualTo(String value) {
            addCriterion("second_logistics_code <>", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeGreaterThan(String value) {
            addCriterion("second_logistics_code >", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeGreaterThanOrEqualTo(String value) {
            addCriterion("second_logistics_code >=", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeLessThan(String value) {
            addCriterion("second_logistics_code <", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeLessThanOrEqualTo(String value) {
            addCriterion("second_logistics_code <=", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeLike(String value) {
            addCriterion("second_logistics_code like", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeNotLike(String value) {
            addCriterion("second_logistics_code not like", value, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeIn(List<String> values) {
            addCriterion("second_logistics_code in", values, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeNotIn(List<String> values) {
            addCriterion("second_logistics_code not in", values, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeBetween(String value1, String value2) {
            addCriterion("second_logistics_code between", value1, value2, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsCodeNotBetween(String value1, String value2) {
            addCriterion("second_logistics_code not between", value1, value2, "secondLogisticsCode");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameIsNull() {
            addCriterion("second_logistics_name is null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameIsNotNull() {
            addCriterion("second_logistics_name is not null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameEqualTo(String value) {
            addCriterion("second_logistics_name =", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameNotEqualTo(String value) {
            addCriterion("second_logistics_name <>", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameGreaterThan(String value) {
            addCriterion("second_logistics_name >", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameGreaterThanOrEqualTo(String value) {
            addCriterion("second_logistics_name >=", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameLessThan(String value) {
            addCriterion("second_logistics_name <", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameLessThanOrEqualTo(String value) {
            addCriterion("second_logistics_name <=", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameLike(String value) {
            addCriterion("second_logistics_name like", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameNotLike(String value) {
            addCriterion("second_logistics_name not like", value, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameIn(List<String> values) {
            addCriterion("second_logistics_name in", values, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameNotIn(List<String> values) {
            addCriterion("second_logistics_name not in", values, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameBetween(String value1, String value2) {
            addCriterion("second_logistics_name between", value1, value2, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsNameNotBetween(String value1, String value2) {
            addCriterion("second_logistics_name not between", value1, value2, "secondLogisticsName");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceIsNull() {
            addCriterion("second_logistics_min_price is null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceIsNotNull() {
            addCriterion("second_logistics_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceEqualTo(BigDecimal value) {
            addCriterion("second_logistics_min_price =", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceNotEqualTo(BigDecimal value) {
            addCriterion("second_logistics_min_price <>", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceGreaterThan(BigDecimal value) {
            addCriterion("second_logistics_min_price >", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("second_logistics_min_price >=", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceLessThan(BigDecimal value) {
            addCriterion("second_logistics_min_price <", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("second_logistics_min_price <=", value, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceIn(List<BigDecimal> values) {
            addCriterion("second_logistics_min_price in", values, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceNotIn(List<BigDecimal> values) {
            addCriterion("second_logistics_min_price not in", values, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_logistics_min_price between", value1, value2, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andSecondLogisticsMinPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("second_logistics_min_price not between", value1, value2, "secondLogisticsMinPrice");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }
        public Criteria andTypeEqual(Integer type) {
            addCriterion("type =", type, "type");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}