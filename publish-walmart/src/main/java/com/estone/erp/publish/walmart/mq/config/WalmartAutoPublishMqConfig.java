package com.estone.erp.publish.walmart.mq.config;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.walmart.mq.consumer.WalmartAutoPublishMqListener;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "mq-config")
public class WalmartAutoPublishMqConfig {

    private Boolean walmartAutoPublishQueueEnable;
    private Integer walmartAutoPublishQueueConsumer;
    private Integer walmartAutoPublishQueuePrefetchCount;

    @Bean
    public Queue walmartAutoPublish() {
        return new Queue(PublishQueues.WALMART_AUTO_PUBLISH_QUEUE, true);
    }

    @Bean
    public Binding walmartAutoPublishBinding() {
        return new Binding(PublishQueues.WALMART_AUTO_PUBLISH_QUEUE, Binding.DestinationType.QUEUE, PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                PublishQueues.WALMART_AUTO_PUBLISH_QUEUE_KEY, null);
    }

    @Bean
    public WalmartAutoPublishMqListener walmartAutoPublishMqListener() {
        return new WalmartAutoPublishMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer walmartAutoPublishListenerContainer(
            WalmartAutoPublishMqListener walmartAutoPublishMqListener, ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        SimpleMessageListenerContainer(container, PublishQueues.WALMART_AUTO_PUBLISH_QUEUE, walmartAutoPublishMqListener);
        return container;
    }

    private void SimpleMessageListenerContainer(SimpleMessageListenerContainer container, String queue,
                                                ChannelAwareMessageListener channelAwareMessageListener) {
        if (walmartAutoPublishQueueEnable) {
            container.setQueueNames(queue);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(walmartAutoPublishQueuePrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(walmartAutoPublishQueueConsumer);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(channelAwareMessageListener);// 监听处理类
        }
    }
}
