package com.estone.erp.publish.walmart.handler.publish;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.walmart.componet.WalmartTemplateNewBuilderHelper;
import com.estone.erp.publish.walmart.enums.WalmartTemplateStatusEnum;
import com.estone.erp.publish.walmart.enums.WalmartTemplateTableEnum;
import com.estone.erp.publish.walmart.handler.publish.param.TemplatePublishParam;
import com.estone.erp.publish.walmart.model.WalmartTemplate;
import com.estone.erp.publish.walmart.model.dto.WalmartTemplateDTO;
import com.estone.erp.publish.walmart.service.WalmartTemplateService;
import com.estone.erp.publish.walmart.util.WalmartFeedTaskUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 模板刊登
 *
 * <AUTHOR>
 * @date 2024-01-30 12:15
 */
@Slf4j
@Component
public class WalmartTemplatePublishExecutor extends PublishExecutor<TemplatePublishParam> {

    @Resource
    private WalmartTemplateService walmartTemplateService;

    @Resource
    private WalmartTemplateNewBuilderHelper walmartTemplateNewBuilderHelper;


    @Override
    protected WalmartTemplateDTO getTemplateData(TemplatePublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());
        // 查询模板
        WalmartTemplate template = walmartTemplateService.selectByPrimaryKey(param.getTemplateId(), WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        if (null == template
                || WalmartTemplateStatusEnum.PUBLISH_SUCCESS.getCode() == template.getPublishStatus()
                || WalmartTemplateStatusEnum.PARTIAL_SUCCESS.getCode() == (template.getPublishStatus())) {
            throw new RuntimeException("刊登失败，该模板状态无法刊登:" + param.getTemplateId());
        }

        WalmartTemplateDTO templateDTO = new WalmartTemplateDTO();
        BeanCopier beanCopier = BeanCopier.create(WalmartTemplate.class, WalmartTemplateDTO.class, false);
        beanCopier.copy(template, templateDTO, null);

        // 保存模板
        WalmartTemplate update = new WalmartTemplate();
        update.setId(template.getId());
        update.setTable(WalmartTemplateTableEnum.WALMART_TEMPLATE.getCode());
        update.setPublishStatus(WalmartTemplateStatusEnum.PUBLISHING.getCode());
        update.setIsPublish(0);
        update.setPublishType(param.getType());
        update.setPublishRole(param.getRole());
        walmartTemplateService.updateByPrimaryKeySelective(update);

        // 创建处理报告
        WalmartFeedTaskUtil.generateFeedTask(Lists.newArrayList(templateDTO), WalmartTaskTypeEnum.UPLOAD_ITEM.getStatusMsgEn());

        return templateDTO;
    }
}
