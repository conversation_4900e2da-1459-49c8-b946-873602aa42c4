package com.estone.erp.publish.ozon.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.SortOrder;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.executors.ExecutorUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch4.model.EsOzonGrossProfit;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonStockInfo;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.elasticsearch4.util.EsOzonItemUtils;
import com.estone.erp.publish.ozon.call.model.AccountWareHouseInfo;
import com.estone.erp.publish.ozon.common.OzonConstant;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.*;
import com.estone.erp.publish.ozon.handler.OzonSyncListingHandler;
import com.estone.erp.publish.ozon.handler.OzonUpdateHandler;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRule;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceRequest;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.OzonSyncItemDO;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.model.vo.OzonEditItemDescVO;
import com.estone.erp.publish.ozon.model.vo.OzonListingVO;
import com.estone.erp.publish.ozon.model.vo.OzonWareHouseGrossProfitVO;
import com.estone.erp.publish.ozon.model.vo.OzonWarehouseStockInfoVO;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonEsItemService;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.utils.OzonCalcUtils;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.service.SmallPlatformExcelDownloadLogService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-04 11:48
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class OzonEsItemServiceImpl implements OzonEsItemService {

    @Autowired
    private EsOzonItemService esOzonItemService;
    @Autowired
    private PermissionsHelper permissionHelper;
    @Autowired
    private OzonSyncListingHandler syncListingHandler;
    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;
    @Autowired
    private OzonUpdateHandler updateHandler;
    @Autowired
    private SmallPlatformExcelDownloadLogService downloadLogService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private OzonAccountConfigService ozonAccountConfigService;
    @Autowired
    private OzonEsItemBulkProcessor ozonEsItemBulkProcessor;

    @Override
    public void save(EsOzonItem esOzonItem) {
        esOzonItemService.save(esOzonItem);
    }

    @Override
    public EsOzonItem getOneSelectFieldsWithId(EsOzonItemRequest request) {
        List<EsOzonItem> itemList = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        return itemList.get(0);
    }

    @Override
    public PageInfo<OzonListingVO> searchListing(EsOzonItemRequest request) {
        // 权限检查
        EsOzonItemUtils.checkListingSearchPermission(request, permissionHelper);

        // 处理查询条件
        handleRequest(request);

        PageInfo<EsOzonItem> itemPageInfo = esOzonItemService.searchPageInfo(request);
        List<EsOzonItem> contents = itemPageInfo.getContents();
        if (CollectionUtils.isEmpty(contents)) {
            List<OzonListingVO> emptyList = new ArrayList<>();
            PageInfo<OzonListingVO> emptyPage = new PageInfo<>();
            emptyPage.setPageIndex(itemPageInfo.getPageIndex());
            emptyPage.setPageSize(itemPageInfo.getPageSize());
            emptyPage.setContents(emptyList);
            emptyPage.setTotalPages(itemPageInfo.getTotalPages());
            return emptyPage;
        }
        List<String> accountNumbers = contents.stream()
                .map(EsOzonItem::getAccountNumber)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumbers, SaleChannel.CHANNEL_OZON);
        // VO转换,销售组织架构查询
        List<OzonListingVO> voList = contents.stream().map(esOzonItem -> {
            OzonListingVO ozonListingVO = BeanUtil.copyProperties(esOzonItem, OzonListingVO.class);
            // 销售、销售组长、销售主管
            if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                Triple<String, String, String> stringStringStringTriple = saleSuperiorMap.get(esOzonItem.getAccountNumber());
                if (null != stringStringStringTriple) {
                    ozonListingVO.setSaleMan(stringStringStringTriple.getLeft());
                    ozonListingVO.setSaleManLeader(stringStringStringTriple.getMiddle());
                    ozonListingVO.setSaleManManager(stringStringStringTriple.getRight());
                }
            }

            // 多仓库库存
            List<EsOzonStockInfo> warehouseStockInfos = esOzonItem.getWarehouseStockInfos();
            if (CollectionUtils.isNotEmpty(warehouseStockInfos)) {
                // 毛利
                List<EsOzonGrossProfit> esOzonGrossProfits = esOzonItem.getEsOzonGrossProfits();
                Map<String, EsOzonGrossProfit> grossProfitMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(esOzonGrossProfits)) {
                    grossProfitMap = Optional
                            .of(esOzonGrossProfits.stream().collect(Collectors.toMap(EsOzonGrossProfit::getWarehouseName, o -> o, (k1, k2) -> k1)))
                            .orElse(Collections.emptyMap());
                }
                List<OzonWareHouseGrossProfitVO> ozonWareHouseGrossProfitVOList = new ArrayList<>();
                for (EsOzonStockInfo warehouseStockInfo : warehouseStockInfos) {
                    OzonWareHouseGrossProfitVO ozonWareHouseGrossProfitVO = new OzonWareHouseGrossProfitVO();
                    ozonWareHouseGrossProfitVO.setName(warehouseStockInfo.getWarehouseName());
                    ozonWareHouseGrossProfitVO.setStock(warehouseStockInfo.getPresent());
                    EsOzonGrossProfit esOzonGrossProfit = grossProfitMap.get(warehouseStockInfo.getWarehouseName().toUpperCase());
                    if (null != esOzonGrossProfit) {
                        ozonWareHouseGrossProfitVO.setGrossProfit(esOzonGrossProfit.getGrossProfit());
                        ozonWareHouseGrossProfitVO.setGrossProfitRate(esOzonGrossProfit.getGrossProfitRate());
                    }
                    ozonWareHouseGrossProfitVOList.add(ozonWareHouseGrossProfitVO);
                }
                ozonListingVO.setWareHouseInfoVOList(ozonWareHouseGrossProfitVOList);
            }
            return ozonListingVO;
        }).collect(Collectors.toList());

        PageInfo<OzonListingVO> pageResult = new PageInfo<>();
        pageResult.setPageIndex(itemPageInfo.getPageIndex());
        pageResult.setPageSize(itemPageInfo.getPageSize());
        pageResult.setContents(voList);
        pageResult.setTotal(itemPageInfo.getTotal());
        pageResult.setTotalPages(itemPageInfo.getTotalPages());
        return pageResult;
    }

    private void handleRequest(EsOzonItemRequest request) {
        // 查询字段
        request.setFields(OzonConstant.LISTING_FILES);

        if (StringUtils.isBlank(request.getSequence())) {
            request.setSequence(SortOrder.DESC.toString());
        }
        if (StringUtils.isBlank(request.getOrderBy())) {
            request.setOrderBy("createDate");
        }

        // 处理仓库字段
        Long warehouseId = request.getWarehouseId();
        List<String> accountNumbers = request.getAccountNumbers();
        if (null != warehouseId && CollectionUtils.isNotEmpty(accountNumbers)) {
            AccountWareHouseInfo accountWareHouseInfo = ozonAccountConfigService.getAccountWareHouseInfo(request.getAccountNumbers().get(0), warehouseId);
            request.setWarehouseName(accountWareHouseInfo.getName());
        }
    }

    @Override
    public void syncProductInfo(OzonSyncItemDO syncItemDO) {
        if (StringUtils.isNotBlank(syncItemDO.getAccountNumber()) && CollectionUtils.isNotEmpty(syncItemDO.getSellerSkus())) {
            syncProductInfoWithSellerSkuFeed(syncItemDO.getAccountNumber(), syncItemDO.getSellerSkus());
            return;
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        if (CollectionUtils.isNotEmpty(syncItemDO.getProductIds())) {
            request.setProductIds(syncItemDO.getProductIds());
        }
        if (CollectionUtils.isNotEmpty(syncItemDO.getSellerSkus())) {
            request.setSellerSkus(syncItemDO.getSellerSkus());
        }

        request.setFields(new String[]{"id", "productId", "sku", "spu", "accountNumber", "statusCode", "state", "actualWeight"});
        List<EsOzonItem> itemList = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(itemList)) {
            throw new RuntimeException("未找到对应数据");
        }
        String username = WebUtils.getUserName();
        for (EsOzonItem item : itemList) {
            ExecutorUtils.execute(OzonExecutors.UPDATE_ITEM_INFO, () -> {
                // 同步单个产品
                updateItemInfo(item, username);
            }, "OZON_UPDATE_ITEM_INFO");
        }
    }

    @Override
    public void syncProductInfoWithSellerSkuFeed(String accountNumber, List<String> sellerSkus) {
        String username = WebUtils.getUserName();
        // 查询es 是否存在
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setSellerSkus(sellerSkus);
        request.setAccountNumber(accountNumber);
        request.setFields(new String[]{"id", "sellerSku"});
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        Map<String, List<EsOzonItem>> collect = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getSellerSku));
        for (String sku : sellerSkus) {
            List<EsOzonItem> esOzonItems1 = collect.getOrDefault(sku, new ArrayList<>());
            List<String> ids = esOzonItems1.stream().map(EsOzonItem::getId).collect(Collectors.toList());
            ExecutorUtils.execute(OzonExecutors.SYNC_UPDATE_ITEM_INFO, () -> {
                // 同步单个产品
                DataContextHolder.setUsername(username);
                FeedTask feedTask = ozonFeedTaskService.newTask(null, accountNumber, OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), "", sku);
                try {
                    OzonItemDO ozonItemDO = syncListingHandler.syncProductInfoWithSellerSku(sku, accountNumber);
                    if (ozonItemDO != null) {
                        syncListingHandler.saveAndCallSyncProductInfo(ozonItemDO);
                        syncListingHandler.syncWarehouseStockInfo(ozonItemDO.getProductId(), ozonItemDO.getOzonSku(), ozonItemDO.getAccountNumber());
                        syncListingHandler.syncAttributes(ozonItemDO.getProductId(), ozonItemDO.getAccountNumber(), null);
                        ozonFeedTaskService.succeedTask(feedTask, "success");
                    } else {
                        if (CollectionUtils.isNotEmpty(ids)) {
                            for (String id : ids) {
                                ozonEsItemBulkProcessor.setOnline(id, false);
                            }
                        }
                        ozonFeedTaskService.failTask(feedTask, "Product not found");
                    }
                } catch (Exception e) {
                    ozonFeedTaskService.failTask(feedTask, e.getMessage());
                }
            }, "OZON_SYNC_ITEM_INFO");
        }
    }

    @Override
    public void syncAccountList(List<String> accountNumbers) {
        if (CollectionUtils.isEmpty(accountNumbers)) {
            return;
        }
        List<String> syncTypelist = List.of(OzonFeedTaskEnums.SyncItemType.ALL.name(), OzonFeedTaskEnums.SyncItemType.DELTA.name());
        for (String accountNumber : accountNumbers) {
            // 检查是否存在未完成的同步任务
            if (checkExecutingSyncItemTask(accountNumber, syncTypelist)) {
                ozonFeedTaskService.addFailFeedTask(null, accountNumber, OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), "", "存在未完成的同步任务，请稍后再试");
                continue;
            }
            FeedTask feedTask = ozonFeedTaskService.newWaitingTask(null, accountNumber, OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), OzonFeedTaskEnums.SyncItemType.DELTA.name());
            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_OZON, accountNumber, false);
            ApiResult<String> result = syncListingHandler.asyncAccountListing(account, feedTask, OzonFeedTaskEnums.SyncItemType.DELTA.name());
            if (!result.isSuccess()) {
                log.error("同步店铺发送消息失败,accountNumber:{},errorMsg:{}", account.getAccountNumber(), result.getErrorMsg());
                ozonFeedTaskService.failTask(feedTask, result.getErrorMsg());
            }
        }
    }

    private boolean checkExecutingSyncItemTask(String accountNumber, List<String> syncTypeList) {
        FeedTask executingSyncItemTask = ozonFeedTaskService.getExecutingSyncItemTask(accountNumber, syncTypeList);
        if (null == executingSyncItemTask) {
            return false;
        }
        LocalDateTime createTime = LocalDateTimeUtil.of(executingSyncItemTask.getCreateTime());
        LocalDateTime now = LocalDateTime.now();
        if (createTime.plusHours(24).isBefore(now)) {
            // 超过24小时未完成,重新执行
            ozonFeedTaskService.failTask(executingSyncItemTask, "任务超时,重新执行");
            return false;
        }
        return true;
    }

    @Override
    public void syncAllAccountList(List<String> accountNumbers, String beginDate, String syncType) {
        List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_OZON);

        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            saleAccountListBySaleChannel = saleAccountListBySaleChannel.stream().filter(o -> accountNumbers.contains(o.getAccountNumber())).collect(Collectors.toList());
        }

        // 移除指定时间段同步成功的店铺
        try {
            removeSuccessAccount(beginDate, saleAccountListBySaleChannel);
        } catch (Exception e) {
            log.error("移除指定时间段同步成功的店铺失败,beginDate:{}", beginDate);
        }
        if (CollectionUtils.isEmpty(saleAccountListBySaleChannel)) {
            return;
        }
        List<String> syncTypelist = List.of(OzonFeedTaskEnums.SyncItemType.ALL.name(), OzonFeedTaskEnums.SyncItemType.DELTA.name());
        for (SaleAccountAndBusinessResponse account : saleAccountListBySaleChannel) {
            try {
                // 检查是否存在未完成的同步任务
                if (checkExecutingSyncItemTask(account.getAccountNumber(), syncTypelist)) {
                    log.error("存在未完成的同步任务,accountNumber:{}", account.getAccountNumber());
                    continue;
                }

                FeedTask feedTask = ozonFeedTaskService.newWaitingTask(null, account.getAccountNumber(), OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), syncType);
                ApiResult<String> result = syncListingHandler.asyncAccountListing(account, feedTask, syncType);
                if (!result.isSuccess()) {
                    log.error("同步店铺发送消息失败,accountNumber:{},errorMsg:{}", account.getAccountNumber(), result.getErrorMsg());
                    ozonFeedTaskService.failTask(feedTask, result.getErrorMsg());
                }
            } catch (Exception e) {
                log.error("同步店铺失败,accountNumber:{}", account.getAccountNumber(), e);
            }
        }
    }

    @Override
    public void syncAllAccountListByArchived(List<String> accountNumbers) {
        List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_OZON);
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            saleAccountListBySaleChannel = saleAccountListBySaleChannel.stream().filter(o -> accountNumbers.contains(o.getAccountNumber())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(saleAccountListBySaleChannel)) {
            return;
        }
        List<String> syncTypeList = List.of(OzonFeedTaskEnums.SyncItemType.ARCHIVED.name());
        for (SaleAccountAndBusinessResponse account : saleAccountListBySaleChannel) {
            try {
                // 检查是否存在未完成的同步任务
                if (checkExecutingSyncItemTask(account.getAccountNumber(), syncTypeList)) {
                    log.error("存在未完成的同步任务,accountNumber:{}", account.getAccountNumber());
                    continue;
                }
                FeedTask feedTask = ozonFeedTaskService.newWaitingTask(null, account.getAccountNumber(), OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), OzonFeedTaskEnums.SyncItemType.ARCHIVED.name());
                ApiResult<String> result = syncListingHandler.asyncAccountListing(account, feedTask, OzonFeedTaskEnums.SyncItemType.ARCHIVED.name());
                if (!result.isSuccess()) {
                    log.error("同步店铺发送消息失败,accountNumber:{},errorMsg:{}", account.getAccountNumber(), result.getErrorMsg());
                    ozonFeedTaskService.failTask(feedTask, result.getErrorMsg());
                }
            } catch (Exception e) {
                log.error("同步店铺失败,accountNumber:{}", account.getAccountNumber(), e);
            }
        }
    }

    private void removeSuccessAccount(String beginDate, List<SaleAccountAndBusinessResponse> saleAccountListBySaleChannel) {
        if (StringUtils.isBlank(beginDate)) {
            return;
        }
        List<String> allAccountNumber = saleAccountListBySaleChannel.stream().map(SaleAccountAndBusinessResponse::getAccountNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<List<String>> lists = PagingUtils.newPagingList(allAccountNumber, 300);
        List<String> removeAccount = new ArrayList<>();
        for (List<String> list : lists) {
            try {
                FeedTaskExample example = new FeedTaskExample();
                example.setCustomColumn("id, account_number");
                example.createCriteria()
                        .andAccountNumberIn(list)
                        .andPlatformEqualTo(Platform.Ozon.name())
                        .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.SYNC_ITEM.name())
                        .andResultStatusEqualTo(FeedTaskResultStatusEnum.SUCCESS.getResultStatus())
                        .andCreateTimeGreaterThanOrEqualTo(Timestamp.valueOf(beginDate))
                        .andArticleNumberEqualTo("ALL");
                List<FeedTask> feedTasks = ozonFeedTaskService.selectByExample(example, Platform.Ozon.name());
                if (CollectionUtils.isEmpty(feedTasks)) {
                    continue;
                }
                List<String> collect = feedTasks.stream().map(FeedTask::getAccountNumber).collect(Collectors.toList());
                removeAccount.addAll(collect);
            } catch (Exception e) {
                log.error("获取同步成功店铺失败,beginDate:{}, accountNumberList:{}", beginDate, list);
            }
        }
        if (CollectionUtils.isEmpty(removeAccount)) {
            return;
        }

        saleAccountListBySaleChannel.removeIf(o -> removeAccount.contains(o.getAccountNumber()));
    }

    @Override
    public ApiResult<String> updateStock(List<OzonUpdateDO> updateParam) {
        if (CollectionUtils.isEmpty(updateParam)) {
            return ApiResult.newError("请勾选数据后操作");
        }
        Map<String, List<OzonUpdateDO>> accountUpdateMap = updateParam.stream()
                .filter(updateData -> !updateData.getUpdateBeforeStock().equals(updateData.getUpdateAfterStock()))
                .collect(Collectors.groupingBy(OzonUpdateDO::getAccountNumber));
        accountUpdateMap.forEach((accountNumber, updateDateList) -> {
            updateHandler.updateStock(accountNumber, updateDateList);
        });
        return ApiResult.newSuccess("请求成功,请前往处理报告查询结果");
    }

    @Override
    public ApiResult<String> updatePrice(List<OzonUpdateDO> updateParam, OzonUpdatePriceTypeEnum typeEnum, boolean ignoreTheSame) {
        if (CollectionUtils.isEmpty(updateParam)) {
            return ApiResult.newError("请勾选数据后操作");
        }

        // 过滤有报错信息且改后价格为空的数据
        List<OzonUpdateDO> errorList = updateParam.stream().filter(o -> StringUtils.isNotBlank(o.getErrorMsg())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorList)) {
            updateParam.removeAll(errorList);

            // 记录处理报告
            String errorMsg = "调用试算器失败，请重新调价";
            for (OzonUpdateDO ozonUpdateDO : errorList) {
                ozonFeedTaskService.addFailFeedTask(ozonUpdateDO.getProductId(), ozonUpdateDO.getAccountNumber(),
                        OzonUpdatePriceTypeEnum.getTaskType(typeEnum), ozonUpdateDO.getSku(), errorMsg, ozonUpdateDO.getSellerSku());
            }
        }

        Map<String, List<OzonUpdateDO>> accountUpdateMap = updateParam.stream()
                .filter(updateData -> StringUtils.isNotBlank(updateData.getUpdateAfterPrice()) && new BigDecimal(updateData.getUpdateAfterPrice()).compareTo(BigDecimal.ZERO) > 0) // 改后价需大于0
                .filter(updateData -> {
                    if (StringUtils.isBlank(updateData.getUpdateBeforePrice())) {
                        return true;
                    }
                    if (ignoreTheSame) {
                        return true;
                    }
                    BigDecimal beforeValue = new BigDecimal(updateData.getUpdateBeforePrice());
                    BigDecimal afterValue = new BigDecimal(updateData.getUpdateAfterPrice());
                    return beforeValue.compareTo(afterValue) != 0;
                })
                .collect(Collectors.groupingBy(OzonUpdateDO::getAccountNumber));
        accountUpdateMap.forEach((accountNumber, updateDateList) -> {
            updateHandler.updatePrice(accountNumber, updateDateList, typeEnum, false);
        });
        return ApiResult.newSuccess("请求成功,请前往处理报告查询结果");
    }

    @Override
    public ApiResult<String> deleteItem(List<Long> productId) {
        if (CollectionUtils.isEmpty(productId)) {
            return ApiResult.newError("请勾选数据后操作");
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "productId", "sellerSku", "sku"});
        request.setProductIds(productId);
        List<EsOzonItem> itemList = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(itemList)) {
            return ApiResult.newError("无符合条件数据");
        }

        Map<String, List<EsOzonItem>> accountItems = itemList.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        accountItems.forEach((accountNumber, items) -> {
            updateHandler.deleteItems(accountNumber, items);
        });
        return ApiResult.newSuccess("请求成功,请前往处理报告查询结果");
    }

    @Override
    public ApiResult<String> unarchiveItems(List<Long> productId) {
        if (CollectionUtils.isEmpty(productId)) {
            return ApiResult.newError("请勾选数据后操作");
        }
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "productId", "sellerSku", "sku"});
        request.setProductIds(productId);
        List<EsOzonItem> itemList = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(itemList)) {
            return ApiResult.newError("无符合条件数据");
        }

        Map<String, List<EsOzonItem>> accountItems = itemList.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        accountItems.forEach((accountNumber, items) -> {
            updateHandler.unarchiveItems(accountNumber, items);
        });
        return ApiResult.newSuccess("请求成功,请前往处理报告查询结果");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> export(EsOzonItemRequest request) {
        try {
            EsOzonItemUtils.checkListingSearchPermission(request, permissionHelper);
            // 处理查询条件
            handleRequest(request);
            request.setPageIndex(0);
            request.setPageSize(1);
            PageInfo<EsOzonItem> itemPageInfo = esOzonItemService.searchPageInfo(request);
            if (itemPageInfo.getTotal() > 500000) {
                return ApiResult.newError("导出数据量过大,请缩小查询条件");
            }

            SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
            downloadLog.setType(SmallPlatformDownloadEnums.Type.LISTING.name());
            downloadLog.setQueryCondition(JSON.toJSONString(request));
            downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);
            downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
            downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            int insert = downloadLogService.insert(downloadLog);
            if (insert > 0) {
                // 发送到下载队列
                rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY, downloadLog.getId());
            }
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("前往导出结果页查询");
    }

    @Override
    public ApiResult<List<OzonEditItemDescVO>> editItemDesc(List<Long> productIds) {
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(OzonConstant.EDIT_PRODUCT_DESC_FILES);
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(esOzonItems)) {
            return ApiResult.newError("未查询到对应产品信息");
        }
        List<OzonEditItemDescVO> data = esOzonItems.stream().map(OzonEditItemDescVO::convent2VO).collect(Collectors.toList());
        return ApiResult.newSuccess(data);
    }

    @Override
    public ApiResult<String> updateItemDesc(List<OzonEditItemDescVO> updateData) {
        Map<String, List<OzonEditItemDescVO>> accountDataMap = updateData.stream().collect(Collectors.groupingBy(OzonEditItemDescVO::getAccountNumber));
        String username = WebUtils.getUserName();
        accountDataMap.forEach((account, data) -> {
            OzonExecutors.UPDATE_PRICE_POOL.execute(() -> {
                updateHandler.updateItemDesc(username, account, data);
            });
        });
        return ApiResult.newSuccess("请求成功,请前往处理报告查询结果");
    }

    @Override
    public ApiResult<List<OzonWarehouseStockInfoVO>> getWarehouseStockInfo(List<Long> productIds) {
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(OzonConstant.UPDATE_STOCK_FILES);
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(esOzonItems)) {
            return ApiResult.newSuccess(Collections.emptyList());
        }
        List<OzonWarehouseStockInfoVO> stockInfoVOS = esOzonItems.stream().map(item -> {
            OzonWarehouseStockInfoVO stockInfoVO = new OzonWarehouseStockInfoVO();
            stockInfoVO.setAccountNumber(item.getAccountNumber());
            stockInfoVO.setProductId(item.getProductId());
            stockInfoVO.setSellerSku(item.getSellerSku());
            stockInfoVO.setItemStatus(item.getSkuStatus());
            stockInfoVO.setSku(item.getSku());
            if (CollectionUtils.isEmpty(item.getWarehouseStockInfos())) {
                stockInfoVO.setWarehouseStockInfos(Collections.emptyList());
            } else {
                stockInfoVO.setWarehouseStockInfos(item.getWarehouseStockInfos());
            }
            return stockInfoVO;
        }).collect(Collectors.toList());
        return ApiResult.newSuccess(stockInfoVOS);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public List<EsOzonItem> listItemByRequest(EsOzonItemRequest request) {
        return esOzonItemService.listItemByRequest(request);
    }

    private void updateItemInfo(EsOzonItem item, String username) {
        DataContextHolder.setUsername(username);
        FeedTask feedTask = ozonFeedTaskService.newTask(item.getProductId(), item.getAccountNumber(), OzonFeedTaskEnums.TaskType.SYNC_ITEM.name(), item.getSku());
        try {
            OzonItemDO ozonItemDO = syncListingHandler.syncAndSaveItemInfo(item.getAccountNumber(), item.getProductId(), new ArrayList<>());
            if (ozonItemDO == null) {
                ozonEsItemBulkProcessor.setOnline(item.getId(), false);
                ozonFeedTaskService.failTask(feedTask, "Product not found");
                return;
            }
            syncListingHandler.syncWarehouseStockInfo(ozonItemDO.getProductId(), ozonItemDO.getOzonSku(), ozonItemDO.getAccountNumber());
            syncListingHandler.syncAttributes(ozonItemDO.getProductId(), ozonItemDO.getAccountNumber(), item.getActualWeight());
            ozonFeedTaskService.succeedTask(feedTask, "success");
        } catch (Exception e) {
            ozonFeedTaskService.failTask(feedTask, e.getMessage());
        }
    }

    @Override
    public void markAirFreight(List<String> idList) {
        updateHandler.markAirFreight(idList);
    }

    @Override
    public void deleteAirFreight(List<String> idList) {
        updateHandler.deleteAirFreight(idList);
    }

    @Override
    public void syncCategoryPath(Long productId, Long categoryId, String checkCategoryPath) {
        syncListingHandler.syncCategoryPath(productId, categoryId, checkCategoryPath);
    }

    @Override
    public void updateMinPriceByAccountConfig(OzonAccountConfig config, List<OzonEstdaLogisticsRule> allRules, String createBy, List<String> skuList, boolean minPriceNumberIsNull, boolean ignoreTheSame){
        if(config == null || config.getBottomPriceGrossProfit() == null){
            log.error("店铺配置为空");
            return;
        }

        String accountNumber = config.getAccountNumber();
        String fbsWarehouseInfo = config.getFbsWarehouseInfo();
        List<AccountWareHouseInfo> accountWareHouseInfos = JSON.parseArray(fbsWarehouseInfo, AccountWareHouseInfo.class);
        log.info("开始处理店铺：{}, 毛利率：{}%",
                config.getAccountNumber(),
                new BigDecimal(config.getBottomPriceGrossProfit()).multiply(new BigDecimal("100")));

        try {
            // do 在线数据改为不在线
            EsOzonItemRequest request = new EsOzonItemRequest();
            request.setAccountNumber(accountNumber);
            if(CollectionUtils.isNotEmpty(skuList)){
                request.setSkus(skuList);
            }
            if(minPriceNumberIsNull){
                //查询最低价为空的数据
                request.setMinPriceNumberIsNull(true);
            }
            // 设置需要的字段
            request.setFields(new String[]{
                    "id", "accountNumber", "itemId", "skuId", "productId" ,"priceNumber",
                    "minPriceNumber", "warehouseStockInfos", "costPrice",
                    "status", "createdTime", "updatedTime", "sellerSku", "sku",
                    "currencyCode", "actualWeight", "linkTag", "specialGoodsCode",
                    "skuCount"
            });
            request.setOrderBy("id");
            request.setSequence("asc");
            esOzonItemService.scrollQueryExecutorTask(request, (itemList) -> {
                if (CollectionUtils.isEmpty(itemList)) {
                    return;
                }

                // 处理当前页的商品
                List<OzonUpdateDO> updateList = new ArrayList<>();
                for (EsOzonItem item : itemList) {
                    try {
                        // 处理单个商品定价
                        OzonUpdateDO updateDO = processItemMinPricing(item, accountWareHouseInfos, null, new BigDecimal(config.getBottomPriceGrossProfit()), allRules, createBy);
                        if (updateDO != null) {
                            updateList.add(updateDO);
                        }
                    } catch (Exception e) {
                        log.error("商品 {} 价格计算失败", item.getProductId(), e);
                    }
                }

                // 批量更新商品价格
                if (!CollectionUtils.isEmpty(updateList)) {
                    ApiResult<String> result = this.updatePrice(updateList, OzonUpdatePriceTypeEnum.UPDATE_MIN_PRICE, ignoreTheSame);
                    if (result.isSuccess()) {
                        log.debug("批量更新 {} 条商品最低价成功", updateList.size());
                    } else {
                        log.error("批量更新商品最低价失败: {}", result.getErrorMsg());
                    }
                }
            });

        } catch (Exception e) {
            log.error("店铺 {} 处理失败", accountNumber, e);
        }
    }


    /**
     * 处理单个商品定价
     * @param item 商品信息
     * @param accountWareHouseInfos 店铺的所有仓库
     * @param selectWarehouseNameList 业务需要，只能用的仓库
     * @param grossProfitRate 毛利率
     * @param allRules 所有自动算法规则
     * @param createBy 创建人
     * @return 更新参数（如果需要更新的话）
     */
    @Override
    public OzonUpdateDO processItemMinPricing(EsOzonItem item, List<AccountWareHouseInfo> accountWareHouseInfos, List<String> selectWarehouseNameList, BigDecimal grossProfitRate, List<OzonEstdaLogisticsRule> allRules, String createBy) {
        try {
            log.info("开始处理商品定价：itemId={}, sellerSku={}",
                    item.getProductId(), item.getSellerSku());

            List<EsOzonStockInfo> warehouseStockInfos = item.getWarehouseStockInfos();
            if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                log.info("商品 {} 没有库存信息，跳过定价", item.getProductId());
                return null;
            }
            //warehouseStockInfos需要过滤有库存的数据
            warehouseStockInfos = warehouseStockInfos.stream().filter(t -> t.getPresent() != null && t.getPresent() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                log.info("商品 {} 没有库存信息，跳过定价", item.getProductId());
                return null;
            }
            if (CollectionUtils.isNotEmpty(selectWarehouseNameList)){
                warehouseStockInfos = warehouseStockInfos.stream().filter(t -> selectWarehouseNameList.contains(t.getWarehouseName())).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                log.info("商品 {} 没有选择仓库，跳过定价", item.getProductId());
                return null;
            }

            warehouseStockInfos = warehouseStockInfos.stream().filter(t -> OzonWarehouseEnum.allWarehouseLowersContains(t.getWarehouseName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                log.info("商品 {} 没有指定仓库信息，跳过定价", item.getProductId());
                return null;
            }

            boolean isEstdaWarehouse = false;
            boolean isEstdbWarehouse = false;
            try {
                // 1. 优先estda 再 estdb 再随机一个
                isEstdaWarehouse = isEstdaWarehouse(accountWareHouseInfos, warehouseStockInfos);
                isEstdbWarehouse = isEstdbWarehouse(accountWareHouseInfos, warehouseStockInfos);
            } catch (Exception e) {
                log.error("商品 {} 获取仓库类型失败", item.getProductId(), e);
                return null;
            }

            log.info("商品 {} 仓库类型：{}", item.getProductId(),
                    isEstdaWarehouse ? "ESTDA" : "STANDARD");

            // 2. 计算最低价
            BigDecimal calculatedMinPrice;
            if (isEstdaWarehouse || isEstdbWarehouse) {
                if (isEstdaWarehouse) {
                    calculatedMinPrice = doMinPriceAuto(item, grossProfitRate, allRules, 1);
                } else {
                    calculatedMinPrice = doMinPriceAuto(item, grossProfitRate, allRules, 2);
                }
            } else {
                calculatedMinPrice = doMinPriceManual(item, warehouseStockInfos, grossProfitRate);
            }

            // 3. 验证价格有效性
            if (calculatedMinPrice == null || calculatedMinPrice.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("商品 {} 计算出的最低价无效：{}", item.getProductId(), calculatedMinPrice);
                return null;
            }

            // 如果 calculatedMinPrice的价格大于 priceNumber 则用 priceNumber 作为 calculatedMinPrice
            BigDecimal priceNumber = item.getPriceNumber()!= null?
                    new BigDecimal(item.getPriceNumber().toString()) : null;
            if (priceNumber != null && calculatedMinPrice.compareTo(priceNumber) > 0) {
                calculatedMinPrice = priceNumber;
            }

            // 4. 检查是否需要更新（避免重复更新相同价格）
            BigDecimal currentMinPrice = item.getMinPriceNumber() != null ?
                    new BigDecimal(item.getMinPriceNumber().toString()) : null;
            if (currentMinPrice != null &&
                    currentMinPrice.compareTo(calculatedMinPrice) == 0) {
                log.info("商品 {} 最低价无变化，跳过更新：{}", item.getProductId(), calculatedMinPrice);
                return null;
            }

            // 5. 构建更新参数
            OzonUpdateDO updateDO = new OzonUpdateDO();
            updateDO.setProductId(item.getProductId());
            updateDO.setAccountNumber(item.getAccountNumber());
            updateDO.setSellerSku(item.getSellerSku());
            updateDO.setSku(item.getSku());
            updateDO.setListingPrice(item.getPriceNumber()!= null? item.getPriceNumber().toString() : null);
            updateDO.setUpdateBeforePrice(currentMinPrice != null ? currentMinPrice.toString() : null);
            updateDO.setUpdateAfterPrice(calculatedMinPrice.toString());
            updateDO.setJob(createBy);
            updateDO.setCurrencyCode(item.getCurrencyCode());

            log.info("商品 {} 价格计算完成：{} → {}",
                    item.getProductId(), currentMinPrice, calculatedMinPrice);

            return updateDO;

        } catch (Exception e) {
            log.error("商品 {} 定价处理失败", item.getProductId(), e);
            throw new RuntimeException("商品定价失败: " + e.getMessage());
        }
    }

    /**
     * ESTDA仓库自动计算最低价
     * @param item 商品信息
     * @param grossProfitRate 毛利率
     * @return 计算出的最低价
     */
    private BigDecimal doMinPriceAuto(EsOzonItem item, BigDecimal grossProfitRate, List<OzonEstdaLogisticsRule> allRules, Integer autoType) {
        try {
            // 构建自动算价请求
            OzonCalcPriceRequest request = new OzonCalcPriceRequest();
            request.setBusinessId(item.getId());
            request.setSpecialGoodsCode(item.getSpecialGoodsCode());
            request.setLinkTag(item.getLinkTag());
            request.setGrossProfitRate(grossProfitRate.doubleValue());
            request.setSku(item.getSku());
            request.setSkuCount(item.getSkuCount());
            request.setSite(OzonSiteCurrencyEnum.getSiteByCurrency(item.getCurrencyCode()));
            request.setAccountNumber(item.getAccountNumber());
            // 设置重量
            Double actualWeight = item.getActualWeight();
            if (actualWeight != null) {
                request.setWeight(actualWeight * 1000);
            }
//            OzonCalcUtils.setCalcLogistics(request, allRules);
            // 自动最低价计算，现在就只有estdA
            request.setAutoType(autoType);
            // 调用算价工具进行计算
            List<OzonCalcPriceRequest> requests = new ArrayList<>();
            requests.add(request);
            ApiResult<List<OzonCalcPriceResponse>> result = OzonCalcUtils.listingBatchCalcByAutoPrice(requests, allRules);

            if (result.isSuccess() && !CollectionUtils.isEmpty(result.getResult())) {
                OzonCalcPriceResponse response = result.getResult().get(0);
                if (response.getIsSuccess()) {
                    return BigDecimal.valueOf(response.getSalePrice());
                } else {
                    log.error("自动算价失败: {}", response.getErrorMsg());
                    return null;
                }
            } else {
                log.error("调用自动算价接口失败: {}", result.getErrorMsg());
                return null;
            }

        } catch (Exception e) {
            log.error("ESTDA仓库自动计算价格异常", e);
            return null;
        }
    }

    /**
     * 标准仓库手动计算最低价
     * @param item 商品信息
     * @param grossProfitRate 毛利率
     * @return 计算出的最低价
     */
    private BigDecimal doMinPriceManual(EsOzonItem item, List<EsOzonStockInfo> warehouseStockInfos, BigDecimal grossProfitRate) {
        try {
            // 构建手动算价请求
            OzonCalcPriceRequest request = new OzonCalcPriceRequest();
            request.setSku(item.getSku());
            request.setCurrency(item.getCurrencyCode());
            request.setGrossProfitRate(grossProfitRate.doubleValue());
            request.setBusinessId(item.getId());
            request.setSkuCount(item.getSkuCount());
            request.setAccountNumber(item.getAccountNumber());

            // 设置物流方式（从仓库信息中获取）
            String logistics = getLogisticsFromWarehouse(warehouseStockInfos);
            log.info("产品id:{}, sku:{}, 物流方式: {}", item.getProductId(),item.getSku(), logistics);
            request.setCalcLogistics(logistics);

            // 调用算价工具进行计算
            List<OzonCalcPriceRequest> requests = new ArrayList<>();
            requests.add(request);
            // 按照指定的算价规则来计算价格用 CALC_PRICE
            List<OzonCalcPriceResponse> responses = OzonCalcUtils.listingBatchCalc(requests, OzonCalcPriceTypeEnum.CALC_PRICE.getCode());

            if (!CollectionUtils.isEmpty(responses)) {
                OzonCalcPriceResponse response = responses.get(0);
                if (response.getIsSuccess()) {
                    BigDecimal price = BigDecimal.valueOf(response.getSalePrice());
                    return price;
                } else {
                    log.error("手动算价失败: {}", response.getErrorMsg());
                    return null;
                }
            } else {
                log.error("手动算价返回空结果");
                return null;
            }

        } catch (Exception e) {
            log.error("标准仓库手动计算价格异常", e);
            return null;
        }
    }

    /**
     * 从仓库信息中获取物流方式
     * @param warehouseInfos 仓库信息列表
     * @return 物流方式
     */
    private String getLogisticsFromWarehouse(List<EsOzonStockInfo> warehouseInfos) {
        if (CollectionUtils.isEmpty(warehouseInfos)) {
            return "OZON-MXEUB"; // 默认物流方式
        }

        //warehouseInfos 打乱顺序
        Collections.shuffle(warehouseInfos);

        for (EsOzonStockInfo stockInfo : warehouseInfos) {
            if (stockInfo.getWarehouseName() != null) {
                String warehouseName = stockInfo.getWarehouseName().trim();
                String logistics = OzonWarehouseEnum.getLogisticsByCode(warehouseName);
                if (logistics != null && !logistics.isEmpty()) {
                    return logistics;
                }
            }
        }

        return "OZON-MXEUB"; // 默认物流方式
    }

    private boolean isEstdaWarehouse(List<AccountWareHouseInfo> accountWareHouseInfos, List<EsOzonStockInfo> warehouseInfos) {
        //店铺配置的仓库信息是最准确的id不变，名称可能会变化
        // accountWareHouseInfos 获取 仓库名称为ESTDA或ESTD A的warehouseId 集合
        List<Long> estdaWarehouseIds = accountWareHouseInfos.stream()
                .filter(t -> OzonWarehouseEnum.estdaWareHouseLowerCaseContains(t.getName()))
                .map(AccountWareHouseInfo::getWarehouseId)
                .collect(Collectors.toList());
        for (EsOzonStockInfo stockInfo : warehouseInfos) {
            if (stockInfo.getWarehouseName() != null) {
                Long warehouseId = stockInfo.getWarehouseId();
                if (estdaWarehouseIds.contains(warehouseId)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isEstdbWarehouse(List<AccountWareHouseInfo> accountWareHouseInfos, List<EsOzonStockInfo> warehouseInfos) {
        //店铺配置的仓库信息是最准确的id不变，名称可能会变化
        // accountWareHouseInfos 获取 仓库名称为ESTDA或ESTD A的warehouseId 集合
        List<Long> estdaWarehouseIds = accountWareHouseInfos.stream()
                .filter(t -> OzonWarehouseEnum.estdbWareHouseLowerCaseContains(t.getName()))
                .map(AccountWareHouseInfo::getWarehouseId)
                .collect(Collectors.toList());
        for (EsOzonStockInfo stockInfo : warehouseInfos) {
            if (stockInfo.getWarehouseName() != null) {
                Long warehouseId = stockInfo.getWarehouseId();
                if (estdaWarehouseIds.contains(warehouseId)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void resetExpiredMinPrice(List<String> accountNumberList, List<String> skuList) {
        log.info("开始执行OZON最低价重设任务，店铺：{}，SKU：{}",
                accountNumberList, skuList);
        
        try {
            // 查询超过20天未更新的商品
            List<EsOzonItem> expiredItems = getExpiredMinPriceItems(accountNumberList, skuList);
            
            if (CollectionUtils.isEmpty(expiredItems)) {
                log.info("未找到需要重设最低价的商品");
                return;
            }
            
            log.info("找到{}个需要重设最低价的商品", expiredItems.size());
            
            // 构建更新参数
            List<OzonUpdateDO> updateList = buildUpdateParams(expiredItems);
            
            // 分批处理，避免一次性处理过多数据
            int batchSize = 100;
            List<List<OzonUpdateDO>> batches = PagingUtils.newPagingList(updateList, batchSize);
            
            AtomicInteger successCount = new AtomicInteger();
            AtomicInteger failCount = new AtomicInteger();

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (List<OzonUpdateDO> batch : batches) {
                try {
                    CompletableFuture<Void> future = CompletableFuture
                            .runAsync(() -> {
                                        ApiResult<String> result = updatePrice(batch, OzonUpdatePriceTypeEnum.UPDATE_MIN_PRICE, true);
                                        if (result.isSuccess()) {
                                            successCount.addAndGet(batch.size());
                                            log.info("批次处理成功，更新{}个商品", batch.size());
                                        } else {
                                            failCount.addAndGet(batch.size());
                                            log.warn("批次处理失败：{}", result.getErrorMsg());
                                        }
                                    }
                                    ,OzonExecutors.TIME_RESET_UPDATE_MIN_PRICE_POOL)
                            .exceptionally(ex -> {
                                log.error("Error processing batch {} {}",
                                         ex.getMessage(), ex);
                                return null;
                            });

                    futures.add(future);

                } catch (Exception e) {
                    failCount.addAndGet(batch.size());
                    log.error("批次处理异常：", e);
                }
            }
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("Completed processing all batches for");
            } catch (Exception e) {
                log.error("Error waiting for batch processing completion for {}",
                        e.getMessage(), e);
            }
            
            log.info("OZON最低价重设任务完成，成功：{}个，失败：{}个", successCount, failCount);
            
        } catch (Exception e) {
            log.error("OZON最低价重设任务执行异常：", e);
            throw new RuntimeException("最低价重设任务执行失败", e);
        }
    }
    
    /**
     * 查询超过20天未更新的商品
     * 
     * @param accountNumberList 指定店铺列表
     * @param skuList 指定SKU列表
     */
    private List<EsOzonItem> getExpiredMinPriceItems(List<String> accountNumberList, List<String> skuList) {
        EsOzonItemRequest request = new EsOzonItemRequest();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 计算20天前的时间
        LocalDateTime twentyDaysAgo = LocalDateTime.now().minusDays(20);
        // 替换为使用 Date.from()
        request.setMinPriceNumberUpdateDateTo(df.format(twentyDaysAgo));

        // 设置店铺过滤条件
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            request.setAccountNumbers(accountNumberList);
        }

        // 设置SKU过滤条件
        if (CollectionUtils.isNotEmpty(skuList)) {
            request.setSkus(skuList);
        }

        // 只查询需要的字段
        request.setFields(new String[]{
            "id", "accountNumber", "productId", "sellerSku", "sku", 
            "minPriceNumber", "minPrice", "priceNumber", "price", "currencyCode", "updateDate"
        });

        // 只查询有最低价的商品（minPriceNumber不为空且大于0）
        request.setFromMinPrice(0.0000001d);

        // 设置分页参数，批量获取所有数据
        request.setPageIndex(0);
        request.setPageSize(10000); // 设置较大的页面大小

        List<EsOzonItem> allItems = new ArrayList<>();
        PageInfo<EsOzonItem> pageInfo;

        do {
            pageInfo = esOzonItemService.searchPageInfo(request);
            if (pageInfo != null && CollectionUtils.isNotEmpty(pageInfo.getContents())) {
                allItems.addAll(pageInfo.getContents());
            }
            request.setPageIndex(request.getPageIndex() + 1);
        } while (pageInfo != null && pageInfo.getPageIndex() < pageInfo.getTotalPages());

        log.info("查询到{}个超过20天未更新的商品，查询条件：店铺{}，SKU{}",
                allItems.size(), accountNumberList, skuList);
        return allItems;
    }
    
    /**
     * 构建更新参数
     */
    private List<OzonUpdateDO> buildUpdateParams(List<EsOzonItem> items) {
        List<OzonUpdateDO> updateList = new ArrayList<>();
        
        for (EsOzonItem item : items) {
            try {
                OzonUpdateDO updateDO = new OzonUpdateDO();
                updateDO.setProductId(item.getProductId());
                updateDO.setAccountNumber(item.getAccountNumber());
                updateDO.setSellerSku(item.getSellerSku());
                updateDO.setSku(item.getSku());
                updateDO.setListingPrice(item.getPriceNumber() == null ? null : item.getPriceNumber().toString());
                updateDO.setCurrencyCode(item.getCurrencyCode());
                
                // 使用当前的最低价作为新的最低价（即使值相同也要更新）
                Double minPriceNumber = item.getMinPriceNumber();
                if (minPriceNumber != null && minPriceNumber >= 0) {
                    updateDO.setUpdateBeforePrice(minPriceNumber.toString());
                    updateDO.setUpdateAfterPrice(String.valueOf(minPriceNumber));
                    
                    // 设置listing价格（更新最低价时需要）
                    if (item.getPriceNumber() != null) {
                        updateDO.setListingPrice(String.valueOf(item.getPriceNumber()));
                    }
                    
                    updateDO.setJob("resetExpiredMinPrice"); // 标识为定时任务执行
                    updateList.add(updateDO);
                } else {
                    log.warn("商品{}的最低价无效，跳过更新", item.getSellerSku());
                }
                
            } catch (Exception e) {
                log.error("构建商品{}更新参数失败：", item.getSellerSku(), e);
            }
        }
        
        return updateList;
    }
}
