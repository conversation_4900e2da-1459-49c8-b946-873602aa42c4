package com.estone.erp.publish.ozon.utils;

import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.ozon.enums.OperateLogEnum;
import com.estone.erp.publish.ozon.model.OzonCalcPriceRule;
import com.estone.erp.publish.ozon.model.OzonOperateLog;
import com.estone.erp.publish.ozon.service.OzonCalcPriceRuleService;
import com.estone.erp.publish.ozon.service.OzonOperateLogService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2023/7/6
 */
public class OzonAccountUtil {

    /**
     * 检查算价规则同标签的价格区间是否重合
     * @param ozonCalcPriceRuleList
     * @return
     */
    public static Boolean checkPriceSection(List<OzonCalcPriceRule> ozonCalcPriceRuleList) {
        if (CollectionUtils.isEmpty(ozonCalcPriceRuleList)) {
            return true;
        }
        String labelIsNull = "labelIsNull";
        // 根据仓库 + 标签分组
        Map<String, Map<String, List<OzonCalcPriceRule>>> warehouseIdAndLabelMap = new HashMap<>();
        for (OzonCalcPriceRule ozonCalcPriceRule : ozonCalcPriceRuleList) {
            List<String> labelList = ozonCalcPriceRule.getLabelList();
            Long warehouseId = ozonCalcPriceRule.getWarehouseId();

            String key = warehouseId == null ? "" : warehouseId.toString();

            Map<String, List<OzonCalcPriceRule>> labelAndOzonCaclPriceRule = warehouseIdAndLabelMap.getOrDefault(key, new HashMap<>());
            // 标签为空时分为一组
            if (CollectionUtils.isNotEmpty(labelList)) {
                for (String label : labelList) {
                    List<OzonCalcPriceRule> orDefault = labelAndOzonCaclPriceRule.getOrDefault(label, new ArrayList<>());
                    orDefault.add(ozonCalcPriceRule);
                    labelAndOzonCaclPriceRule.put(label, orDefault);
                }
            } else {
                List<OzonCalcPriceRule> orDefault = labelAndOzonCaclPriceRule.getOrDefault(labelIsNull, new ArrayList<>());
                orDefault.add(ozonCalcPriceRule);
                labelAndOzonCaclPriceRule.put(labelIsNull, orDefault);
            }
            warehouseIdAndLabelMap.put(key, labelAndOzonCaclPriceRule);
        }

        for (Map.Entry<String, Map<String, List<OzonCalcPriceRule>>> entry : warehouseIdAndLabelMap.entrySet()) {
            Map<String, List<OzonCalcPriceRule>> labelAndConfigMap = entry.getValue();
            for (Map.Entry<String, List<OzonCalcPriceRule>> stringListEntry : labelAndConfigMap.entrySet()) {
                String label = stringListEntry.getKey();
                List<OzonCalcPriceRule> priceRules = new ArrayList<>(stringListEntry.getValue());
                if (!StringUtils.equals(label, labelIsNull)) {
                    List<OzonCalcPriceRule> ozonCalcPriceRules = labelAndConfigMap.get(labelIsNull);
                    if (CollectionUtils.isNotEmpty(ozonCalcPriceRules)) {
                        priceRules.addAll(ozonCalcPriceRules);
                    }
                }
                if (priceRules.size() == 1) {
                    continue;
                }
                priceRules = priceRules.stream()
                        .sorted(Comparator.comparing(OzonCalcPriceRule::getFromWeight, Comparator.nullsFirst(Double::compareTo)))
                        .collect(Collectors.toList());
                // 将价格由小到大放入集合
                List<Double> priceList = new ArrayList<>();
                for (OzonCalcPriceRule priceRule : priceRules) {
                    priceList.add(priceRule.getFromWeight());
                    priceList.add(priceRule.getToWeight());
                }
                // 判断价格区间是否正常
                for (int i=0; i<priceList.size()-2; i++) {
                    Double prevPrice = priceList.get(i);
                    Double nextPrice = priceList.get(i + 1);
                    if((i == 0 && null == prevPrice) || (i == priceList.size()-2 && null == nextPrice)) {
                        continue;
                    }
                    // 如果在中间出现空值
                    if(null == prevPrice || null == nextPrice) {
                        return false;
                    }
                    // 比较前一个值和后一个值的大小
                    if (i % 2 == 0) {
                        if (prevPrice >= nextPrice) {
                            return false;
                        }
                    } else {
                        if (prevPrice > nextPrice) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 检查算价配置毛利率
     *
     * @param minGrossProfitRate
     * @param updateCalcPriceRules
     * @return
     */
    public static Boolean checkGrossProfitRate(Double minGrossProfitRate, List<OzonCalcPriceRule> updateCalcPriceRules) {
        if (null == minGrossProfitRate) {
            return true;
        }

        for (OzonCalcPriceRule updateCalcPriceRule : updateCalcPriceRules) {
            Double grossProfitRate = updateCalcPriceRule.getGrossProfitRate();
            if (grossProfitRate != null && grossProfitRate < minGrossProfitRate) {
                return false;
            }
        }

        return true;
    }

    /**
     * 店铺配置毛利率小于最低毛利率，则清空
     *
     * @param accountNumber
     * @param minGrossProfitRate
     */
    public static void checkGrossProfitRate(String accountNumber, Double minGrossProfitRate) {
        if (StringUtils.isBlank(accountNumber)) {
            return;
        }

        // 查询算价配置
        OzonCalcPriceRuleService ozonCalcPriceRuleService = SpringUtils.getBean(OzonCalcPriceRuleService.class);
        List<OzonCalcPriceRule> ozonCalcPriceRules = ozonCalcPriceRuleService.selectByAccountNumber(accountNumber);
        if (CollectionUtils.isEmpty(ozonCalcPriceRules)) {
            return;
        }

        List<OzonOperateLog> logs = new ArrayList<>();
        for (OzonCalcPriceRule dbOzonCalcPriceRule : ozonCalcPriceRules) {
            OzonCalcPriceRule ozonCalcPriceRule = BeanUtil.copyProperties(dbOzonCalcPriceRule, OzonCalcPriceRule.class);
            Double grossProfitRate = ozonCalcPriceRule.getGrossProfitRate();
            if (null == grossProfitRate) {
                continue;
            }
            if (grossProfitRate < minGrossProfitRate) {
                // 清空毛利率
                ozonCalcPriceRule.setGrossProfitRate(null);
                ozonCalcPriceRuleService.updateByPrimaryKey(ozonCalcPriceRule);

                // 记录日志
                List<OzonOperateLog> ozonOperateLogs = OzonOperateLogUtils.buildLog(dbOzonCalcPriceRule, ozonCalcPriceRule, OperateLogEnum.UPDATE_CALC_PRICE_RULE, "id", false);
                logs.addAll(ozonOperateLogs);
            }
        }
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }

        for (OzonOperateLog ozonOperateLog : logs) {
            ozonOperateLog.setAccountNumber(accountNumber);
        }
        OzonOperateLogService ozonOperateLogService = SpringUtils.getBean(OzonOperateLogService.class);
        ozonOperateLogService.batchInsert(logs);
    }
}
