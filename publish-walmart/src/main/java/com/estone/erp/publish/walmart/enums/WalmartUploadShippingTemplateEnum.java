package com.estone.erp.publish.walmart.enums;

import lombok.Getter;

/**
 * 上传运费模板状态枚举
 * <AUTHOR>
 * @date 2023/5/29
 */
@Getter
public enum WalmartUploadShippingTemplateEnum {

    NOT_UPLOAD(0, "未上传"),
    UPLOAD_SUCCESS(1, "上传成功"),
    UPLOADING(2, "上传中"),
    WAIT_UPLOAD(3, "待上传"),
    UPLOAD_FAILED(4, "上传失败"),
    ;
    
    private int code;
    private String name;
    
    private WalmartUploadShippingTemplateEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }
}