<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.WalmartCategoryForecastLogMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.WalmartCategoryForecastLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="articleNumber" column="article_number" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="handledTitle" column="handled_title" jdbcType="VARCHAR"/>
            <result property="categoryPath" column="category_path" jdbcType="VARCHAR"/>
            <result property="handledCategoryPath" column="handled_category_path" jdbcType="VARCHAR"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="resultStatus" column="result_status" jdbcType="INTEGER"/>
            <result property="resultMsg" column="result_msg" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,article_number,title,
        handled_title,category_path,handled_category_path,
        category_id,created_time,result_status,
        result_msg
    </sql>
</mapper>
