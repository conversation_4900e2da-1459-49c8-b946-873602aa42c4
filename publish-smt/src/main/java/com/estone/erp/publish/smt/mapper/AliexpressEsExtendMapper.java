package com.estone.erp.publish.smt.mapper;

import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import com.estone.erp.publish.smt.model.AliexpressEsExtendExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AliexpressEsExtendMapper {
    int countByExample(AliexpressEsExtendExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int deleteByProductId(@Param("list") List<Long> productId);

    int insert(AliexpressEsExtend record);

    AliexpressEsExtend selectByPrimaryKey(Long id);

    List<AliexpressEsExtend> selectByExample(AliexpressEsExtendExample example);

    int updateByExampleSelective(@Param("record") AliexpressEsExtend record, @Param("example") AliexpressEsExtendExample example);

    int updateByPrimaryKeySelective(AliexpressEsExtend record);

    int updateOFFandOnByProductId(AliexpressEsExtend record);

    int batchUpdate(@Param("list") List<AliexpressEsExtend> extendList);

    /**
     * 删除指定账号下指定的数据记录
     *
     * @param account 店铺账号
     * @param orphanedProductIds 需要删除的 productId 列表
     * @return 删除的记录数
     */
    int deleteOrphanedRecordsByAccount(@Param("account") String account, @Param("orphanedProductIds") List<Long> orphanedProductIds);

}