package com.estone.erp.publish.smt.call.direct;

import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.HttpUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.platform.service.WatermarkTemplateService;
import com.estone.erp.publish.smt.enums.TemplateTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.SmtImgFail;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.SmtImgFailService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import com.global.iop.util.FileItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description: templateType 刊登类型 ，自动刊登需要上传图片水印
 * @date 2019/11/815:54
 */
@Slf4j
public class UploadImageOpenCall {

    private static WatermarkTemplateService watermarkTemplateService = SpringUtils.getBean(WatermarkTemplateService.class);
    private static AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);
    private static SmtImgFailService smtImgFailService = SpringUtils.getBean(SmtImgFailService.class);

    public String uploadImageToAliexpress(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String imgUrlValue, Integer templateType, boolean isFlip,Boolean isPopToSku) throws Exception {
        long begin = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
        String key = RedisConstant.SMT_IMAGE_KEY + ":" + accountNumber + ":" + imgUrlValue;
        String redisUrl = PublishRedisClusterUtils.get(key);
        if(StringUtils.isNotBlank(redisUrl)){
            //log.info("刊登上传图片耗时,使用了缓存图片：{},：{}ms", redisUrl, (end - begin));
            return redisUrl;
        }
        if (saleAccountByAccountNumber != null && StringUtils.isNotBlank(saleAccountByAccountNumber.getAccessToken())
                && StringUtils.isNotBlank(imgUrlValue)) {
            ResponseJson res = new ResponseJson(StatusCode.FAIL);
            int retryCount = 3;
            do{
                try {
                    res = singleThreadExe(saleAccountByAccountNumber,imgUrlValue,templateType);
//                    if(isPopToSku != null && isPopToSku == Boolean.TRUE){
//                        res = singleThreadExe(saleAccountByAccountNumber,imgUrlValue,templateType);
//                    }else {
//                        CompletionService<ResponseJson> completionService = new ExecutorCompletionService<>(AliexpressExecutors.IMG_POOL);
//                        Future<ResponseJson> responseJsonFuture = AliexpressExecutors.submitUploadtempImageForSdk(completionService, responseJson -> {
//                            responseJson = singleThreadExe(saleAccountByAccountNumber, imgUrlValue, templateType);
//                        });
//                        res = responseJsonFuture.get();
//                    }
                    if(res.isSuccess() && StringUtils.isNotBlank(res.getMessage())){
                        end = System.currentTimeMillis();
                        log.info("刊登上传图片耗时：{}ms", (end - begin));
                        PublishRedisClusterUtils.set(key , res.getMessage(), 8, TimeUnit.MINUTES);
                        return res.getMessage();
                    }
                } catch (Exception e) {
                    log.error(imgUrlValue + " 调用平台接口上传失败" +  e.getMessage(), e);
                }
            }while(--retryCount > 0);

            //成功
            if(res.isSuccess() && StringUtils.isNotBlank(res.getMessage())){
                end = System.currentTimeMillis();
                log.info("刊登上传图片耗时：{}ms", (end - begin));
                PublishRedisClusterUtils.set(key , res.getMessage(), 8, TimeUnit.MINUTES);
                return res.getMessage();
            }else{
                if(StringUtils.equalsIgnoreCase(StatusCode.FAIL, res.getStatus())){
                    String message = imgUrlValue + " 上传图片失败: " + res.getMessage();
                    SmtImgFail smtImgFail = new SmtImgFail();
                    smtImgFail.setUrl(imgUrlValue);
                    smtImgFail.setFailInfo(message);
                    smtImgFail.setAccount(saleAccountByAccountNumber.getAccountNumber());
                    smtImgFail.setTokenError(StringUtils.contains(message, "更新token"));
                    smtImgFailService.insert(smtImgFail);
                    throw new Exception(message);
                }
            }
        }
        end = System.currentTimeMillis();
        log.info("刊登上传图片耗时：{}ms", (end - begin));
        return null;
    }

    public String uploadImageToAliexpress(SaleAccountAndBusinessResponse saleAccountByAccountNumber, byte[] content){
        // 对字节数组Base64编码
//        String fileData = Base64.getEncoder().encodeToString(content);
        File file = bytesToFile(content);
        // 使用png后缀上传不成功
        String suffix = ".jpg";
        String srcFileName = new Date().getTime() + suffix;
        String body = null;
        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.image.redefining.uploadtempimageforsdk");
            request.addFileParameter("file_data",new FileItem(file));
            request.addApiParameter("src_file_name", srcFileName);
            IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);

            body = response.getBody();
        } catch (ApiException e) {
            log.error("{} ({})", e.getErrorCode(), e.getErrorMessage());
        }
        finally {
            file.delete();
        }
        if (StringUtils.isNotEmpty(body)) {
            JSONObject obj = JSONObject.parseObject(body);
            if (obj.containsKey("aliexpress_image_redefining_uploadtempimageforsdk_response")) {
                JSONObject rsp = obj
                        .getJSONObject("aliexpress_image_redefining_uploadtempimageforsdk_response");
                if (rsp.containsKey("result")) {
                    JSONObject result = rsp.getJSONObject("result");
                    if (result.containsKey("url")) {
                        return result.getString("url");
                    }
                }
            }
        }
        return null;
    }


    public static byte[] image2Bytes(String imagePath) {
        if (StringUtils.isBlank(imagePath) || null == imagePath) {
            return null;
        }

        byte[] dataByte = getFileStream(imagePath);

        return dataByte;
    }

    /**
     * 得到文件流
     * @param url
     * @return
     */
    public static byte[] getFileStream(String url){
        int retryCount = 3;
        do{
            try {
                url = HttpUtils.dealUrl(url);
                URL httpUrl = new URL(url);
                HttpURLConnection conn = (HttpURLConnection)httpUrl.openConnection();
                conn.setRequestMethod("GET");
                conn.setConnectTimeout(5 * 1000);
                InputStream inStream = conn.getInputStream();//通过输入流获取图片数据
                byte[] btImg = readInputStream(inStream);//得到图片的二进制数据
                return btImg;
            } catch (Exception e) {
                log.error(url + " 上传失败" +  e.getMessage(), e);
            }
        }while(--retryCount > 0);
        return null;
    }

    private ResponseJson singleThreadExe(SaleAccountAndBusinessResponse saleAccountByAccountNumber,String imgUrlValue,Integer templateType) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        String imgUrl = imgUrlValue;
        //自动刊登
        if (templateType != null && TemplateTypeEnum.AUTO_PUBLISH.intCode() == templateType) {
            String accountNumber = saleAccountByAccountNumber.getAccountNumber();
            try {
                AliexpressConfig aliexpressConfig = aliexpressConfigService.selectByAccount(accountNumber);
                if(null == aliexpressConfig) {
                    throw new Exception("添加水印获取店铺配置为空" + accountNumber);
                }
                List<Integer> watermarkTemplateIds = CommonUtils.splitSortIntList(aliexpressConfig.getWatermarkTemplateIdStr(), ",");
                if(CollectionUtils.isNotEmpty(watermarkTemplateIds)) {
                    imgUrl = watermarkTemplateService.imageAddWaterMarkImg(imgUrl, watermarkTemplateIds);
                }
            }
            catch (Exception e) {
                log.error(e.getMessage());
                responseJson.setMessage(e.getMessage());
                return responseJson;
            }
        }

        int index = imgUrl.indexOf("?");
        if (index != -1) {
            imgUrl = imgUrl.substring(0, index);
        }
        // 使用png后缀上传不成功
        String suffix = ".jpg";
        String srcFileName = new Date().getTime() + suffix;

        byte[] content = image2Bytes(imgUrl);
        File file = bytesToFile(content);
        String body = null;
        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.image.redefining.uploadtempimageforsdk");
            request.addFileParameter("file_data",new FileItem(file));
            request.addApiParameter("src_file_name", srcFileName);
            IopResponse response = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);

            body = response.getBody();
        } catch (ApiException e) {
            log.error("{} ({})", e.getErrorCode(), e.getErrorMessage());
        }finally {
            file.delete();
        }

        if (StringUtils.isNotEmpty(body)) {
            JSONObject obj = JSONObject.parseObject(body);
            if (obj.containsKey("aliexpress_image_redefining_uploadtempimageforsdk_response")) {
                JSONObject rsp = obj
                        .getJSONObject("aliexpress_image_redefining_uploadtempimageforsdk_response");
                if (rsp.containsKey("result")) {
                    JSONObject result = rsp.getJSONObject("result");
                    if (result.containsKey("url")) {
                        responseJson.setStatus(StatusCode.SUCCESS);
                        responseJson.setMessage(result.getString("url"));
                    }
                }
            }
        }
        return responseJson;
    }
    public static byte[] readInputStream(InputStream inStream) throws Exception{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while( (len=inStream.read(buffer)) != -1 ){
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }

    public static String postProductImage(SaleAccountAndBusinessResponse saleAccountByAccountNumber,String str, Integer templateType, String... param2) throws Exception {
        StringBuilder postedImgStr = new StringBuilder();
        String[] imgArr = str.split(";");
        for (int i = 0; i < imgArr.length; i++) {
            String img = imgArr[i];
            String s = "";
            if(param2 !=null && param2.length > 0){
                s = param2[0];
            }
            if(StringUtils.equalsIgnoreCase(s, "1") && StringUtils.contains(img, "alicdn")){

                if((i + 1) == imgArr.length){
                    postedImgStr.append(img);
                }else{
                    postedImgStr.append(img).append(";");
                }
                continue; //不需要重复上传图片
            }
            UploadImageOpenCall call = new UploadImageOpenCall();
            String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, img, templateType, false,null);
            if (StringUtils.isNotBlank(postedImgUrl)) {
                if((i + 1) == imgArr.length){
                    postedImgStr.append(postedImgUrl);
                }else{
                    postedImgStr.append(postedImgUrl).append(";");
                }
            }
        }
        return postedImgStr.toString();
    }

    public static String postDetailImage(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String str, Integer templateType, String... param2) throws Exception{
        String postedImgStr = str;
        Matcher m = Pattern.compile("src=\"?(.*?)(\"|>|\\s+)").matcher(str);
        while (m.find()) {
            String imgUrl = m.group(1);
            String s = "";
            if(param2 !=null && param2.length > 0){
                s = param2[0];
            }
            if(StringUtils.equalsIgnoreCase(s, "1") && StringUtils.contains(imgUrl, "alicdn")){
                continue; //不需要重复上传图片
            }
            UploadImageOpenCall call = new UploadImageOpenCall();
            String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, imgUrl, templateType, false,null);
            if (StringUtils.isNotBlank(postedImgUrl)) {
                postedImgStr =Pattern.quote(postedImgStr).replaceAll(Pattern.quote(imgUrl), postedImgUrl);
                postedImgStr =postedImgStr.replaceFirst("\\\\Q","");
                if (postedImgStr.endsWith("\\E")){
                    postedImgStr = postedImgStr.substring(0,postedImgStr.length()-2);
                }
//                postedImgStr = postedImgStr.replaceAll(imgUrl, postedImgUrl);
            }
        }
        return postedImgStr;
    }

    public static String postSkuPropertyImage(SaleAccountAndBusinessResponse saleAccountByAccountNumber,String str, Integer templateType, String... param2) throws Exception {
        String postedImgStr = str;
        Matcher m = Pattern.compile("http://(.*?\")").matcher(str);
        if(null != templateType && TemplateTypeEnum.SP_PUBLISH.intCode() == templateType) {
            m = Pattern.compile("https://(.*?\")").matcher(str);
        }

        while (m.find()) {
            String imgUrl = m.group();
            imgUrl = imgUrl.substring(0, imgUrl.length() - 1);
            String s = "";
            if(param2 !=null && param2.length > 0){
                s = param2[0];
            }
            if(StringUtils.equalsIgnoreCase(s, "1") && StringUtils.contains(imgUrl, "alicdn")){
                continue; //不需要重复上传图片
            }
            UploadImageOpenCall call = new UploadImageOpenCall();
            String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, imgUrl, templateType, true,null);
            if (StringUtils.isNotBlank(postedImgUrl)) {
                postedImgStr =Pattern.quote(postedImgStr).replaceAll(Pattern.quote(imgUrl), postedImgUrl);
                postedImgStr =postedImgStr.replaceFirst("\\\\Q","");
                if (postedImgStr.endsWith("\\E")){
                    postedImgStr = postedImgStr.substring(0,postedImgStr.length()-2);
                }
//                postedImgStr = postedImgStr.replaceAll(imgUrl, postedImgUrl);
            }
        }
        return postedImgStr;
    }

    /**
     * 替换子sku 图片信息
     * @param saleAccountByAccountNumber
     * @param str
     * @param templateType
     * @return
     * @throws Exception
     */
    public static String postSkuPropertyImageReplate(SaleAccountAndBusinessResponse saleAccountByAccountNumber,String str, String replateImg, Integer templateType) throws Exception {
        String postedImgStr = str;
        Matcher m = Pattern.compile("http://(.*?\")").matcher(str);
        if(null != templateType && TemplateTypeEnum.SP_PUBLISH.intCode() == templateType) {
            m = Pattern.compile("https://(.*?\")").matcher(str);
        }

        while (m.find()) {
            String imgUrl = m.group();
            imgUrl = imgUrl.substring(0, imgUrl.length() - 1);
            UploadImageOpenCall call = new UploadImageOpenCall();
            String postedImgUrl = call.uploadImageToAliexpress(saleAccountByAccountNumber, replateImg, templateType, false,null);
            if (StringUtils.isNotBlank(postedImgUrl)) {
                postedImgStr =Pattern.quote(postedImgStr).replaceAll(Pattern.quote(imgUrl), postedImgUrl);
                postedImgStr =postedImgStr.replaceFirst("\\\\Q","");
                if (postedImgStr.endsWith("\\E")){
                    postedImgStr = postedImgStr.substring(0,postedImgStr.length()-2);
                }
//                postedImgStr = postedImgStr.replaceAll(imgUrl, postedImgUrl);
            }
        }
        return postedImgStr;
    }

    /**
     * 替换描述图片内容
     * @param detail
     * @param newImgUrls
     * @return
     */
    public static String changeDetailImg(String detail, String newImgUrls) {
        if(StringUtils.isBlank(detail)) {
            return "";
        }
        String regEx_img = "<(img|IMG)(.*?)(/>|></img>|>)";
        Pattern pattern = Pattern.compile(regEx_img);
        Matcher matcher = pattern.matcher(detail);
        while (matcher.find()) {
            String temp = matcher.group();
            detail = detail.replace(temp, "");
        }

        StringBuilder newDetail = new StringBuilder(detail);
        String[] split = StringUtils.split(newImgUrls, ";");
        for (String imgUrl : split) {
            newDetail.append("<p><img src=\"" + imgUrl + "\" width=\"800\" /></p>");
        }
        return newDetail.toString();
    }

    public static File bytesToFile(byte[] bytes) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            file = File.createTempFile("smt-" + System.currentTimeMillis(), ".jpg");
            if (!file.getParentFile().exists()){
                //文件夹不存在 生成
                file.getParentFile().mkdirs();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
           log.error("图片处理异常：" + e.getMessage(), e);
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
        }
        return file;
    }

}
