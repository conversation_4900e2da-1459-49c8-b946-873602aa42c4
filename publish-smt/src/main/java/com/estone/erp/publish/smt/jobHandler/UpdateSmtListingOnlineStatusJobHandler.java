package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.bean.ScheduleJobParamInfo;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.util.AliexpressScheduleJobParamUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月21日/10:11
 * @Description: <p>定时更新smt在线列表中的在线状态</p>
 * @Version: 1.0.0
 * @modified:
 */
@Component
@Slf4j
public class UpdateSmtListingOnlineStatusJobHandler extends AbstractJobHandler {

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;


    public UpdateSmtListingOnlineStatusJobHandler() {
        super("UpdateSmtListingOnlineStatusJobHandler");
    }


    /**
     * 根据传入的周期去计算对应的最后同步时间，然后根据对应的最后同步时间去查询对应的数据然后去修改在线状态；
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("UpdateSmtListingOnlineStatusJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        ScheduleJobParamInfo innerParam = AliexpressScheduleJobParamUtils.getScheduleJobParamInfo(param);
        List<SaleAccountAndBusinessResponse> accountList = AliexpressScheduleJobParamUtils.getSaleAccountAndBusinessResponseList(innerParam.getAccountNumbers());
        //内容变更时间间隔,默认30天
        Integer dataChangeTime = Integer.parseInt(StringUtils.isBlank(innerParam.getDuration()) ? "30" : innerParam.getDuration().trim());
        LocalDateTime fromLocalDateTime = LocalDateTime.now().plusDays((~dataChangeTime + 1));
        String date = DateUtils.format(Date.from(fromLocalDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
        XxlJobLogger.log("更新列表在线状态:间隔周期为{}，筛选时间：{}", dataChangeTime, date);
        for (SaleAccountAndBusinessResponse account : accountList) {
            //多线程去执行
            AliexpressExecutors.updateProductListingOnlineStatus(() -> doUpdateListingOnlineStatus(date, account));
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 执行更新在线状态
     *
     * @param date    筛选日期
     * @param account 店铺
     */
    private void doUpdateListingOnlineStatus(String date, SaleAccountAndBusinessResponse account) {
        log.info("doUpdateListingOnlineStatus- 店铺：{} 开始执行", account.getAccountNumber());
        int pageSize = 500;
        int pageIndex = 0;
        //统计一个店铺更新状态的数量
        int count = 0;
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        //最后同步时间
        request.setToLastSynchDate(date);
        //在线状态
        request.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
        request.setAliexpressAccountNumber(account.getAccountNumber());
        request.setPageFields(new String[]{"id", "productId"});
        request.setOrderBy("lastSyncTime");
        request.setSequence("ASC");
        //不断轮询，查询不到数据时结束
        while (true) {
            try {
                Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
                if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getContent())) {
                    break;
                }
                List<EsAliexpressProductListing> productListingList = page.getContent();
                esAliexpressProductListingService.deleteByList(productListingList);
                List<Long> productId = productListingList.stream().map(o ->o.getProductId()).collect(Collectors.toList());
                aliexpressEsExtendService.deleteByProductId(productId);
                pageIndex++;
            } catch (Exception e) {
               XxlJobLogger.log(e.getMessage());
            }

            /*for (EsAliexpressProductListing product : productListingList) {
                try {
                    //更新在线状态
                    product.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
                    esAliexpressProductListingService.updateRequest(product);
                    count++;
                } catch (Exception e) {
                    log.error("doUpdateListingOnlineStatus-更新在线状态失败 account:{},id:{}, errMsg={},e", account.getAccountNumber(), product.getId(), e.getMessage(), e);
                    XxlJobLogger.log("更新在线状态失败 account:{},id:{}, errMsg={},e", account.getAccountNumber(), product.getId(), e.getMessage(), e);
                }
            }*/


        }
        XxlJobLogger.log("店铺：{},更新状态为不在线的数量:{}", account.getAccountNumber(), count);
    }


}
