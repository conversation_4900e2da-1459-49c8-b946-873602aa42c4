package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * AliexpressEsExtend 数据清理定时任务
 * 
 * 功能说明：
 * 清理 AliexpressEsExtend 表中的冗余数据，删除在 EsAliexpressProductListing 表中不存在的 productId 记录
 * 
 * 处理策略：
 * 1. 按店铺账号分批处理，避免一次性处理大量数据导致性能问题
 * 2. 使用批量删除逻辑，提高删除效率
 * 3. 记录详细的处理进度和删除统计信息
 * 4. 添加完善的异常处理和超时控制
 * 
 * 数据规模：
 * - AliexpressEsExtend 表：约 2500万 条记录
 * - EsAliexpressProductListing 表：约 1700万 条记录
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-10
 */
@Component
@Slf4j
public class AliexpressEsExtendDataCleanJobHandler extends AbstractJobHandler {

    @Resource
    private AliexpressConfigService aliexpressConfigService;
    
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;

    /**
     * 每批处理的最大记录数，避免单次删除过多数据
     */
    private static final int BATCH_SIZE = 1000;
    
    /**
     * 最大处理时间（毫秒），超过此时间将停止处理
     */
    private static final long MAX_EXECUTION_TIME = 2 * 60 * 60 * 1000L; // 2小时

    public AliexpressEsExtendDataCleanJobHandler() {
        super("AliexpressEsExtendDataCleanJobHandler");
    }

    @Override
    @XxlJob("AliexpressEsExtendDataCleanJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        long startTime = System.currentTimeMillis();
        
        // 记录任务开始信息
        XxlJobLogger.log("=== AliexpressEsExtend 数据清理任务开始执行 ===");
        // 统计信息
        AtomicInteger processedAccountCount = new AtomicInteger(0);
        AtomicLong totalDeletedCount = new AtomicLong(0);
        AtomicInteger errorAccountCount = new AtomicInteger(0);
        
        try {
            // 获取需要处理的店铺账号列表
            List<String> accountList = getAccountListFromParam(param);
            if (CollectionUtils.isEmpty(accountList)) {
                XxlJobLogger.log("未找到任何店铺账号，任务结束");
                return ReturnT.SUCCESS;
            }
            XxlJobLogger.log("共找到 " + accountList.size() + " 个店铺账号需要处理");
            
            // 按账号逐个处理
            for (String account : accountList) {
                // 检查执行时间是否超限
              /*  if (System.currentTimeMillis() - startTime > MAX_EXECUTION_TIME) {
                    XxlJobLogger.log("执行时间超过限制，停止处理。已处理账号数: " + processedAccountCount.get());
                    break;
                }*/
                
                try {
                    // 处理单个账号的数据清理
                    long deletedCount =  aliexpressEsExtendService.deleteOrphanedRecordsByAccountWithPaging(account, BATCH_SIZE);
                    totalDeletedCount.addAndGet(deletedCount);
                    processedAccountCount.incrementAndGet();
                    
                    XxlJobLogger.log(String.format("账号 [%s] 处理完成，删除记录数: %d", account, deletedCount));
                    
                } catch (Exception e) {
                    errorAccountCount.incrementAndGet();
                    log.error("处理账号 [{}] 时发生异常: {}", account, e.getMessage(), e);
                    XxlJobLogger.log(String.format("账号 [%s] 处理失败: %s", account, e.getMessage()));
                }
            }
            
        } catch (Exception e) {
            log.error("数据清理任务执行异常: {}", e.getMessage(), e);
            XxlJobLogger.log("任务执行异常: " + e.getMessage());
            return ReturnT.FAIL;
        }
        
        // 记录任务完成信息
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        XxlJobLogger.log("=== AliexpressEsExtend 数据清理任务执行完成 ===");
        XxlJobLogger.log("任务结束时间: " + new java.util.Date());
        XxlJobLogger.log("总执行时间: " + (executionTime / 1000) + " 秒");
        XxlJobLogger.log("处理账号总数: " + processedAccountCount.get());
        XxlJobLogger.log("删除记录总数: " + totalDeletedCount.get());
        XxlJobLogger.log("异常账号数量: " + errorAccountCount.get());
        
        return ReturnT.SUCCESS;
    }

    /**
     * 根据参数获取需要处理的店铺账号列表
     *
     * @param param 任务参数，支持账号列表（逗号分隔）或为空
     * @return 店铺账号列表
     */
    private List<String> getAccountListFromParam(String param) {
        // 如果参数不为空，解析参数中的账号列表
        if (StringUtils.isNotBlank(param)) {
            XxlJobLogger.log("使用参数指定的账号列表: " + param);
            return parseAccountListFromParam(param);
        }
        // 参数为空时，获取所有可用账号
        XxlJobLogger.log("参数为空，获取所有可用的店铺账号");
        return getAllAccountList();
    }

    /**
     * 解析参数中的账号列表
     *
     * @param param 参数字符串，格式：account1,account2,account3 或 account1
     * @return 解析后的账号列表
     */
    private List<String> parseAccountListFromParam(String param) {
        List<String> accountList = new ArrayList<>();
        try {
            // 按逗号分割账号
            String[] accounts = param.split(",");
            for (String account : accounts) {
                String trimmedAccount = account.trim();
                if (StringUtils.isNotBlank(trimmedAccount)) {
                    accountList.add(trimmedAccount);
                }
            }
            if (CollectionUtils.isEmpty(accountList)) {
                XxlJobLogger.log("参数中未找到有效的账号");
            }
            return accountList;
        } catch (Exception e) {
            log.error("解析参数账号列表时发生异常: {}", e.getMessage(), e);
            XxlJobLogger.log("解析参数失败: " + e.getMessage() + "，将获取所有可用账号");
        }
        return accountList;
    }


    /**
     * 获取所有可用的店铺账号列表
     *
     * @return 店铺账号列表
     */
    private List<String> getAllAccountList() {
        try {
            // 创建查询条件，只查询账号字段
            AliexpressConfigExample example = new AliexpressConfigExample();
            example.setColumns("account"); // 只查询账号字段，提高查询效率
            List<AliexpressConfig> configList = aliexpressConfigService.selectCustomColumnByExample(example);
            if (CollectionUtils.isEmpty(configList)) {
                XxlJobLogger.log("未找到可用的店铺账号配置");
                return null;
            }
            
            // 提取账号列表
            List<String> accountList = configList.stream()
                    .map(AliexpressConfig::getAccount)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());
            
            XxlJobLogger.log("成功获取到 " + accountList.size() + " 个有效账号");
            return accountList;
            
        } catch (Exception e) {
            log.error("获取账号列表时发生异常: {}", e.getMessage(), e);
            XxlJobLogger.log("获取账号列表失败: " + e.getMessage());
            throw new RuntimeException("获取账号列表失败", e);
        }
    }
}
