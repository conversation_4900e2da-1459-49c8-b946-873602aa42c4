package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.job.executor.core.config.XxlJobConfig;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressOfflineProductListing;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressOfflineProductListingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * @Description: 下架列表数据迁移任务
 * <AUTHOR>
 * @Date 2025/6/6 19:00
 */
@Slf4j
@Component
@Deprecated
public class AliexpressOfflineProductJobHandler extends AbstractJobHandler {

    @Resource
    private EsAliexpressProductListingService productListingService;

    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;

    @Resource
    private AliexpressOfflineProductListingService aliexpressOfflineProductListingService;

    public AliexpressOfflineProductJobHandler(XxlJobConfig xxlJobConfig) {
        super(AliexpressOfflineProductJobHandler.class.getName());
    }

    @Data
    public static class InnerParam {

        /**
         * 账号
         */
        private List<String> accountList;

        /**
         * 商品id
         */
        private List<Long> productIdList;

        /**
         * 天数
         */
        private Integer days = 31;

        /**
         * 具体时间日期
         */
        private String dateStr;
    }

    /**
     * 自动迁移下架产品数据
     * 1. 在线列表链接同步时间 > 31天（不含当天）
     * 2. 且在线状态为"不在线"
     */
    @XxlJob("AliexpressOfflineProductJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        AliexpressOfflineProductJobHandler.InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, AliexpressOfflineProductJobHandler.InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new AliexpressOfflineProductJobHandler.InnerParam();
        }
        List<String> accountList = innerParam.getAccountList();
        List<Long> productIdList = innerParam.getProductIdList();
        Integer days = innerParam.getDays();
        String dateStr = innerParam.getDateStr();
        XxlJobLogger.log("AliexpressOfflineProductJobHandler start");
        try {
            XxlJobLogger.log("AliexpressOfflineProductJobHandler 在线列表链接同步时间 > {}天（不含当天）", days);
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(days);
            if (StringUtils.isNotBlank(dateStr)) {
                localDateTime = LocalDateTime.parse(dateStr);
            }

            String date = DateUtils.format(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()), DateUtils.STANDARD_DATE_PATTERN);
            if (CollectionUtils.isEmpty(accountList)) {
                List<SaleAccountAndBusinessResponse> aliexpressAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
                accountList = aliexpressAccounts.stream().map(SaleAccountAndBusinessResponse::getAccountNumber).collect(Collectors.toList());
            }

            // 计算耗时
            Long startTime = System.currentTimeMillis();
            for (String accountNumber : accountList) {
                // 滚动查询列表数据
                EsAliexpressProductListingRequest esAliexpressProductListingRequest = new EsAliexpressProductListingRequest();
                esAliexpressProductListingRequest.setOnlineStatus(OnlineStatusEnum.NOT_ONLINE.getCode());
                esAliexpressProductListingRequest.setToLastSynchDate(date);
                esAliexpressProductListingRequest.setAliexpressAccountNumberList(List.of(accountNumber));
                Optional.ofNullable(productIdList).ifPresent(esAliexpressProductListingRequest::setProductIdList);
                esAliexpressProductListingRequest.setOrderBy("lastSyncTime");
                esAliexpressProductListingRequest.setSequence("DESC");
                productListingService.scrollQueryExecutorTask(esAliexpressProductListingRequest, aliexpressProductListings -> {
                    if (CollectionUtils.isEmpty(aliexpressProductListings)) {
                        return;
                    }
                    // 通过店铺+productid+货号去判断是否存在，如果存在则不做处理
                    List<String> alixpressAccountNumberList = aliexpressProductListings.stream().map(EsAliexpressProductListing::getAliexpressAccountNumber).collect(Collectors.toList());
                    List<Long> productNumberList = aliexpressProductListings.stream().map(EsAliexpressProductListing::getProductId).collect(Collectors.toList());
                    LambdaQueryWrapper<AliexpressOfflineProductListing> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.in(AliexpressOfflineProductListing::getAliexpressAccountNumber, alixpressAccountNumberList).in(AliexpressOfflineProductListing::getProductId, productNumberList);
                    List<AliexpressOfflineProductListing> aliexpressOfflineProductListingList = aliexpressOfflineProductListingService.list(lambdaQueryWrapper);
                    Map<String, List<AliexpressOfflineProductListing>> aliexpressOfflineProductListingListGroupMap = aliexpressOfflineProductListingList.stream().collect(Collectors.groupingBy(esAliexpressProductListing -> esAliexpressProductListing.getAliexpressAccountNumber() + "_" + esAliexpressProductListing.getProductId() + "_" + esAliexpressProductListing.getArticleNumber()));

                    // 数据转换
                    BiFunction<List<EsAliexpressProductListing>, Map<String, AliexpressEsExtend>, List<AliexpressOfflineProductListing>> aliFunction = (list, map) -> {
                        List<AliexpressOfflineProductListing> offlineProductListings = new ArrayList<>();
                        for (EsAliexpressProductListing esAliexpressProductListing : list) {
                            List<AliexpressOfflineProductListing> productListings = aliexpressOfflineProductListingListGroupMap.get(esAliexpressProductListing.getAliexpressAccountNumber() + "_" + esAliexpressProductListing.getProductId() + "_" + esAliexpressProductListing.getArticleNumber());
                            if (CollectionUtils.isNotEmpty(productListings)) {
                                continue;
                            }
                            AliexpressEsExtend aliexpressEsExtend = map.get(esAliexpressProductListing.getId());

                            // 数据转换
                            AliexpressOfflineProductListing aliexpressOfflineProductListing = mapToOfflineProduct(esAliexpressProductListing, aliexpressEsExtend);
                            offlineProductListings.add(aliexpressOfflineProductListing);
                        }
                        return offlineProductListings;
                    };

                Map<String, AliexpressEsExtend> aliexpressEsExtendMap = aliexpressEsExtendService.productInfo(aliexpressProductListings, true, new HashMap<>());
                List<AliexpressOfflineProductListing> offlineProductListings = aliFunction.apply(aliexpressProductListings, aliexpressEsExtendMap);

                    // 去重，店铺+productid唯一
                    List<AliexpressOfflineProductListing> aliexpressOfflineProductListings = offlineProductListings.stream().collect(Collectors.groupingBy(t -> t.getAliexpressAccountNumber() + "-" + t.getProductId() + "-" + t.getArticleNumber())).values().stream().map(item -> item.get(0)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(aliexpressOfflineProductListings)) {
                        aliexpressOfflineProductListingService.saveBatch(aliexpressOfflineProductListings);
                    }

                    // 删除es数据
                    productListingService.deleteByList(aliexpressProductListings);

                });

            }
            log.info("下架产品数据迁移任务执行耗时：{}", System.currentTimeMillis() - startTime);
            XxlJobLogger.log("下架产品数据迁移任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log("下架产品数据迁移任务执行失败：{}", e.getMessage());
            log.error("下架产品数据迁移任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    public AliexpressOfflineProductListing mapToOfflineProduct(EsAliexpressProductListing product, AliexpressEsExtend aliexpressEsExtend) {
        AliexpressOfflineProductListing offlineProduct = new AliexpressOfflineProductListing();
        offlineProduct.setId(null); // ID 置空，保持与原逻辑一致

        // 1. 组织与角色
        offlineProduct.setAliexpressAccountNumber(product.getAliexpressAccountNumber()); // account (3)
        if (aliexpressEsExtend != null) {
            offlineProduct.setGroupNames(aliexpressEsExtend.getGroupNames()); // shop_group (14)
            offlineProduct.setFreightTemplateName(aliexpressEsExtend.getFreightTemplateName()); // freight_group (15, 修正拼写)
            offlineProduct.setTagCodes(aliexpressEsExtend.getTagCodes());
            offlineProduct.setDeclareCnName(POIUtils.transferObj2Str(aliexpressEsExtend.getDeclarecnname()));
        }

        // 2. 产品基本信息
        offlineProduct.setProductId(product.getProductId()); // product_id (4)
        offlineProduct.setSubject(POIUtils.transferStr2Str(product.getSubject())); // title (5)
        Map<String, String> brand = product.getBrand();
        if (brand != null) {
            offlineProduct.setBrand(JSON.toJSONString(brand)); // brand (42)
        }
        offlineProduct.setSpu(product.getSpu());
        offlineProduct.setPlatSkuId(product.getPlatSkuId());
        offlineProduct.setArticleNumber(product.getArticleNumber()); // article_number (7)
        offlineProduct.setSkuCode(product.getSkuCode()); // sku (8)
        offlineProduct.setSkuId(product.getSkuId()); // skuId (51)
        offlineProduct.setTagNames(product.getTagNames()); // item_label (22)
        offlineProduct.setCategoryId(product.getCategoryId()); // category_id (23)
        offlineProduct.setManufactureId(product.getManufactureId());
        offlineProduct.setDataSourceType(product.getDataSourceType());
        offlineProduct.setSkuStatus(product.getSkuStatus());
        offlineProduct.setComposeStatus(product.getComposeStatus());
        offlineProduct.setDeliveryAddress(product.getDeliveryAddress());
        offlineProduct.setCnProvince(product.getCnProvince());
        offlineProduct.setForbidChannel(product.getForbidChannel()); // forbid (21)
        offlineProduct.setSpecialGoodsCode(product.getSpecialGoodsCode()); // forbid (21)
        offlineProduct.setUnsalableTag(product.getUnsalableTag());
        offlineProduct.setItemShow(product.getItemShow());
        offlineProduct.setImageUrls(product.getImageUrls());

        AliexpressCategory aliexpressCategory = aliexpressEsExtend.getAliexpressCategory();
        if (aliexpressEsExtend != null && aliexpressCategory != null) {
            offlineProduct.setCategoryName(aliexpressCategory.getCategoryZhName());
            offlineProduct.setProCategoryCnName(aliexpressCategory.getCategoryZhName());
            offlineProduct.setProCategoryId(aliexpressCategory.getCategoryId().toString());
            // 递归查询aliexpressCategory中的upCategory，然后将类目fullCnName以"-"的方式拼接起来、
            String fullCategoryName = buildCategoryFullPath(aliexpressCategory);
            // 删除最后的一个字符
            fullCategoryName = fullCategoryName.substring(0, fullCategoryName.length() - 1);
            offlineProduct.setCategoryFullName(fullCategoryName);
        }

        // 3. 价格与成本
        offlineProduct.setSkuPrice(product.getSkuPrice()); // price (9)
        if (aliexpressEsExtend != null) {
            offlineProduct.setGrossProfit(aliexpressEsExtend.getGrossProfit()); // gross_profit (12)
            offlineProduct.setGrossProfitMargin(aliexpressEsExtend.getGrossProfitMargin()); // gross_profit_rate (13)
            double skuPurchasePrice = aliexpressEsExtend.getSkuPurchasePrice() != null ? aliexpressEsExtend.getSkuPurchasePrice() : 0.0;
            offlineProduct.setSaleCost(BigDecimal.valueOf(skuPurchasePrice).setScale(3, RoundingMode.HALF_UP).doubleValue()); // sku_cost (17)
            offlineProduct.setPmPrice(aliexpressEsExtend.getPmPrice()); // packing_cost (19)
            offlineProduct.setShippingCost(aliexpressEsExtend.getShippingCost()); // shippingCost (54)
            offlineProduct.setIsOffAndOn(aliexpressEsExtend.getIsOffandOn());
        }

        // 4. 库存与状态
        offlineProduct.setIpmSkuStock(product.getIpmSkuStock()); // stock (10)
        offlineProduct.setProductStatusType(product.getProductStatusType()); // item_status (11)
        offlineProduct.setOnlineStatus(OnlineStatusEnum.getNameByCode(product.getOnlineStatus())); // onlineStatus (53)
        String articleNumber = product.getArticleNumber();
        if (articleNumber != null) {
            offlineProduct.setSystemStock(SkuStockUtils.getSkuStockToEbay(articleNumber)); // system_stock (52) 👤
        }

        // 5. 重量与物流
        if (aliexpressEsExtend != null) {
            offlineProduct.setExtendedField5(String.valueOf(aliexpressEsExtend.getSkuTotalWeight())); // sku_weight (16)
            offlineProduct.setPmWeight(aliexpressEsExtend.getPmWeight());
            offlineProduct.setStandardWeight(aliexpressEsExtend.getStandardWeight()); // standardWeight (59)
            offlineProduct.setSkuTotalWeight(aliexpressEsExtend.getSkuTotalWeight());
        }
        offlineProduct.setGrossWeight(product.getGrossWeight()); // gross_weight (26)

        // 6. 禁售相关
        if (aliexpressEsExtend != null) {
            offlineProduct.setCurrencyCode(aliexpressEsExtend.getCurrencyCode());
            offlineProduct.setFreightTemplateId(aliexpressEsExtend.getFreightTemplateId());
            offlineProduct.setGroupId(aliexpressEsExtend.getGroupId());
            offlineProduct.setGroupIds(aliexpressEsExtend.getGroupIds());
            offlineProduct.setIsCnProvinceCategory(aliexpressEsExtend.getIsCnProvinceCategory());
            offlineProduct.setIsHasVideo(aliexpressEsExtend.getIsHasVideo());
            offlineProduct.setIsOverseas(aliexpressEsExtend.getIsOverseas());
            offlineProduct.setSkuDisplayImg(aliexpressEsExtend.getSkuDisplayImg());

            Double skuPurchasePrice = aliexpressEsExtend.getSkuPurchasePrice() == null ? 0.0 : aliexpressEsExtend.getSkuPurchasePrice();
            offlineProduct.setSaleCost(BigDecimal.valueOf(skuPurchasePrice).setScale(3, RoundingMode.HALF_UP).doubleValue());
        }
        List<String> infringementTypeNames = product.getInfringementTypeNames();
        offlineProduct.setInfringementTypeNames(CollectionUtils.isEmpty(infringementTypeNames) ? "" : JSON.toJSONString(infringementTypeNames)); // infringementTypeNames (43)
        List<String> infringementObjs = product.getInfringementObjs();
        offlineProduct.setInfringementObjs(CollectionUtils.isEmpty(infringementObjs) ? "" : JSON.toJSONString(infringementObjs)); // infringementObjs (44)
        List<String> prohibitionSites = product.getProhibitionSites();
        offlineProduct.setProhibitionSites(CollectionUtils.isEmpty(prohibitionSites) ? "" : JSON.toJSONString(prohibitionSites)); // prohibitionSites (45)

        // 7. 销售与曝光数据
        offlineProduct.setOrder24hCount(product.getOrder_24H_count()); // order_24H_count (27)
        offlineProduct.setOrderLast7dCount(product.getOrder_last_7d_count()); // order_last_7d_count (28)
        offlineProduct.setOrderLast14dCount(product.getOrder_last_14d_count()); // order_last_14d_count (29)
        offlineProduct.setOrderLast30dCount(product.getOrder_last_30d_count()); // order_last_30d_count (30)
        offlineProduct.setOrderLast60dCount(product.getOrder_last_60d_count()); // order_last_60d_count (31)
        offlineProduct.setOrderLast180dCountNew(product.getOrder_last_180d_count_new()); // order_last_180d_count_new
        offlineProduct.setOrderNumTotal(product.getOrder_num_total()); // order_num_total (32)
        offlineProduct.setOrderDaysWithin30d(product.getOrder_days_within_30d()); // order_days_within_30d (33)
        offlineProduct.setOrderDaysWithin60d(product.getOrder_days_within_60d()); // order_days_within_60d (34)
        Double orderDaysWithin30dRate = product.getOrder_days_within_30d_rate() != null ? product.getOrder_days_within_30d_rate() : 0.0;
        offlineProduct.setOrderDaysWithin30dRate(orderDaysWithin30dRate); // order_days_within_30d_rate (35)
        Double orderDaysWithin60dRate = product.getOrder_days_within_60d_rate() != null ? product.getOrder_days_within_60d_rate() : 0.0;
        offlineProduct.setOrderDaysWithin60dRate(orderDaysWithin60dRate); // order_days_within_60d_rate (36)
        Date viewUpdateDate = product.getViewUpdateDate();
        if (viewUpdateDate != null) {
            offlineProduct.setExposure7dCount(product.getExposure_7d_count()); // exposure_7d_count (55)
            offlineProduct.setExposure14dCount(product.getExposure_14d_count()); // exposure_14d_count (57)
            offlineProduct.setExposure30dCount(product.getExposure_30d_count()); // exposure_30d_count (37)
            offlineProduct.setView7dCount(product.getView_7d_count()); // view_7d_count (56)
            offlineProduct.setView14dCount(product.getView_14d_count()); // view_14d_count (58)
            offlineProduct.setView30dCount(product.getView_30d_count()); // view_30d_count (38)

            // 曝光更新时间
            Date viewUpdateDate1 = product.getViewUpdateDate();
            offlineProduct.setViewUpdateDate(dateToLocalDateTime(viewUpdateDate1)); // viewUpdateDate

        }

        // 8. 时间戳
        Date gmtCreate = product.getGmtCreate();
        if (gmtCreate != null) {
            offlineProduct.setGmtCreate(dateToLocalDateTime(gmtCreate)); // gmtCreate (39)
        }
        Date wsOfflineDate = product.getWsOfflineDate();
        if (wsOfflineDate != null) {
            offlineProduct.setWsOfflineDate(dateToLocalDateTime(wsOfflineDate)); // wsOfflineDate (40)
        }
        Date lastSyncTime = product.getLastSyncTime();
        if (lastSyncTime != null) {
            offlineProduct.setLastSyncTime(dateToLocalDateTime(lastSyncTime)); // lastSyncTime (41)
        }
        Date infringementCheckTime = product.getInfringementCheckTime();
        if (infringementCheckTime != null) {
            offlineProduct.setInfringementCheckTime(dateToLocalDateTime(infringementCheckTime));
        }


        // 9. 促销与折扣
        Integer promotion = product.getPromotion();
        if (promotion != null) {
            offlineProduct.setPromotion(promotion); // promotion (47)
        }
        Boolean newState = product.getNewState();
        if (newState != null) {
            offlineProduct.setNewState(newState); // newState (48)
        }
        offlineProduct.setBulkOrder(product.getBulkOrder()); // bulkOrder (49)
        offlineProduct.setBulkDiscount(product.getBulkDiscount()); // bulkDiscount (50)
        if (aliexpressEsExtend != null) {
            offlineProduct.setStatusName(aliexpressEsExtend.getStatusName()); // statusName (61)
            offlineProduct.setWholeStationDiscount(aliexpressEsExtend.getWholeStationDiscount()); // wholeStationDiscount (62)
            offlineProduct.setStoreClubDiscountRate(aliexpressEsExtend.getStoreClubDiscountRate()); // storeClubDiscountRate (63)
            offlineProduct.setSingleDiscountName(aliexpressEsExtend.getSingleDisCountName());
        }

        // 10. JIT 销量
        offlineProduct.setJitOrderNum24h(product.getJit_order_num_24h()); // jit_order_num_24h (70)
        offlineProduct.setJitOrderNum7d(product.getJit_order_num_7d()); // jit_order_num_7d (71)
        offlineProduct.setJitOrderNum14d(product.getJit_order_num_14d()); // jit_order_num_14d (72)
        offlineProduct.setJitOrderNum30d(product.getJit_order_num_30d()); // jit_order_num_30d (73)
        offlineProduct.setJitOrderNum60d(product.getJit_order_num_60d()); // jit_order_num_60d (74)
        offlineProduct.setJitOrderNum180d(product.getJit_order_num_180d()); // jit_order_num_180d (75)
        offlineProduct.setJitOrderNumTotal(product.getJit_order_num_total()); // jit_order_num_total (76)

        offlineProduct.setPublishRole(product.getPublishRole());
        offlineProduct.setCategoryLabel(product.getCategoryLabel());
        offlineProduct.setCategoryLabel(product.getCategoryLabel());
        offlineProduct.setIsHasQualification(product.getIsHasQualification());
        if (CollectionUtils.isNotEmpty(product.getInfringementWordInfos())) {
            offlineProduct.setInfringementWordInfos(JSON.toJSONString(product.getInfringementWordInfos()));
        }
        if (CollectionUtils.isNotEmpty(product.getInfringementWordList())) {
            offlineProduct.setInfringementWordList(JSON.toJSONString(product.getInfringementWordList()));
        }
        // 11. 其他
        offlineProduct.setHalfCountryExitLabel(product.getHalfCountryExitLabel()); // halfCountryExitLabel (77)
        offlineProduct.setCreateTime(LocalDateTime.now()); // 非映射字段，记录创建时间
        return offlineProduct;
    }

    private LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * 递归查询类目路径
     *
     * @param category
     * @return
     */
    public String buildCategoryFullPath(AliexpressCategory category) {
        StringBuilder path = new StringBuilder();
        buildPathRecursively(category, path);
        return path.toString();
    }

    private void buildPathRecursively(AliexpressCategory category, StringBuilder path) {
        if (category == null) {
            return;
        }
        // 加到前面
        path.insert(0, category.getCategoryZhName() + "-");
        buildPathRecursively(category.getUpCategory(), path);
    }
}