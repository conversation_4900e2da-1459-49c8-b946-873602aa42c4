package com.estone.erp.publish.smt.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.smt.bean.*;
import com.estone.erp.publish.smt.bean.excel.DiscountProExcel;
import com.estone.erp.publish.smt.bean.excel.PopStockExcel;
import com.estone.erp.publish.smt.bean.excel.UpdateStockExcel;
import com.estone.erp.publish.smt.call.direct.wholesale.bean.DiscountParam;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.AliexpressProductListingMsgDto;
import com.estone.erp.publish.smt.model.dto.AliexpressUpdateImageTitleRequest;
import com.estone.erp.publish.smt.model.dto.EsAliexpressProductListingResponse;
import com.estone.erp.publish.smt.mq.bean.HolidayStockMqBean;
import com.estone.erp.publish.smt.mq.publish.bean.PublishBean;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> aliexpress_es_extend
 * 2021-11-23 18:46:22
 */
public interface AliexpressEsExtendService {
    int countByExample(AliexpressEsExtendExample example);

    CQueryResult<AliexpressEsExtend> search(CQuery<AliexpressEsExtendCriteria> cquery);

    List<AliexpressEsExtend> selectByExample(AliexpressEsExtendExample example);

    AliexpressEsExtend selectByPrimaryKey(Long id);

    AliexpressEsExtend selectByAccountandProductId(String account, Long productId);

    int insert(AliexpressEsExtend record);

    int updateByPrimaryKeySelective(AliexpressEsExtend record);

    int updateOFFandOnByProductId(AliexpressEsExtend record);

    int updateByExampleSelective(AliexpressEsExtend record, AliexpressEsExtendExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int deleteByProductId(List<Long> productId);

    //通过主键id同步产品
    void syncProductByIds(List<String> ids);

    //同步产品
    String synchProduct(String response, EsAliexpressProductListing esAliexpressProductListing, SaleAccountAndBusinessResponse saleAccountByAccountNumber, String... field);

    //定时同步产品信息变更 刷新到ES
    void syncProductInfo(List<String> skuList, List<String> accountNumberList) throws Exception;

    //定时同步3天未同步的在线产品
    void timingSynchAccountForDelete(SaleAccountAndBusinessResponse aliexpressAccount, String toDate);

    //列表查询
    EsAliexpressProductListingResponse list(CQuery<EsAliexpressProductListingRequest> cquery) throws Exception;

    @Deprecated
    Map<String, AliexpressEsExtend> productInfo(List<EsAliexpressProductListing> esProductList, boolean isSeleteProduct, Map<String, AliexpressEsExtend> esExtendMap);

    //优化版在线列表产品扩展
    Map<String, AliexpressEsExtend> productInfoOptimized(List<EsAliexpressProductListing> esProductList, boolean isSeleteProduct, Map<String, AliexpressEsExtend> esExtendMap);

    //查询重复产品
    List<Long> repeatSkuCode(List<Long> productIdList);

    //修改价格
    String batchEditProductPriceNew(List<String> ids, Double expectedMargin, Double adjustmentRange,
            String sfmCode, String customCurrencyRate, Double expectPriceVal);
    //修改库存
    String batchEditProductStock(List<String> ids, String stock, boolean isTimingUpdateStock, boolean isCheck, boolean halfLableCheck, HolidayStockMqBean holidayStockMqBean, boolean isQueue);

    //修改图片
    void updateImg(UpdateImgBean updateImgBean, String operator);

    //修改子sku图片
    void updateSonSkuImg(List<EsAliexpressProductListing> esProductList, SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, String operator);

    //修改标题描述
    String batchUpdateSubjectAndDetail(String idStr, Boolean isUpdateSubject, Boolean isUpdateDetail);

    //替换标题描述
    String batchReplaceSubjectAndDetail(String idStr, String oldWord, String newWord, Boolean isReplaceSubject, Boolean isReplaceDetail);

    //修改重量
    void updateGrossWeight(List<UpdatePriceEntity> returnResultList, Map<String, String> productWeightMap, String user, boolean isWait);

    //转模板
    void transTemp(Long productId, String operator);

    //es产品编辑
    AliexpressTemplate esTransTemp(Long productId) throws Exception;

    //es产品转tg模板
    AliexpressTgTemplate esTransTgTemp(Long productId, String account, Double grossMargin) throws Exception;

    void transTemp(EsAliexpressProductListing esProduct, AliexpressEsExtend esExtend);

    //批量设置分组
    String batchSetGroup(List<String> ids, String groupId, boolean retain);

    //批量设置运费模板
    String batchSetEditsimpleproductfiled(List<String> ids, String fiedName, String fiedValue, String userName, String... ruleName);

    //批量设置运费模板,产品分组，服务模板
    void batchSetEditsimpleproductfiled(EsAliexpressProductListingRequest esRequest, String freightTemplateIdStr, String groupIdStr, String promiseTemplateIdStr, String userName);

    //批量上下架
    void batchOnlineOrOfflineProduct(List<String> ids, int manipulateType, Boolean isCheck, String optimized);

    //是否需要 校验 产品单品状态为清仓，甩卖，且库存+在途-待发＞0，且SKU不存在SMT禁售 纯系统操作不用校验，其他需要校验
    void onlineOrOfflineProduct(String accountNumber, List<Long> productIds, int manipulateType, String currentUser, Boolean isCheck, SaleAccountAndBusinessResponse saleAccountByAccountNumber, String... optimized);

    //修改侵权产品
    ResponseJson updateTortProduct(SaleAccountAndBusinessResponse saleAccountByAccountNumber, AliexpressProduct product, String updateImg, String updateText);

    //车型库修改
    void updateProductCarType(AliexpressProduct product,String json, String userName);

    //63国改价
    List<UpdatePriceEntity> updateProductCountryPriceNew(Map<String, List<AliexpressEditProductBean>> excelDataList, String userName, Long groupId, boolean isCheckAuth, String... isMysql);

    //63国价格信息
    List<AliexpressProductStatePriceEntity> getStateResultList(EsAliexpressProductListingRequest request);

    //63国调价 请求到页面
    ResponseJson product28Calc(List<EsAliexpressProductListing> esAliexpressProductListing, Map<String, AliexpressEsExtend> esExtendMap, String logisticsName, String createBy, Double gross, List<String> updateCountryCodeList, Boolean isCalcPrice) throws Exception;

    //修改店铺尺寸
    ResponseJson updateProductSize(ProductSizeBean productSizeBean, SaleAccountAndBusinessResponse saleAccountAndBusinessResponse);

    //excel 改重量
    List<UpdatePriceEntity> excelUpdateGrossWeight(String[] headers, MultipartFile file, String userName);

    //excel 删除产品
    List<UpdatePriceEntity> excelDeleteProduct(String[] headers, MultipartFile file, String userName);

    //excel 改价格
    List<UpdatePriceEntity> updateProductPrice(String[] headers, MultipartFile file, String userName);

    //excel 改运费模板
    List<UpdatePriceEntity> updateFreightTemplateIdForExcle(String[] headers, MultipartFile file, String userName);

    //excel 改产品分组
    List<UpdatePriceEntity> updateGroupIdForExcle(String[] headers, MultipartFile file, String userName);

    //excel 获取库存
    List<PopStockExcel> popStockForExcle(String[] headers, MultipartFile file, String userName);

    //excel 修改pop库存
    List<UpdateStockExcel> updatePopStockForExcle(String[] headers, MultipartFile file, String userName);

    //产品系统获取销售属性
    AliexpressProduct querySkuSaleAttr(String articleNumber, Integer categoryId);

    //新增list
    List<AliexpressDataView> countListing(String beginTime, String endTime);

    //统计listing总数
    List<AliexpressDataView> selectTotalListing(AliexpressDataViewCriteria query);

    /**
     * 分页删除指定账号下的孤立记录（优化版本）
     * 采用分页查询和处理模式，避免一次性加载大量数据到内存
     *
     * @param account 店铺账号
     * @param pageSize 每页处理的记录数
     * @return 删除的记录数
     */
    int deleteOrphanedRecordsByAccountWithPaging(String account, int pageSize);

    //获取最新上架的产品的属性
    String queryProductAttr(List<String> skuList, Integer categoryId);

    //批量上传视频
    void batchUploadVideo(List<AliexpressEsExtend> esExtendList);

    int batchUpdate(List<AliexpressEsExtend> extendList);

    /**
     * 清空区域调价
     * @param bean
     */
    void clearAreaModifyPrice(Aliexpress28CalcBean bean);

    /**
     * 产品搬家
     * @param publishBean
     * @return
     */
    ResponseJson productMoving(PublishBean publishBean);

    /**
     * 修改产品
     * @param aliexpressTemplate
     * @return
     */
    ResponseJson updateProduct(AliexpressTemplate aliexpressTemplate);

    /**
     * 上传修改标题
     * @param file
     * @return
     */
    ApiResult<?> uploadUpdateSubject(MultipartFile file);

    /**
     * 修改销售方式
     * @param saleAccountAndBusinessResponse
     * @param productId
     * @param productUnit
     * @param packageType
     * @param lotNum
     */
    void updateSaleMode(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, Integer productUnit, Boolean packageType, Integer lotNum, String userName);

    /**
     * 修改资质
     * @param saleAccountAndBusinessResponse
     * @param productId
     * @param json
     * @param userName
     * @param isSpecial  true 标识只上传链接没有资质的图片，如果本身链接有就不处理，
     */
    void updateQualification(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String json, String userName, boolean isCheck, boolean isSpecial);

    /**
     * 修改产品类目id
     * @param saleAccountAndBusinessResponse
     * @param productId
     * @param categoryId
     */
    void updateProductCategoryId(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, Integer categoryId, String json, String userName);

    /**
     * 删除产品
     * @param saleAccountAndBusinessResponse
     * @param productId
     * @param userName
     */
    ResponseJson deleteProduct(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Long productId, String userName, String ruleName);

    /**
     * 获取63国价格
     * @param aliexpress28PriceBean
     * @return
     */
    Map<String,Double> get28PriceDetail(Aliexpress28PriceBean aliexpress28PriceBean);

    /**
     * 删除店铺数据
     * @param account
     * @param userName
     */
    void deleteAccountData(String account, String userName);

    /**
     * 根据产品id获取折扣率
     *
     * @param accountNumber
     * @param productIdList
     * @return
     */
    Map<String, List<String>> getDiscountByProductId(String accountNumber, List<String> productIdList);

    /**
     * 根据账号获取产品id
     *
     * @param accountNumber
     * @return
     */
    List<Long> getProductIdByAccount(String accountNumber);

    /**
     * 添加EPR费用
     *
     * @param esAliexpressProductListing
     * @param map
     */
    void increaseEprFee(List<EsAliexpressProductListing> esAliexpressProductListing, Map<String, List<BatchPriceCalculatorResponse>> map);

    /**
     * 同步店铺品牌
     *
     * @param accountNumber
     * @param categoryIdList
     */
    void syncAccountBrand(String accountNumber, List<Integer> categoryIdList);

    //修改产品的欧盟负责人
    void updateProductEuId(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, Long euId, String userName);

    //修改折扣
    void updateDiscount(SaleAccountAndBusinessResponse saleAccountByAccountNumber, Long productId, Integer bulkOrder, Integer bulkDiscount, String userName);

    void updateMarketImages(List<MarketImageBean> imageBeanList, String userName);

    /**
     * 提供给产品系统,根据商品id查询标题描述信息
     *
     * @param productId
     * @return
     */
    ApiResult<AliexpressProductListingMsgDto> apiToProductListingMsgDto(String productId);

    //修改制造商
    void upManufacturer(List<EsAliexpressProductListing> contentList, String userName);

    /**
     * 导入商品折扣
     *
     * @param file
     * @param user
     * @return
     */

    List<DiscountProExcel> updateOrAddDiscountProductForExcel(MultipartFile file, String user);

    /**
     * 批量设置阶梯价
     * @param productId
     * @param discountParam
     */
    ResponseJson aliexpressProductLadderPriceUpdate(String account, Long productId, DiscountParam discountParam, AliexpressProductLog productLog);

    /**
     * 修改链接计税方式
     * @param saleAccountByAccountNumber
     * @param productListingList
     * @param setTaxType
     * @return
     */
    ResponseJson updateProductTaxType(SaleAccountAndBusinessResponse saleAccountByAccountNumber, List<EsAliexpressProductListing> productListingList, String setTaxType);

    /**
     * 获取商品主图和标题数据
     * @param ids 在线列表ID列表
     * @return 商品主图和标题数据列表
     */
    List<AliexpressImageTitleResponse> getImageAndTitleData(List<String> ids);

    /**
     * 修改商品主图和标题
     * @param request 修改商品主图和标题数据列表
     */
    void updateMainImgTitle(List<AliexpressUpdateImageTitleRequest> request);

    /**
     * 批量去除半托管退出标签
     * @param requestCriteria 查询条件
     * @return null 就是成功
     */
    String batchClearHalfCountryExitLabel(EsAliexpressProductListingRequest requestCriteria);

    /**
     * 处理更新主图和标题请求
     * @param requests 更新请求列表
     */
    void processImageTitleRequests(List<AliexpressUpdateImageTitleRequest> requests);

}
