package com.estone.erp.publish.smt.helper;


import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountAddProductNewCall;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountCreateCall;
import com.estone.erp.publish.smt.call.direct.singlediscount.SingleDiscountEditProductNewCall;
import com.estone.erp.publish.smt.enums.MarketingSingleDiscountStatusEnum;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.BatchUpdatedConfigParam;
import com.estone.erp.publish.smt.model.dto.SingleDiscountProDTO;
import com.estone.erp.publish.smt.service.SmtMarketingSingleDiscountFailLogService;
import com.estone.erp.publish.smt.service.SmtMarketingSingleDiscountService;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProduct;
import com.estone.erp.publish.tidb.publishtidb.model.SmtSingleDiscountProductExample;
import com.estone.erp.publish.tidb.publishtidb.service.SmtSingleDiscountProductService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SingleDiscountOrProductHelper {

    @Resource
    private SmtMarketingSingleDiscountFailLogService smtMarketingSingleDiscountFailLogService;

    @Resource
    private SmtSingleDiscountProductService smtSingleDiscountProductService;

    @Resource
    private SmtMarketingSingleDiscountService smtMarketingSingleDiscountService;

    @Resource
    private AliexpressProductLogHelper aliexpressProductLogHelper;

    /**
     * 更新库中折扣活动的商品数量
     *
     * @param singleDiscount
     * @param productCountByEs
     */
    public void updateDiscountProductCount(SmtMarketingSingleDiscount singleDiscount, Long productCountByEs) {
        if (ObjectUtils.isNotEmpty(singleDiscount)) {
            //更新一下商品的数量
            SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
            smtSingleDiscountProductExample.createCriteria().andLocalSingleDiscountIdEqualTo(singleDiscount.getId());
            int count = smtSingleDiscountProductService.countByExample(smtSingleDiscountProductExample);
            if (ObjectUtils.isNotEmpty(productCountByEs)) {
                long l = (productCountByEs - count) >= 0 ? (productCountByEs - count) : 0;
                singleDiscount.setNoLinkNum((int) l);
                singleDiscount.setListingNum(productCountByEs.intValue());
            }
            singleDiscount.setSingleDiscountProdNum(count);
            singleDiscount.setLastSubmitTime(Timestamp.valueOf(LocalDateTime.now()));
            smtMarketingSingleDiscountService.updateByPrimaryKeySelective(singleDiscount);
        }
    }

    /**
     * 根据店铺账号、状态集合、商品id查询对应的单品折扣活动和单品折扣产品信息
     * left:单品折扣活动 right:单品折扣产品信息
     *
     * @param accountNumber 店铺账号
     * @param statusList    状态集合
     * @param itemId        商品id
     * @return
     */
    public Pair<SmtMarketingSingleDiscount, SmtSingleDiscountProduct> getSmtSingleDiscountProduct(String accountNumber, List<Integer> statusList, Long itemId) {
        //按创建时间排序
        List<SmtMarketingSingleDiscount> dbSmtSingleDiscounts = getDiscountsByCreateTimeDesc(accountNumber, statusList);
        if (CollectionUtils.isEmpty(dbSmtSingleDiscounts)) {
            return Pair.of(null, null);
        }
        List<SmtMarketingSingleDiscount> singleDiscounts = Lists.newArrayList();
        List<SmtMarketingSingleDiscount> onGoingDiscounts = Lists.newArrayList();
        List<SmtMarketingSingleDiscount> noOnGoingDiscounts = Lists.newArrayList();
        //保证优先匹配生效中的单品折扣
        for (SmtMarketingSingleDiscount dbSmtSingleDiscount : dbSmtSingleDiscounts) {
            if (dbSmtSingleDiscount.getStatus().equals(MarketingSingleDiscountStatusEnum.ONGOING.getCode())) {
                onGoingDiscounts.add(dbSmtSingleDiscount);
            } else {
                noOnGoingDiscounts.add(dbSmtSingleDiscount);
            }
        }
        singleDiscounts.addAll(onGoingDiscounts);
        singleDiscounts.addAll(noOnGoingDiscounts);


        for (SmtMarketingSingleDiscount singleDiscount : singleDiscounts) {
            List<SmtSingleDiscountProduct> dbSingleProducts = getSmtSingleDiscountProducts(singleDiscount.getSingleDiscountId(), itemId);
            if (CollectionUtils.isNotEmpty(dbSingleProducts)) {
                return Pair.of(singleDiscount, dbSingleProducts.get(0));
            }
        }
        return Pair.of(null, null);
    }

    public List<SmtMarketingSingleDiscount> getDiscountsByCreateTimeDesc(String accountNumber, List<Integer> statusList) {
        SmtMarketingSingleDiscountExample example = new SmtMarketingSingleDiscountExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber).andStatusIn(statusList);
        example.setOrderByClause("created_time desc");
        return smtMarketingSingleDiscountService.selectByExample(example);
    }


    /**
     * 根据平台单品折扣主键id列表和商品id查询对应的产品信息
     *
     * @param platSingleDiscountIds 平台单品折扣主键id列表
     * @param itemId                商品id
     * @return
     */
    public List<SmtSingleDiscountProduct> getSmtSingleDiscountProducts(List<Long> platSingleDiscountIds, Long itemId) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andPlatSingleDiscountIdIn(platSingleDiscountIds).andItemIdEqualTo(itemId);
        return smtSingleDiscountProductService.selectByExample(example);
    }

    /**
     * 根据平台单品折扣主键id和商品id查询对应的产品信息
     *
     * @param platSingleDiscountId 平台单品折扣主键id
     * @param itemId               商品id
     * @return
     */
    public List<SmtSingleDiscountProduct> getSmtSingleDiscountProducts(Long platSingleDiscountId, Long itemId) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andPlatSingleDiscountIdEqualTo(platSingleDiscountId).andItemIdEqualTo(itemId);
        return smtSingleDiscountProductService.selectByExample(example);
    }

    /**
     * 根据平台单品折扣主键id列表查询对应的产品信息
     *
     * @param platSingleDiscountIds 平台单品折扣主键id列表
     * @return
     */
    public List<SmtSingleDiscountProduct> getDiscountProductsByPlatSingleDiscountIds(List<Long> platSingleDiscountIds) {
        if (CollectionUtils.isEmpty(platSingleDiscountIds)) {
            return Lists.newArrayList();
        }
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andPlatSingleDiscountIdIn(platSingleDiscountIds);
        return smtSingleDiscountProductService.selectByExample(example);
    }


    /**
     * 根据本地单品折扣主键id集合查询对应的产品信息
     *
     * @param localSingleDiscountIds 本地单品折扣主键id集合
     * @return
     */
    public List<SmtSingleDiscountProduct> getSingleDiscountProducts(List<Long> localSingleDiscountIds) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andLocalSingleDiscountIdIn(localSingleDiscountIds);
        return smtSingleDiscountProductService.selectByExample(example);
    }

    /**
     * 根据本地单品折扣主键id查询对应的产品信息
     *
     * @param localSingleDiscountId 本地单品折扣主键id
     * @return
     */
    public List<SmtSingleDiscountProduct> getSingleDiscountProducts(Long localSingleDiscountId) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andLocalSingleDiscountIdEqualTo(localSingleDiscountId);
        return smtSingleDiscountProductService.selectByExample(example);
    }


    /**
     * 根据店铺和状态查询对应的产品信息
     *
     * @param accountNumber
     * @param statusList
     * @return
     */
    public List<SmtMarketingSingleDiscount> getSingleDiscounts(String accountNumber, List<Integer> statusList) {
        SmtMarketingSingleDiscountExample example = new SmtMarketingSingleDiscountExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andStatusIn(statusList);
        return smtMarketingSingleDiscountService.selectByExample(example);
    }


    /**
     * 分页查询：根据本地单品折扣主键id查询对应的产品信息
     *
     * @param localSingleDiscountId 本地单品折扣主键id
     * @param offset                偏移量
     * @param limit                 分页大小
     * @return
     */
    public List<SmtSingleDiscountProduct> getSingleDiscountProductsByPage(Long localSingleDiscountId, int offset, int limit) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andLocalSingleDiscountIdEqualTo(localSingleDiscountId);
        example.setOffset(offset);
        example.setLimit(limit);
        return smtSingleDiscountProductService.selectByExample(example);
    }


    /**
     *
     * smt在线列表扩展单品折扣信息
     * 根据测试和产品要求的新改动:单品折扣活动状态为未生效、生效中、已暂停,按照创建时间排序,如若存在生效中的单品折扣优先返回
     * @param aliexpressEsExtend 在线列表的单个对象
     * @param accountNumber      店铺账号
     * @param itemId             商品Id
     */
    public void extendDiscountInfo(AliexpressEsExtend aliexpressEsExtend, String accountNumber, Long itemId) {
        //单品折扣活动状态为未生效、生效中、已暂停
        List<Integer> statusList = Arrays.asList(MarketingSingleDiscountStatusEnum.NEXT.getCode(), MarketingSingleDiscountStatusEnum.ONGOING.getCode(), MarketingSingleDiscountStatusEnum.PAUSED.getCode());
        Pair<SmtMarketingSingleDiscount, SmtSingleDiscountProduct> pair = this.getSmtSingleDiscountProduct(accountNumber, statusList, itemId);
        SmtMarketingSingleDiscount smtSingleDiscount = pair.getLeft();
        SmtSingleDiscountProduct smtSingleDiscountProduct = pair.getRight();
        if (null != smtSingleDiscount) {
            aliexpressEsExtend.setSingleDisCountName(smtSingleDiscount.getName());
            aliexpressEsExtend.setStatusName(MarketingSingleDiscountStatusEnum.convert(smtSingleDiscount.getStatus()));
        }
        if (null != smtSingleDiscountProduct) {
            aliexpressEsExtend.setWholeStationDiscount(smtSingleDiscountProduct.getDiscount());
            aliexpressEsExtend.setStoreClubDiscountRate(smtSingleDiscountProduct.getStore_club_discount_rate());
        }
    }

    /**
     * 拉取结束时间大于或等于指定时间（即进行中）的单品折扣活动集合
     *
     * @param accountNumber 店铺账号
     * @param statusList    状态
     * @param currentTime   指定时间
     * @return
     */
    public List<SmtMarketingSingleDiscount> getSystemCreateDiscounts(String accountNumber, List<Integer> statusList, Timestamp currentTime) {
        SmtMarketingSingleDiscountExample example = new SmtMarketingSingleDiscountExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber).andStatusIn(statusList)
                .andCreatedTypeEqualTo(2)
                .andEndTimeGreaterThanOrEqualTo(currentTime);
        return smtMarketingSingleDiscountService.selectByExample(example);
    }


    /**
     * 获取结束时间最长的单品折扣
     *
     * @param accountNumber 店铺账号
     * @param status        状态
     * @return
     */
    public SmtMarketingSingleDiscount getLongestEndTimeSingleDiscount(String accountNumber, Integer status) {
        SmtMarketingSingleDiscountExample example = new SmtMarketingSingleDiscountExample();
        example.createCriteria().andStatusEqualTo(status).andAccountNumberEqualTo(accountNumber).andCreatedTypeEqualTo(2);
        example.setOrderByClause("end_time desc");
        return smtMarketingSingleDiscountService.selectByExample(example).stream().findFirst().orElse(null);
    }

    /**
     * 查询生效的单品折扣活动
     *
     * @param accountNumberList 店铺账号集合，为空集合则查询所有
     * @return
     */
    public List<SmtMarketingSingleDiscount> getSystemOnGoingSingleDiscounts(List<String> accountNumberList) {
        SmtMarketingSingleDiscountExample smtMarketingSingleDiscountExample = new SmtMarketingSingleDiscountExample();
        SmtMarketingSingleDiscountExample.Criteria criteria = smtMarketingSingleDiscountExample.createCriteria();
        criteria.andStatusIn(List.of(MarketingSingleDiscountStatusEnum.ONGOING.getCode(), MarketingSingleDiscountStatusEnum.NEXT.getCode()));
        criteria.andCreatedTypeEqualTo(2);
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountNumberIn(accountNumberList);
        }
        return smtMarketingSingleDiscountService.selectByExample(smtMarketingSingleDiscountExample);
    }

    /**
     * 根据店铺账号和活动id获取单品折扣活动
     *
     * @param accountNumber    店铺账号
     * @param singleDiscountId 活动id
     * @return
     */

    public SmtMarketingSingleDiscount getSingleDiscount(String accountNumber, Long singleDiscountId) {
        SmtMarketingSingleDiscountExample example = new SmtMarketingSingleDiscountExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber).andSingleDiscountIdEqualTo(singleDiscountId);
        example.setOrderByClause("created_time desc");
        List<SmtMarketingSingleDiscount> list = smtMarketingSingleDiscountService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据店铺账号和商品id集合获取单品折扣商品集合,按照创建时间倒序排序
     *
     * @param accountNumber
     * @param itemIds
     * @return
     */
    public List<SmtSingleDiscountProduct> getDiscountProductsByCreateTimeDesc(String accountNumber, List<Long> itemIds) {
        SmtSingleDiscountProductExample example = new SmtSingleDiscountProductExample();
        example.createCriteria().andItemIdIn(itemIds).andAccountNumberEqualTo(accountNumber);
        example.setOrderByClause("create_time desc");
        return smtSingleDiscountProductService.selectByExample(example);
    }

    /**
     * 生成折扣名称
     *
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     * @return
     */
    private String buildDiscountName(LocalDateTime startTime, LocalDateTime endTime) {

        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return "自动单品折扣_" + startTimeStr + "_" + endTimeStr;
    }


    /**
     * 创建单品折扣活动
     * left:提示信息 middle:折扣活动 right:结果状态
     *
     * @param saleAccount 店铺账号
     * @param startTime   活动开始时间
     * @param endTime     活动结束时间
     * @param currentTime 当前时间
     * @param config      对应的单品折扣配置
     * @param isRenewal   是否为续期
     * @return
     */
    public Triple<String, SmtMarketingSingleDiscount, Boolean> createSingleDiscount(SaleAccountAndBusinessResponse saleAccount,
                                                                                    LocalDateTime startTime, LocalDateTime endTime, Timestamp currentTime,
                                                                                    AliexpressMarketingConfig config, boolean isRenewal) {
        String resultMsg;
        String accountNumber = saleAccount.getAccountNumber();
        String discountName = this.buildDiscountName(startTime, endTime);
        ResponseJson rsp = RetryUtil.doRetry(() -> {
            ResponseJson createResp = SingleDiscountCreateCall.createSingleDiscount(saleAccount, discountName, startTime, endTime);
            if (!createResp.isSuccess()) {
                String errorMsg = "创建单品折扣失败，smt平台接口报错:" + createResp.getMessage();
                throw new RuntimeException(errorMsg);
            }
            return createResp;
        }, 3);

        //能到这里说明调用成功,否则前边已抛出
        //调用成功则记录至数据库中
        long singleDiscountId = Long.parseLong(rsp.getMessage());
        /*算一遍在线列表的数量*/
        Long productCountByEs = smtMarketingSingleDiscountService.getProductCountByEs(accountNumber);
        SmtMarketingSingleDiscount smtMarketingSingleDiscount = SmtMarketingSingleDiscount.builder()
                .accountNumber(accountNumber)
                .singleDiscountId(singleDiscountId)
                .name(discountName)
                .startTime(Timestamp.valueOf(startTime))
                .endTime(Timestamp.valueOf(endTime))
                .status(MarketingSingleDiscountStatusEnum.NEXT.getCode())
                .createdTime(currentTime)
                .configId(config.getId())
                .ruleJson(config.getRuleJson())
                .singleDiscountProdNum(0)
                .listingNum(productCountByEs.intValue())
                .createdType(2)
                .noLinkNum(productCountByEs.intValue()).build();
        smtMarketingSingleDiscountService.insert(smtMarketingSingleDiscount);

        resultMsg = "创建成功，单品折扣名称：" + smtMarketingSingleDiscount.getName();
        if (isRenewal) {
            resultMsg = "创建续期活动成功，单品折扣名称：" + smtMarketingSingleDiscount.getName();
        }
        return Triple.of(resultMsg, smtMarketingSingleDiscount, Boolean.TRUE);
    }


    /**
     * 调用平台添加折扣商品,并保存在db中
     * @param saleAccount
     * @param smtMarketingSingleDiscount
     * @param addProDTOs
     * @param currentUser
     */
    public void addProducts(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount smtMarketingSingleDiscount,
                            List<SingleDiscountProDTO> addProDTOs, String currentUser, String relationId, String ruleName, String operateType) {
        currentUser = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        String accountNumber = saleAccount.getAccountNumber();
        String newRemark = smtMarketingSingleDiscount.getName();

        List<SingleDiscountProDTO> needAddDbProDTOs = Lists.newArrayList();
        //调用远程接口:添加商品
        ResponseJson addProsResp = SingleDiscountAddProductNewCall.addPros(saleAccount, smtMarketingSingleDiscount, addProDTOs);
        if (addProsResp.isSuccess()) {
            if (CollectionUtils.isNotEmpty(addProsResp.getErrors())) {//说明有可能存在部分失败
                //当批量添加时,有可能存在部分成功,部分失败
                List<Long> errorItemIds = addProsResp.getErrors()
                        .stream().map(error -> Long.valueOf(error.getField())).collect(Collectors.toList());
                needAddDbProDTOs = addProDTOs.stream().filter(t -> !errorItemIds.contains(t.getItemId())).collect(Collectors.toList());
                for (ResponseError error : addProsResp.getErrors()) {
                    //生成类型为【operateType】的失败处理报告
                    Long productId = Optional.ofNullable(error.getField()).map(Long::parseLong).orElse(null);
                    aliexpressProductLogHelper.insertLog(relationId, ruleName, productId,
                            operateType, Boolean.FALSE, error.getMessage(), accountNumber, currentUser, newRemark, null);

                    // 生成失败记录
                    addMarketingSingleFailLog(smtMarketingSingleDiscount, error, productId);

                }
            } else {//说明全部成功
                needAddDbProDTOs = addProDTOs;
            }
        } else {
            for (SingleDiscountProDTO addProDTO : addProDTOs) {
                //生成类型为【operateType】的失败处理报告
                aliexpressProductLogHelper.insertLog(relationId, ruleName, addProDTO.getItemId(), operateType,
                        Boolean.FALSE, addProsResp.getMessage(), accountNumber, currentUser, newRemark, null);
            }
        }
        if (ObjectUtils.isNotEmpty(needAddDbProDTOs)) {
            for (SingleDiscountProDTO needAddDbProDTO : needAddDbProDTOs) {
                //生成类型为【operateType】的成功处理报告
                aliexpressProductLogHelper.insertLog(relationId, ruleName,
                        needAddDbProDTO.getItemId(), operateType,
                        Boolean.TRUE, null, accountNumber, currentUser, newRemark, null);
            }
            List<Long> needAddDbItemIds = needAddDbProDTOs.stream().map(SingleDiscountProDTO::getItemId).collect(Collectors.toList());
            List<SmtSingleDiscountProduct> singleDiscountProductList = needAddDbProDTOs.stream().map(proDTO -> {
                SmtSingleDiscountProduct smtSingleDiscountProduct = BeanUtil.copyProperties(proDTO, SmtSingleDiscountProduct.class);
                smtSingleDiscountProduct.setAccountNumber(smtMarketingSingleDiscount.getAccountNumber());
                smtSingleDiscountProduct.setLocalSingleDiscountId(smtMarketingSingleDiscount.getId());
                smtSingleDiscountProduct.setPlatSingleDiscountId(smtMarketingSingleDiscount.getSingleDiscountId());
                smtSingleDiscountProduct.setClub_discount_type(ObjectUtils.isEmpty(smtSingleDiscountProduct.getStore_club_discount_rate()) ? 0 : 1);
                smtSingleDiscountProduct.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
                return smtSingleDiscountProduct;
            }).collect(Collectors.toList());
            //入库
            smtSingleDiscountProductService.batchInsert(singleDiscountProductList);
            log.info("店铺[{}],平台折扣活动id[{}],添加商品[{}]成功", saleAccount.getAccountNumber(), smtMarketingSingleDiscount.getSingleDiscountId(), needAddDbItemIds);
        }
    }

    /**
     * 生成失败记录
     *
     * @param smtMarketingSingleDiscount
     * @param error
     * @param productId
     */
    private void addMarketingSingleFailLog(SmtMarketingSingleDiscount smtMarketingSingleDiscount, ResponseError error, Long productId) {
        SmtMarketingSingleDiscountFailLog smtMarketingSingleDiscountFailLog = new SmtMarketingSingleDiscountFailLog();
        smtMarketingSingleDiscountFailLog.setSingleDiscountId(smtMarketingSingleDiscount.getSingleDiscountId());
        smtMarketingSingleDiscountFailLog.setAccountNumber(smtMarketingSingleDiscount.getAccountNumber());
        smtMarketingSingleDiscountFailLog.setProductId(productId);
        smtMarketingSingleDiscountFailLog.setRetryCount(0);
        smtMarketingSingleDiscountFailLog.setResult(Boolean.FALSE);
        smtMarketingSingleDiscountFailLog.setMsg(error.getMessage());
        smtMarketingSingleDiscountFailLog.setCreatedDate(Timestamp.valueOf(LocalDateTime.now()));
        smtMarketingSingleDiscountFailLogService.insert(smtMarketingSingleDiscountFailLog);
    }

    /**
     * 调用平台更新折扣商品,并更新在db中
     *
     * @param saleAccount
     * @param singleDiscount
     * @param updateProDTOs
     */
    public void editProducts(SaleAccountAndBusinessResponse saleAccount,
                             SmtMarketingSingleDiscount singleDiscount, List<SingleDiscountProDTO> updateProDTOs) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String accountNumber = saleAccount.getAccountNumber();
        String newRemark = singleDiscount.getName();
        List<SingleDiscountProDTO> needUpdateDbProDTOs = Lists.newArrayList();
        //调用平台更新折扣商品接口
        ResponseJson updateProsResp = SingleDiscountEditProductNewCall.editPros(saleAccount, singleDiscount.getSingleDiscountId(), updateProDTOs);
        if (updateProsResp.isSuccess()) {
            if (CollectionUtils.isNotEmpty(updateProsResp.getErrors())) {
                //当批量更新时,有可能存在部分成功,部分失败
                List<Long> errorItemIds = updateProsResp.getErrors()
                        .stream().map(error -> Long.valueOf(error.getField())).collect(Collectors.toList());
                needUpdateDbProDTOs = updateProDTOs.stream().filter(t -> !errorItemIds.contains(t.getItemId())).collect(Collectors.toList());

                for (ResponseError error : updateProsResp.getErrors()) {
                    //生成类型为【生成或编辑单品折扣】的失败处理报告
                    Long productId = Optional.ofNullable(error.getField()).map(Long::parseLong).orElse(null);
                    aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null, productId,
                            OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                            Boolean.FALSE, error.getMessage(), accountNumber, currentUser, newRemark, null);
                }
            } else {//说明全部成功
                needUpdateDbProDTOs = updateProDTOs;
            }
        } else {
            for (SingleDiscountProDTO updateProDTO : updateProDTOs) {
                //生成类型为【生成或编辑单品折扣】的失败处理报告
                aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null, updateProDTO.getItemId(),
                        OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                        Boolean.FALSE, updateProsResp.getMessage(), accountNumber, currentUser, newRemark, null);
            }
        }
        if (ObjectUtils.isNotEmpty(needUpdateDbProDTOs)) {
            for (SingleDiscountProDTO needUpdateDbProDTO : needUpdateDbProDTOs) {
                //生成类型为【生成或编辑单品折扣】的成功处理报告
                aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null,
                        needUpdateDbProDTO.getItemId(), OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                        Boolean.TRUE, null, accountNumber, currentUser, newRemark, null);
            }
            List<Long> needUpdateDbItemIds = needUpdateDbProDTOs.stream().map(SingleDiscountProDTO::getItemId).collect(Collectors.toList());
            //更新入库
            smtSingleDiscountProductService.batchUpdate(needUpdateDbProDTOs);
            log.info("店铺[{}],平台折扣活动id[{}],更新商品[{}]成功", saleAccount.getAccountNumber(), singleDiscount.getSingleDiscountId(), needUpdateDbItemIds);
        }
    }

    public void editProductCommonDiscount(SaleAccountAndBusinessResponse saleAccount, SmtMarketingSingleDiscount singleDiscount,
                                          List<Long> itemIds, Integer discount, Integer buyMaxNum,
                                          BatchUpdatedConfigParam batchUpdatedConfigParam, Long localSingleDiscountId) {

        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String accountNumber = saleAccount.getAccountNumber();
        String newRemark = singleDiscount.getName();
        List<Long> needUpdateDbItemIds = Lists.newArrayList();
        //调用平台更新折扣商品接口
        ResponseJson updateProsResp = SingleDiscountEditProductNewCall.
                editProductCommonDiscount(saleAccount, singleDiscount, itemIds, discount, buyMaxNum, batchUpdatedConfigParam);
        if (updateProsResp.isSuccess()) {
            if (CollectionUtils.isNotEmpty(updateProsResp.getErrors())) {
                //当批量更新时,有可能存在部分成功,部分失败
                List<Long> errorItemIds = updateProsResp.getErrors()
                        .stream().map(error -> Long.valueOf(error.getField())).collect(Collectors.toList());
                needUpdateDbItemIds = itemIds.stream().filter(t -> !errorItemIds.contains(t)).collect(Collectors.toList());

                for (ResponseError error : updateProsResp.getErrors()) {
                    //生成类型为【生成或编辑单品折扣】的失败处理报告
                    Long productId = Optional.ofNullable(error.getField()).map(Long::parseLong).orElse(null);
                    aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null, productId,
                            OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                            Boolean.FALSE, error.getMessage(), accountNumber, currentUser, newRemark, null);
                }
            } else {//说明全部成功
                needUpdateDbItemIds = itemIds;
            }
        } else {
            for (Long itemId : itemIds) {
                //生成类型为【生成或编辑单品折扣】的失败处理报告
                aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null, itemId,
                        OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                        Boolean.FALSE, updateProsResp.getMessage(), accountNumber, currentUser, newRemark, null);
            }
        }
        if (ObjectUtils.isNotEmpty(needUpdateDbItemIds)) {
            for (Long needUpdateDbItemId : needUpdateDbItemIds) {
                //生成类型为【生成或编辑单品折扣】的成功处理报告
                aliexpressProductLogHelper.insertLog(singleDiscount.getId().toString(), null,
                        needUpdateDbItemId, OperateLogTypeEnum.SINGLE_DISCOUNT_CREATE_OR_UPDATE.getCode(),
                        Boolean.TRUE, null, accountNumber, currentUser, newRemark, null);
            }
            //更新入库
            SmtSingleDiscountProductExample smtSingleDiscountProductExample = new SmtSingleDiscountProductExample();
            smtSingleDiscountProductExample.createCriteria().andLocalSingleDiscountIdEqualTo(localSingleDiscountId)
                    .andItemIdIn(needUpdateDbItemIds);
            SmtSingleDiscountProduct smtSingleDiscountProduct = new SmtSingleDiscountProduct();
            smtSingleDiscountProduct.setDiscount(discount);
            smtSingleDiscountProduct.setBuy_max_num(buyMaxNum);
            smtSingleDiscountProductService.updateByExampleSelective(smtSingleDiscountProduct, smtSingleDiscountProductExample);
            log.info("店铺[{}],平台折扣活动id[{}],更新商品[{}]成功", saleAccount.getAccountNumber(), singleDiscount.getSingleDiscountId(), needUpdateDbItemIds);
        }
    }
    /**
     * 获取指定状态的折扣活动中Db中存在的商品id
     *
     * @param accountNumber
     * @return
     */
    public List<Long> getOnGoingExistItemIds(String accountNumber) {
        List<Integer> statusList = Lists.newArrayList(MarketingSingleDiscountStatusEnum.ONGOING.getCode(), MarketingSingleDiscountStatusEnum.NEXT.getCode());
        List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = getSingleDiscounts(accountNumber, Lists.newArrayList(statusList));
        if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
            // 排除掉系统创建的 next, 因为这里要添加，新品在系统创建的next添加过了也要在 ongoing 里添加
            smtMarketingSingleDiscounts = smtMarketingSingleDiscounts.stream()
                    .filter(a -> !(a.getCreatedType() == 2 && Objects.equals(a.getStatus(), MarketingSingleDiscountStatusEnum.NEXT.getCode())))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
                List<Long> singleDiscountIds = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
                return getDiscountProductsByPlatSingleDiscountIds(singleDiscountIds)
                        .stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    public List<Long> getNextExistItemIds(String accountNumber, Timestamp endTime) {
        List<Integer> statusList = Lists.newArrayList(MarketingSingleDiscountStatusEnum.ONGOING.getCode(), MarketingSingleDiscountStatusEnum.NEXT.getCode());
        List<SmtMarketingSingleDiscount> smtMarketingSingleDiscounts = getSingleDiscounts(accountNumber, Lists.newArrayList(statusList));
        if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
            // next 是加载只剩3天过期就过期的所有生效活动下的商品
            smtMarketingSingleDiscounts = smtMarketingSingleDiscounts.stream()
                    .filter(a -> Objects.equals(a.getStatus(), MarketingSingleDiscountStatusEnum.NEXT.getCode()) || a.getEndTime().after(endTime))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(smtMarketingSingleDiscounts)) {
                List<Long> singleDiscountIds = smtMarketingSingleDiscounts.stream().map(SmtMarketingSingleDiscount::getSingleDiscountId).collect(Collectors.toList());
                return getDiscountProductsByPlatSingleDiscountIds(singleDiscountIds)
                        .stream().map(SmtSingleDiscountProduct::getItemId).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }
}
