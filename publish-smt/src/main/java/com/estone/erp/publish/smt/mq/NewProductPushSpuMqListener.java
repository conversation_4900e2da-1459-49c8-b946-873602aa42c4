package com.estone.erp.publish.smt.mq;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.smt.bean.SaleAllocationCount;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.estone.erp.publish.smt.util.NewProductUtils;
import com.estone.erp.publish.system.hr.HrClient;
import com.estone.erp.publish.system.hr.model.HrNewUser;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductRecommendation;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressNewProductPublishService;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductRecommendationService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新品推荐 spu 推送
 */
@Slf4j
@Component
public class NewProductPushSpuMqListener implements ChannelAwareMessageListener {
    @Resource
    private SmtNewProductRecommendationService smtNewProductRecommendationService;
    @Resource
    private AliexpressNewProductPublishService aliexpressNewProductPublishService;
    @Resource
    private HrClient hrClient;
    @Resource
    private AliexpressConfigService aliexpressConfigService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        //log.info("body:" + body);
        try {
            if(StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            //log.info("smt spu 新品推荐 " + body);
            List<String> spuList = JSON.parseObject(body, List.class);

            boolean sign = false;
            try {
                // 过滤掉已经推送过的SPU
                List<List<String>> partition = Lists.partition(spuList, 100);
                partition.stream().forEach(spuGroup -> {
                    List<String> filterSpuList = smtNewProductRecommendationService.list(
                                    new LambdaQueryWrapper<SmtNewProductRecommendation>()
                                            .select(SmtNewProductRecommendation::getSpu)
                                            .in(SmtNewProductRecommendation::getSpu, spuGroup)
                            )
                            .stream()
                            .map(SmtNewProductRecommendation::getSpu)
                            .collect(Collectors.toList());
                    XxlJobLogger.log("过滤掉已经推送过的SPU：{}", filterSpuList);
                    spuList.removeAll(filterSpuList);

                });
                if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(spuList)) {
                    XxlJobLogger.log("无可分配SPU");
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                }

                // 分配销售组长新品推荐
                assignSaleLeaderNewProductInfo(spuList);
            }catch (Exception e) {
                log.error("NewProductPushSpuMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if(sign){
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }else{
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            }
            catch (IOException ioe) {
                log.error("NewProductPushSpuMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        }catch (Exception e) {
            log.error("NewProductPushSpuMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.error("NewProductPushSpuMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }


    /**
     * http://172.16.2.103:8080/browse/ES-11764
     * 分配销售组长新品推荐
     */
    private void assignSaleLeaderNewProductInfo(List<String> spuList) {
        Map<String, String> argsMap = new HashMap<>(2);
        argsMap.put("args", SaleChannel.CHANNEL_SMT);
        ApiResult<List<HrNewUser>> lastLeaderMembersByPlatformResult = hrClient.getLastLeaderMembersByPlatform(JSON.toJSONString(argsMap));
        if(!lastLeaderMembersByPlatformResult.isSuccess()){
            log.error("smt新品推荐 获取HR 组长结构异常" + lastLeaderMembersByPlatformResult.getErrorMsg());
            return;
        }

        // 获取平台所有组长信息 需要排除特殊人员
        List<HrNewUser> hrNewUserListUser = lastLeaderMembersByPlatformResult.getResult();
        if(CollectionUtils.isEmpty(hrNewUserListUser)){
            log.info("smt新品推荐 hr 返回 无组长");
            return;
        }

        List<String> addLeader = hrNewUserListUser.stream().filter(a -> NewProductUtils.addLeaderList.contains(a.getEmployeeNo())).map(a -> a.getEmployeeNo()).collect(Collectors.toList());

        List<String> leaderInfo = hrNewUserListUser.stream()
                .map(HrNewUser::getEmployeeNo)
                .filter(t -> !NewProductUtils.excludeNewProductLeaderList.contains(t))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderInfo)) {
            log.info("smt新品推荐 无组长");
            return;
        }

        List<List<String>> lists = PagingUtils.newPagingList(spuList, 100);

        List<Map<String, List<ProductInfo>>> listMap = new ArrayList<>();

        for (List<String> list : lists) {
            // 获取新品spu和禁售站点的对应关系
            List<ProductInfo> productInfos = ProductUtils.findProductInfos(list);
            if (CollectionUtils.isNotEmpty(productInfos)) {
                Map<String, List<ProductInfo>> spuAndProductInfosMap = productInfos.stream().collect(Collectors.groupingBy(ProductInfo::getMainSku, Collectors.toList()));
                listMap.add(spuAndProductInfosMap);
            }
        }

        Map<String, List<ProductInfo>> spuMap = CommonUtils.mergeMaps(listMap);
        Date date = new Date();
        List<SmtNewProductRecommendation> smtNewProductRecommendations = new ArrayList<>();

        //查询spu的类目，根据类目分组
        Map<String, List<String>> fullpathcodeMap = new HashMap<>();
        for (Map.Entry<String, List<ProductInfo>> stringListEntry : spuMap.entrySet()) {
            String spu = stringListEntry.getKey();
            ProductInfo productInfo = stringListEntry.getValue().get(0);
            String fullpathcode = productInfo.getFullpathcode();
            if(StringUtils.isEmpty(fullpathcode)){
                log.error("spu 无分类:" + spu);
                continue;
            }
            try {
                //9996_3482_3620_3645_3646
                String[] s = fullpathcode.split("_");
                String code = s[(s.length - 1)];

                List<String> spusList = fullpathcodeMap.get(code);
                if(CollectionUtils.isEmpty(spusList)){
                    spusList = new ArrayList<>();
                    fullpathcodeMap.put(code, spusList);
                }
                spusList.add(spu);
            } catch (Exception e) {
                log.error("spu 分类异常:" + spu + " 分类 " + fullpathcode);
                continue;
            }
        }

        //记录销售被分配的spu数量
        List<SaleAllocationCount> saleAllocationCountList = new ArrayList<>();

        //根据分类 找到店铺 和对应的销售，根据销售平均分配  需要去除特殊人员的店铺 NewProductUtils.excludeNewProductLeaderList
        for (Map.Entry<String, List<String>> stringListEntry : fullpathcodeMap.entrySet()) {
            String code = stringListEntry.getKey();
            List<String> value = stringListEntry.getValue();

            AliexpressConfigExample configExample = new AliexpressConfigExample();
            configExample.createCriteria().andUsableEqualTo(true).andCategoryIdsByLike("%" + code + "%");
            List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);
            if(CollectionUtils.isEmpty(aliexpressConfigs)){
                log.info("分类{},无有效店铺 spu[{}]", code, StringUtils.join(value, ","));
                for (String spu : value) {
                    match(spu, null, null, spuMap, date, smtNewProductRecommendations);
                }
                continue;
            }

            //需要再次匹配下店铺分类，有模糊匹配问题
            List<String> accountList = aliexpressConfigs.stream().filter(t -> CommonUtils.splitList(t.getCategoryIds(), ",").contains(code)).map(t -> t.getAccount()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(accountList)){
                log.info("分类{},无有效店铺 spu[{}]", code, StringUtils.join(value, ","));
                for (String spu : value) {
                    match(spu, null, null, spuMap, date, smtNewProductRecommendations);
                }
                continue;
            }

            //过滤掉 销售是 031  销售 组长 主管是 3381 的店铺
            Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountList, SaleChannel.CHANNEL_SMT);

            //可以分配的销售 对应的组长
            Map<String, String> saleMap = new HashMap<>();

            //需要得到 可以分配的销售
            for (Map.Entry<String, Triple<String, String, String>> stringTripleEntry : saleSuperiorMap.entrySet()) {
                String key = stringTripleEntry.getKey();
                Triple<String, String, String> value1 = stringTripleEntry.getValue();
                //销售
                String left = value1.getLeft();
                if(StringUtils.isBlank(left)){
                    continue;
                }
                String sale = left.split("-")[1];
                int length = left.split("-").length;
                if(length > 2){
                    sale = left.split("-")[length -1];
                }
                if(StringUtils.isNotBlank(left)){
                    if(NewProductUtils.excludeNewProductLeaderList.contains(sale)){
//                        log.info("分类{},spu[{}],店铺%s,包含3381或031销售 过滤", code, StringUtils.join(value, ","), key);
                        continue;
                    }
                }

                String middle = value1.getMiddle();
                String salemanagerLeader = "";
                if(StringUtils.isNotBlank(middle)){
                    salemanagerLeader = middle.split("-")[1];
                    if(NewProductUtils.addLeaderList.contains(salemanagerLeader)){
//                        log.info("分类{},spu[{}],店铺%s,包含3381销售组长 过滤", code, StringUtils.join(value, ","), key);
                        continue;
                    }
                }
                String right = value1.getRight();
                if(StringUtils.isNotBlank(right)){
                    String s = right.split("-")[1];
                    if(NewProductUtils.addLeaderList.contains(s)){
//                        log.info("分类{},spu[{}],店铺%s,包含3381销售主管 过滤", code, StringUtils.join(value, ","), key);
                        continue;
                    }
                }
                saleMap.put(sale, salemanagerLeader);
            }

            if(saleMap.isEmpty()){
//                log.info("分类{},spu[{}],无可分配销售", code, StringUtils.join(value, ","));
                for (String spu : value) {
                    match(spu, null, null, spuMap, date, smtNewProductRecommendations);
                }
                continue;
            }
            log.info("分类{},spu[{}],可分配销售[{}]", code, StringUtils.join(value, ","), StringUtils.join(saleMap.keySet(), ","));

            //spu 平均分配销售
            List<String> saleList = new ArrayList<>(saleMap.keySet());

            //记录销售被分配的spu数量，然后优先分配次数最少的销售
            for (String sale : saleList) {
                List<SaleAllocationCount> allocationCountList = saleAllocationCountList.stream().filter(t -> t.getSale().equals(sale)).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(allocationCountList)){
                    SaleAllocationCount saleAllocationCount = new SaleAllocationCount();
                    saleAllocationCount.setSale(sale);
                    saleAllocationCount.setCount(0);
                    saleAllocationCountList.add(saleAllocationCount);
                }
            }

            //saleAllocationCountList 根据count 升序 排序
            saleAllocationCountList = saleAllocationCountList.stream().sorted(Comparator.comparingInt(SaleAllocationCount::getCount)).collect(Collectors.toList());

            Collections.shuffle(saleAllocationCountList);

            Map<String, SaleAllocationCount> stringSaleAllocationCountMap = saleAllocationCountList.stream().collect(Collectors.toMap(k -> k.getSale(), v -> v, (k1, k2) -> k1));

            List<String> allocationSaleList = saleAllocationCountList.stream().filter(t -> saleList.contains(t.getSale())).map(t -> t.getSale()).collect(Collectors.toList());

            // 打乱顺序
//            Collections.shuffle(saleList);

            int spuCount = value.size();            // 剩余SPU数量
            int saleCount = saleList.size();           // 销售数量
            int basePerSale = spuCount / saleCount;   // 每个销售基础分配数量
            int extraSpus = spuCount % saleCount;       // 剩余需要额外分配的数量

            int saleIndex = 0;
            int spuIndex = 0;
            while (spuIndex < spuCount) {
                String sale = allocationSaleList.get(saleIndex % saleCount);
                SaleAllocationCount saleAllocationCount = stringSaleAllocationCountMap.get(sale);
                log.info("平均分配 分类{},spu[{}], 分配销售：{}, 历史分配次数：{}", code, StringUtils.join(value, ","), sale, saleAllocationCount.getCount());
                String saleLeader = saleMap.get(sale);
                // 计算该销售应得的数量：基础数量 + (如果还有额外剩余则加1)
                int assignCount = basePerSale + (saleIndex < extraSpus ? 1 : 0);

                for (int i = 0; i < assignCount && spuIndex < spuCount; i++) {
                    String spu = value.get(spuIndex);
                    log.info("NewProductPushSpuMqListener 处理SPU，平均分配给所有销售, {}销售数据 spu {}", sale, spu);

                    saleAllocationCount.setCount(saleAllocationCount.getCount() + 1);
                    match(spu, sale, saleLeader, spuMap, date, smtNewProductRecommendations);
                    spuIndex++;
                }
                saleIndex++;
            }
        }

        if (smtNewProductRecommendations.isEmpty()) {
            return;
        }
        // 保存推荐列表
        smtNewProductRecommendationService.saveBatch(smtNewProductRecommendations, 200);

        //保存刊登次数表
        Set<String> leadListSet = new HashSet<>(leaderInfo);
        if(CollectionUtils.isNotEmpty(addLeader)){
            leadListSet.addAll(addLeader);
        }
        aliexpressNewProductPublishService.saveEmptyPublish(smtNewProductRecommendations, leadListSet);
    }

    /**
     * 分配
     * @param spu
     * @param sale
     * @param leader
     * @param spuMap
     * @param date
     * @param smtNewProductRecommendations
     */
    private void match(String spu, String sale, String leader, Map<String, List<ProductInfo>> spuMap, Date date, List<SmtNewProductRecommendation> smtNewProductRecommendations){
        SmtNewProductRecommendation recommendation = new SmtNewProductRecommendation();
        List<ProductInfo> productInfoList = spuMap.get(spu);
        if(CollectionUtils.isNotEmpty(productInfoList)){
            Optional<String> first = productInfoList.stream().map(ProductInfo::getItemStatus).filter(a -> SkuStatusEnum.NORMAL.getCode().equalsIgnoreCase(a)).findFirst();
            if (first.isPresent()) {
                recommendation.setItemStatus(first.get());
            } else {
                recommendation.setItemStatus(productInfoList.get(0).getItemStatus());
            }

            List<String> enTagList = productInfoList.stream().map(ProductInfo::getEnTag).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(enTagList)){
                recommendation.setTags(StringUtils.join(enTagList, ","));
            }
            recommendation.setCategoryFullPathCode(productInfoList.get(0).getFullpathcode());
            recommendation.setCategoryPathName(productInfoList.get(0).getFullpath());
            recommendation.setTitle(productInfoList.get(0).getTitleCn());
            Long inSingleTime = productInfoList.get(0).getInSingleTime();
            if(inSingleTime != null){
                Date enterProductTime = new Date(inSingleTime);
                recommendation.setEnterProductTime(enterProductTime);
            }
            recommendation.setIsBanned(NewProductUtils.isAllProhibitions(productInfoList));
        }
        recommendation.setSale(sale);
        recommendation.setSaleLeader(leader);
        recommendation.setSpu(spu);
        recommendation.setPushTime(date);
        recommendation.setCreatedTime(date);
        smtNewProductRecommendations.add(recommendation);
    }

    /**
     * 获取组员信息
     *
     * @param listApiResult
     * @param leaderInfo
     * @return
     */
    private static Set<String> getSaleInfo(ApiResult<List<NewUser>> listApiResult, List<String> leaderInfo) {
        Set<String> subordinates = listApiResult.getResult().stream()
                .map(NewUser::getEmployeeNo)
                .filter(employeeNo -> !leaderInfo.contains(employeeNo)) // 过滤掉已经是组长的成员
                .collect(Collectors.toSet());
        return subordinates;
    }
}