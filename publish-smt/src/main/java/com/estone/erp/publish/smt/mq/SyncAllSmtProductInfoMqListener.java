package com.estone.erp.publish.smt.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.smt.componet.AliexpressSyncTempProductHelper;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.service.AliexpressEsTgExtendService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 专门全量同步单品信息到listing
 * <AUTHOR>
 * @date 2022/6/4 14:19
 */
@Slf4j
public class SyncAllSmtProductInfoMqListener implements ChannelAwareMessageListener {

    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;

    @Resource
    private AliexpressEsTgExtendService aliexpressEsTgExtendService;

    @Resource
    private ChangeSkuLogService changeSkuLogService;

    @Autowired
    private AliexpressSyncTempProductHelper aliexpressSyncTempProductHelper;

    private static final int MAX_RETRY_COUNT = 3;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }

            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_SMT);
            }
        } catch (Exception e) {
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT , SaleChannel.CHANNEL_SMT);
            log.error("smt SMT_SYNC_PRODUCT_INFO_QUEUE Exception error: {}", e.getMessage());

        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) throws Exception{
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            //log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        List<String> accountNumberList = changeSku.getAccountNumberList();
        aliexpressEsExtendService.syncProductInfo(skuList, accountNumberList);

        aliexpressEsTgExtendService.syncProductInfo(skuList, accountNumberList);

        // 范本、admin范本
        aliexpressSyncTempProductHelper.syncSkuStatusBySkus(skuList);

        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }
}
