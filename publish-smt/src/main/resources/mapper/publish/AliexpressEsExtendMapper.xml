<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressEsExtendMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressEsExtend" >
    <id column="extend_id" property="extendId" jdbcType="BIGINT" />
    <result column="aliexpress_account_number" property="aliexpressAccountNumber" jdbcType="VARCHAR" />
    <result column="owner_member_id" property="ownerMemberId" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="BIGINT" />
    <result column="aeop_ae_product_skus_json" property="aeopAeProductSkusJson" jdbcType="VARCHAR" />
    <result column="aeop_national_quote_configuration" property="aeopNationalQuoteConfiguration" jdbcType="VARCHAR" />
    <result column="aeop_ae_multimedia" property="aeopAeMultimedia" jdbcType="VARCHAR" />
    <result column="aeop_ae_product_propertys_json" property="aeopAeProductPropertysJson" jdbcType="VARCHAR" />
    <result column="mobile_detail" property="mobileDetail" jdbcType="VARCHAR" />
    <result column="auto_off_date" property="autoOffDate" jdbcType="TIMESTAMP" />
    <result column="auto_on_date" property="autoOnDate" jdbcType="TIMESTAMP" />
    <result column="start_task_date" property="startTaskDate" jdbcType="TIMESTAMP" />
    <result column="end_task_date" property="endTaskDate" jdbcType="TIMESTAMP" />
    <result column="record_off_date" property="recordOffDate" jdbcType="TIMESTAMP" />
    <result column="record_on_date" property="recordOnDate" jdbcType="TIMESTAMP" />
    <result column="square_img" property="squareImg" jdbcType="VARCHAR" />
    <result column="long_img" property="longImg" jdbcType="VARCHAR" />
    <result column="inter_subjects" property="interSubjects" jdbcType="VARCHAR" />
    <result column="aeop_qualification_struct_json" property="aeopQualificationStructJson" jdbcType="VARCHAR" />
    <result column="extended_field1" property="extendedField1" jdbcType="VARCHAR" />
    <result column="extended_field2" property="extendedField2" jdbcType="VARCHAR" />
    <result column="extended_field3" property="extendedField3" jdbcType="VARCHAR" />
    <result column="extended_field4" property="extendedField4" jdbcType="VARCHAR" />
    <result column="extended_field5" property="extendedField5" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    extend_id, aliexpress_account_number, owner_member_id, product_id, aeop_ae_product_skus_json,
    aeop_national_quote_configuration, aeop_ae_multimedia, aeop_ae_product_propertys_json, 
    mobile_detail, auto_off_date, auto_on_date, start_task_date, end_task_date, record_off_date,
    record_on_date, square_img, long_img, inter_subjects, aeop_qualification_struct_json,
    extended_field1, extended_field2, extended_field3, extended_field4, extended_field5
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressEsExtendExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from aliexpress_es_extend
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_es_extend
    where extend_id = #{extendId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_es_extend
    where extend_id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByProductId" >
    delete from aliexpress_es_extend
    where product_id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <!-- 删除指定账号下指定的 productId 列表记录 -->
  <delete id="deleteOrphanedRecordsByAccount">
    DELETE FROM aliexpress_es_extend
    WHERE aliexpress_account_number = #{account}
    <if test="orphanedProductIds != null and orphanedProductIds.size() > 0">
      AND product_id IN
      <foreach collection="orphanedProductIds" item="productId" open="(" close=")" separator=",">
        #{productId}
      </foreach>
    </if>
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressEsExtend" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_es_extend (aliexpress_account_number, owner_member_id, 
      product_id, aeop_ae_product_skus_json, aeop_national_quote_configuration, 
      aeop_ae_multimedia, aeop_ae_product_propertys_json, mobile_detail, square_img,
      long_img, inter_subjects, aeop_qualification_struct_json, extended_field1,
      extended_field2, extended_field3, extended_field4,
      extended_field5)
    values (#{aliexpressAccountNumber,jdbcType=VARCHAR}, #{ownerMemberId,jdbcType=VARCHAR}, 
      #{productId,jdbcType=BIGINT}, #{aeopAeProductSkusJson,jdbcType=VARCHAR}, #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR}, 
      #{aeopAeMultimedia,jdbcType=VARCHAR}, #{aeopAeProductPropertysJson,jdbcType=VARCHAR}, 
      #{mobileDetail,jdbcType=VARCHAR}, #{squareImg,jdbcType=VARCHAR}, #{longImg,jdbcType=VARCHAR},
      #{interSubjects,jdbcType=VARCHAR}, #{aeopQualificationStructJson,jdbcType=VARCHAR}, #{extendedField1,jdbcType=VARCHAR},
      #{extendedField2,jdbcType=VARCHAR}, #{extendedField3,jdbcType=VARCHAR}, #{extendedField4,jdbcType=VARCHAR},
      #{extendedField5,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressEsExtendExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_es_extend
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_es_extend
    <set >
      <if test="record.extendId != null" >
        extend_id = #{record.extendId,jdbcType=BIGINT},
      </if>
      <if test="record.aliexpressAccountNumber != null" >
        aliexpress_account_number = #{record.aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerMemberId != null" >
        owner_member_id = #{record.ownerMemberId,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=BIGINT},
      </if>
      <if test="record.aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{record.aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopNationalQuoteConfiguration != null" >
        aeop_national_quote_configuration = #{record.aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{record.aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{record.aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="record.mobileDetail != null" >
        mobile_detail = #{record.mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.autoOffDate != null" >
        auto_off_date = #{record.autoOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.autoOnDate != null" >
        auto_on_date = #{record.autoOnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.startTaskDate != null" >
        start_task_date = #{record.startTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTaskDate != null" >
        end_task_date = #{record.endTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordOffDate != null" >
        record_off_date = #{record.recordOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.recordOnDate != null" >
        record_on_date = #{record.recordOnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.squareImg != null" >
        square_img = #{record.squareImg,jdbcType=VARCHAR},
      </if>
      <if test="record.longImg != null" >
        long_img = #{record.longImg,jdbcType=VARCHAR},
      </if>
      <if test="record.interSubjects != null" >
        inter_subjects = #{record.interSubjects,jdbcType=VARCHAR},
      </if>
      <if test="record.aeopQualificationStructJson != null" >
        aeop_qualification_struct_json = #{record.aeopQualificationStructJson,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedField1 != null" >
        extended_field1 = #{record.extendedField1,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedField2 != null" >
        extended_field2 = #{record.extendedField2,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedField3 != null" >
        extended_field3 = #{record.extendedField3,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedField4 != null" >
        extended_field4 = #{record.extendedField4,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedField5 != null" >
        extended_field5 = #{record.extendedField5,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressEsExtend" >
    update aliexpress_es_extend
    <set >
      <if test="aliexpressAccountNumber != null" >
        aliexpress_account_number = #{aliexpressAccountNumber,jdbcType=VARCHAR},
      </if>
      <if test="ownerMemberId != null" >
        owner_member_id = #{ownerMemberId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=BIGINT},
      </if>
      <if test="aeopAeProductSkusJson != null" >
        aeop_ae_product_skus_json = #{aeopAeProductSkusJson,jdbcType=VARCHAR},
      </if>
        aeop_national_quote_configuration = #{aeopNationalQuoteConfiguration,jdbcType=VARCHAR},
      <if test="aeopAeMultimedia != null" >
        aeop_ae_multimedia = #{aeopAeMultimedia,jdbcType=VARCHAR},
      </if>
      <if test="aeopAeProductPropertysJson != null" >
        aeop_ae_product_propertys_json = #{aeopAeProductPropertysJson,jdbcType=VARCHAR},
      </if>
      <if test="mobileDetail != null" >
        mobile_detail = #{mobileDetail,jdbcType=VARCHAR},
      </if>
      <if test="autoOffDate != null" >
        auto_off_date = #{autoOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="autoOnDate != null" >
        auto_on_date = #{autoOnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="startTaskDate != null" >
        start_task_date = #{startTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endTaskDate != null" >
        end_task_date = #{endTaskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordOffDate != null" >
        record_off_date = #{recordOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="recordOnDate != null" >
        record_on_date = #{recordOnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="squareImg != null" >
        square_img = #{squareImg,jdbcType=VARCHAR},
      </if>
      <if test="longImg != null" >
        long_img = #{longImg,jdbcType=VARCHAR},
      </if>
      <if test="interSubjects != null" >
        inter_subjects = #{interSubjects,jdbcType=VARCHAR},
      </if>
      <if test="aeopQualificationStructJson != null" >
        aeop_qualification_struct_json = #{aeopQualificationStructJson,jdbcType=VARCHAR},
      </if>
      <if test="extendedField1 != null" >
        extended_field1 = #{extendedField1,jdbcType=VARCHAR},
      </if>
      <if test="extendedField2 != null" >
        extended_field2 = #{extendedField2,jdbcType=VARCHAR},
      </if>
      <if test="extendedField3 != null" >
        extended_field3 = #{extendedField3,jdbcType=VARCHAR},
      </if>
      <if test="extendedField4 != null" >
        extended_field4 = #{extendedField4,jdbcType=VARCHAR},
      </if>
      <if test="extendedField5 != null" >
        extended_field5 = #{extendedField5,jdbcType=VARCHAR},
      </if>
    </set>
    where extend_id = #{extendId,jdbcType=BIGINT}
  </update>

  <update id="updateOFFandOnByProductId" parameterType="com.estone.erp.publish.smt.model.AliexpressEsExtend" >
    update aliexpress_es_extend
    <set >
        auto_off_date = #{autoOffDate,jdbcType=TIMESTAMP},
        auto_on_date = #{autoOnDate,jdbcType=TIMESTAMP},
        start_task_date = #{startTaskDate,jdbcType=TIMESTAMP},
        end_task_date = #{endTaskDate,jdbcType=TIMESTAMP},
        record_off_date = #{recordOffDate,jdbcType=TIMESTAMP},
        record_on_date = #{recordOnDate,jdbcType=TIMESTAMP},
    </set>
    where product_id = #{productId,jdbcType=BIGINT}
  </update>


  <update id="batchUpdate">
    <foreach collection="list" item="item" open="" separator=";" close=";">
    update aliexpress_es_extend
    <set >
      <if test="item.recordOffDate != null" >
        record_off_date = #{item.recordOffDate,jdbcType=TIMESTAMP},
      </if>
      <if test="item.recordOnDate != null" >
        record_on_date = #{item.recordOnDate,jdbcType=TIMESTAMP},
      </if>
    </set>
      where extend_id = #{item.extendId,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>