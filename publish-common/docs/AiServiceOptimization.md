# AI服务调用优化方案

## 概述

为了解决AI服务调用速度慢的问题，我们实现了基于线程池和信号量的并发控制方案，参考了`WalmartTaskOffLinkListingRetireJobHandler`的信号量使用方式。

## 核心组件

### 1. AiServiceExecutor
- **功能**: AI服务调用执行器，提供线程池和信号量控制
- **特性**: 
  - 支持自定义线程数量（默认10）
  - 使用信号量控制并发速率
  - 自动管理线程池生命周期
  - 支持批量和单个调用

### 2. AiServiceUtils
- **功能**: AI服务调用工具类，提供便捷的调用方法
- **特性**:
  - 简化的批量处理接口
  - 支持自定义数据转换和响应处理
  - 统计执行结果和性能指标

### 3. AiServiceResult
- **功能**: AI服务调用结果包装类
- **特性**:
  - 统计成功/失败数量
  - 计算成功率和执行时间
  - 提供执行摘要

## 使用方法

### 基本用法

```java
@Autowired
private AiServiceUtils aiServiceUtils;

// 批量调用腾讯AI服务
List<ChatOpenaiRequest> requests = buildRequests();
Integer threadNum = 10; // 并发线程数
AiServiceResult<ChatCompletionResponse> result = 
    aiServiceUtils.batchCallTencentAi(requests, threadNum);

System.out.println(result.getSummary());
```

### 高级用法 - 自定义数据处理

```java
// 批量处理产品数据并调用AI服务
List<Product> products = getProducts();

AiServiceResult<ChatCompletionResponse> result = aiServiceUtils.batchProcessWithTencentAi(
    products,
    10, // 线程数量
    product -> buildAiRequest(product), // 数据转AI请求
    response -> handleResponse(product, response) // 处理响应
);
```

### 单个调用

```java
@Autowired
private AiServiceExecutor aiServiceExecutor;

ChatOpenaiRequest request = buildRequest();
ApiResult<ChatCompletionResponse> result = 
    aiServiceExecutor.callTencentAiWithRateLimit(request, 10);
```

## 参数说明

### threadNum参数
- **类型**: Integer
- **默认值**: 10
- **说明**: 控制并发线程数量，同时也是信号量的许可数量
- **建议**: 根据AI服务的承载能力和系统资源调整

## 性能优化特性

### 1. 线程池管理
- 核心线程数: threadNum
- 最大线程数: threadNum * 2
- 队列容量: 1000
- 拒绝策略: CallerRunsPolicy

### 2. 信号量控制
- 许可数量: threadNum
- 控制并发调用AI服务的数量
- 防止过载和限流

### 3. 超时控制
- 批量调用最大等待时间: 30分钟
- 自动收集已完成的结果
- 避免无限等待

## 集成示例

### 在Job Handler中使用

```java
@Component
public class MyAiJobHandler extends AbstractJobHandler {
    
    @Autowired
    private AiServiceUtils aiServiceUtils;
    
    @Data
    public static class InnerParam {
        private Integer threadNum = 10; // 默认线程数
        // 其他参数...
    }
    
    @Override
    @XxlJob("MyAiJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        // 解析参数
        InnerParam innerParam = parseParam(param);
        
        // 获取待处理数据
        List<MyData> dataList = getDataToProcess();
        
        try {
            // 批量调用AI服务
            AiServiceResult<ChatCompletionResponse> result = 
                aiServiceUtils.batchProcessWithTencentAi(
                    dataList,
                    innerParam.getThreadNum(),
                    this::buildAiRequest,
                    this::handleAiResponse
                );
            
            XxlJobLogger.log("AI服务调用完成: {}", result.getSummary());
            return ReturnT.SUCCESS;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            XxlJobLogger.log("任务被中断: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }
    
    private ChatOpenaiRequest buildAiRequest(MyData data) {
        // 构建AI请求
        return ChatOpenaiRequest.builder()
            .prompt(data.getPrompt())
            .model("deepseek-r1")
            .build();
    }
    
    private void handleAiResponse(ApiResult<ChatCompletionResponse> response) {
        // 处理AI响应
        if (response.isSuccess()) {
            // 保存结果
            saveResult(response.getResult());
        } else {
            // 记录错误
            log.error("AI调用失败: {}", response.getErrorMsg());
        }
    }
}
```

## 监控和调试

### 线程池状态监控

```java
@Autowired
private AiServiceUtils aiServiceUtils;

// 获取线程池状态
String status = aiServiceUtils.getAiServiceStatus();
log.info("AI服务线程池状态: {}", status);
```

### 执行结果统计

```java
AiServiceResult<ChatCompletionResponse> result = aiServiceUtils.batchCallTencentAi(requests, 10);

log.info("执行摘要: {}", result.getSummary());
log.info("成功率: {}%", result.getSuccessRate());
log.info("执行时间: {}ms", result.getExecutionTime());
```

## 注意事项

1. **线程数量设置**: 根据AI服务的QPS限制和系统资源合理设置
2. **异常处理**: 所有AI调用都有异常保护，单个失败不影响整体
3. **中断处理**: 支持任务中断，会正确释放资源
4. **内存管理**: 使用并发安全的集合类，避免内存泄漏

## 性能对比

### 优化前
- 串行调用AI服务
- 无并发控制
- 调用速度慢

### 优化后
- 并行调用AI服务
- 信号量控制并发
- 显著提升调用速度
- 可配置线程数量

## 扩展性

该方案具有良好的扩展性，可以：
1. 支持新的AI服务接口
2. 自定义并发控制策略
3. 集成更多的监控指标
4. 支持动态调整参数
