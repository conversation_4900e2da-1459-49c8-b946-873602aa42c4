# AI服务调用优化使用指南

## 概述

`AiServiceHelper` 是一个简化的AI服务调用工具类，使用信号量+线程池控制并发，参考了`WalmartTaskOffLinkListingRetireJobHandler`的实现方式。

## 核心特性

- 使用信号量控制并发数量
- 线程池管理异步执行
- 支持自定义threadNum参数（默认10）
- 简单易用的接口设计

## 使用方法

### 1. 基本用法

```java
@Component
public class MyJobHandler extends AbstractJobHandler {
    
    @Autowired
    private AiServiceHelper aiServiceHelper;
    
    @Data
    public static class InnerParam {
        private Integer threadNum = 10; // 线程数量，默认为10
        // 其他参数...
    }
    
    @Override
    @XxlJob("MyJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        // 解析参数
        InnerParam innerParam = parseParam(param);
        
        // 获取待处理数据
        List<MyData> dataList = getDataToProcess();
        
        try {
            // 批量调用腾讯AI服务
            aiServiceHelper.batchCallTencentAi(dataList, innerParam.getThreadNum(), 
                new AiServiceHelper.AiDataProcessor<MyData>() {
                    @Override
                    public ChatOpenaiRequest buildRequest(MyData data) {
                        // 构建AI请求
                        return ChatOpenaiRequest.builder()
                            .prompt(data.getPrompt())
                            .model("deepseek-r1")
                            .systemMessage("")
                            .options(new ChatOpenaiRequest.Options())
                            .build();
                    }
                    
                    @Override
                    public void handleResponse(MyData data, ApiResult<ChatCompletionResponse> response) {
                        if (response.isSuccess()) {
                            // 处理成功响应
                            saveResult(data, response.getResult());
                            XxlJobLogger.log("成功处理数据: {}", data.getId());
                        } else {
                            // 处理失败响应
                            XxlJobLogger.log("AI调用失败: {}", response.getErrorMsg());
                        }
                    }
                    
                    @Override
                    public void handleError(MyData data, Exception error) {
                        XxlJobLogger.log("处理数据 {} 时发生错误: {}", data.getId(), error.getMessage());
                    }
                });
            
            XxlJobLogger.log("批量AI服务调用完成");
            return ReturnT.SUCCESS;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            XxlJobLogger.log("任务被中断: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }
}
```

### 2. Lambda表达式简化写法

```java
// 使用Lambda表达式简化代码
aiServiceHelper.batchCallTencentAi(dataList, threadNum, 
    new AiServiceHelper.AiDataProcessor<MyData>() {
        @Override
        public ChatOpenaiRequest buildRequest(MyData data) {
            return buildAiRequest(data);
        }
        
        @Override
        public void handleResponse(MyData data, ApiResult<ChatCompletionResponse> response) {
            handleAiResponse(data, response);
        }
    });
```

### 3. 优化现有的Amazon AI任务

```java
@Component
public class AmazonNewProductCopywritingAiGenerateJob extends AbstractJobHandler {
    
    @Autowired
    private AiServiceHelper aiServiceHelper;
    
    @Autowired
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;
    
    // 其他依赖...
    
    @Override
    @XxlJob("AmazonNewProductCopywritingAiGenerateJob")
    public ReturnT<String> run(String param) throws Exception {
        // 解析参数
        InnerParam innerParam = parseParam(param);
        
        // 获取需要处理的产品列表
        List<AmazonMustPublishNewProduct> productList = getProductsToProcess(innerParam);
        
        if (CollectionUtils.isEmpty(productList)) {
            XxlJobLogger.log("未找到需要处理的产品数据");
            return ReturnT.SUCCESS;
        }
        
        XxlJobLogger.log("找到 {} 个产品需要生成AI标题", productList.size());
        
        try {
            // 使用优化后的AI服务调用
            aiServiceHelper.batchCallTencentAi(productList, innerParam.getThreadNum(),
                new AiServiceHelper.AiDataProcessor<AmazonMustPublishNewProduct>() {
                    @Override
                    public ChatOpenaiRequest buildRequest(AmazonMustPublishNewProduct product) {
                        return buildAiRequestForProduct(product);
                    }
                    
                    @Override
                    public void handleResponse(AmazonMustPublishNewProduct product, 
                                             ApiResult<ChatCompletionResponse> response) {
                        if (response.isSuccess()) {
                            // 解析AI响应并保存标题
                            List<String> generatedTitles = parseAiResponse(response.getResult());
                            saveGeneratedTitles(product.getSpu(), generatedTitles);
                            XxlJobLogger.log("成功处理产品 SPU: {}", product.getSpu());
                        } else {
                            XxlJobLogger.log("产品 {} AI调用失败: {}", product.getSpu(), response.getErrorMsg());
                        }
                    }
                    
                    @Override
                    public void handleError(AmazonMustPublishNewProduct product, Exception error) {
                        XxlJobLogger.log("处理产品 {} 时发生错误: {}", product.getSpu(), error.getMessage());
                    }
                });
            
            XxlJobLogger.log("Amazon新产品AI文案生成任务执行完成");
            return ReturnT.SUCCESS;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            XxlJobLogger.log("任务被中断: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }
    
    private ChatOpenaiRequest buildAiRequestForProduct(AmazonMustPublishNewProduct product) {
        // 获取Amazon文案数据
        OfficialAmazonResponse amazonData = getAmazonOfficialData(product.getSpu());
        ReviewBasisInfoVO vo = ReviewBasisInfoVO.transfer(amazonData);
        
        // 提取参考标题
        List<String> referenceTitles = extractReferenceTitles(vo);
        
        // 构建AI请求
        return buildAiRequest(referenceTitles, getInstructions());
    }
}
```

## 参数说明

### threadNum参数
- **类型**: Integer
- **默认值**: 10
- **说明**: 控制并发线程数量，同时也是信号量的许可数量
- **使用**: 参考WalmartTaskOffLinkListingRetireJobHandler的方式

## 实现原理

### 信号量控制
```java
// 控制线程池数量，创建Semaphore信号量
final Semaphore sp = new Semaphore(threadNum);

// 按数据循环处理
for (T data : dataList) {
    sp.acquire();  // 获取许可
    executor.execute(() -> {
        try {
            // 处理数据
            processData(data);
        } finally {
            sp.release();  // 释放许可
        }
    });
}
```

### 线程池管理
- 核心线程数: 10
- 最大线程数: 20
- 队列容量: 1000
- 拒绝策略: CallerRunsPolicy
- 自动关闭和资源清理

## 优势

1. **简单易用**: 接口简洁，易于集成到现有代码
2. **性能优化**: 使用线程池和信号量控制并发
3. **资源控制**: 防止过多并发调用导致系统过载
4. **异常安全**: 完善的异常处理和资源清理
5. **参考成熟方案**: 基于WalmartTaskOffLinkListingRetireJobHandler的成功实践

## 注意事项

1. **线程数量**: 根据AI服务承载能力合理设置threadNum
2. **异常处理**: 实现handleError方法处理异常情况
3. **资源清理**: 框架自动管理线程池生命周期
4. **中断处理**: 支持任务中断，正确处理InterruptedException
