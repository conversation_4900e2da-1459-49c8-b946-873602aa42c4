<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.feginService.mapper.SpuPublishMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.feginService.modle.SpuPublishRecord">
    </resultMap>

    <select id="getAmazonSpuList" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT(parent_SKU) from amazon_template_auto
        where is_lock is true
        and (is_delete is false or is_delete is null)
        and country = #{site}
        ;
    </select>

    <select id="getSmtSpuList" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT(article_number) from aliexpress_auto_template
        where is_parent is true;
    </select>

    <select id="getSmtTgSpuList" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT(article_number) from aliexpress_tg_template
        where is_parent is true;
    </select>

    <select id="getEbaySpuList" parameterType="java.lang.String" resultType="java.lang.String">
        select DISTINCT(article_number) from ebay_auto_template
        where is_parent is true
        and site = #{site}
    </select>


    <select id="getShopeeSpuList" parameterType="java.lang.String" resultType="java.lang.String">
        <choose>
            <when test="site == 'CNSC'">
                select distinct sku  from shopee_admin_global_template
            </when>
            <otherwise>
                select DISTINCT(sku) from shopee_admin_template_new
                where 1 = 1
                and site = #{site}
            </otherwise>
        </choose>
    </select>

    <!--获取smtspu刊登记录-->

    <select id="getSmtSpuRecordList" parameterType="java.util.List" resultMap="BaseResultMap">
        select
            'SMT' as saleChannel, t.aliexpress_account_number as account, t.creator as salesman,
            (case when t.template_status = 3 then 1 when t.template_status = 4 then 0 else 0 end) as publishStatus,
            t.create_time as publishDate
        from
            aliexpress_template t
            where t.is_parent is false
            and t.template_type = 3
            and t.template_status in (3,4)
            and t.article_number = #{articleNumber}
        order by t.create_time desc
    </select>
    <!--获取amazon spu刊登记录-->
    <select id="getAmazonSpuRecordList" parameterType="java.util.List" resultMap="BaseResultMap">
        select
            'Amazon' saleChannel,
            seller_id account,
            seller_sku sellerSku,
            created_by salesman,
            IF(publish_status = 8, 1, 0) publishStatus,
            creation_date publishDate
        from amazon_template
        where parent_SKU = #{articleNumber}
        and is_lock = 0
        <!--and publish_type = 2-->
        and publish_status in(8, 9)
        order by creation_date desc
    </select>

    <!--获取ebay spu刊登记录-->
    <select id="getEbaySpuRecordList" parameterType="java.util.List" resultMap="BaseResultMap">
        select
            'Ebay' as saleChannel, t.account_number as account, t.created_by as salesman,
            (case when t.template_status = 3 then 1 when t.template_status = 4 then 0 else 0 end) as publishStatus,
            t.create_date as publishDate
        from
            ebay_template t
            where t.is_parent is false
            and t.template_type  in (2,4)
            and t.template_status in (3,4)
            and t.article_number = #{articleNumber}
        order by t.create_date desc
    </select>

    <!--查询amazon spu生成范本的站点-->
    <select id="selectAmazonTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct parent_SKU spu, country site
        from amazon_template_auto
        where parent_SKU in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询ebay spu生成范本的站点-->
    <select id="selectEbayTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct article_number spu, site
        from ebay_auto_template
        where article_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询SMT spu生成范本的站点-->
    <select id="selectSmtTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct article_number spu, 'US' site
        from aliexpress_auto_template
        where article_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询shopee spu生成范本的站点-->
    <select id="selectShopeeTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct sku spu, site
        from shopee_admin_template_new
        where is_enabled = 1
        and sku in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询shopee spu 存在的范本-->
    <select id="selectShopeeGlobalTemplateSiteBySpu" resultType="java.lang.String">
        select distinct sku
        from shopee_admin_global_template
        where is_enabled = 1
        and sku in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectOzonTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct article_number spu, 'RU' site
        from ozon_admin_template
        where article_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTiktokTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct article_number spu, success_account site
        from tiktok_admin_template
        where article_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectWalmartTemplateSiteBySpu" resultType="com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo">
        select distinct article_number spu, 'US' site
        from publish_tidb.walmart_admin_template
        where article_number in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
