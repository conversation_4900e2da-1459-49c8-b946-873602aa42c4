package com.estone.erp.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.ERPInvoker;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/***
 * 公共工具类
 * 
 * <AUTHOR>
 *
 */
public class CommonUtils {
    public static final Random random = new Random();

    /**
     * 批量分页处理集合
     *
     * @param list 集合
     * @param batchSize 批量大小
     * @param consumer 处理函数
     */
    public static <E> void batchResolve(List<E> list, int batchSize, Consumer<List<E>> consumer) {
        if (CollectionUtils.isEmpty(list) || batchSize <= 0 || consumer == null) {
            return;
        }

        int size = list.size();
        if (size <= batchSize) {
            consumer.accept(list);
            return;
        }

        int page = (size - 1) / batchSize + 1;
        int start = 0;
        int end = 0;
        for (int i = 1; i <= page; i++) {
            end = start + (i == page ? (size - batchSize * (i - 1)) : batchSize);
            consumer.accept(list.subList(start, end));
            start = end;
        }
    }

    /**
     * 转换List集合模型
     * 
     * @param list
     * @param resolve
     * @return
     */
    public static <T, R> List<R> transfer(List<T> list, Function<T, R> resolve) {
        if (CollectionUtils.isEmpty(list) || resolve == null) {
            return emptyList();
        }

        List<R> result = new ArrayList<>(list.size());
        for (T t : list) {
            result.add(resolve.apply(t));
        }

        return result;
    }

    /**
     * 通过stream-流方式转换List集合模型
     * 
     * @param list
     * @param resolve
     * @return
     */
    public static <T, R> List<R> transferByStream(List<T> list, Function<T, R> resolve) {
        if (CollectionUtils.isEmpty(list) || resolve == null) {
            return emptyList();
        }

        List<R> result = list.stream().map(resolve).collect(Collectors.toList());
        return result;
    }

    /**
     * 转换Set集合模型
     * 
     * @param set
     * @param resolve
     * @return
     */
    public static <T, R> Set<R> transfer(Set<T> set, Function<T, R> resolve) {
        if (CollectionUtils.isEmpty(set) || resolve == null) {
            return emptySet();
        }

        Set<R> result = new HashSet<>(set.size());
        for (T t : set) {
            result.add(resolve.apply(t));
        }

        return result;
    }

    /**
     * 通过stream-流方式转换Set集合模型
     * 
     * @param set
     * @param resolve
     * @return
     */
    public static <T, R> Set<R> transferByStream(Set<T> set, Function<T, R> resolve) {
        if (CollectionUtils.isEmpty(set) || resolve == null) {
            return emptySet();
        }

        Set<R> result = set.stream().map(resolve).collect(Collectors.toSet());
        return result;
    }

    /**
     * 将map转换为指定泛型结构的map
     * 
     * @param map
     * @param keyFun key转换函数
     * @param valueFun value转换函数
     * @return
     */
    public static <T, R, K, V> Map<K, V> transfer(Map<T, R> map, Function<T, K> keyFun, Function<R, V> valueFun) {
        if (MapUtils.isEmpty(map) || keyFun == null || valueFun == null) {
            return emptyMap();
        }

        Map<K, V> result = new HashMap<>(map.size());
        map.forEach((key, value) -> {
            result.put(keyFun.apply(key), valueFun.apply(value));
        });

        return result;
    }

    /**
     * 根据classPath获取Resource数组
     * 
     * @param classPaths classPath路径
     * @return Resource数组
     */
    public static Resource[] getResouces(String[] classPaths) {
        if (ArrayUtils.isEmpty(classPaths)) {
            return new Resource[0];
        }

        PathMatchingResourcePatternResolver pathResolver = new PathMatchingResourcePatternResolver();
        List<Resource> resources = new ArrayList<>();
        for (String path : classPaths) {
            try {
                Resource[] rcss = pathResolver.getResources(path);
                for (Resource resource : rcss) {
                    resources.add(resource);
                }
            }
            catch (IOException e) {
            }
        }

        return resources.toArray(new Resource[resources.size()]);
    }

    /**
     * 克隆对象
     * 
     * @param t 要复制的对象
     * @return 新对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T clone(T t) {
        return (T) JSON.parseObject(JSON.toJSONString(t), t.getClass());
    }

    /**
     * 克隆对象
     * 
     * @param t 要复制的对象
     * @param typeRefer 对象类型引用
     * @return 新对象
     */
    public static <T> T clone(T t, TypeReference<T> typeRefer) {
        return JSON.parseObject(JSON.toJSONString(t), typeRefer);
    }

    /**
     * 计算方法执行时间
     * 
     * @param
     * 
     * @param invoker
     * @return
     */
    public static long calcExeTime(ERPInvoker invoker) {
        if (invoker == null) {
            return 0;
        }

        long start = System.currentTimeMillis();
        invoker.invoke();
        return System.currentTimeMillis() - start;
    }

    /**
     * 返回空List
     *
     * @return
     */
    public static <T> List<T> emptyList() {
        return new ArrayList<>(0);
    }

    /**
     * 返回空Set
     *
     * @return
     */
    public static <T> Set<T> emptySet() {
        return new HashSet<>(0);
    }

    /**
     * 返回空Map
     *
     * @return
     */
    public static <K, V> Map<K, V> emptyMap() {
        return new HashMap<>(0);
    }

    @SafeVarargs
    public static <T> List<T> arrayAsList(T... array) {
        if (ArrayUtils.isEmpty(array)) {
            return emptyList();
        }

        List<T> result = new ArrayList<>(array.length);
        for (int i = 0; i < array.length; i++) {
            result.add(array[i]);
        }

        return result;
    }

    /**
     * 格式化字符串
     * 
     * @param format
     * @param args 参数
     * @return value
     */
    public static String format(String format, Object... args) {
        if (StringUtils.isEmpty(format) || ArrayUtils.isEmpty(args)) {
            return format;
        }

        return MessageFormatter.arrayFormat(format, args).getMessage();
    }

    public static boolean containsIgnoreCase(Collection<String> collection, String value) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(collection) || org.apache.commons.lang.StringUtils.isEmpty(value)) {
            return false;
        }

        for (String item : collection) {
            if (value.equalsIgnoreCase(item)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 拆分为set
     * @param str
     * @param separator
     * @return
     */
    public static Set<String> splitSet(String str, String separator) {
        Set<String> stringSet = new HashSet<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                String trim = StringUtils.trim(split[i]);
                if (StringUtils.isNotBlank(trim)) {
                    stringSet.add(trim);
                }
            }
        }

        return stringSet;
    }

    /**
     * 拆分为list
     *
     * @param str 要拆分的字符串
     * @param separator 分隔符
     * @return
     */
    public static List<String> splitList(String str, String separator) {
        List<String> stringList = new ArrayList<String>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                String trim = StringUtils.trim(split[i]);
                if (StringUtils.isNotBlank(trim) && !stringList.contains(trim)) {
                    stringList.add(trim);
                }
            }
        }

        return stringList;
    }

    /**
     * 拆分为list 元素不去重
     *
     * @param str 要拆分的字符串
     * @param separator 分隔符
     * @return
     */
    public static List<String> splitNotDistinctList(String str, String separator) {
        List<String> stringList = new ArrayList<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                String trim = StringUtils.trim(split[i]);
                if (StringUtils.isNotBlank(trim)) {
                    stringList.add(trim);
                }
            }
        }

        return stringList;
    }

    /**
     * 拆分为list
     *
     * @param str 要拆分的字符串
     * @param separator 分隔符
     * @return
     */
    public static List<Integer> splitIntList(String str, String separator) {
        Set<Integer> intergerList = new HashSet<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                if (StringUtils.isNotBlank(split[i])) {
                    intergerList.add(Integer.valueOf(StringUtils.trim(split[i])));
                }
            }
        }

        return new ArrayList<>(intergerList);
    }

    /**
     * 拆分为list 按按原顺序不去重
     *
     * @param str 要拆分的字符串
     * @param separator 分隔符
     * @return
     */
    public static List<Integer> splitSortIntList(String str, String separator) {
        List<Integer> intergerList = new ArrayList<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                if (StringUtils.isNotBlank(split[i])) {
                    intergerList.add(Integer.valueOf(StringUtils.trim(split[i])));
                }
            }
        }

        return intergerList;
    }

    public static File createFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            if (file.isFile()) {
                file.createNewFile();
            }
        }

        return file;
    }

    /**
     * 拆分为list
     *
     * @param str 要拆分的字符串
     * @param separator 分隔符
     * @return
     */
    public static List<Long> splitLongList(String str, String separator) {
        Set<Long> longList = new HashSet<>();
        if (StringUtils.isNotBlank(str)) {
            String[] split = StringUtils.split(str, separator);

            for (int i = 0, len = split.length; i < len; i++) {
                if (StringUtils.isNotBlank(split[i])) {
                    longList.add(Long.valueOf(StringUtils.trim(split[i])));
                }
            }
        }
        return new ArrayList<>(longList);
    }

    /**
     * 合并多个map
     * @param maps
     * @param <K>
     * @param <V>
     * @return
     * @throws Exception
     */
    public static <K, V> Map<K, V> mergeMaps(List<Map<K, V>> maps) {
        if (maps == null || maps.isEmpty()) {
            return new HashMap<>();
        }

        // 获取第一个 Map 的类型，并尝试创建相同类型的新实例
        Map<K, V> result;
        try {
            result = maps.get(0).getClass().getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            // 如果创建失败，默认使用 HashMap
            result = new HashMap<>();
        }

        // 合并所有 Map
        for (Map<K, V> map : maps) {
            if (map != null) {
                result.putAll(map);
            }
        }
        return result;
    }

    /**
     * 比较list内容是否完全相同
     * @param list1
     * @param list2
     * @return
     */
    public static boolean listCompare(List<?> list1, List<?> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return true;
        }

        if ((CollectionUtils.isEmpty(list1) && !CollectionUtils.isEmpty(list2)) || (!CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2))) {
            return false;
        }

        if (list1.size() != list2.size()) {
            return false;
        }

        return list1.stream().allMatch(e -> list2.contains(e));
    }

    /**
     * 比较set内容是否完全相同
     * @param set1
     * @param set2
     * @return
     */
    public static boolean setCompare(Set<?> set1, Set<?> set2) {
        if (CollectionUtils.isEmpty(set1) && CollectionUtils.isEmpty(set2)) {
            return true;
        }

        if ((CollectionUtils.isEmpty(set1) && !CollectionUtils.isEmpty(set2)) || (!CollectionUtils.isEmpty(set1) && CollectionUtils.isEmpty(set2))) {
            return false;
        }

        if (set1.size() != set2.size()) {
            return false;
        }

        return set1.stream().allMatch(e -> set2.contains(e));
    }


    public static Double transferNumericCellValue(Cell cell, Double defaultValue) {
        if (cell == null) {
            return defaultValue;
        }

        try {
            Double value = null;

            switch (cell.getCellType()) {
                case NUMERIC:
                    value = cell.getNumericCellValue();
                    break;

                case STRING:
                    String str = cell.getStringCellValue().trim();
                    if (StringUtils.isBlank(str)) {
                        return defaultValue;
                    }
                    value = Double.valueOf(str);
                    break;

                case FORMULA:
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook()
                            .getCreationHelper().createFormulaEvaluator();
                    CellValue evaluatedValue = evaluator.evaluate(cell);
                    if (evaluatedValue != null && evaluatedValue.getCellType() == CellType.NUMERIC) {
                        value = evaluatedValue.getNumberValue();
                    }
                    break;

                default:
                    return defaultValue;
            }

            if (value == null) {
                return defaultValue;
            }

            DecimalFormat df = new DecimalFormat("0.00");
            return Double.valueOf(df.format(value).trim());

        } catch (Exception e) {
            return defaultValue;
        }
    }
}
