package com.estone.erp.publish.system.ai;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.ai.bean.AiServiceResult;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * AI服务调用工具类 - 提供便捷的AI服务调用方法
 * 参考WalmartTaskOffLinkListingRetireJobHandler的信号量使用方式
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-16
 */
@Slf4j
@Component
public class AiServiceUtils {

    @Autowired
    private AiServiceExecutor aiServiceExecutor;

    /**
     * 批量处理数据并调用AI服务
     * 
     * @param dataList 待处理的数据列表
     * @param threadNum 线程数量，默认为10
     * @param requestBuilder 将数据转换为AI请求的函数
     * @param responseHandler 处理AI响应的函数
     * @param <T> 数据类型
     * @return 处理结果
     * @throws InterruptedException 中断异常
     */
    public <T> AiServiceResult<ChatCompletionResponse> batchProcessWithTencentAi(
            List<T> dataList,
            Integer threadNum,
            Function<T, ChatOpenaiRequest> requestBuilder,
            Consumer<ApiResult<ChatCompletionResponse>> responseHandler) throws InterruptedException {
        
        if (dataList == null || dataList.isEmpty()) {
            return new AiServiceResult<>(0, 0, new ArrayList<>(), new ArrayList<>(), 0L);
        }

        long startTime = System.currentTimeMillis();
        
        // 使用传入的线程数量，如果为空则使用默认值10
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;
        
        // 创建信号量控制并发速率
        final Semaphore semaphore = new Semaphore(actualThreadNum);
        
        List<ChatCompletionResponse> successResults = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        
        log.info("开始批量处理数据并调用腾讯AI服务，数据数量：{}，并发线程数：{}", dataList.size(), actualThreadNum);
        
        // 为每个数据项创建AI请求并处理
        for (T data : dataList) {
            try {
                // 获取信号量许可
                semaphore.acquire();
                
                try {
                    // 构建AI请求
                    ChatOpenaiRequest request = requestBuilder.apply(data);
                    
                    // 调用AI服务
                    ApiResult<ChatCompletionResponse> result = 
                            aiServiceExecutor.callTencentAiWithRateLimit(request, actualThreadNum);
                    
                    // 处理响应
                    if (responseHandler != null) {
                        responseHandler.accept(result);
                    }
                    
                    // 收集结果
                    if (result.isSuccess()) {
                        successResults.add(result.getResult());
                        successCount++;
                    } else {
                        errorMessages.add(result.getErrorMsg());
                        failCount++;
                    }
                    
                } finally {
                    // 释放信号量许可
                    semaphore.release();
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("AI服务调用被中断", e);
                errorMessages.add("AI服务调用被中断: " + e.getMessage());
                failCount++;
                break;
            } catch (Exception e) {
                log.error("处理数据时发生异常", e);
                errorMessages.add("处理数据异常: " + e.getMessage());
                failCount++;
            }
        }
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        AiServiceResult<ChatCompletionResponse> result = new AiServiceResult<>(
                successCount, failCount, successResults, errorMessages, executionTime);
        
        log.info("批量AI服务调用完成，{}", result.getSummary());
        
        return result;
    }

    /**
     * 批量处理数据并调用OpenAI服务
     * 
     * @param dataList 待处理的数据列表
     * @param threadNum 线程数量，默认为10
     * @param requestBuilder 将数据转换为AI请求的函数
     * @param responseHandler 处理AI响应的函数
     * @param <T> 数据类型
     * @return 处理结果
     * @throws InterruptedException 中断异常
     */
    public <T> AiServiceResult<ChatCompletionResponse> batchProcessWithOpenAi(
            List<T> dataList,
            Integer threadNum,
            Function<T, ChatOpenaiRequest> requestBuilder,
            Consumer<ApiResult<ChatCompletionResponse>> responseHandler) throws InterruptedException {
        
        if (dataList == null || dataList.isEmpty()) {
            return new AiServiceResult<>(0, 0, new ArrayList<>(), new ArrayList<>(), 0L);
        }

        long startTime = System.currentTimeMillis();
        
        // 使用传入的线程数量，如果为空则使用默认值10
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;
        
        // 创建信号量控制并发速率
        final Semaphore semaphore = new Semaphore(actualThreadNum);
        
        List<ChatCompletionResponse> successResults = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        
        log.info("开始批量处理数据并调用OpenAI服务，数据数量：{}，并发线程数：{}", dataList.size(), actualThreadNum);
        
        // 为每个数据项创建AI请求并处理
        for (T data : dataList) {
            try {
                // 获取信号量许可
                semaphore.acquire();
                
                try {
                    // 构建AI请求
                    ChatOpenaiRequest request = requestBuilder.apply(data);
                    
                    // 调用AI服务
                    ApiResult<ChatCompletionResponse> result = 
                            aiServiceExecutor.callOpenAiWithRateLimit(request, actualThreadNum);
                    
                    // 处理响应
                    if (responseHandler != null) {
                        responseHandler.accept(result);
                    }
                    
                    // 收集结果
                    if (result.isSuccess()) {
                        successResults.add(result.getResult());
                        successCount++;
                    } else {
                        errorMessages.add(result.getErrorMsg());
                        failCount++;
                    }
                    
                } finally {
                    // 释放信号量许可
                    semaphore.release();
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("AI服务调用被中断", e);
                errorMessages.add("AI服务调用被中断: " + e.getMessage());
                failCount++;
                break;
            } catch (Exception e) {
                log.error("处理数据时发生异常", e);
                errorMessages.add("处理数据异常: " + e.getMessage());
                failCount++;
            }
        }
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        AiServiceResult<ChatCompletionResponse> result = new AiServiceResult<>(
                successCount, failCount, successResults, errorMessages, executionTime);
        
        log.info("批量AI服务调用完成，{}", result.getSummary());
        
        return result;
    }

    /**
     * 简化的批量腾讯AI调用方法
     * 
     * @param requests AI请求列表
     * @param threadNum 线程数量，默认为10
     * @return 处理结果
     * @throws InterruptedException 中断异常
     */
    public AiServiceResult<ChatCompletionResponse> batchCallTencentAi(
            List<ChatOpenaiRequest> requests, Integer threadNum) throws InterruptedException {
        
        return batchProcessWithTencentAi(
                requests,
                threadNum,
                request -> request, // 直接返回请求对象
                null // 不需要额外的响应处理
        );
    }

    /**
     * 简化的批量OpenAI调用方法
     * 
     * @param requests AI请求列表
     * @param threadNum 线程数量，默认为10
     * @return 处理结果
     * @throws InterruptedException 中断异常
     */
    public AiServiceResult<ChatCompletionResponse> batchCallOpenAi(
            List<ChatOpenaiRequest> requests, Integer threadNum) throws InterruptedException {
        
        return batchProcessWithOpenAi(
                requests,
                threadNum,
                request -> request, // 直接返回请求对象
                null // 不需要额外的响应处理
        );
    }

    /**
     * 获取AI服务执行器的线程池状态
     * 
     * @return 线程池状态描述
     */
    public String getAiServiceStatus() {
        return aiServiceExecutor.getThreadPoolStatus();
    }
}
