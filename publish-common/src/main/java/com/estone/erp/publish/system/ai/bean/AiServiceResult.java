package com.estone.erp.publish.system.ai.bean;

import lombok.Data;

import java.util.List;

/**
 * AI服务调用结果包装类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-16
 */
@Data
public class AiServiceResult<T> {
    
    /**
     * 成功处理的数量
     */
    private int successCount;
    
    /**
     * 失败处理的数量
     */
    private int failCount;
    
    /**
     * 总处理数量
     */
    private int totalCount;
    
    /**
     * 成功的结果列表
     */
    private List<T> successResults;
    
    /**
     * 失败的错误信息列表
     */
    private List<String> errorMessages;
    
    /**
     * 执行耗时（毫秒）
     */
    private long executionTime;
    
    /**
     * 是否全部成功
     */
    private boolean allSuccess;
    
    /**
     * 构造函数
     */
    public AiServiceResult() {
        this.allSuccess = false;
    }
    
    /**
     * 构造函数
     * 
     * @param successCount 成功数量
     * @param failCount 失败数量
     * @param successResults 成功结果列表
     * @param errorMessages 错误信息列表
     * @param executionTime 执行耗时
     */
    public AiServiceResult(int successCount, int failCount, List<T> successResults, 
                          List<String> errorMessages, long executionTime) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.totalCount = successCount + failCount;
        this.successResults = successResults;
        this.errorMessages = errorMessages;
        this.executionTime = executionTime;
        this.allSuccess = failCount == 0;
    }
    
    /**
     * 获取成功率
     * 
     * @return 成功率（百分比）
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) successCount / totalCount * 100;
    }
    
    /**
     * 获取执行摘要
     * 
     * @return 执行摘要字符串
     */
    public String getSummary() {
        return String.format("总数: %d, 成功: %d, 失败: %d, 成功率: %.2f%%, 耗时: %dms",
                totalCount, successCount, failCount, getSuccessRate(), executionTime);
    }
}
