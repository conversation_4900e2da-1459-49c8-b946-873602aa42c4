package com.estone.erp.publish.system.newUsermgt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.usermgt_n.EmployeeInfo;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch.model.beanrequest.AuthorizedAccountRequest;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.lang.Tuple5;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: publish
 * @description:
 * @author: lf
 * @create: 2021-07-27
 **/
@Slf4j
@Component
public class NewUsermgtUtils {
    private static NewUsermgtClient newUsermgtClient;

    /**
     * 需要添加 “平台” 后缀的平台
     */
    private static final List<String> SPECIAL_PLATFORMS = Lists.newArrayList(SaleChannel.CHANNEL_AMAZON,
            SaleChannel.CHANNEL_SMT, SaleChannel.CHANNEL_SHOPEE, SaleChannel.CHANNEL_EBAY,
            SaleChannel.CHANNEL_WISH, SaleChannel.CHANNEL_JOOM, SaleChannel.CHANNEL_LAZADA,
            SaleChannel.CHANNEL_WALMART, SaleChannel.CHANNEL_MERCADO, SaleChannel.CHANNEL_FRUUGO,
            SaleChannel.CHANNEL_COUPANG, SaleChannel.CHANNEL_B2W, SaleChannel.CHANNEL_JDWALMART,
            SaleChannel.CHANNEL_NOCNOC, SaleChannel.CHANNEL_OZON, SaleChannel.CHANNEL_HEPSIBURADA);


    @PostConstruct
    private void init() {
        try {
            newUsermgtClient = SpringUtils.getBean(NewUsermgtClient.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 根据token获取用户信息
     *
     * @return
     */
    public static ApiResult<NewUser> tokenUser() {
        //获取token
//        String accessToken = WebUtils.getAccessToken();
//        try {
//            //因为用户系统那边查不到token是向上抛异常，所以需要捕获异常,如果发生异常就提示token失效
//            return newUsermgtClient.tokenUser(accessToken);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(e.getMessage());
//            return ApiResult.newError("用户未登录或者token失效");
//        }
        //获取账户
        String userName = WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            userName = DataContextHolder.getUsername();
        }
        if (StringUtils.isBlank(userName)) {
            return ApiResult.newError("获取账号失败，请检查用户是否登录！");
        }
        //获取token，登出则返回null
        Boolean isLogin = PublishRedisClusterUtils.hExists(ErpUsermgtNRedisConStant.GET_TOKEN_BY_EMPLOYEE_NO, userName);
        if (BooleanUtils.isFalse(isLogin)) {
            return ApiResult.newError("获取token失败，请检查用户是否登录！");
        }
        //获取登录用户信息
        NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
        }, userName);
        if (newUser == null) {
            return ApiResult.newError("获取用户信息失败，请检查用户是否登录！");
        }
//        //查询用户角色列表
//        List<String> roleList = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_ROLE_NAMES_BY_EMPLOYEE_NO, new TypeReference<List<String>>() {
//        }, userName);
//        newUser.setRsRoleNames(roleList);
        return ApiResult.newSuccess(newUser);
    }

    /**
     * 判断角色是否是超管
     * 或某平台助理
     *
     * @param roleName：角色名称
     * @param saleChannel：平台
     * @return
     */
    private static Boolean isSuperAdminOrEquivalent(String roleName, String saleChannel) {
        if (StringUtils.isBlank(roleName)) {
            return false;
        }
        //判断是否超管或某平台助理
        return isAdmin(roleName) || isAssistant(roleName, saleChannel);
    }

    /**
     * 检查给定的角色名称是否是指定销售渠道的助理角色。
     *
     * @param roleName
     * @param saleChannel
     * @return
     */
    private static Boolean isAssistant(String roleName, String saleChannel) {
        if (StringUtils.isBlank(roleName) || StringUtils.isBlank(saleChannel)) {
            return false;
        }

        if (!StringUtils.startsWith(roleName, RoleConstant.ASSISTANT)) {
            return false;
        }

        int start = roleName.indexOf("（");
        int end = roleName.indexOf("）");
        if (start == -1 || end == -1) {
            start = roleName.indexOf("(");
            end = roleName.indexOf(")");
        }
        if (start == -1 || end == -1) {
            return false;
        }

        String platform = roleName.substring(start + 1, end);
        return StringUtils.equalsIgnoreCase(platform, saleChannel);
    }


    /**
     * 判断是否是超管
     * 或某平台助理（因为助理也拥有所有的数据权限）
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static ApiResult<Boolean> isSuperAdminOrEquivalent(String saleChannel) {
        Boolean isSuperAdminOrEquivalent = false;
        try {
            // shopee平台，数据分析部门能看到全部数据不用调整。其他平台，数据分析部门只能看到自己关联店铺的数据
            if (SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(saleChannel)) {
                Boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
                if (BooleanUtils.isTrue(dataSupportDepartment)) {
                    return ApiResult.newSuccess(dataSupportDepartment);
                }
            }

            ApiResult<?> tokenUser = tokenUser();
            if (!tokenUser.isSuccess()) {
                return ApiResult.newError("调用用户系统异常信息:" + tokenUser.getErrorMsg());
            }
            if (StringUtils.isBlank(saleChannel)) {
                return ApiResult.newSuccess(isSuperAdminOrEquivalent);
            }
            if (tokenUser != null && tokenUser.isSuccess()) {
                NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
                //如果职位就是超管或某平台助理那就直接返回
                if (isSuperAdminOrEquivalent(newUser.getPositionName(), saleChannel)) {
                    isSuperAdminOrEquivalent = true;
                }
                //如果职位不是，则判断权限列表中是否是超管或某平台助理
                else {
                    List<String> roleIdAndRoleName = newUser.getRsRoleNames();
                    if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                        //循环判断权限列表
                        for (String roleName : roleIdAndRoleName) {
                            //判断是否超管或某平台助理
                            if (isSuperAdminOrEquivalent(roleName, saleChannel)) {
                                isSuperAdminOrEquivalent = true;
                                break;
                            }
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(isSuperAdminOrEquivalent);
    }

    /**
     * 判断是否是超管
     * 或者某平台最高权限者
     * 或某平台助理（因为助理也拥有所有的数据权限）
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static Boolean isSuperAdminOrEquivalent(NewUser newUser, String saleChannel) {
        Boolean isSuperAdminOrEquivalent = false;
        //如果职位就是超管或某平台助理那就直接返回
        if (isSuperAdminOrEquivalent(newUser.getPositionName(), saleChannel)) {
            isSuperAdminOrEquivalent = true;
        }
        //如果职位不是，则判断是否超管或某平台助理
        else {
            List<String> roleIdAndRoleName = newUser.getRsRoleNames();
            if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                //循环判断权限列表
                for (String roleName : roleIdAndRoleName) {
                    //判断是否超管或某平台助理
                    if (isSuperAdminOrEquivalent(roleName, saleChannel)) {
                        isSuperAdminOrEquivalent = true;
                        break;
                    }
                }
            }

        }
        return isSuperAdminOrEquivalent;
    }

    /**
     * 判断是否是超管
     * 或者某平台最高权限者
     * 或某平台助理（因为助理也拥有所有的数据权限）
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static ApiResult<Boolean> isSuperAdminOrSupervisor(String saleChannel) {
        Boolean isSuperAdminOrSupervisor = false;
        try {
            ApiResult<?> tokenUser = tokenUser();
            if (!tokenUser.isSuccess()) {
                return ApiResult.newError("调用用户系统异常信息:" + tokenUser.getErrorMsg());
            }
            if (StringUtils.isBlank(saleChannel)) {
                return ApiResult.newSuccess(isSuperAdminOrSupervisor);
            }
            if (tokenUser != null && tokenUser.isSuccess()) {
                NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
                //如果职位就是主管或超管那就直接返回
                if (isRole(newUser.getPositionName(), saleChannel)) {
                    isSuperAdminOrSupervisor = true;
                }
                //如果职位不是，则判断权限列表中是否是超管或者主管
                else {
                    List<String> roleIdAndRoleName = newUser.getRsRoleNames();
                    if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                        //循环判断权限列表
                        for (String roleName : roleIdAndRoleName) {
                            //判断是否超管，或某平台主管
                            if (isRole(roleName, saleChannel)) {
                                isSuperAdminOrSupervisor = true;
                                break;
                            }
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(isSuperAdminOrSupervisor);
    }

    /**
     * 判断是否是超管
     * 或者某平台最高权限者
     * 或某平台助理（因为助理也拥有所有的数据权限）
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static Boolean isSuperAdminOrSupervisor(NewUser newUser, String saleChannel) {
        Boolean isSuperAdminOrSupervisor = false;
        //如果职位就是主管或超管那就直接返回
        if (isRole(newUser.getPositionName(), saleChannel)) {
            isSuperAdminOrSupervisor = true;
        }
        //如果职位不是，则判断权限列表中是否是超管或者主管
        else {
            List<String> roleIdAndRoleName = newUser.getRsRoleNames();
            if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                //循环判断权限列表
                for (String roleName : roleIdAndRoleName) {
                    //判断是否超管，或某平台主管
                    if (isRole(roleName, saleChannel)) {
                        isSuperAdminOrSupervisor = true;
                        break;
                    }
                }
            }

        }
        return isSuperAdminOrSupervisor;
    }

    /**
     * 判断是否是超管
     * 或者平台是否相符
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static Boolean isSuperAdminOrPlatform(NewUser newUser, String saleChannel) {
        //判断是否超管
        if (isAdmin(newUser.getPositionName())) {
            return true;
        } else {
            List<String> roleIdAndRoleName = newUser.getRsRoleNames();
            if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                //循环判断权限列表
                for (String roleName : roleIdAndRoleName) {
                    //判断是否超管
                    if (isAdmin(roleName)) {
                        return true;
                    }
                }
            }
        }
        //如果不是超管就要判断是否包含平台
        if (StringUtils.containsIgnoreCase(newUser.getPositionName(), saleChannel)) {
            return true;
        } else {
            List<String> roleIdAndRoleName = newUser.getRsRoleNames();
            if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                //循环判断权限列表
                for (String roleName : roleIdAndRoleName) {
                    //判断是否超管
                    if (StringUtils.containsIgnoreCase(roleName, saleChannel)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 判断角色是否是超管
     * 或某平台最高权限者
     * 或某平台助理（因为助理也拥有所有的数据权限）
     * 或编辑主管
     *
     * @param roleName：角色名称
     * @param saleChannel：平台
     * @return
     */
    private static Boolean isRole(String roleName, String saleChannel) {
        if (StringUtils.isBlank(roleName)) {
            return false;
        }
        //判断是否超管
        if (isAdmin(roleName)
                //或某平台最高权限者
                || (isSupremePermissions(roleName, saleChannel))
                //或某平台助理(不包含EBAY)
                || (!SaleChannel.CHANNEL_EBAY.equals(saleChannel) && StringUtils.containsIgnoreCase(roleName, saleChannel) && StringUtils.containsIgnoreCase(roleName, RoleConstant.ASSISTANT))
                || StringUtils.containsIgnoreCase(roleName, RoleConstant.EDIT_SUPERVISOR)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否最高权限者
     *
     * @param roleName
     * @param saleChannel
     * @return
     */
    private static Boolean isSupremePermissions(String roleName, String saleChannel) {
        if (StringUtils.containsIgnoreCase(roleName, RoleConstant.KNOWLEDGE_SUPERVISOR)
                || StringUtils.containsIgnoreCase(roleName, RoleConstant.DEVELOPMENT_MANAGER)
        ) {
            return true;
        }
        switch (saleChannel) {
            case SaleChannel.CHANNEL_AMAZON:
            case SaleChannel.CHANNEL_SMT:
            case SaleChannel.CHANNEL_EBAY:
            case SaleChannel.CHANNEL_SHOPIFY:
                //大主管
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel))
                    return true;
                break;
            case SaleChannel.CHANNEL_WISH:
            case SaleChannel.CHANNEL_SHOPEE:
                //中级主管
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.MIDDLE_SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel))
                    return true;
                break;
            case SaleChannel.CHANNEL_LAZADA:
            case SaleChannel.CHANNEL_WALMART:
            case SaleChannel.CHANNEL_COUPANG:
            case SaleChannel.CHANNEL_FRUUGO:
            case SaleChannel.CHANNEL_B2W:
            case SaleChannel.CHANNEL_JDWALMART:
            case SaleChannel.CHANNEL_NOCNOC:
            case SaleChannel.CHANNEL_MERCADOLIBRE:
                //初级主管
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.PRIMARY_SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel))
                    return true;
                break;
            case SaleChannel.CHANNEL_JOOM:
                //组长/储备组长
                if ((StringUtils.containsIgnoreCase(roleName, RoleConstant.GROUP_LEADER) || StringUtils.containsIgnoreCase(roleName, RoleConstant.RESERVE_GROUP_LEADER)) && StringUtils.containsIgnoreCase(roleName, saleChannel))
                    return true;
                break;
            case SaleChannel.CHANNEL_OZON: // 主管也不可以直接查所有
            case SaleChannel.CHANNEL_TIKTOK:
                return false;
            default:
                //大主管
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel)) return true;
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.MIDDLE_SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel)) return true;
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.PRIMARY_SUPERVISOR) && StringUtils.containsIgnoreCase(roleName, saleChannel)) return true;
                break;
        }
        return false;
    }

    /**
     * 判断职位/角色是否超管
     *
     * @param roleName
     * @return
     */
    private static Boolean isAdmin(String roleName) {
        //判断是否超管
        if (RoleConstant.SUPER_ADMIN.equals(roleName) || RoleConstant.PUBLISH_SUPER_ADMIN.equals(roleName)) {
            return true;
        }
        return false;
    }

    /**
     * 是否数据分析组角色
     *
     * @param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2024/6/18 15:38
     */
    public static Boolean isDataSupportDepartment() {
        ApiResult<?> tokenUser = tokenUser();
        if (tokenUser == null || !tokenUser.isSuccess()) {
            throw new BusinessException("调用用户系统异常信息:" + tokenUser.getErrorMsg());
        }
        NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);

        List<String> roleIdAndRoleName = newUser.getRsRoleNames();

        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            //循环判断权限列表
            for (String roleName : roleIdAndRoleName) {
                //判断是否数据分析组
                if (RoleConstant.DATA_SUPPORT_DEPARTMENT.equals(roleName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 是否超管
     *
     * @return
     */
    public static ApiResult<Boolean> isSuperAdmin() {
        ApiResult<?> tokenUser = tokenUser();
        if (!tokenUser.isSuccess()) {
            return ApiResult.newError("调用用户系统异常信息:" + tokenUser.getErrorMsg());
        }
        Boolean isSuperAdmin = false;
        if (tokenUser != null && tokenUser.isSuccess()) {
            NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
            //如果职位就是超管那就直接返回
            String positionName = newUser.getPositionName();
            if (StringUtils.isNotBlank(positionName) && isAdmin(positionName)) {
                isSuperAdmin = true;
            }
            //如果职位不是，则判断权限列表中是否是超管
            else {
                List<String> roleIdAndRoleName = newUser.getRsRoleNames();
                if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                    //循环判断权限列表
                    for (String roleName : roleIdAndRoleName) {
                        //判断是否超管
                        if (isAdmin(roleName)) {
                            isSuperAdmin = true;
                            break;
                        }
                    }
                }
            }
        }

        return ApiResult.newSuccess(isSuperAdmin);
    }

    /**
     * 是否超管
     *
     * @return
     */
    public static Boolean isSuperAdmin(NewUser newUser) {
        Boolean isSuperAdmin = false;
        //如果职位就是主管或超管那就直接返回
        String positionName = newUser.getPositionName();
        if (StringUtils.isNotBlank(positionName) && isAdmin(positionName)) {
            isSuperAdmin = true;
        }
        //如果职位不是，则判断权限列表中是否是超管
        else {
            List<String> roleIdAndRoleName = newUser.getRsRoleNames();
            if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
                //循环判断权限列表
                for (String roleName : roleIdAndRoleName) {
                    //判断是否超管，或某平台主管
                    if (isAdmin(roleName)) {
                        isSuperAdmin = true;
                        break;
                    }
                }
            }
        }
        return isSuperAdmin;
    }

    /**
     * 判断是否是超管
     * 或者某平台大主管
     */
    public static ApiResult<Boolean> isSuperAdminOrBigSupervisor(@NotBlank String saleChannel) {
        ApiResult<?> tokenUser = tokenUser();
        if (!tokenUser.isSuccess()) {
            return ApiResult.newError("调用用户系统异常信息:" + tokenUser.getErrorMsg());
        }
        NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
        List<String> rsRoleNames = newUser.getRsRoleNames();
        boolean match = rsRoleNames.stream().anyMatch(roleName -> {
            return isAdmin(roleName) || (isSupremePermissions(roleName, saleChannel));
        });
        //如果职位就是主管或超管那就直接返回
        if (match) {
            return ApiResult.newSuccess(Boolean.TRUE);
        }

        return ApiResult.newSuccess(Boolean.FALSE);
    }

    /**
     * 是否是那个平台销售主管，根据当前登录人判断
     *
     * @return
     */
    public static String isSaleAuthority() {
        ApiResult<?> tokenUser = tokenUser();
        if (tokenUser == null || !tokenUser.isSuccess()) {
            return null;
        }

        NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);

        List<String> roleIdAndRoleName = newUser.getRsRoleNames();
        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            //循环判断权限列表
            for (String roleName : roleIdAndRoleName) {
                //判断是否超管，或某平台主管
                if (!roleName.contains(RoleConstant.SUPERVISOR)) {
                    continue;
                }

                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_AMAZON)) {
                    return SaleChannel.CHANNEL_AMAZON;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_WISH)) {
                    return SaleChannel.CHANNEL_WISH;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_SHOPEE)) {
                    return SaleChannel.CHANNEL_SHOPEE;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_LAZADA)) {
                    return SaleChannel.CHANNEL_LAZADA;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_SMT)) {
                    return SaleChannel.CHANNEL_SMT;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_EBAY)) {
                    return SaleChannel.CHANNEL_EBAY;
                }
                if (StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_JOOM)) {
                    return SaleChannel.CHANNEL_JOOM;
                }
            }
        }

        return null;
    }

    /**
     * 是否包含该角色，根据当前登录人判断
     * @param saleChannel
     * @param inputRoleName
     * @return
     */
    public static Boolean containRole(String saleChannel, String inputRoleName) {
        ApiResult<?> tokenUser = tokenUser();
        if (tokenUser == null || !tokenUser.isSuccess() || StringUtils.isBlank(saleChannel) || StringUtils.isBlank(inputRoleName)) {
            return null;
        }

        NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);

        List<String> roleIdAndRoleName = newUser.getRsRoleNames();
        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            //循环判断权限列表
            for (String roleName : roleIdAndRoleName) {
                if(StringUtils.contains(roleName, saleChannel) && StringUtils.contains(roleName, inputRoleName) ) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取当前登录用户下级员工列表
     *
     * @param saleChannel：平台 例：SaleChannel.CHANNEL_WISH
     * @return
     */
    public static ApiResult<List<NewUser>> listTeamLeaderByEmployeeNo(String saleChannel) {
        ApiResult<?> apiResult = tokenUser();
        if (!apiResult.isSuccess()) {
            return ApiResult.newError(apiResult.getErrorMsg());
        }
        //员工实体列表
        List<NewUser> userList = new ArrayList<>();
        if (apiResult != null && apiResult.isSuccess()) {
            NewUser newUser = JSON.parseObject(JSON.toJSONString(apiResult.getResult()), NewUser.class);
            //如果不是超管那就查询下级员工
            if (!isSuperAdminOrEquivalent(newUser, saleChannel)) {
                // 文案没有平台 带平台查不出上下级
                Boolean isWenan = isWenan(newUser);

                //先添加一份自己的信息,将PositionName，RoleIdAndRoleName ServicePlatform设置为空，统一对象数据,用于去重
                newUser.setPositionName(null);
                newUser.setRsRoleNames(null);
                newUser.setServicePlatform(null);
                userList.add(newUser);
                Map<String, Object> argsMap = new HashMap<>();
                Map<String, String> reqMap = new HashMap<>();
                reqMap.put("employeeNo", newUser.getEmployeeNo());
                if (StringUtils.isNotBlank(saleChannel) && BooleanUtils.isNotTrue(isWenan)) {
                    reqMap.put("servicePlatform", getSale(saleChannel));
                }
                argsMap.put("args", JSON.toJSONString(reqMap));
                ApiResult<?> result = newUsermgtClient.listTeamLeaderByEmployeeNo(JSON.toJSONString(argsMap));
                if (result != null && result.isSuccess()) {
                    JSONArray leaderArray = JSONArray.parseArray(JSON.toJSONString(result.getResult()));
                    for (Object leader : leaderArray) {
                        String orgName = JSONObject.parseObject(leader.toString()).getString("orgName");
                        //兼容美客多
                        if (SaleChannel.CHANNEL_MERCADOLIBRE.equals(saleChannel) && !"Mercado".equals(orgName)) {
                            log.info("[mkd-accounts] user:{}, {}", newUser.getEmployeeNo(),result.getResult());
                            continue;
                        }
                        //下级员工列表
                        JSONArray employeeTeamLeaders = JSONObject.parseObject(leader.toString()).getJSONArray("employeeTeamLeaders");
                        if (CollectionUtils.isNotEmpty(employeeTeamLeaders)) {
                            for (Object employeeTeamLeader : employeeTeamLeaders) {
                                if (employeeTeamLeader != null) {
                                    NewUser employeeUser = new NewUser();
                                    employeeUser.setEmployeeNo(JSON.parseObject(employeeTeamLeader.toString()).getString("employeeNo"));
                                    employeeUser.setName(JSON.parseObject(employeeTeamLeader.toString()).getString("employeeName"));
                                    employeeUser.setEmployeeId(JSON.parseObject(employeeTeamLeader.toString()).getInteger("employeeId"));
                                    userList.add(employeeUser);
                                }
                            }
                        }
                    }
                }
            }
            //如果是超管，那就直接查询平台所有员工
            else {
                try {
                    ApiResult<?> employeeByServicePlatform = newUsermgtClient.getEmployeeByServicePlatform(getSale(saleChannel));
                    if (employeeByServicePlatform != null && employeeByServicePlatform.isSuccess()) {
                        userList = JSON.parseArray(JSON.toJSONString(employeeByServicePlatform.getResult()), NewUser.class);
                    }
                    String employeeNo = newUser.getEmployeeNo();
                    // 判断自己是否存在，存在就不添加进去
                    userList = userList.stream().filter(a -> {
                        String employeeNo1 = a.getEmployeeNo();
                        if (StringUtils.isBlank(employeeNo1)) {
                            return true;
                        }
                        return !StringUtils.equals(employeeNo, employeeNo1);
                    }).collect(Collectors.toList());

                    //添加一份自己的信息,将PositionName，RoleIdAndRoleName设置为空，统一对象数据,用于去重
                    newUser.setPositionName(null);
                    newUser.setRsRoleNames(null);
                    newUser.setServicePlatform(null);
                    userList.add(newUser);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }

        }
        if (CollectionUtils.isNotEmpty(userList)) {
            userList = userList.stream().distinct().collect(Collectors.toList());
        }
        return ApiResult.newSuccess(userList);
    }

    /**
     * 根据员工编号 管理的用户
     *
     * @param saleChannel
     * @param employeeNo
     * @return
     */
    public static ApiResult<List<NewUser>> subordinateTeamLeaderByEmployeeNo(String saleChannel, String employeeNo) {
        List<NewUser> userList = new ArrayList<>();
        Map<String, Object> argsMap = new HashMap<>(2);
        Map<String, String> reqMap = new HashMap<>(2);
        reqMap.put("employeeNo", employeeNo);
        reqMap.put("servicePlatform", getSale(saleChannel));
        argsMap.put("args", JSON.toJSONString(reqMap));
        ApiResult<?> result = newUsermgtClient.listTeamLeaderByEmployeeNo(JSON.toJSONString(argsMap));

        if (null == result) {
            return ApiResult.newError("调用用户接口返回null");
        }

        if (!result.isSuccess()) {
            return ApiResult.newError(result.getErrorMsg());
        }

        JSONArray leaderArray = JSONArray.parseArray(JSON.toJSONString(result.getResult()));
        for (Object leader : leaderArray) {
            String orgName = JSONObject.parseObject(leader.toString()).getString("orgName");
            //兼容美客多
            if (SaleChannel.CHANNEL_MERCADOLIBRE.equals(saleChannel) && !"Mercado".equals(orgName)) {
                continue;
            }
            //下级员工列表
            JSONArray employeeTeamLeaders = JSONObject.parseObject(leader.toString()).getJSONArray("employeeTeamLeaders");
            if (CollectionUtils.isNotEmpty(employeeTeamLeaders)) {
                for (Object employeeTeamLeader : employeeTeamLeaders) {
                    if (employeeTeamLeader != null) {
                        NewUser employeeUser = new NewUser();
                        employeeUser.setEmployeeNo(JSON.parseObject(employeeTeamLeader.toString()).getString("employeeNo"));
                        employeeUser.setName(JSON.parseObject(employeeTeamLeader.toString()).getString("employeeName"));
                        employeeUser.setEmployeeId(JSON.parseObject(employeeTeamLeader.toString()).getInteger("employeeId"));
                        userList.add(employeeUser);
                    }
                }
            }
        }
        return ApiResult.newSuccess(userList);
    }

    /**
     * 判断员工是否为负责人,返回下一层员工信息
     *
     * @param saleChannel
     * @param employeeNo
     * @return
     */
    public static ApiResult<List<NewUser>> listSecondaryTeamByEmployeeNo(String saleChannel, String employeeNo) {
        Map<String, Object> argsMap = new HashMap<>(2);
        Map<String, String> reqMap = new HashMap<>(2);
        reqMap.put("employeeNo", employeeNo);
        reqMap.put("servicePlatform", saleChannel);
        argsMap.put("args", JSON.toJSONString(reqMap));
        ApiResult<List<NewUser>> result = newUsermgtClient.listSecondaryTeamByEmployeeNo(JSON.toJSONString(argsMap));
        if (null == result) {
            return ApiResult.newError("调用用户接口返回null");
        }

        return ApiResult.newSuccess(result.getResult());
    }

    /**
     * 匹配授权的销售id
     * @param authorizedAccountRequest
     * @return
     */
    public static List<String> matchingAuthorizedSaleIds(AuthorizedAccountRequest authorizedAccountRequest) {
        List<String> saleIds = new ArrayList<>();

        String saleChannel = authorizedAccountRequest.getSaleChannel();
        List<String> salemanagerList = authorizedAccountRequest.getSalemanagerList();
        List<String> salemanagerLeaderList = authorizedAccountRequest.getSalemanagerLeaderList();
        List<String> salesSupervisorList = authorizedAccountRequest.getSalesSupervisorList();

        //有选择销售
        if(CollectionUtils.isNotEmpty(salemanagerList)){
            for (String sale : salemanagerList) {
                //根据销售查询销售信息，获取employeeId
                ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(sale);
                if(!userByNo.isSuccess()){
                    //不抛错误，只打印日志
                    log.error("获取员工信息异常：" + userByNo.getErrorMsg());
                }else{
                    saleIds.add(userByNo.getResult().getEmployeeId().toString());
                }
            }
            if(CollectionUtils.isEmpty(saleIds)){
                throw new RuntimeException("选择的销售查不到用户信息！");
            }
        }

        //有选择销售组长
        if(CollectionUtils.isNotEmpty(salemanagerLeaderList)){
            // 管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : salemanagerLeaderList) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.subordinateTeamLeaderByEmployeeNo(saleChannel, string);
                if(!listApiResult.isSuccess()){
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString()).collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }
            if(CollectionUtils.isEmpty(saleIdsSet)){
                throw new RuntimeException("选择的销售组长查不到下级！");
            }

            if(CollectionUtils.isEmpty(saleIds)) {
                saleIds = new ArrayList<>(saleIdsSet);
            } else {
                saleIds.retainAll(new ArrayList<>(saleIdsSet));
            }
            if(CollectionUtils.isEmpty(saleIds)){
                throw new RuntimeException("选择的销售组长没有权限！");
            }
        }


        //有选择销售主管
        if(CollectionUtils.isNotEmpty(salesSupervisorList)){
            //管理的销售人员
            Set<String> saleIdsSet = new HashSet<>();
            for (String string : salesSupervisorList) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.subordinateTeamLeaderByEmployeeNo(saleChannel, string);
                if(!listApiResult.isSuccess()){
                    throw new RuntimeException(listApiResult.getErrorMsg());
                }
                List<String> collect = listApiResult.getResult().stream().map(t -> t.getEmployeeId().toString()).collect(Collectors.toList());
                saleIdsSet.addAll(collect);
            }
            if(CollectionUtils.isEmpty(saleIdsSet)){
                throw new RuntimeException("选择的销售主管查不到下级！");
            }

            if(CollectionUtils.isEmpty(saleIds)) {
                saleIds = new ArrayList<>(saleIdsSet);
            } else {
                saleIds.retainAll(new ArrayList<>(saleIdsSet));
            }
            if(CollectionUtils.isEmpty(saleIds)){
                throw new RuntimeException("选择的销售主管没有权限！");
            }
        }

        return saleIds;
    }

    /**
     * 转换平台入参
     *
     * @param sale
     * @return
     */
    private static String getSale(String sale) {
//        if (SaleChannel.CHANNEL_MERCADOLIBRE.equals(sale)){
//            sale = SaleChannel.CHANNEL_MERCADO;
//        }
//        if (SPECIAL_PLATFORMS.contains(sale)) {
//            return sale.toUpperCase() + "平台";
//        }
        return sale;
    }

    /**
     * 获取员工及其下级员工工号
     *
     * @param saleChannel 平台
     * @return
     */
    public static ApiResult<List<String>> getAuthorEmployeeNos(String saleChannel) {
        ApiResult<List<NewUser>> listApiResult = listTeamLeaderByEmployeeNo(saleChannel);
        if (!listApiResult.isSuccess()) {
            return ApiResult.newError(listApiResult.getErrorMsg());
        }
        List<String> employeeNos = listApiResult.getResult().stream().map(NewUser::getEmployeeNo).collect(Collectors.toList());
        return ApiResult.newSuccess(employeeNos);
    }

    /**
     * 根据工号查询员工信息：未查到的时返回空
     *
     * @param employeeNo
     * @return
     */
    public static ApiResult<NewUser> getUserByNo(String employeeNo) {
        try {
            if(StringUtils.isBlank(employeeNo)){
                return ApiResult.newError("获取用户信息失败，请检查用户是否登录！");
            }
            //获取登录用户信息
            NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
            }, employeeNo);
            if (newUser == null) {
                return ApiResult.newError("获取用户信息失败，请检查用户是否登录！");
            }
            return ApiResult.newSuccess(newUser);
        } catch (Exception e) {
            log.error("根据工号[{}]查询员工信息,调用异常：", employeeNo, e);
            return ApiResult.newError("获取用户信息失败，请检查用户是否登录！");
        }
    }

    /**
     * 指定用户是否为文案
     *
     * @param userName
     * @return
     */
    public static ApiResult<Boolean> isWenan(String userName) {
        ApiResult<NewUser> apiResult = getUserByNo(userName);
        if (!apiResult.isSuccess()) {
            return ApiResult.newError(apiResult.getErrorMsg());
        }
        NewUser newUser = apiResult.getResult();
        Boolean wenan = isWenan(newUser);
        return ApiResult.newSuccess(wenan);
    }

    /**
     * 是否为文案
     *
     * @param newUser
     * @return
     */
    public static Boolean isWenan(NewUser newUser) {
        if (null == newUser) {
            return null;
        }

        List<String> roleIdAndRoleName = newUser.getRsRoleNames();
        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            //循环判断权限列表
            for (String roleName : roleIdAndRoleName) {
                //判断是否文案，文案编辑 文案组长(西安文案) 编辑主管 这三个为西安文案
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.PAPERWORK)
                        || (StringUtils.containsIgnoreCase(roleName, RoleConstant.PAPERWORK_LEADER) && StringUtils.containsIgnoreCase(roleName, RoleConstant.XIAN_PAPERWORK))
                        || StringUtils.containsIgnoreCase(roleName, RoleConstant.EDIT_SUPERVISOR)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 指定用户是否为文案主管
     *
     * @param userName
     * @return
     */
    public static ApiResult<Boolean> isWenanSupervisor(String userName) {
        ApiResult<NewUser> apiResult = getUserByNo(userName);
        if (!apiResult.isSuccess()) {
            return ApiResult.newError(apiResult.getErrorMsg());
        }
        NewUser newUser = apiResult.getResult();
        Boolean wenan = isWenanSupervisor(newUser);
        return ApiResult.newSuccess(wenan);
    }

    /**
     * 是否为文案主管
     *
     * @param newUser
     * @return
     */
    public static Boolean isWenanSupervisor(NewUser newUser) {
        if (null == newUser) {
            return false;
        }

        List<String> roleIdAndRoleName = newUser.getRsRoleNames();
        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            //循环判断权限列表
            for (String roleName : roleIdAndRoleName) {
                if (StringUtils.containsIgnoreCase(roleName, RoleConstant.EDIT_SUPERVISOR)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取员工上级
     *
     * @param empNo
     * @return
     */
    public static ApiResult<SuperEmployeeInfo> getSaleSuperior(String empNo) {
        Map<String, Object> reqMap = new HashMap();
        reqMap.put("empNo", empNo);
        return newUsermgtClient.superByEmpNo(JSON.toJSONString(reqMap));
    }

    /**
     * 判断是否是平台初级主管，smt用
     * @param saleChannel
     * @return
     */
    public static ApiResult<Boolean> isJuniorEexecutive(String saleChannel) {
        Boolean isJuniorEexecutive = false;
        try {
            ApiResult<?> tokenUser = tokenUser();
            if(!tokenUser.isSuccess()){
                return ApiResult.newError("调用用户系统异常信息:" + tokenUser.getErrorMsg());
            }
            if (StringUtils.isBlank(saleChannel)) {
                return ApiResult.newSuccess(isJuniorEexecutive);
            }
            if (tokenUser != null && tokenUser.isSuccess()) {
                NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
                List<String> roleIdAndRoleName = newUser.getRsRoleNames();
                if(CollectionUtils.isNotEmpty(roleIdAndRoleName)){
                    //循环判断权限列表
                    for (String roleName : roleIdAndRoleName) {
                        if(StringUtils.containsIgnoreCase(roleName, "主管") && StringUtils.containsIgnoreCase(roleName, saleChannel)){
                            isJuniorEexecutive = true;
                            break;
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess(isJuniorEexecutive);
    }

    /**
     * 根据角色name集合，查询角色对应员工工号、id及名称
     */
    public static ApiResult<List<NewUser>> listUserByRoleNames(List<String> roleNames) {
        if (CollectionUtils.isEmpty(roleNames)) {
            return ApiResult.newError("角色名称不能为空");
        }
        try {
            return newUsermgtClient.getEmpNoAndEmpNameDTOByRoleNames(roleNames);
        } catch (Exception e) {
            log.error("调用用户系统出错",e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据平台获取员工信息
     */
    public static List<NewUser> getEmployeeByServicePlatform(String platform) {
        try {
            ApiResult<?> employeeByServicePlatform = newUsermgtClient.getEmployeeByServicePlatform(platform);
            if (employeeByServicePlatform != null && employeeByServicePlatform.isSuccess()) {
                return JSON.parseArray(JSON.toJSONString(employeeByServicePlatform.getResult()), NewUser.class);
            }
        } catch (Exception e) {
            log.error("根据平台[{}]获取员工信息,调用异常：", platform, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据平台获取在职员工信息
     */
    public static List<NewUser> getInUseEmplByPlatform(String platform) {
        try {
            ApiResult<?> employeeByServicePlatform = newUsermgtClient.getInUseEmplByPlatform(platform);
            if (employeeByServicePlatform != null && employeeByServicePlatform.isSuccess()) {
                return JSON.parseArray(JSON.toJSONString(employeeByServicePlatform.getResult()), NewUser.class);
            }
        } catch (Exception e) {
            log.error("根据平台[{}]获取员工信息,调用异常：", platform, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据平台和职位获取在职员工信息
     */
    public static List<NewUser> getInUseEmplByPlatformAndPosition(String saleChannel, String position) {
        try {
            ApiResult<?> employeeByServicePlatform = newUsermgtClient.getInUseEmplByPlatform(saleChannel);
            if (Objects.isNull(employeeByServicePlatform) || !employeeByServicePlatform.isSuccess()) {
                return Collections.emptyList();
            }

            // 获取所有在职员工
            List<NewUser> newUserList = JSON.parseArray(JSON.toJSONString(employeeByServicePlatform.getResult()), NewUser.class);
            switch (position) {
                case RoleConstant.SALE:
                    return newUserList.stream()
                            .filter(newUser -> StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.SALE))
                            .collect(Collectors.toList());
                case RoleConstant.GROUP_LEADER:
                    return newUserList.stream()
                            .filter(newUser -> StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.GROUP_LEADER))
                            .collect(Collectors.toList());
                case RoleConstant.MIDDLE_SUPERVISOR:
                    return newUserList.stream()
                            .filter(newUser -> StringUtils.containsIgnoreCase(newUser.getPositionName(), RoleConstant.MIDDLE_SUPERVISOR))
                            .collect(Collectors.toList());
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("根据平台[{}]获取员工信息,调用异常：", saleChannel, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据条件获取主管信息
     * employeeNo：工号
     * servicePlatform：平台
     */
    public static LeaderInfo getLeadersByCondition(String platform) {
        try {
            ApiResult<LeaderInfo> leadersByCondition = newUsermgtClient.getLeadersByCondition(platform);
            if (leadersByCondition != null && leadersByCondition.isSuccess()) {
                return leadersByCondition.getResult();
            }
            return null;
        } catch (Exception e) {
            log.error("根据平台[{}]获取员工信息,调用异常：", platform, e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 根据工号获取员工平台
     * @param employeeNo 工号
     */
    public static List<String> getUserPlatformByEmployeeNo(String employeeNo) {
        try {
            Map<String, List<String>> servicePlatformByEmployeeNos = getServicePlatformByEmployeeNos(Lists.newArrayList(employeeNo));
            return servicePlatformByEmployeeNos.get(employeeNo);
        } catch (Exception e) {
            log.error("根据工号[{}]获取员工平台,调用异常：", employeeNo, e);
        }
        return Collections.emptyList();

    }


    /**
     * 根据工号批量获取员工及对应刊登平台
     * @return k:工号， v: 销售服务平台
     */
    public static Map<String, List<String>> getServicePlatformByEmployeeNos(List<String> employeeNos) {
        if (CollectionUtils.isEmpty(employeeNos)) {
            return Collections.emptyMap();
        }
        Map<String, List<String>> paramMap = new HashMap<>();
        paramMap.put("employeesNos", employeeNos);
        String body = JSON.toJSONString(paramMap);
        try {
            ApiResult<List<NewUser>> userServicePlatformResult = newUsermgtClient.getEmplServicePlatformByEmplNo(body);
            if (userServicePlatformResult != null && userServicePlatformResult.isSuccess()) {
                List<NewUser> userServicePlatformList = userServicePlatformResult.getResult();
                return convert2PublishSalePlatform(userServicePlatformList);
            }
        } catch (Exception e) {
            log.error("根据工号[{}]获取员工平台,调用异常：", body, e);
        }
        return Collections.emptyMap();
    }

    /**
     * 将用户系统的服务平台转换为刊登对应的销售平台
     * @param userServicePlatformList 用户
     * @return k:工号， v: 销售服务平台
     */
    private static Map<String, List<String>> convert2PublishSalePlatform(List<NewUser> userServicePlatformList) {
        Map<String, List<String>> salePlatformMap = Maps.newHashMap();
        for (NewUser newUser : userServicePlatformList) {
            String servicePlatform = newUser.getServicePlatform();
            if (StringUtils.isBlank(servicePlatform)) {
                continue;
            }
            String replaceServicePlatformStr = servicePlatform.replaceAll("平台", "");
            String[] platformArray = replaceServicePlatformStr.split(",");
            List<String> platforms = new ArrayList<>(Arrays.asList(platformArray));
            salePlatformMap.put(newUser.getEmployeeNo(), platforms);
        }
        return salePlatformMap;
    }

    /**
     * 根据员工工号获取对应的名称
     * @param employeeNo 工号
     * @param userMap    用户缓存Map
     */
    public static String getUserNameByEmployeeNo(Map<String,String> userMap, String employeeNo) {
        String name = userMap.get(employeeNo);
        if (name != null) {
            return name;
        }
        NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<>() {
        }, employeeNo);
        if (newUser != null) {
            String userName = newUser.getName();
            userMap.put(employeeNo, userName);
        } else {
            userMap.put(employeeNo, employeeNo+"，无此用户信息");
        }
        return userMap.get(employeeNo);
    }

    /**
     * 判断是否为主管级别
     * @return
     */
    public static Boolean isContainsSupervisor(){
        ApiResult<?> tokenUser = tokenUser();
        if (tokenUser == null || !tokenUser.isSuccess()) {
            return null;
        }
        NewUser newUser = JSON.parseObject(JSON.toJSONString(tokenUser.getResult()), NewUser.class);
        List<String> roleIdAndRoleName = newUser.getRsRoleNames();
        if (CollectionUtils.isNotEmpty(roleIdAndRoleName)) {
            // 循环判断权限列表(是否为主管级别)
            for (String roleName : roleIdAndRoleName) {
                boolean contains = StringUtils.containsIgnoreCase(roleName, SaleChannel.CHANNEL_AMAZON);
                if (!contains) {
                    continue;
                }
                return roleName.contains(RoleConstant.SUPERVISOR) || roleName.contains(RoleConstant.MIDDLE_SUPERVISOR) ||
                        roleName.contains(RoleConstant.PRIMARY_SUPERVISOR) || roleName.contains(RoleConstant.JUNIOR_SUPERVISOR_PLATFORM) || roleName.contains(RoleConstant.MIDDLE_SUPERVISOR_PLATFORM);
            }
        }
        return null;
    }

    /**
     * 根据用户id获取该用户信息
     * @param employeeId
     * @return
     */
    public static EmployeeInfo getEmployeeInfoByEmployeeId(Long employeeId) {
        //获取登录用户信息
        if (null == employeeId) {
            return null;
        }
        EmployeeInfo employeeInfo = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_EMPLOYEE_ID, new TypeReference<EmployeeInfo>() {
        }, String.valueOf(employeeId));
        if (employeeInfo == null) {
            try {
                ApiResult<List<NewUser>> newUserResult = newUsermgtClient.getEmplInfoById(String.valueOf(employeeId));
                if (newUserResult != null && newUserResult.isSuccess() && newUserResult.getResult().size() > 0) {
                    employeeInfo = BeanUtil.copyProperties(newUserResult.getResult().get(0), EmployeeInfo.class);
                }
            } catch (Exception e) {
                log.error("根据用户id[{}]获取员工信息,调用异常：", employeeId, e);
            }
        }
        return employeeInfo;
    }


    /**
     * 获取员工上级
     *
     * @param employeeNo
     * @return
     */
    public static ApiResult<SuperEmployeeInfo> getAllSuperByEmpNo(String employeeNo) {
        try {
            Map<String, Object> reqMap = new HashMap();
            reqMap.put("employeeNo", employeeNo);
            return newUsermgtClient.getAllSuperByEmpNo(JSON.toJSONString(reqMap));
        } catch (Exception e) {
            log.error("获取员工上级,调用异常：", e);
            return ApiResult.newError(e.getMessage());
        }
    }


    /**
     * 组员 182483-王环
     * 销售组长（AMAZON） 6162-高雅慧
     * 精铺主管（AMAZON） 181989-黄大源
     * 平台销售主管（Amazon） 182287-吴蓓蕾
     *
     * 店铺A 负责销售为 王环	销售 王环 组长 高雅慧 主管 黄大源
     * 店铺B 负责销售为 高雅慧	销售 高雅慧 组长 高雅慧 主管 黄大源
     * 店铺C 负责销售为 黄大源	销售 黄大源 组长 黄大源  主管 吴蓓蕾
     * 店铺D 负责销售为 吴蓓蕾	销售 吴蓓蕾 组长  吴蓓蕾  主管 吴蓓蕾
     *
     *
     * 组员 182703-陈福
     * 储备组长 170082-陈锡培
     * 销售组长 4094-任成
     * 中级主管（AMAZON）181750-李振露
     * 平台销售主管（Amazon） 182287-吴蓓蕾
     *
     * 店铺E 负责销售为 陈福	销售 陈福 组长  任成  主管 李振露
     *
     * @param resultEmployeeDTO
     * @param saleStructureVO
     */
    public static void setSaleSuperior(ResultEmployeeDTO resultEmployeeDTO, SaleStructureVO saleStructureVO) {
        if(resultEmployeeDTO == null || saleStructureVO == null) {
            return;
        }

        // 默认取自己的直属组长，如果自己是组长上级依旧是组长，则取上级
        if (StringUtils.isBlank(saleStructureVO.getSaleLeader()) || saleStructureVO.getSaleLeader().contains(saleStructureVO.getSale())) {
            if (StringUtils.contains(resultEmployeeDTO.getPositionName(), "组长")) {
                saleStructureVO.setSaleLeader(resultEmployeeDTO.getName() + "-" + resultEmployeeDTO.getSuperEmployeeNo());
            }
        }
        // 优先他的直属上级，存在多个主管的 情况下  自己是主管 主管取上级 自己不是主管取第一级主管
        if((StringUtils.isBlank(saleStructureVO.getSaleManager()) || StringUtils.equalsIgnoreCase(saleStructureVO.getSaleManager(), saleStructureVO.getSale()))
                && StringUtils.containsAny(resultEmployeeDTO.getPositionName(), "主管", "经理", "技术总监")) {
            saleStructureVO.setSaleManager(resultEmployeeDTO.getName() + "-" + resultEmployeeDTO.getSuperEmployeeNo());
        }
        if(CollectionUtils.isEmpty(resultEmployeeDTO.getLeaderList())) {
            return;
        }

        // 递归调用
        setSaleSuperior(resultEmployeeDTO.getLeaderList().get(0), saleStructureVO);
    }


    public static SaleStructureVO getSaleSuperiorNew(String sale) {
        String saleStructureVOStr = PublishRedisClusterUtils.get(RedisConstant.SALE_SUPERIOR_NEWEST + sale);
        if (StringUtils.isNotBlank(saleStructureVOStr)) {
            return JSON.parseObject(saleStructureVOStr, SaleStructureVO.class);
        }

        SaleStructureVO saleStructureVO = new SaleStructureVO();
        if (StringUtils.isBlank(sale)){
            saleStructureVO.setErrorMsg("获取店铺负责销售为空");
            return saleStructureVO;
        }
        saleStructureVO.setSale(sale);

        QueryEmployeeRequest request = new QueryEmployeeRequest();
        request.setEmployeeNo(sale);
        ApiResult<ResultEmployeeDTO> resultEmployeeDTOApi;
        try {
            resultEmployeeDTOApi = newUsermgtClient.getAllSuperByEmpNo(request);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            saleStructureVO.setErrorMsg("调用用户系统错误:" + e.getMessage());
            return saleStructureVO;
        }
        if(resultEmployeeDTOApi != null && resultEmployeeDTOApi.isSuccess()) {
            ResultEmployeeDTO resultEmployeeDTO = resultEmployeeDTOApi.getResult();
            setSaleSuperior(resultEmployeeDTO, saleStructureVO);

            // 普通职员直属领导是经理（主管）  182326-闫碧霞
            if(StringUtils.isBlank(saleStructureVO.getSaleLeader()) && StringUtils.isNotBlank(saleStructureVO.getSaleManager())) {
                saleStructureVO.setSaleLeader(saleStructureVO.getSaleManager());
            }
//            if(StringUtils.isBlank(saleStructureVO.getSaleLeader())) {
//                saleStructureVO.setSaleLeader(saleStructureVO.getSale());
//            }
//            if(StringUtils.isEmpty(saleStructureVO.getSaleManager())) {
//                saleStructureVO.setSaleManager(saleStructureVO.getSaleLeader());
//            }
        } else {
            saleStructureVO.setErrorMsg("未成功获取组长主管:" + (resultEmployeeDTOApi == null ? "返回结果为空" : resultEmployeeDTOApi.getErrorMsg()));
        }
        PublishRedisClusterUtils.set(RedisConstant.SALE_SUPERIOR_NEWEST + sale, JSON.toJSONString(saleStructureVO), 2, TimeUnit.HOURS);
        return saleStructureVO;
    }


    /**
     * 获取店铺对应的销售 销售组长 销售主管
     * left:销售 middle:销售组长 right:销售主管
     *
     * @param accountNumberList
     * @param channel
     * @return
     */
    public static Map<String, Triple<String, String, String>> getSaleSuperiorMap(List<String> accountNumberList, String channel) {
        // 查询账号对应销售姓名-工号
        Map<String, Set<String>> salesmanAccountMap =  EsAccountUtils.getSalesmanAccountMapByEs(accountNumberList, channel);
        Map<String, Triple<String, String, String>> saleSuperiorMap = new HashMap<>();
        for (String accountNumber : accountNumberList) {
            Set<String> salesmanSet = salesmanAccountMap.get(accountNumber);
            if (CollectionUtils.isEmpty(salesmanSet)) {
                saleSuperiorMap.put(accountNumber, Triple.of(null, null, null));
                continue;
            }

            String salesman = salesmanSet.iterator().next();
            String sale = salesman.substring(salesmanSet.iterator().next().lastIndexOf("-") + 1);
            SaleStructureVO saleSuperiorNew = NewUsermgtUtils.getSaleSuperiorNew(sale);
            String saleLeader = saleSuperiorNew.getSaleLeader();
            String saleSupervisor = saleSuperiorNew.getSaleManager();
            saleSuperiorMap.put(accountNumber, Triple.of(salesman, saleLeader, saleSupervisor));
        }
        return saleSuperiorMap;
    }


    /**
     * 根据条件获取上级信息
     * employeeNo：工号
     * servicePlatform：平台
     */
    public static LeaderInfo getEmplByCondition(String platform,String employeeNo) {
        try {
            Map<String, String> params = new HashMap<>();
            // 岗位名称
            params.put("servicePlatform", platform);
            params.put("employeeNo", employeeNo);
            ApiResult<LeaderInfo> leadersByCondition = newUsermgtClient.getEmplByCondition(params);
            if (leadersByCondition != null && leadersByCondition.isSuccess()) {
                return leadersByCondition.getResult();
            }
            return null;
        } catch (Exception e) {
            log.error("根据平台[{}]获取员工 [{}] 上级信息,调用异常：", platform,employeeNo, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取店铺对应的销售,储备组长 销售组长 销售主管
     * 1:销售 2:储备组长 3:销售组长 4:分部主管 (初级主管 > 中级主管 ) 5:平台销售主管
     *
     * @param accountNumberList
     * @param platform
     * @return
     */
    public static Map<String, Tuple5<String, String, String,String,String>> getAllSaleSuperiorMap(List<String> accountNumberList, String platform) {
        // 查询账号对应销售姓名-工号
        Map<String, Set<String>> salesmanAccountMap =  EsAccountUtils.getSalesmanAccountMapByEs(accountNumberList, platform);
        Map<String, Tuple5<String, String, String,String,String>> saleSuperiorMap = new HashMap<>();
        for (String accountNumber : accountNumberList) {
            Set<String> salesmanSet = salesmanAccountMap.get(accountNumber);
            if (CollectionUtils.isEmpty(salesmanSet)) {
                saleSuperiorMap.put(accountNumber, new Tuple5(null, null, null, null,null));
                continue;
            }

            String salesman = salesmanSet.iterator().next();
            String sale = salesman.substring(salesmanSet.iterator().next().lastIndexOf("-") + 1);
            LeaderInfo leaderInfo = NewUsermgtUtils.getEmplByCondition(platform,sale);
            String storeGroupLeader = "";
            String saleGroupLeader = "";
            String juniorExecutive = "";
            String intermediateExecutive = "";
            String platformSalesSupervisor = "";
            if(!Objects.isNull(leaderInfo)) {
                // 储备组长
                List<LeaderInfo.Employee> storeGroupLeaders = leaderInfo.getStoreGroupLeaders();
                if (CollectionUtils.isNotEmpty(storeGroupLeaders)) {
                    LeaderInfo.Employee employee = storeGroupLeaders.get(0);
                    storeGroupLeader = employee.getName() + "-" + employee.getEmployeeNo();
                }
                // 	销售组长
                List<LeaderInfo.Employee> saleGroupLeaders = leaderInfo.getSaleGroupLeaders();
                if (CollectionUtils.isNotEmpty(saleGroupLeaders)) {
                    LeaderInfo.Employee employee = saleGroupLeaders.get(0);
                    saleGroupLeader = employee.getName() + "-" + employee.getEmployeeNo();
                }
                // 初级主管列表
                List<LeaderInfo.Employee> juniorExecutives = leaderInfo.getJuniorExecutives();
                if (CollectionUtils.isNotEmpty(juniorExecutives)) {
                    LeaderInfo.Employee employee = juniorExecutives.get(0);
                    juniorExecutive = employee.getName() + "-" + employee.getEmployeeNo();
                }
                // 中级主管列表
                List<LeaderInfo.Employee> intermediateExecutives = leaderInfo.getIntermediateExecutives();
                if (CollectionUtils.isNotEmpty(intermediateExecutives)) {
                    LeaderInfo.Employee employee = intermediateExecutives.get(0);
                    intermediateExecutive = employee.getName() + "-" + employee.getEmployeeNo();
                }
                // 平台销售主管列表
                List<LeaderInfo.Employee> platformSalesSupervisors = leaderInfo.getPlatformSalesSupervisors();
                if (CollectionUtils.isNotEmpty(platformSalesSupervisors)) {
                    LeaderInfo.Employee employee = platformSalesSupervisors.get(0);
                    platformSalesSupervisor = employee.getName() + "-" + employee.getEmployeeNo();
                }
            }
            String manager = StringUtils.isNotBlank(juniorExecutive)?juniorExecutive:StringUtils.isNotBlank(intermediateExecutive)?intermediateExecutive:null;
            saleSuperiorMap.put(accountNumber, new Tuple5(salesman, storeGroupLeader, saleGroupLeader,manager,platformSalesSupervisor));
        }
        return saleSuperiorMap;
    }
}
