package com.estone.erp.publish.system.pmssalePublicData.client;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelDO;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.product.response.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "sale-publish-walmart-service")
public interface PublishWalmartClient {
    /**
     * 根据店铺获取统计数据
     * @param accountNumbers 店铺
     * @return 店铺统计数据
     */
    @PostMapping(value = "sale-publish-walmart/statisticsData/getAccountData", headers = "content-type=application/json")
    ApiResult<EsSalesStatisticsData> getStatisticsDataAccountData(@RequestBody List<String> accountNumbers);

    /**
     * 根据店铺获取统计数据
     * @param accountNumbers 店铺
     * @return 店铺统计数据
     */
    @PostMapping(value = "sale-publish-walmart/JDWalmart/statisticsData/getAccountData", headers = "content-type=application/json")
    ApiResult<EsSalesStatisticsData> getJDWalmartStatisticsDataAccountData(@RequestBody List<String> accountNumbers);

    /**
     * 获取刊登SPU
     * 各个平台存在范本spu查询接口
     * @param request      request
     * @return PageResult<PublishSpuModelDO>
     */
    @PostMapping(value = "sale-publish-walmart/ozonAdminTemplate/getPublishSpuModelPage", headers = "content-type=application/json")
    ApiResult<PageResult<PublishSpuModelDO>> getPublishSpuModelPage(PublishSpuModelRequest request);

    /**
     * 获取刊登SPU
     * 各个平台存在范本spu查询接口
     * @param request      request
     * @return PageResult<PublishSpuModelDO>
     */
    @PostMapping(value = "sale-publish-walmart/walmartAdminTemplate/getPublishSpuModelPage", headers = "content-type=application/json")
    ApiResult<PageResult<PublishSpuModelDO>> getWalmartPublishSpuModelPage(@RequestBody PublishSpuModelRequest request);
}
