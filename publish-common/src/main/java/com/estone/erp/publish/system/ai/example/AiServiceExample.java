package com.estone.erp.publish.system.ai.example;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.ai.AiServiceHelper;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * AI服务调用示例
 * 展示如何使用AiServiceHelper进行批量AI服务调用
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-16
 */
@Slf4j
@Component
public class AiServiceExample extends AbstractJobHandler {

    @Autowired
    private AiServiceHelper aiServiceHelper;

    public AiServiceExample() {
        super("AiServiceExample");
    }

    /**
     * 内部参数类
     */
    @Data
    public static class InnerParam {
        /**
         * 线程数量，默认为10
         */
        private Integer threadNum = 10;
        
        /**
         * 处理的数据ID列表
         */
        private List<String> dataIds;
        
        /**
         * AI模型名称
         */
        private String model = "deepseek-r1";
    }

    @Override
    @XxlJob("AiServiceExample")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("AI服务调用示例任务开始执行，参数：{}", param);

        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);
            
            // 获取待处理数据
            List<ExampleData> dataList = getDataToProcess(innerParam);
            
            if (CollectionUtils.isEmpty(dataList)) {
                XxlJobLogger.log("未找到需要处理的数据");
                return ReturnT.SUCCESS;
            }
            
            XxlJobLogger.log("找到 {} 条数据需要处理", dataList.size());
            
            // 使用AiServiceHelper批量调用腾讯AI服务
            aiServiceHelper.batchCallTencentAi(dataList, innerParam.getThreadNum(),
                new AiServiceHelper.AiDataProcessor<ExampleData>() {
                    @Override
                    public ChatOpenaiRequest buildRequest(ExampleData data) {
                        // 构建AI请求
                        String prompt = String.format("请为以下内容生成标题：%s", data.getContent());
                        
                        return ChatOpenaiRequest.builder()
                            .prompt(prompt)
                            .model(innerParam.getModel())
                            .systemMessage("你是一个专业的标题生成助手")
                            .options(new ChatOpenaiRequest.Options())
                            .build();
                    }
                    
                    @Override
                    public void handleResponse(ExampleData data, ApiResult<ChatCompletionResponse> response) {
                        if (response.isSuccess()) {
                            // 处理成功响应
                            String generatedTitle = extractTitleFromResponse(response.getResult());
                            saveGeneratedTitle(data.getId(), generatedTitle);
                            XxlJobLogger.log("成功处理数据 ID: {}, 生成标题: {}", data.getId(), generatedTitle);
                        } else {
                            // 处理失败响应
                            XxlJobLogger.log("数据 {} AI调用失败: {}", data.getId(), response.getErrorMsg());
                        }
                    }
                    
                    @Override
                    public void handleError(ExampleData data, Exception error) {
                        log.error("处理数据 {} 时发生错误", data.getId(), error);
                        XxlJobLogger.log("处理数据 {} 时发生错误: {}", data.getId(), error.getMessage());
                    }
                });
            
            XxlJobLogger.log("AI服务调用示例任务执行完成");
            return ReturnT.SUCCESS;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            XxlJobLogger.log("任务被中断: {}", e.getMessage());
            return ReturnT.FAIL;
        } catch (Exception e) {
            log.error("AI服务调用示例任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 解析参数
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                log.warn("解析参数失败，使用默认参数", e);
                XxlJobLogger.log("解析参数失败，使用默认参数: {}", e.getMessage());
            }
        }
        return innerParam;
    }

    /**
     * 获取待处理数据
     */
    private List<ExampleData> getDataToProcess(InnerParam innerParam) {
        // 这里模拟获取数据的逻辑
        // 实际项目中应该从数据库或其他数据源获取
        return List.of(
            new ExampleData("1", "这是第一条测试内容"),
            new ExampleData("2", "这是第二条测试内容"),
            new ExampleData("3", "这是第三条测试内容")
        );
    }

    /**
     * 从AI响应中提取标题
     */
    private String extractTitleFromResponse(ChatCompletionResponse response) {
        if (response != null && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(0).getMessage().getContent();
        }
        return "未能生成标题";
    }

    /**
     * 保存生成的标题
     */
    private void saveGeneratedTitle(String dataId, String title) {
        // 这里模拟保存标题的逻辑
        // 实际项目中应该保存到数据库
        log.info("保存标题 - ID: {}, 标题: {}", dataId, title);
    }

    /**
     * 示例数据类
     */
    @Data
    public static class ExampleData {
        private String id;
        private String content;
        
        public ExampleData(String id, String content) {
            this.id = id;
            this.content = content;
        }
    }
}
