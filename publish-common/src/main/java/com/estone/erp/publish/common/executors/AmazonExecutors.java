package com.estone.erp.publish.common.executors;

import com.estone.erp.publish.common.json.ResponseJson;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Consumer;

/**
 * Amazon线程池管理类
 */
public class AmazonExecutors {

    /**
     * 最大线程数
     */
    public static final int MAX_THREADS = 50;

    /**
     * 线程数
     */
    public static final int DOWNLOAD_IMAGE_THREADS = 100;

    /**
     * 线程数
     */
    public static final int IMAGE_THREADS = 30;

    /**
     * 最大同步产品的线程数
     */
    public static final int MAX_SYNC_PRODUCT_THREADS = 20;

    /**
     * 最小同步产品的线程数
     */
    public static final int MIN_SYNC_PRODUCT_THREADS = 5;

    /**
     * 同步产品的线程数
     */
    public static final int NEW_SYNC_PRODUCT_THREADS = 10;

    /**
     * 最大上传数据任务线程数
     */
    public static final int SUBMIT_TASK_THREADS = 300;

    /**
     * 最大上传数据进度查询线程数
     */
    public static final int SUBMIT_PROGRESS_THREADS = 30;

    /**
     * 最大下载亚马逊图片任务线程数
     */
    public static final int MAX_DOWNLOAD_AMAZON_IMAGE = 10;

    /**
     * 更新标题线程数
     */
    public static final int UPDATE_AMAZON_LISTING_TITLE = 8;


    /**
     * listing和模板关联
     */
    public static final int AMAZON_LISTING_AND_TEMPLATE_RELATION = 5;

    /**
     * 校验侵权外层
     */
    public static final int CHECK_INFRINGEMENTWORD_THREADS = 10;

    /**
     * 校验侵权最小
     */
    public static final int CHECK_INFRINGEMENTWORD_INFO_THREADS = 40;

    /**
     * 页面下架listing 线程数
     */
    public static final int SALE_DELETE_AMAZON_LISTING_THREADS = 20;

    /**
     * 定时任务下架listing 线程数
     */
    public static final int SYSTEM_DELETE_AMAZON_LISTING_THREADS = 100;

    /**
     * 算价规则应用到店铺 线程数
     */
    public static final int APPLY_TO_ACCOUNT_THREADS = 100;

    /**
     * 同步运费线程池 最多30 定时任务可配置比30小的线程数
     */
    public static final int SYNC_SHIPPING_COST_THREADS = 30;

    /**
     * 销售可刊登分类线程数
     */
    public static final int PUBLISHABLE_CATEGORY_THREADS = 5;

    /**
     * 修改库存线程数
     */
    public static final int UPDATE_INVENTORY_THREADS = 10;

    /**
     * 停产存档下架线程数
     */
    public static final int DELETE_ARCHIVED_STOP_THREADS = 5;

    /**
     * 导出数据线程数
     */
    public static final int DOWNLOAD_DATA_THREADS = 1;

    public static final ThreadPoolExecutor POOL = ExecutorUtils.newFixedThreadPool("amazon-pool", MAX_THREADS);

    public static final ThreadPoolExecutor QUERY_POOL = ExecutorUtils.newFixedThreadPool("amazon-query-pool", MIN_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor REALITION_SHIP_POOL = ExecutorUtils.newFixedThreadPool("amazon-update-relationship-pool", MIN_SYNC_PRODUCT_THREADS);


    /**
     * 刷数据线程池
     */
    public static final ThreadPoolExecutor REFRESH_LISTING = ExecutorUtils
            .newFixedThreadPool("amazon-refresh-listing-pool", 5);

    /**
     * 检测品牌
     */
    public static final ThreadPoolExecutor CHECK_BRAND_LISTING = ExecutorUtils
            .newFixedThreadPool("check-brand-listing-pool", 5);

    public static final ThreadPoolExecutor SUBMIT_TASK_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-submit-task-pool", SUBMIT_TASK_THREADS);

    public static final ThreadPoolExecutor PUBLISH_VARIANT_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-publish-variant-pool", 10);

    public static final ThreadPoolExecutor SUBMIT_PROGRESS_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-submit-progress-pool", SUBMIT_PROGRESS_THREADS);

    public static final ThreadPoolExecutor SYNC_PRODUCT_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-sync-product-pool", MIN_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor SYNC_UNQUALIFIED_PRICE_SKU_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-unqualified-price-sku-pool", 50);

    public static final ThreadPoolExecutor SYNC_UNQUALIFIED_SKU_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-unqualified-sku-pool", IMAGE_THREADS);

    public static final ThreadPoolExecutor DOWNLOAD_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-download-sku-image-pool", DOWNLOAD_IMAGE_THREADS);

    public static final ThreadPoolExecutor AMAZON_NORMAL_STOCK_POLL = ExecutorUtils
            .newFixedThreadPool("amazon-normal-stock-pool", 10);

    public static Future<ResponseJson> submit(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(POOL, consumer, "amazon");
    }

    public static final ThreadPoolExecutor DOWNLOAD_AMAZON_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("download-amazon-image-pool", MAX_DOWNLOAD_AMAZON_IMAGE);

    public static final ThreadPoolExecutor CLEARANCE_SALE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-clearance-sale-pool", MAX_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor AMAZON_SKU_COUNTRY_AMOUNT_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-sku-country-amount-pool", IMAGE_THREADS);

    public static final ThreadPoolExecutor UPDATE_AMAZON_LISTING_TITLE_POOL = ExecutorUtils
            .newFixedThreadPool("update-amazon-listing-title-pool", UPDATE_AMAZON_LISTING_TITLE);

    public static final ThreadPoolExecutor UPDATE_AMAZON_LISTING_TITLE_RESULT_POOL = ExecutorUtils
            .newFixedThreadPool("update-amazon-listing-title-result-pool", MAX_DOWNLOAD_AMAZON_IMAGE);

    public static final ThreadPoolExecutor AMAZON_LISTING_AND_TEMPLATE_RELATION_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-listing-and-template-replation-pool", AMAZON_LISTING_AND_TEMPLATE_RELATION);

    public static final ThreadPoolExecutor AUTO_PUBLISH_POOL = ExecutorUtils
            .newFixedThreadPool("auto-publish-pool", 150);

    public static final ThreadPoolExecutor BATCH_PUBLISH_POOL = ExecutorUtils
            .newFixedThreadPool("batch-publish-pool", 4);

    public static final ThreadPoolExecutor CHECK_INFRINGEMENTWORD_POOL = ExecutorUtils
            .newFixedThreadPool("check_infringementword-pool", CHECK_INFRINGEMENTWORD_THREADS);


    public static final ThreadPoolExecutor CHECK_INFRINGEMENTWORD_INFO_POOL = ExecutorUtils
            .newFixedThreadPool("check_infringementword-info-pool", CHECK_INFRINGEMENTWORD_INFO_THREADS);

    public static final ThreadPoolExecutor CHECK_LISTING_INFRINGEMENTWORD_POOL = ExecutorUtils
            .newFixedThreadPool("check_listing_infringementword_pool", 30);

    public static final ThreadPoolExecutor SYNC_PRODUCT_DETAIL_MSG_POOL = ExecutorUtils
            .newFixedThreadPool("sync-product-deatail-msg-pool", IMAGE_THREADS);

    public static final ThreadPoolExecutor REFRESH_ALL_PLATFORM_PRODUCT_INFO_MSG = ExecutorUtils
            .newFixedThreadPool("refresh-all-platform-product-info-msg-pool", MIN_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor SYNC_PRODUCT_FILE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-sync-product-file-pool", NEW_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor MIN_SYNC_PRODUCT_FILE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-sync-product-file-pool", MIN_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor AUTO_PUBLISH_QUEUE_POOL = ExecutorUtils
            .newFixedThreadPool("auto-publish-queue-pool", UPDATE_AMAZON_LISTING_TITLE);

    public static final ThreadPoolExecutor DELETE_LISTING_POOL = ExecutorUtils
            .newFixedThreadPool("sale-delete-listing-pool", SALE_DELETE_AMAZON_LISTING_THREADS);

    public static final ThreadPoolExecutor APPLY_TO_ACCOUNT_POOL = ExecutorUtils
            .newFixedThreadPool("apply-to-account-pool", APPLY_TO_ACCOUNT_THREADS);

    public static final ThreadPoolExecutor SYNC_SHIPPING_COST = ExecutorUtils
            .newFixedThreadPool("sync-shipping-cost-pool", SYNC_SHIPPING_COST_THREADS);

    public static final ThreadPoolExecutor GET_CATEGORY_CODES_POOL = ExecutorUtils
            .newFixedThreadPool("get-category-codes-pool", PUBLISHABLE_CATEGORY_THREADS);

    public static final ThreadPoolExecutor PROHIBITION_LISTING_POOL = ExecutorUtils
            .newFixedThreadPool("refresh-prohibition-Listing", 10);

    public static final ThreadPoolExecutor UPDATE_PROHIBITION_LISTING_POOL = ExecutorUtils
            .newFixedThreadPool("update-prohibition-Listing", 20);

    public static final ThreadPoolExecutor DOWNLOAD_DATA_POOL = ExecutorUtils
            .newFixedThreadPool("download-data-pool", DOWNLOAD_DATA_THREADS);

    public static final ThreadPoolExecutor QUERY_TEMPLATE_POOL = ExecutorUtils
            .newFixedThreadPool("query-template-pool", 10);

    public static final ThreadPoolExecutor QUERY_TEMPLATE_REPORT_POOL = ExecutorUtils
            .newFixedThreadPool("query-template-report-pool", 10);

    public static final ThreadPoolExecutor UPDATE_INVENTORY_POOL = ExecutorUtils
            .newFixedThreadPool("update-inventory-pool", UPDATE_INVENTORY_THREADS);

    public static final ThreadPoolExecutor DELETE_ARCHIVED_STOP_POOL = ExecutorUtils
            .newFixedThreadPool("delete-archived-stop-pool", DELETE_ARCHIVED_STOP_THREADS);

    public static final ThreadPoolExecutor SALE_DATA_STATISTICS = ExecutorUtils
            .newFixedThreadPool("sale-data-statistics", 10);

    public static final ThreadPoolExecutor REASSIGN_POOL = ExecutorUtils
            .newFixedThreadPool("reassign_pool", 10);

    public static final ThreadPoolExecutor ACCOUNT_LIMIT_POOL = ExecutorUtils
            .newFixedThreadPool("offline_account_limit_pool", 40);

    public static final ThreadPoolExecutor OFFLINE_LIMIT_POOL = ExecutorUtils
            .newFixedThreadPool("offline_limit_pool", 50);

    public static final ThreadPoolExecutor SYSYTEM_DELETE_LISTING_POOL = ExecutorUtils
            .newFixedThreadPool("system_delete_listing_pool", SYSTEM_DELETE_AMAZON_LISTING_THREADS);

    public static final ThreadPoolExecutor INFRINGEMENT_WORD_FREQUENCY_POOL = ExecutorUtils
            .newFixedThreadPool("infringement-word-frequency-pool", 20);

    public static final ThreadPoolExecutor INFRINGEMENT_WORD_FREQUENCY_STATISTICS_POOL = ExecutorUtils
            .newFixedThreadPool("infringement_word_frequency_statistics_pool", 20);

    public static final ThreadPoolExecutor INFRINGEMENT_WORD_FREQUENCY_STATISTICS_BATCH_UPDATE_POOL = ExecutorUtils
            .newFixedThreadPool("infringement_word_frequency_statistics_batch_update_pool", 50);

    public static final ThreadPoolExecutor UPDATE_LISTING_GPSR_POOL = ExecutorUtils
            .newFixedThreadPool("update-listing-gpsr-pool", 10);

    // 3个 * 5台，并发高的情况，算法会存在内存不足的情况导致报错 没法并发，算法显存不足，本地测试5个比较稳
    // 单台机器消费会超时，改掉，改用队列，只有一太机器消费，直接开15个线程处理
    public static final ThreadPoolExecutor CALL_SIM_IMAGES_POLL = ExecutorUtils
            .newFixedThreadPool("call_sim_images_poll", 10);
    public static final ThreadPoolExecutor ACCOUNT_CALL_SIM_IMAGES_POLL = ExecutorUtils
            .newFixedThreadPool("account_call_sim_images_poll", 2);
    public static final ThreadPoolExecutor CALL_SIM_IMAGES_POLL_NO_GPU = ExecutorUtils
            .newFixedThreadPool("call_sim_images_poll_no_gpu", 15);

    public static final ThreadPoolExecutor STATISTICS_INFRINGING_WORD_COUNT_POOL = ExecutorUtils
            .newFixedThreadPool("statistics_infringing_word_count", 5);

    public static final ThreadPoolExecutor UPDATE_AMAZON_LISTING_GPSR = ExecutorUtils
            .newFixedThreadPool("update-listing-gpsr-pool", 5);

    /**
     * AI服务调用专用线程池
     */
    public static final ThreadPoolExecutor AI_SERVICE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-ai-service-pool", 50);

    public static final ThreadPoolExecutor UPDATE_AMAZON_LISTING_COUNTRY_TASK = ExecutorUtils
            .newFixedThreadPool("update-listing-country-task-pool", 5);

    public static final ThreadPoolExecutor SYNC_AMAZON_PRODUCT_TYPE_POOL = ExecutorUtils
            .newFixedThreadPool("sync-amazon-product-type-pool-pool", 10);

    public static final ThreadPoolExecutor UPDATE_NEW_YEAR_HANDLING_TIME = ExecutorUtils
            .newFixedThreadPool("update-new-year-handling-time-pool", 100);

    public static final ThreadPoolExecutor UPDATE_NEW_YEAR_STOCK = ExecutorUtils
            .newFixedThreadPool("update-new-year-stock-pool", 100);

    /**
     * 新品推荐重新分配店铺
     * @param runnable
     */
    public static void reassign(Runnable runnable) {
        ExecutorUtils.execute(REASSIGN_POOL, runnable, "reassign");
    }

    /**
     * 
     * @Description: 添加上传数据任务
     *
     * @param consumer
     * @return
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public static Future<ResponseJson> submitSubmitFeedTask(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SUBMIT_TASK_POOL, consumer, "amazon-submit-task");
    }

    public static CompletableFuture<Void> checkInfringementWordAsyncTask(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, AmazonExecutors.CHECK_INFRINGEMENTWORD_INFO_POOL);
    }

    /**
     * 
     * @Description: 添加上传数据进度处理任务
     *
     * @param consumer
     * @return
     * @Author: Kevin
     * @Date: 2019/03/28
     * @Version: 0.0.1
     */
    public static Future<ResponseJson> submitSubmitFeedProgress(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SUBMIT_PROGRESS_POOL, consumer, "amazon-submit-progress");
    }

    /**
     * 执行amazon同步产品任务
     * 
     * @param consumer consumer
     * @return
     */
    public static Future<ResponseJson> submitSyncProduct(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SYNC_PRODUCT_POOL, consumer, "amazon-product");
    }

    /**
     * 批量刊登校验侵权
     * @param consumer
     */
    public static Future<ResponseJson> submitCheckInfringementword(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(CHECK_INFRINGEMENTWORD_POOL, consumer, "amazon-check_infringementword");
    }

    /**
     * 批量刊登校验侵权 info
     * @param consumer
     */
    public static Future<ResponseJson> submitCheckInfringementwordInfo(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(CHECK_INFRINGEMENTWORD_INFO_POOL, consumer, "amazon-check_infringementwor_info");
    }

    /**
     * 新在线列表侵权词校验
     * @param runnable
     */
    public static void checkListingInfringementword(Runnable runnable) {
        ExecutorUtils.execute(CHECK_LISTING_INFRINGEMENTWORD_POOL, runnable, "check_listing_infringementword");
    }



    /**
     * 用于执行 针对PMS系统停产，存档的SKU,亚马逊平台可以直接把库存修改为0
     * @param runnable
     */
    public static void executeUnqualifiedSku(Runnable runnable) {
        ExecutorUtils.execute(SYNC_UNQUALIFIED_SKU_POOL, runnable, "amazon-unqualified-sku");
    }

    /**
     * 用于执行 春节调价
     * @param runnable
     */
    public static void executeUnqualifiedPriceSku(Runnable runnable) {
        ExecutorUtils.execute(SYNC_UNQUALIFIED_PRICE_SKU_POOL, runnable, "amazon-unqualified-price-sku-pool");
    }

    /**
     * 图片下载
     * @param runnable
     */
    public static void executeDownloadImage(Runnable runnable) {
        ExecutorUtils.execute(DOWNLOAD_IMAGE_POOL, runnable, "amazon-download-sku-image");
    }

    /**
     * 用來查詢的执行线程
     * @param runnable
     */
    public static void executeQuery(Runnable runnable) {
        ExecutorUtils.execute(QUERY_POOL, runnable, "amazon-query-listing");
    }

    /**
     * 用于更新在线listing关系执行线程
     * @param runnable
     */
    public static void executeUpdateListingRelationShip(Runnable runnable) {
        ExecutorUtils.execute(REALITION_SHIP_POOL, runnable, "amazon-update-relationship-pool");
    }


    /**
     * 清仓甩卖sku处理
     * @param runnable
     */
    public static void executeClearanceSaleSku(Runnable runnable) {
        ExecutorUtils.execute(CLEARANCE_SALE_POOL, runnable, "amazon-clearance-sale");
    }

    public static void executeHandleSkuSellerAmount(Runnable runnable) {
        ExecutorUtils.execute(AMAZON_SKU_COUNTRY_AMOUNT_POOL, runnable, "amazon-sku-country-amount-pool");
    }

    /**
     * 更新listing标题或者描述到亚马逊
     * @param runnable
     */
    public static void executeUpdateListingTitleOrDesc(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_AMAZON_LISTING_TITLE_POOL, runnable, "update-amazon-listing-title-pool");
    }

    /**
     * 更新listing标题或者描述到亚马逊
     * @param runnable
     */
    public static void executeSyncProdutType(Runnable runnable) {
        ExecutorUtils.execute(SYNC_AMAZON_PRODUCT_TYPE_POOL, runnable, "sync-amazon-product-type-pool");
    }


    /**
     * 更新listing关联的模板id
     * @param runnable
     */
    public static void executeAmazonListingAndTemplateRelation(Runnable runnable) {
        ExecutorUtils.execute(AMAZON_LISTING_AND_TEMPLATE_RELATION_POOL, runnable, "amazon-listing-and-template-replation-pool");
    }


    /**
     * 下载图片
     * @param runnable
     * @return
     */
    public static void downloadAmazonImage(Runnable runnable) {
        ExecutorUtils.execute(DOWNLOAD_AMAZON_IMAGE_POOL, runnable, "download-amazon-image-pool");
    }



    /**
     * 自动刊登 处理
     * @param runnable
     */
    public static void executeAutoPublish(Runnable runnable) {
        ExecutorUtils.execute(AUTO_PUBLISH_POOL, runnable, "auto-publish");
    }

    /**
     * 自动刊登 处理
     * @param runnable
     */
    public static void executeAmazonAutoQueuePublish(Runnable runnable) {
        ExecutorUtils.execute(AUTO_PUBLISH_QUEUE_POOL, runnable, "auto-publish-generate-queue");
    }


    /**
     * 执行amazon同步产品报表任务
     *
     * @param consumer consumer
     * @return
     */
    public static Future<ResponseJson> syncProductFile(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SYNC_PRODUCT_FILE_POOL, consumer, "amazon-product-file");
    }

    /**
     * 执行amazon同步产品报表任务
     *
     * @param consumer consumer
     * @return
     */
    public static Future<ResponseJson> minSyncProductFile(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(MIN_SYNC_PRODUCT_FILE_POOL, consumer, "amazon-product-file");
    }


    /**
     * 执行amazon同步产品详细信息任务
     *
     * @param consumer consumer
     * @return
     */
    public static Future<ResponseJson> syncProductDetailMsg(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SYNC_PRODUCT_DETAIL_MSG_POOL, consumer, "sync-product-deatail-msg");
    }

    /**
     * 全平台产品信息刷新任务
     *
     * @param runnable runnable
     * @return
     */
    public static void refreshAllPlatformProductInfo(Runnable runnable) {
        ExecutorUtils.execute(REFRESH_ALL_PLATFORM_PRODUCT_INFO_MSG, runnable, "refresh-all-platform-product-info-msg");
    }

    /**
     * 页面下架listing
     * @param runnable
     */
    public static void executeSaleAmazonDeleteListing(Runnable runnable) {
        ExecutorUtils.execute(DELETE_LISTING_POOL, runnable, "sale-delete-listing-pool");
    }

    /**
     * 系统下架listing
     * @param runnable
     */
    public static void executeSystemAmazonDeleteListing(Runnable runnable) {
        ExecutorUtils.execute(SYSYTEM_DELETE_LISTING_POOL, runnable, "system_delete_listing_pool");
    }

    /**
     * 停产存档下架账号维度
     * @param runnable
     */
    public static void executeDeleteArchivedStop(Runnable runnable) {
        ExecutorUtils.execute(DELETE_ARCHIVED_STOP_POOL, runnable, "delete-archived-stop");
    }


    /**
     * 用于执行 针对PMS系统停产，存档的SKU,亚马逊平台可以直接把库存修改为0
     * @param runnable
     */
    public static void executeAccountNormalStock(Runnable runnable) {
        ExecutorUtils.execute(AMAZON_NORMAL_STOCK_POLL, runnable, "amazon-normal-stock");
    }

    /**
     * Amazon算价规则应用到店铺配置
     * @param runnable
     */
    public static void executeCalcPriceRuleApplyToAccount(Runnable runnable) {
        ExecutorUtils.execute(APPLY_TO_ACCOUNT_POOL, runnable, "amazon-normal-stock");
    }

    /**
     * 同步运费
     * @param runnable
     */
    public static void executeSyncShippingCost(Runnable runnable) {
        ExecutorUtils.execute(SYNC_SHIPPING_COST, runnable, "sync-shipping-cost");
    }

    /**
     * 同步运费
     * @param runnable
     */
    public static void executeRefreshListing(Runnable runnable) {
        ExecutorUtils.execute(REFRESH_LISTING, runnable, "refresh-listing-cost");
    }

    /**
     * 检测品牌不一致数据
     * @param runnable
     */
    public static void executeCheckBrandListing(Runnable runnable) {
        ExecutorUtils.execute(CHECK_BRAND_LISTING, runnable, "check-brand-listing");
    }

    /**
     * 获取分类code
     * @param runnable
     */
    public static void executeGetCategoryCodes(Runnable runnable) {
        ExecutorUtils.execute(GET_CATEGORY_CODES_POOL, runnable, "get-category-codes");
    }

    /**
     * 侵权禁售Listing
     * @param runnable
     */
    public static void executeProhibitionListing(Runnable runnable) {
        ExecutorUtils.execute(PROHIBITION_LISTING_POOL, runnable, "prohibition-Listing(");
    }

    /**
     * 侵权禁售Listing 优先级
     * @param runnable
     */
    public static void executeUpdateProhibitionListing(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_PROHIBITION_LISTING_POOL, runnable, "refresh-prohibition-Listing(");
    }

    /**
     * 导出数据
     * @param runnable
     */
    public static void executeDownLoadData(Runnable runnable) {
        ExecutorUtils.execute(DOWNLOAD_DATA_POOL, runnable, "download-data");
    }

    /**
     * 查询模板
     * @param runnable
     */
    public static void executeQueryTemplate(Runnable runnable) {
        ExecutorUtils.execute(QUERY_TEMPLATE_POOL, runnable, "query-template");
    }

    /**
     * 修改库存
     * @param runnable
     */
    public static void executeUpdateInventory(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_INVENTORY_POOL, runnable, "update-inventory");
    }

    /**
     * 销售店铺统计
     */
    public static void executeSaleDataStatisticsTask(Runnable runnable) {
        ExecutorUtils.execute(SALE_DATA_STATISTICS, runnable, "sale-data-statistics");
    }

    /**
     * 词频流水表
     */
    public static void executeInfringementWordFrequencyTask(Runnable runnable) {
        ExecutorUtils.execute(INFRINGEMENT_WORD_FREQUENCY_POOL, runnable, "infringement-word-frequency");
    }

    /**
     * 词频批量更新数据
     */
    public static void executeInfringementWordFrequencyBatchUpdateTask(Runnable runnable) {
        ExecutorUtils.execute(INFRINGEMENT_WORD_FREQUENCY_STATISTICS_BATCH_UPDATE_POOL, runnable, "infringement-word-frequency-batch-update");
    }

    /**
     * 算法计算
     * @param runnable
     */
    public static void executeCallSimImageTask(Runnable runnable) {
        ExecutorUtils.execute(SALE_DATA_STATISTICS, runnable, "sale-data-statistics");
    }

    /**
     * 查询模板报告
     *
     * @param runnable
     */
    public static void executeQueryProcessReport(Runnable runnable) {
        ExecutorUtils.execute(QUERY_TEMPLATE_REPORT_POOL, runnable, "query-template-report");
    }

    /**
     * amazon gpsr
     *
     * @param runnable
     */
    public static void updateListingGPSR(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_LISTING_GPSR_POOL, runnable, "update-listing-gpsr");
    }


    /**
     * amazon gpsr
     *
     * @param runnable
     */
    public static void uploadListingGPSR(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_AMAZON_LISTING_GPSR, runnable, "upload-listing-gpsr");
    }

    /**
     * amazon listing origin country
     *
     * @param runnable
     */
    public static void updateListingCountryTask(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_AMAZON_LISTING_COUNTRY_TASK, runnable, "upload-listing-country-task");
    }

    /**
     * amazon 春节调发货日期
     */
    public static void executeAmazonNewYearHandlingTime(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_NEW_YEAR_HANDLING_TIME, runnable, "update-new-year-handling-time-pool");
    }

    /**
     * AI服务调用执行
     *
     * @param runnable 任务
     */
    public static void executeAiService(Runnable runnable) {
        ExecutorUtils.execute(AI_SERVICE_POOL, runnable, "amazon-ai-service");
    }

    /**
     * amazon 春节调发货日期
     */
    public static void executeAmazonNewStock(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_NEW_YEAR_STOCK, runnable, "update-new-year-stock-pool");

    }
}
