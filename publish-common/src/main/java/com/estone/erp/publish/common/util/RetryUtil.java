package com.estone.erp.publish.common.util;

import com.estone.erp.common.model.api.ApiResult;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023-04-12 15:01
 */
@Slf4j
public class RetryUtil {

    /**
     * 重试
     *
     * @param task     任务
     * @param retryNum 重试次数
     * @param <T>      返回值
     * @return T
     */
    public static <T> T doRetry(Supplier<T> task, int retryNum) {
        try {
            return task.get();
        } catch (Exception e) {
            retryNum--;

            try {
                TimeUnit.MILLISECONDS.sleep(1000);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            }
            if (retryNum >= 0) {
                return doRetry(task, retryNum);
            } else {
                log.error("任务执行失败, 失败原因：{}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }

    public static <T> ApiResult<T> doRetryApiResult(Supplier<ApiResult<T>> task, int retryNum) {
        try {
            return task.get();
        } catch (Exception e) {
            retryNum--;

            try {
                TimeUnit.MILLISECONDS.sleep(1000);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            }

            if (retryNum >= 0) {
                return doRetryApiResult(task, retryNum);
            } else {
                return ApiResult.newError(e.getMessage());
            }
        }
    }

    public static <T> ApiResult<T> doRetryApiResult(Supplier<ApiResult<T>> task, int retryNum, long timeout) {
        try {
            return task.get();
        } catch (Exception e) {
            retryNum--;

            try {
                TimeUnit.MILLISECONDS.sleep(timeout);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
            }

            if (retryNum >= 0) {
                return doRetryApiResult(task, retryNum);
            } else {
                return ApiResult.newError(e.getMessage());
            }
        }
    }


}
