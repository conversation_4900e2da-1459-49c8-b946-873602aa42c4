package com.estone.erp.publish.system.pmssalePublicData.api.platform;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.pmssalePublicData.api.PublishDataApiService;
import com.estone.erp.publish.system.pmssalePublicData.client.PublishWalmartClient;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelDO;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataDO;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataRequest;
import com.estone.erp.publish.system.product.response.PageResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-07 11:46
 */
@Service
public class WalmartPublishDataApiServiceImpl implements PublishDataApiService {
    @Autowired
    private PublishWalmartClient walmartClient;

    @Override
    public String getPlatform() {
        return SaleChannelEnum.WALMART.getChannelName();
    }

    /**
     * 获取平台首页统计数据
     *
     * @param accountNumbers 店铺
     * @return 统计数据
     */
    @Override
    public ApiResult<EsSalesStatisticsData> getStatisticsDataAccountData(List<String> accountNumbers) {
        if (CollectionUtils.isEmpty(accountNumbers)) {
            return ApiResult.newError("店铺不能为空");
        }
        try {
            return walmartClient.getStatisticsDataAccountData(accountNumbers);
        } catch (Exception e) {
            return ApiResult.newError(String.format("请求失败:%s", e.getMessage()));
        }

    }

    @Override
    public ApiResult<PageResult<PublishSpuModelDO>> getPublishSpuModelPage(PublishSpuModelRequest request) {
        if (request == null) {
            return ApiResult.newSuccess();
        }
        try {
            return walmartClient.getWalmartPublishSpuModelPage(request);
        } catch (Exception e) {
            return ApiResult.newError(String.format("请求失败:%s", e.getMessage()));
        }
    }

    @Override
    public ApiResult<List<SpTemplateDataDO>> getSPTemplateData(SpTemplateDataRequest request) {
        return ApiResult.newSuccess();
    }
}
