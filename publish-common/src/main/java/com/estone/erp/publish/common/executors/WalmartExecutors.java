package com.estone.erp.publish.common.executors;

import com.estone.erp.publish.common.json.ResponseJson;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Consumer;

/**
 * Walmart线程池管理类
 *
 * @Auther yucm
 * @Date 2021/3/8
 */
public class WalmartExecutors {

    public static final ThreadPoolExecutor SYNC_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("walmart-sync-item-pool", 30);

    public static final ThreadPoolExecutor SYNC_ITEM_INVENTORY_POOL = ExecutorUtils
            .newFixedThreadPool("walmart-sync-item-inventory-pool", 100);

    public static final ThreadPoolExecutor SYNC_ITEM_INVENTORY_NULL_POOL = ExecutorUtils
            .newFixedThreadPool("walmart-sync-item-inventory-null-pool", 5);

    public static final ThreadPoolExecutor UPDATE_ITEM_INVENTORY_POOL = ExecutorUtils
            .newFixedThreadPool("walmart-update-item-inventory-pool", 10);

    public static final ThreadPoolExecutor DOWNLOAD_WALMART_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("walmart-download-image-pool", 10);

    public static final ThreadPoolExecutor DOWNLOAD_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("amazon-download-sku-image-pool", 100);

    public static final ThreadPoolExecutor RETIRE_ITEM__POOL = ExecutorUtils
            .newFixedThreadPool("walmart-retire-item-pool", 50);

    public static final ThreadPoolExecutor SALE_DATA_STATISTICS = ExecutorUtils
            .newFixedThreadPool("walmart-sale_data_statistics-pool", 10);

    private static final ThreadPoolExecutor UPDATE_ITEM_SKU_STATUS_POOL = ExecutorUtils
            .newFixedThreadPool("update-item-sku-status-pool", 20);

    private static final ThreadPoolExecutor PUBLISH_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("publish-item-pool", 30);

    private static final ThreadPoolExecutor UPLOAD_INVENTORY_POOL = ExecutorUtils
            .newFixedThreadPool("upload-inventory-pool", 30);

    private static final ThreadPoolExecutor UPLOAD_SHIPPING_TEMPLATE_POOL = ExecutorUtils
            .newFixedThreadPool("upload-shipping-template-pool", 30);

    private static final ThreadPoolExecutor REPLACE_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("replace-item-pool", 20);

    private static final ThreadPoolExecutor UPDATE_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("update-item-pool", 10);

    private static final ThreadPoolExecutor SYNC_BRAND_POOL = ExecutorUtils
            .newFixedThreadPool("sync-brand-pool", 30);

    private static final ThreadPoolExecutor CHECK_INFRINGEMENT_WORD_POOL = ExecutorUtils
            .newFixedThreadPool("check-infringement-word-pool", 15);

    private static final ThreadPoolExecutor DELETE_ARCHIVED_STOP_POOL = ExecutorUtils
            .newFixedThreadPool("delete-archived-stop-pool", 10);

    public static final ThreadPoolExecutor CHECK_ITEM_INFRINGEMENT_WORD_POOL = ExecutorUtils
            .newFixedThreadPool("check_item_infringement_word_pool", 100);

    private static final ThreadPoolExecutor SPU_PUBLISH_QUANTITY_LIMIT_POOL = ExecutorUtils
            .newFixedThreadPool("spu_publish_quantity_limit_pool", 20);

    private static final ThreadPoolExecutor RETIRE_NO_SALES_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("retire_no_sales_item_pool", 10);

    private static final ThreadPoolExecutor UPDATE_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("update_image_pool", 5);

    public static final ThreadPoolExecutor CHECK_TITLE_SIMILARITY = ExecutorUtils
            .newFixedThreadPool("check_title_similarity", 20);

    public static final ThreadPoolExecutor RETIRE_NON_SPECIAL_SUPPLY_SHOP_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("retire-non-special-supply-shop-item-pool", 10);

    public static final ThreadPoolExecutor RETIRE_SPECIAL_SUPPLY_SHOP_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("retire-special-supply-shop-item-pool", 10);
    /**
     * 按账号同步在线列表
     * @param runnable
     */
    public static void syncItem(Runnable runnable) {
        ExecutorUtils.execute(SYNC_ITEM_POOL, runnable, "walmart-sync-item");
    }

    /**
     * 同步在线列表库存
     * @param consumer
     * @return
     */
    public static Future<ResponseJson> syncItemInventory(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(SYNC_ITEM_INVENTORY_POOL, consumer, "walmart-sync-item-inventory");
    }

    /**
     * 同步库存为空的item库存
     * @param runnable
     */
    public static void syncItemInventoryNull(Runnable runnable) {
        ExecutorUtils.execute(SYNC_ITEM_INVENTORY_NULL_POOL, runnable, "walmart-sync-item-inventory-null");
    }

    /**
     * 下架产品
     * @param consumer
     * @return
     */
    public static Future<ResponseJson> retireItem(Consumer<ResponseJson> consumer) {
        return ExecutorUtils.submit(RETIRE_ITEM__POOL, consumer, "walmart-retire-item");
    }

    /**
     * 自动下架无销量产品
     * @param runnable
     */
    public static void retireNoSalesItem(Runnable runnable) {
        ExecutorUtils.execute(RETIRE_NO_SALES_ITEM_POOL, runnable, "walmart-retire-no-sales-item");
    }

    /**
     * 修改在线列表库存
     * @param runnable
     */
    public static void updateItemInventory(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_ITEM_INVENTORY_POOL, runnable, "walmart-update-item-inventory");
    }

    /**
     * 下载图片
     * @param runnable
     * @return
     */
    public static void downloadWalmartImage(Runnable runnable) {
        ExecutorUtils.execute(DOWNLOAD_WALMART_IMAGE_POOL, runnable, "walmart-download-image");
    }

    /**
     * 图片下载
     * @param runnable
     */
    public static void executeDownloadImage(Runnable runnable) {
        ExecutorUtils.execute(DOWNLOAD_IMAGE_POOL, runnable, "walmart-download-sku-image");
    }

    /**
     * 刊登产品
     * @param runnable
     */
    public static void executePublishItem(Runnable runnable) {
        ExecutorUtils.execute(PUBLISH_ITEM_POOL, runnable, "walmart-publish-item");
    }

    /**
     * 上传库存
     * @param runnable
     */
    public static void executeUploadInventory(Runnable runnable) {
        ExecutorUtils.execute(UPLOAD_INVENTORY_POOL, runnable, "walmart-upload-inventory");
    }

    /**
     * 上传运费模板
     * @param runnable
     */
    public static void executeUploadShippingTemplate(Runnable runnable) {
        ExecutorUtils.execute(UPLOAD_SHIPPING_TEMPLATE_POOL, runnable, "walmart-upload-shipping-template");
    }

    /**
     * 修改标题描述
     * @param runnable
     */
    public static void executeReplaceItem(Runnable runnable) {
        ExecutorUtils.execute(REPLACE_ITEM_POOL, runnable, "walmart-replace-item");
    }

    /**
     * 更新item
     * @param runnable
     */
    public static void executeUpdateItem(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_ITEM_POOL, runnable, "walmart-update-item");
    }

    /**
     * 同步品牌
     * @param runnable
     */
    public static void executeSyncBrand(Runnable runnable) {
        ExecutorUtils.execute(SYNC_BRAND_POOL, runnable, "walmart-sync-brand");
    }

    /**
     * 校验侵权词
     * @param runnable
     * @return
     */
    public static CompletableFuture<Void> submitAsyncTortTask(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, CHECK_INFRINGEMENT_WORD_POOL);
    }

    /**
     * 下架停产存档sku
     * @param runnable
     */
    public static void executeDeleteArchivedStop(Runnable runnable) {
        ExecutorUtils.execute(DELETE_ARCHIVED_STOP_POOL, runnable, "walmart-delete-archived-stop");
    }

    /**
     * item校验侵权词
     * @param runnable
     */
    public static void checkItemInfringementWord(Runnable runnable) {
        ExecutorUtils.execute(CHECK_ITEM_INFRINGEMENT_WORD_POOL, runnable, "check_item_infringement_word");
    }

    /**
     * SPU刊登店铺数量限制
     * @param runnable
     */
    public static void spuPublishQuantityLimit(Runnable runnable) {
        ExecutorUtils.execute(SPU_PUBLISH_QUANTITY_LIMIT_POOL, runnable, "spu_publish_quantity_limit");
    }

    /**
     * 修改图片
     * @param runnable
     */
    public static void executeUpdateImage(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_IMAGE_POOL, runnable, "walmart-update-image");
    }


    /**
     * 非特供账号自动下架特供标签产品
     *
     * @param runnable
     */
    public static void executeRetireNonSpecialSupplyShopItem(Runnable runnable) {
        ExecutorUtils.execute(RETIRE_NON_SPECIAL_SUPPLY_SHOP_ITEM_POOL, runnable, "retire-non-special-supply-shop-item");
    }


    /**
     * 特供账号自动下架非店铺选择的特供标签产品
     *
     * @param runnable
     */
    public static void executeRetireSpecialSupplyShopItem(Runnable runnable) {
        ExecutorUtils.execute(RETIRE_SPECIAL_SUPPLY_SHOP_ITEM_POOL, runnable, "retire-special-supply-shop-item");
    }
}
