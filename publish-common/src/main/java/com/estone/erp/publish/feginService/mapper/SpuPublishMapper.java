package com.estone.erp.publish.feginService.mapper;

import com.estone.erp.publish.feginService.modle.SpuPublishRecord;
import com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/20 16:53
 * @description
 */
public interface SpuPublishMapper {

    /**
     * 获取amazon对应站点 范本spu集合
     * @param site
     * @return
     */
    List<String> getAmazonSpuList(@Param("site") String site);


    List<String> getSmtSpuList();

    /**
     * 托管数据
     * @return
     */
    List<String> getSmtTgSpuList();


    List<String> getEbaySpuList(@Param("site") String site);


    List<String> getShopeeSpuList(@Param("site") String site);

    /**
     * smt spu刊登记录
     * @param articleNumber
     * @return
     */
    List<SpuPublishRecord> getSmtSpuRecordList(@Param("articleNumber") String articleNumber);

    /**
     * Amazon spu刊登记录
     * @param articleNumber
     * @return
     */
    List<SpuPublishRecord> getAmazonSpuRecordList(@Param("articleNumber") String articleNumber);

    /**
     * Ebay SPU刊登记录
     * @param articleNumber
     * @return
     */
    List<SpuPublishRecord> getEbaySpuRecordList(@Param("articleNumber") String articleNumber);

    /**
     * Amazon spu存在的范本站点
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectAmazonTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * ebay spu存在的范本站点
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectEbayTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * SMT spu存在的范本站点
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectSmtTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * Shopee spu存在的范本站点
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectShopeeTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * shopee spu 存在的范本
     * @param list
     * @return
     */
    List<String> selectShopeeGlobalTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * Ozon spu 存在的范本
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectOzonTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * tiktok spu存在的范本站点
     */
    List<SpuPublishBasicInfo> selectTiktokTemplateSiteBySpu(@Param("list") List<String> list);

    /**
     * Walmart spu存在的范本站点
     * @param list
     * @return
     */
    List<SpuPublishBasicInfo> selectWalmartTemplateSiteBySpu(@Param("list") List<String> list);
}
