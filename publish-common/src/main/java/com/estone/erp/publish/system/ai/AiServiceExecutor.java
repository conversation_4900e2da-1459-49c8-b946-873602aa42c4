package com.estone.erp.publish.system.ai;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * AI服务调用执行器 - 支持线程池和信号量控制
 * 参考WalmartTaskOffLinkListingRetireJobHandler的信号量使用方式
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-16
 */
@Slf4j
@Component
public class AiServiceExecutor {

    @Autowired
    private AiServiceClient aiServiceClient;

    /**
     * AI服务调用线程池
     */
    private ThreadPoolExecutor aiThreadPool;

    /**
     * 默认线程数量
     */
    private static final int DEFAULT_THREAD_NUM = 10;

    /**
     * 初始化线程池
     */
    @PostConstruct
    public void initThreadPool() {
        // 创建线程池：核心线程数10，最大线程数20，空闲时间60秒
        this.aiThreadPool = new ThreadPoolExecutor(
                DEFAULT_THREAD_NUM,
                DEFAULT_THREAD_NUM * 2,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "AI-Service-Worker-" + threadNumber.getAndIncrement());
                        t.setDaemon(false);
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        log.info("AI服务调用线程池初始化完成，默认并发数：{}", DEFAULT_THREAD_NUM);
    }

    /**
     * 销毁线程池
     */
    @PreDestroy
    public void destroyThreadPool() {
        if (aiThreadPool != null) {
            aiThreadPool.shutdown();
            try {
                if (!aiThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                    aiThreadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                aiThreadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("AI服务调用线程池已关闭");
        }
    }

    /**
     * 批量调用AI服务 - 使用线程池和信号量控制速率
     * 
     * @param requests AI请求列表
     * @param threadNum 线程数量，默认为10
     * @param aiServiceCall AI服务调用函数
     * @return 调用结果列表
     * @throws InterruptedException 中断异常
     */
    public <T> List<ApiResult<T>> batchCallAiService(
            List<ChatOpenaiRequest> requests,
            Integer threadNum,
            Function<ChatOpenaiRequest, ApiResult<T>> aiServiceCall) throws InterruptedException {
        
        if (requests == null || requests.isEmpty()) {
            return List.of();
        }

        // 使用传入的线程数量，如果为空则使用默认值
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : DEFAULT_THREAD_NUM;
        
        // 创建信号量控制并发速率
        final Semaphore semaphore = new Semaphore(actualThreadNum);
        
        // 存储结果的并发安全列表
        List<CompletableFuture<ApiResult<T>>> futures = new CopyOnWriteArrayList<>();
        
        log.info("开始批量调用AI服务，请求数量：{}，并发线程数：{}", requests.size(), actualThreadNum);
        
        // 为每个请求创建异步任务
        for (ChatOpenaiRequest request : requests) {
            CompletableFuture<ApiResult<T>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 获取信号量许可
                    semaphore.acquire();
                    try {
                        // 调用AI服务
                        return aiServiceCall.apply(request);
                    } finally {
                        // 释放信号量许可
                        semaphore.release();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("AI服务调用被中断", e);
                    return ApiResult.newError("AI服务调用被中断: " + e.getMessage());
                } catch (Exception e) {
                    log.error("AI服务调用失败", e);
                    return ApiResult.newError("AI服务调用失败: " + e.getMessage());
                }
            }, aiThreadPool);
            
            futures.add(future);
        }
        
        // 等待所有任务完成并收集结果
        List<ApiResult<T>> results = new CopyOnWriteArrayList<>();
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );
        
        try {
            // 等待所有任务完成，最多等待30分钟
            allFutures.get(30, TimeUnit.MINUTES);
            
            // 收集结果
            for (CompletableFuture<ApiResult<T>> future : futures) {
                results.add(future.get());
            }
            
        } catch (TimeoutException e) {
            log.warn("部分AI服务调用未在30分钟内完成");
            // 收集已完成的结果
            for (CompletableFuture<ApiResult<T>> future : futures) {
                if (future.isDone()) {
                    try {
                        results.add(future.get());
                    } catch (Exception ex) {
                        log.error("获取AI服务调用结果失败", ex);
                        results.add(ApiResult.newError("获取结果失败: " + ex.getMessage()));
                    }
                }
            }
        } catch (ExecutionException e) {
            log.error("AI服务调用执行失败", e);
            throw new RuntimeException("AI服务调用执行失败", e);
        }
        
        log.info("批量AI服务调用完成，成功获取结果数量：{}", results.size());
        return results;
    }

    /**
     * 批量调用腾讯deepseek AI服务
     * 
     * @param requests AI请求列表
     * @param threadNum 线程数量，默认为10
     * @return 调用结果列表
     * @throws InterruptedException 中断异常
     */
    public List<ApiResult<ChatCompletionResponse>> batchCallTencentAi(
            List<ChatOpenaiRequest> requests, Integer threadNum) throws InterruptedException {
        return batchCallAiService(requests, threadNum, aiServiceClient::sendProcessOrdinaryTencent);
    }

    /**
     * 批量调用OpenAI服务
     * 
     * @param requests AI请求列表
     * @param threadNum 线程数量，默认为10
     * @return 调用结果列表
     * @throws InterruptedException 中断异常
     */
    public List<ApiResult<ChatCompletionResponse>> batchCallOpenAi(
            List<ChatOpenaiRequest> requests, Integer threadNum) throws InterruptedException {
        return batchCallAiService(requests, threadNum, aiServiceClient::sendProcessOrdinary);
    }

    /**
     * 单个AI服务调用 - 带信号量控制
     * 
     * @param request AI请求
     * @param threadNum 线程数量，用于创建信号量
     * @param aiServiceCall AI服务调用函数
     * @return 调用结果
     * @throws InterruptedException 中断异常
     */
    public <T> ApiResult<T> callAiServiceWithRateLimit(
            ChatOpenaiRequest request,
            Integer threadNum,
            Function<ChatOpenaiRequest, ApiResult<T>> aiServiceCall) throws InterruptedException {
        
        // 使用传入的线程数量，如果为空则使用默认值
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : DEFAULT_THREAD_NUM;
        
        // 创建信号量控制速率
        final Semaphore semaphore = new Semaphore(actualThreadNum);
        
        // 获取信号量许可
        semaphore.acquire();
        try {
            // 调用AI服务
            return aiServiceCall.apply(request);
        } finally {
            // 释放信号量许可
            semaphore.release();
        }
    }

    /**
     * 单个腾讯deepseek AI服务调用 - 带速率控制
     * 
     * @param request AI请求
     * @param threadNum 线程数量，默认为10
     * @return 调用结果
     * @throws InterruptedException 中断异常
     */
    public ApiResult<ChatCompletionResponse> callTencentAiWithRateLimit(
            ChatOpenaiRequest request, Integer threadNum) throws InterruptedException {
        return callAiServiceWithRateLimit(request, threadNum, aiServiceClient::sendProcessOrdinaryTencent);
    }

    /**
     * 单个OpenAI服务调用 - 带速率控制
     * 
     * @param request AI请求
     * @param threadNum 线程数量，默认为10
     * @return 调用结果
     * @throws InterruptedException 中断异常
     */
    public ApiResult<ChatCompletionResponse> callOpenAiWithRateLimit(
            ChatOpenaiRequest request, Integer threadNum) throws InterruptedException {
        return callAiServiceWithRateLimit(request, threadNum, aiServiceClient::sendProcessOrdinary);
    }

    /**
     * 获取线程池状态信息
     * 
     * @return 线程池状态描述
     */
    public String getThreadPoolStatus() {
        if (aiThreadPool == null) {
            return "线程池未初始化";
        }
        
        return String.format("线程池状态 - 核心线程数: %d, 最大线程数: %d, 活跃线程数: %d, 队列大小: %d",
                aiThreadPool.getCorePoolSize(),
                aiThreadPool.getMaximumPoolSize(),
                aiThreadPool.getActiveCount(),
                aiThreadPool.getQueue().size());
    }
}
