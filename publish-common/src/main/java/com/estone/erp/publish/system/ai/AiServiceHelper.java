package com.estone.erp.publish.system.ai;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * AI服务调用辅助类 - 使用信号量+线程池控制并发
 * 参考WalmartTaskOffLinkListingRetireJobHandler的实现方式
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-16
 */
@Slf4j
@Component
public class AiServiceHelper {

    @Autowired
    private AiServiceClient aiServiceClient;

    /**
     * 创建AI服务调用线程池
     * 
     * @return 线程池
     */
    public static ThreadPoolExecutor createAiThreadPool() {
        return new ThreadPoolExecutor(
                10,
                20,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "AI-Service-" + threadNumber.getAndIncrement());
                        t.setDaemon(false);
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 批量调用腾讯AI服务 - 使用信号量控制并发
     * 参考WalmartTaskOffLinkListingRetireJobHandler的方式
     * 
     * @param dataList 数据列表
     * @param threadNum 线程数量，默认为10
     * @param processor 数据处理器
     * @param <T> 数据类型
     * @throws InterruptedException 中断异常
     */
    public <T> void batchCallTencentAi(List<T> dataList, Integer threadNum, 
                                       AiDataProcessor<T> processor) throws InterruptedException {
        
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 使用传入的线程数量，如果为空则使用默认值10
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;
        
        // 控制线程池数量，创建Semaphore信号量
        final Semaphore sp = new Semaphore(actualThreadNum);
        
        // 创建线程池
        ThreadPoolExecutor executor = createAiThreadPool();
        
        log.info("开始批量调用腾讯AI服务，数据数量：{}，并发线程数：{}", dataList.size(), actualThreadNum);
        
        try {
            // 按数据循环处理
            for (T data : dataList) {
                sp.acquire();
                executor.execute(() -> {
                    try {
                        // 处理单个数据的AI调用
                        processDataWithAi(data, processor);
                    } catch (Exception e) {
                        log.error("处理数据时发生异常：", e);
                    } finally {
                        sp.release();
                    }
                });
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.MINUTES)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("批量AI服务调用完成");
    }

    /**
     * 批量调用OpenAI服务 - 使用信号量控制并发
     * 
     * @param dataList 数据列表
     * @param threadNum 线程数量，默认为10
     * @param processor 数据处理器
     * @param <T> 数据类型
     * @throws InterruptedException 中断异常
     */
    public <T> void batchCallOpenAi(List<T> dataList, Integer threadNum, 
                                    AiDataProcessor<T> processor) throws InterruptedException {
        
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        // 使用传入的线程数量，如果为空则使用默认值10
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;
        
        // 控制线程池数量，创建Semaphore信号量
        final Semaphore sp = new Semaphore(actualThreadNum);
        
        // 创建线程池
        ThreadPoolExecutor executor = createAiThreadPool();
        
        log.info("开始批量调用OpenAI服务，数据数量：{}，并发线程数：{}", dataList.size(), actualThreadNum);
        
        try {
            // 按数据循环处理
            for (T data : dataList) {
                sp.acquire();
                executor.execute(() -> {
                    try {
                        // 处理单个数据的AI调用
                        processDataWithOpenAi(data, processor);
                    } catch (Exception e) {
                        log.error("处理数据时发生异常：", e);
                    } finally {
                        sp.release();
                    }
                });
            }
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.MINUTES)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("批量AI服务调用完成");
    }

    /**
     * 处理单个数据的腾讯AI调用
     * 
     * @param data 数据
     * @param processor 处理器
     * @param <T> 数据类型
     */
    private <T> void processDataWithAi(T data, AiDataProcessor<T> processor) {
        try {
            // 构建AI请求
            ChatOpenaiRequest request = processor.buildRequest(data);
            
            // 调用腾讯AI服务
            ApiResult<ChatCompletionResponse> response = aiServiceClient.sendProcessOrdinaryTencent(request);
            
            // 处理响应
            processor.handleResponse(data, response);
            
        } catch (Exception e) {
            log.error("AI服务调用失败", e);
            processor.handleError(data, e);
        }
    }

    /**
     * 处理单个数据的OpenAI调用
     * 
     * @param data 数据
     * @param processor 处理器
     * @param <T> 数据类型
     */
    private <T> void processDataWithOpenAi(T data, AiDataProcessor<T> processor) {
        try {
            // 构建AI请求
            ChatOpenaiRequest request = processor.buildRequest(data);
            
            // 调用OpenAI服务
            ApiResult<ChatCompletionResponse> response = aiServiceClient.sendProcessOrdinary(request);
            
            // 处理响应
            processor.handleResponse(data, response);
            
        } catch (Exception e) {
            log.error("AI服务调用失败", e);
            processor.handleError(data, e);
        }
    }

    /**
     * AI数据处理器接口
     * 
     * @param <T> 数据类型
     */
    public interface AiDataProcessor<T> {
        
        /**
         * 构建AI请求
         * 
         * @param data 数据
         * @return AI请求
         */
        ChatOpenaiRequest buildRequest(T data);
        
        /**
         * 处理AI响应
         * 
         * @param data 原始数据
         * @param response AI响应
         */
        void handleResponse(T data, ApiResult<ChatCompletionResponse> response);
        
        /**
         * 处理错误
         * 
         * @param data 原始数据
         * @param error 错误
         */
        default void handleError(T data, Exception error) {
            log.error("处理数据 {} 时发生错误：{}", data, error.getMessage());
        }
    }
}
