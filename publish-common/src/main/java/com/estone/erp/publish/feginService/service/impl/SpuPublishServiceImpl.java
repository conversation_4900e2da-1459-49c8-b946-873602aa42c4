package com.estone.erp.publish.feginService.service.impl;


import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.MapUtils;
import com.estone.erp.publish.feginService.mapper.SpuPublishMapper;
import com.estone.erp.publish.feginService.modle.SpuPublishBasicInfo;
import com.estone.erp.publish.feginService.modle.SpuPublishRecord;
import com.estone.erp.publish.feginService.service.SpuPublishService;
import com.estone.erp.publish.platform.enums.EbayCommonlyUsedSiteEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.pmssalePublicData.PublishAmazonUtils;
import com.estone.erp.publish.system.pmssalePublicData.PublishSmtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("SpuPublishService")
@Slf4j
public class SpuPublishServiceImpl implements SpuPublishService {

    @Resource
    private SpuPublishMapper spuPublishMapper;

    @Override
    public List<String> getSpuListByPlatform(String platform, String site) {

        if(StringUtils.isBlank(platform) || StringUtils.isBlank(site)){
            return null;
        }

        if(StringUtils.equalsIgnoreCase(platform, Platform.Amazon.name())){
            return spuPublishMapper.getAmazonSpuList(site);
        }else if(StringUtils.equalsIgnoreCase(platform, Platform.Smt.name())){
            if(StringUtils.isNotBlank(site) && StringUtils.equalsIgnoreCase(site, "SMT-TG")){
                return spuPublishMapper.getSmtTgSpuList();
            }
            return spuPublishMapper.getSmtSpuList();
        }else if(StringUtils.equalsIgnoreCase(platform, Platform.Ebay.name())){
            String ebaySite = EbayCommonlyUsedSiteEnum.getEbaySite(site);
            if(StringUtils.isBlank(ebaySite)) {
                return null;
            }
            return spuPublishMapper.getEbaySpuList(ebaySite);
        }else if(StringUtils.equalsIgnoreCase(platform, Platform.Shopee.name()) || StringUtils.equalsIgnoreCase(platform, StrConstant.SHOPEE_CNSC)){

            if(StringUtils.isBlank(site)){
                return null;
            }

//            String likeSite = "%" + "\"site\":\"" + site + "\"" + "%";
            return spuPublishMapper.getShopeeSpuList(site.toUpperCase());
        }

        return null;
    }

    @Override
    public List<SpuPublishRecord> getSpuRecordList(String articleNumber){
        Assert.notNull(articleNumber);
        //TODO 其他平台自己添加list查询，记得创建时间排序
        List<SpuPublishRecord> spuPublishRecordList = new ArrayList<>();
        try {
            //查询Amazon
            //List<SpuPublishRecord> amazonRecordList = spuPublishMapper.getAmazonSpuRecordList(articleNumber);
            ApiResult<List<SpuPublishRecord>> listApiResult = PublishAmazonUtils.getAmazonSpuPublishRecord(articleNumber, Platform.Amazon.name());
            if (null != listApiResult && listApiResult.isSuccess()) {
                spuPublishRecordList.addAll(listApiResult.getResult());
            }
        } catch (Exception e) {
            log.error("获取Amazon spu 刊登记录出错" + articleNumber);
        }
        try {
            ApiResult<List<SpuPublishRecord>> listApiResult = PublishSmtUtils.getSmtSpuRecordList(articleNumber, Platform.Smt.name());
            if (null != listApiResult && listApiResult.isSuccess()) {
                spuPublishRecordList.addAll(listApiResult.getResult());
            }
        } catch (Exception e) {
            log.error("获取Smt spu 刊登记录出错" + articleNumber);
        }
        // 查询Ebay
        List<SpuPublishRecord> ebayRecordList = spuPublishMapper.getEbaySpuRecordList(articleNumber);
        spuPublishRecordList.addAll(ebayRecordList);


        return spuPublishRecordList;
    }

    @Override
    public List<SpuPublishRecord> getAmazonSpuPublishRecord(String articleNumber,String platform) {
        Assert.notNull(articleNumber);
        if (StringUtils.isNotEmpty(platform) && Platform.Amazon.name().equalsIgnoreCase(platform)) {
            //查询Amazon
            List<SpuPublishRecord> amazonRecordList = spuPublishMapper.getAmazonSpuRecordList(articleNumber);
            return amazonRecordList;
        }
        return null;
    }

    @Override
    public List<SpuPublishRecord> getSmtSpuPublishRecord(String articleNumber,String platform) {
        Assert.notNull(articleNumber);
        if (StringUtils.isNotEmpty(platform) && Platform.Smt.name().equalsIgnoreCase(platform)) {
            //查询Amazon
            List<SpuPublishRecord> amazonRecordList = spuPublishMapper.getSmtSpuRecordList(articleNumber);
            return amazonRecordList;
        }
        return null;
    }

    /**
     *
     * @param spuList
     * @return <平台, <spu, 站点>>
     */
    @Override
    public Map<String, Map<String, Set<String>>> findTemplateSiteBySpu(List<String> spuList,String selectPlatform) {
        Map<String, Map<String, Set<String>>> map = new HashMap<>();

        if (null != selectPlatform &&  Platform.Amazon.name().equalsIgnoreCase(selectPlatform)) {
            //查询Amazon
            List<SpuPublishBasicInfo> amazonList = spuPublishMapper.selectAmazonTemplateSiteBySpu(spuList);
            Map<String, Set<String>> amazonSpuSite = amazonList.stream()
                    .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> StringUtils.isEmpty(o.getSite()) ? "-" : o.getSite(), Collectors.toSet())));
            map.put(Platform.Amazon.name(), amazonSpuSite);
        }else if (null != selectPlatform &&  Platform.Smt.name().equalsIgnoreCase(selectPlatform)) {
            // 查询SMT
            List<SpuPublishBasicInfo> smtList = spuPublishMapper.selectSmtTemplateSiteBySpu(spuList);
            Map<String, Set<String>> smtSpuSite = smtList.stream()
                    .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> StringUtils.isEmpty(o.getSite()) ? "-" : o.getSite(), Collectors.toSet())));
            map.put(Platform.Smt.name(), smtSpuSite);
        }else if (null != selectPlatform &&  Platform.Ozon.name().equalsIgnoreCase(selectPlatform)) {
            // 查询Ozon
            List<SpuPublishBasicInfo> smtList = spuPublishMapper.selectOzonTemplateSiteBySpu(spuList);
            Map<String, Set<String>> smtSpuSite = smtList.stream()
                    .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> StringUtils.isEmpty(o.getSite()) ? "-" : o.getSite(), Collectors.toSet())));
            map.put(Platform.Ozon.name(), smtSpuSite);
        } else {
            // 查询Tiktok
            List<SpuPublishBasicInfo> tiktokList = spuPublishMapper.selectTiktokTemplateSiteBySpu(spuList);
            Map<String, Set<String>> tiktokSpuSite = new HashMap<>();
            Map<String, List<SpuPublishBasicInfo>> spuMap = tiktokList.stream().collect(Collectors.groupingBy(SpuPublishBasicInfo::getSpu));
            for (Map.Entry<String, List<SpuPublishBasicInfo>> entry : spuMap.entrySet()) {
                Set<String> siteSet = new HashSet<>();
                List<SpuPublishBasicInfo> basicInfos = entry.getValue();
                for (SpuPublishBasicInfo basicInfo : basicInfos) {
                    String accountNumbers = basicInfo.getSite();
                    if (StringUtils.isBlank(accountNumbers)) {
                        continue;
                    }
                    for (String accountNumber : CommonUtils.splitList(accountNumbers, ",")) {
                        siteSet.add(StringUtils.substringAfterLast(accountNumber, "-"));
                    }
                }
                tiktokSpuSite.put(entry.getKey(), siteSet);
            }
            map.put(Platform.Tiktok.name(), tiktokSpuSite);

            // 查询ebay
            List<SpuPublishBasicInfo> ebayList = spuPublishMapper.selectEbayTemplateSiteBySpu(spuList);
            Map<String, Set<String>> ebaySpuSite = ebayList.stream()
                    .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> StringUtils.isEmpty(o.getSite()) ? "-" : EbayCommonlyUsedSiteEnum.getSite(o.getSite()), Collectors.toSet())));
            map.put(Platform.Ebay.name(), ebaySpuSite);

            // 查询Walmart
            try {
                List<SpuPublishBasicInfo> walmartList = spuPublishMapper.selectWalmartTemplateSiteBySpu(spuList);
                Map<String, Set<String>> walmartSpuSite = new HashMap<>();
                if (walmartList != null && !walmartList.isEmpty()) {
                    walmartSpuSite = walmartList.stream()
                            .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> "US", Collectors.toSet())));
                    map.put(Platform.Walmart.name(), walmartSpuSite);
                }
            } catch (Exception e) {
                log.error("获取Walmart spu 范本查询出错: " + e.getMessage());
            }

            //查询Shopee
            List<SpuPublishBasicInfo> shopeeSpuList = spuPublishMapper.selectShopeeTemplateSiteBySpu(spuList);
            Map<String, Set<String>> shopeeSpuSite = shopeeSpuList.stream()
                    .collect(Collectors.groupingBy(o -> o.getSpu(), Collectors.mapping(o -> StringUtils.isEmpty(o.getSite()) ? "-" : o.getSite(), Collectors.toSet())));
            map.put(Platform.Shopee.name(), shopeeSpuSite);

            List<String> shopeeGlobalSpuList = spuPublishMapper.selectShopeeGlobalTemplateSiteBySpu(spuList);
            Map<String, Set<String>> cnscMap = new HashMap<>(shopeeGlobalSpuList.size());
            shopeeGlobalSpuList.stream().forEach(spu -> {
                Set<String> set = MapUtils.putIfAbsent(cnscMap, spu, () -> new HashSet<>(1));
                set.add(StrConstant.CNSC);
            });
            map.put(Platform.Shopee.name() + "-" +StrConstant.CNSC, cnscMap);
//            shopeeGlobalSpuList.stream().forEach(spu -> {
//                Set<String> set = MapUtils.putIfAbsent(shopeeSpuSite, spu, () -> new HashSet<>());
//                set.add(StrConstant.CNSC);
//            });

        }
    return map;
    }
}
