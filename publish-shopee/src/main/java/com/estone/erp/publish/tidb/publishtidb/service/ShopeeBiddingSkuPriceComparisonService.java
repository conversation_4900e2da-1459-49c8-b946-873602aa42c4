package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeBiddingSkuPriceComparisonDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeBiddingSkuPriceComparisonVO;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingSkuPriceComparison;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * Shopee竞价SKU对比价格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface ShopeeBiddingSkuPriceComparisonService extends IService<ShopeeBiddingSkuPriceComparison> {

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 分页结果
     */
    CQueryResult<ShopeeBiddingSkuPriceComparisonVO> queryPage(CQuery<ShopeeBiddingSkuPriceComparisonDO> query);

    /**
     * 导出
     *
     * @param query 查询条件
     * @return 导出结果
     */
    ApiResult<String> download(CQuery<ShopeeBiddingSkuPriceComparisonDO> query);

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果
     */
    ApiResult<String> importData(MultipartFile file);

    /**
     * 导出模板
     *
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 批量确认
     *
     * @param query 查询条件
     * @return 确认结果
     */
    ApiResult<String> batchConfirm(CQuery<ShopeeBiddingSkuPriceComparisonDO> query);

    /**
     * 批量修改价格
     *
     * @param updateList 价格更新列表
     * @return 更新结果
     */
    ApiResult<String> batchUpdatePrice(List<ShopeeBiddingSkuPriceComparison> updateList);

    /**
     * 批量删除
     * @param ids
     * @return
     */
    ApiResult<String> batchDelete(List<Long> ids);

    /**
     * 批量匹配竞品
     *
     * @param competitorList
     * @return
     */
    ApiResult<String> batchMatchCompetitor(List<ShopeeBiddingSkuPriceComparisonDO> competitorList);
}
