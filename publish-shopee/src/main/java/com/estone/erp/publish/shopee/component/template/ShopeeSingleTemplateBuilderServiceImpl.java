package com.estone.erp.publish.shopee.component.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.param.add.ShopeeSizeChartInfoParamTemplate;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.dto.ShopeeListingOnlineConfigDTO;
import com.estone.erp.publish.shopee.dto.ShopeeProductColumnInfo;
import com.estone.erp.publish.shopee.dto.ShopeeSpuGlobalPublishParam;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAdminGlobalTemplate;
import com.estone.erp.publish.shopee.model.ShopeeAdminGlobalTemplateExample;
import com.estone.erp.publish.shopee.model.ShopeeCategoryV2;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartApplicableCategory;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartTemplates;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartApplicableCategoryService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartTemplatesService;
import com.estone.erp.publish.tidb.publishtidb.service.UnsalableSkuService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理单品
 *
 * <AUTHOR>
 * @date 2022-12-05 14:19
 */
@Slf4j
@Service
public class ShopeeSingleTemplateBuilderServiceImpl implements ShopeeGlobalTemplateBuilderService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Autowired
    private ShopeeTemplateService shopeeTemplateService;

    @Autowired
    private SingleItemEsService singleItemEsService;

    @Autowired
    private ShopeeBrandV2Service shopeeBrandV2Service;

    @Autowired
    private ShopeeAdminGlobalTemplateService shopeeAdminGlobalTemplateService;

    @Autowired
    private CategoryMappingService categoryMappingService;

    @Autowired
    private ShopeeCategoryV2Service shopeeCategoryV2Service;

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private UnsalableSkuService unsalableSkuService;

    @Resource
    private ShopeeSizeChartTemplatesService shopeeSizeChartTemplatesService;
    @Resource
    private ShopeeSizeChartApplicableCategoryService shopeeSizeChartApplicableCategoryService;

    /**
     * 构建全球范本
     *
     * @param sku             sku
     * @param copyWritingType 文案类型
     */
    @Override
    public EsShopeeGlobalTemplate buildSpGlobalTemplate(String sku, Integer copyWritingType) {
        List<ProductInfo> skuList = ProductUtils.findProductInfos(Lists.newArrayList(sku));
        if (CollectionUtils.isEmpty(skuList)) {
            throw new NoSuchElementException("无效的货号!");
        }
        skuList = filterStatus(false, skuList);
        if (CollectionUtils.isEmpty(skuList)) {
            throw new RuntimeException("SKU的单品状态全为停产，存档，废弃，不能创建模板");
        }
        String fullpathcode = skuList.get(0).getFullpathcode();
        boolean b = ShopeeGlobalTemplateUtil.checkSystemFullPathCodeCanPublish(fullpathcode);
        if (!b) {
            throw new RuntimeException("该产品系统分类平台禁止刊登");
        }

        // 图片迁移
        List<String> publicImages = shopeeTemplateService.getAllImagesBySku(sku);
        if (CollectionUtils.isEmpty(publicImages)) {
            throw new NoSuchElementException("无图片的货号!");
        }
        Collections.shuffle(publicImages);

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setSku(sku);
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());

        //视频
        String mainSku = skuList.get(0).getMainSku();
        String videoLink = ShopeeProductInfoUtil.getVideoLink(mainSku);
        shopeeTemplate.setVideo(videoLink);

        // 图片设置进json
        shopeeTemplate.setImagesList(publicImages);
        // 生成子属性sku 范本无账号全球商品价格使用默认系统配置利润
        List<ShopeeSku> shopeeSkus = generateShopeeSkus(skuList, publicImages, null, ShopeeCountryEnum.Singapore.getCode());
        // 所有站点集合
        List<String> allSites = Arrays.stream(ShopeeCountryEnum.values()).map(ShopeeCountryEnum::getCode).collect(Collectors.toList());
        // 过滤所有站点禁售子SKU
        ShopeeProductInfoUtil.filterForbidden(shopeeSkus, skuList, allSites);
        // 过滤所有站点侵权子SKU
        try {
            ShopeeProductInfoUtil.filterInfringement(shopeeSkus, allSites);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(shopeeSkus)) {
            throw new IllegalArgumentException("子属性SKU的单品状态全为停产，存档，废弃，或禁售，侵权不能创建模板！");
        }
        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));
        // 标题描述
        setTitleAndDesc(shopeeTemplate, skuList, copyWritingType);
        // 重量在最重sku的基础上加10g
        Optional<ShopeeSku> max = shopeeSkus.stream()
                .filter(o -> o.getShippingWeight() != null)
                .max((o1, o2) -> o1.getShippingWeight() > o2.getShippingWeight() ? 1 : -1);
        max.ifPresent(shopeeSku -> shopeeTemplate.setWeight(NumberUtils.format((shopeeSku.getShippingWeight() + 0.01), "0.###")));
        shopeeTemplate.setCondition("NEW");
//        shopeeTemplate.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        shopeeTemplate.setDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        return shopeeTemplate;
    }

    @Override
    public EsShopeeGlobalTemplate buildGlobalTemplate(ShopeeSpuGlobalPublishParam param) {
        SpuInfo spu = ShopeeProductInfoUtil.getSpuInfo(param.getSpu());
        if (spu == null) {
            throw new RuntimeException("无此spu的产品");
        }
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Collections.singletonList(param.getSpu()));
        if (CollectionUtils.isEmpty(skuInfoList)) {
            throw new RuntimeException("获取不到sku信息!");
        }
        String fullpathcode = skuInfoList.get(0).getFullpathcode();
        boolean b = ShopeeGlobalTemplateUtil.checkSystemFullPathCodeCanPublish(fullpathcode);
        if (!b) {
            throw new RuntimeException("该产品系统分类平台禁止刊登");
        }
        List<String> accoutNumberList = CommonUtils.splitList(param.getAccounts(), ",");
        String accountNumber = accoutNumberList.get(0);
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, accountNumber);
        boolean isNnShop = Optional.ofNullable(saleAccountByAccountNumber).map(SaleAccountAndBusinessResponse::getShopeeColBool3).orElse(false);
        skuInfoList = filterStatus(isNnShop, skuInfoList);
        if (CollectionUtils.isEmpty(skuInfoList)) {
            throw new RuntimeException("SKU的单品状态全为停产，存档，废弃，不能创建模板");
        }

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setSubAccount(param.getSubAccount());
        shopeeTemplate.setMerchant(param.getMerchant());
        shopeeTemplate.setMerchantId(param.getMerchantId());
        shopeeTemplate.setAccounts(CommonUtils.splitList(param.getAccounts(), ","));
        shopeeTemplate.setSku(param.getSpu());
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());
        //生成默认品牌
        shopeeTemplate.setBrand(JSON.toJSONString(shopeeBrandV2Service.generateDefaultBrand()));

        //类目属性
        ShopeeAdminGlobalTemplate refTemplate = null;
        if (param.getByTemplateId() != null) {
            refTemplate = shopeeAdminGlobalTemplateService.selectByPrimaryKey(param.getByTemplateId());
            if (refTemplate != null) {
                if (!StringUtils.equalsIgnoreCase(refTemplate.getSku(), param.getSpu())) {
                    //sku不同
                    throw new RuntimeException(String.format("admin范本的sku:%s 与 spu:%s 不同，请确认!", refTemplate.getSku(), param.getSpu()));
                }
                if (BooleanUtils.isFalse(refTemplate.getIsEnabled())) {
                    //范本禁用 不能使用
                    throw new RuntimeException(String.format("admin范本[%s]已被禁用，请重新生成模板!", refTemplate.getId()));
                }
                shopeeTemplate.setCategoryId(refTemplate.getCategoryId());
                shopeeTemplate.setAttributesStr(refTemplate.getAttributesStr());
                if (refTemplate.getBrand() != null) {
                    shopeeTemplate.setBrand(refTemplate.getBrand());
                }
            }
        } else {
            //随机一个admin范本
            ShopeeAdminGlobalTemplateExample ex = new ShopeeAdminGlobalTemplateExample();
            ex.createCriteria()
                    .andSkuEqualTo(param.getSpu())
                    .andIsEnabledEqualTo(true)
                    .andCategoryIdIsNotNull();
            List<ShopeeAdminGlobalTemplate> dbList = shopeeAdminGlobalTemplateService.selectByExample(ex);
            if (CollectionUtils.isNotEmpty(dbList)) {
                //过滤掉类目映射禁用的模板数据（新类目站点-过滤掉禁用类目）
                CategoryMappingExample mappingExample = new CategoryMappingExample();
                mappingExample.createCriteria()
                        .andPlatformEqualTo(Platform.Shopee.name())
                        .andSiteEqualTo(StrConstant.CROSSBAR)
                        .andSystemCategoryIdEqualTo(spu.getFullpathcode())
                        .andApplyStateEqualTo(ApplyStatusEnum.NO.getCode());
                List<CategoryMapping> categoryMappingList = categoryMappingService.selectByExample(mappingExample);
                if (CollectionUtils.isNotEmpty(categoryMappingList)) {
                    List<String> disableList = categoryMappingList.stream().map(CategoryMapping::getPlatformCategoryId).collect(Collectors.toList());
                    dbList = dbList.stream().filter(o -> !disableList.contains(o.getCategoryId().toString())).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isNotEmpty(dbList)) {
                refTemplate = dbList.get(RandomUtils.nextInt(0, dbList.size()));
                shopeeTemplate.setCategoryId(refTemplate.getCategoryId());
                shopeeTemplate.setAttributesStr(refTemplate.getAttributesStr());
                if (refTemplate.getBrand() != null) {
                    shopeeTemplate.setBrand(refTemplate.getBrand());
                }
            }
        }

        //生成标题、描述
        ShopeeProductColumnInfo info = ShopeeProductInfoUtil.buildProductColumnInfo(shopeeTemplate.getSubAccount(), spu, skuInfoList, param.getCopyWritingType());
        String title = info.getTitle();
        String description = info.getDescription();
        if (title.length() > 120) {
            title = title.substring(0, 120);
        }
        if (description.length() > 3000) {
            description = description.substring(0, 3000);
        }
        shopeeTemplate.setName(title);
        shopeeTemplate.setDescription(description);
        shopeeTemplate.setDimension(JSON.toJSONString(info.getDimension()));
        shopeeTemplate.setTitleRule(JSON.toJSONString(info.getTitleRule()));
        shopeeTemplate.setCopyWritingType(info.getCopyWritingType());

        // 分类不存在 用标题搜索对应的分类
        if (shopeeTemplate.getCategoryId() == null) {
            ApiResult<List<ShopeeCategoryV2>> result = shopeeCategoryV2Service.categoryRecommend(shopeeTemplate.getName(), null);
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getResult())) {
                List<ShopeeCategoryV2> caList = result.getResult();
                shopeeTemplate.setCategoryId(caList.get(0).getCategoryId());
            }
        }

        //ES-7390 http://************:8080/browse/ES-7390 分类优先级 最后类目映射
        if (shopeeTemplate.getCategoryId() == null) {
            // 查询类目映射得到类目
            CategoryMappingExample mappingExample = new CategoryMappingExample();
            mappingExample.createCriteria()
                    .andPlatformEqualTo(Platform.Shopee.name())
                    .andSiteEqualTo(StrConstant.CROSSBAR)
                    .andSystemCategoryIdEqualTo(spu.getFullpathcode())
                    .andApplyStateEqualTo(ApplyStatusEnum.YES.getCode());
            List<CategoryMapping> categoryMappingList = categoryMappingService.selectByExample(mappingExample);
            if (CollectionUtils.isNotEmpty(categoryMappingList)) {
                //随机取一个类目
                CategoryMapping categoryMapping = categoryMappingList.get(RandomUtils.nextInt(0, categoryMappingList.size()));
                shopeeTemplate.setCategoryId(Integer.valueOf(categoryMapping.getPlatformCategoryId()));
            }
        }

        //主图和特效图片
        List<String> publicImages;
        List<String> sizeImages = new ArrayList<>();
        try {
            publicImages = shopeeTemplateService.getAllImagesBySku(param.getSpu());
            if (CollectionUtils.isNotEmpty(publicImages)) {
                sizeImages.addAll(publicImages);
                shopeeTemplate.setMainImageNum(1);
                List<String> extraImages = ShopeeImageUtil.filterSpuExtraImages(param.getSpu(), publicImages);
                shopeeTemplate.setImagesList(extraImages);
            }
        } catch (Exception e) {
            log.error("查询图片池出错", e);
            throw new RuntimeException("查询图片池出错:" + e.getMessage());
        }
        List<String> sizeChartImages = ShopeeImageUtil.getSpuSizeChartImages(shopeeTemplate.getSku(), sizeImages);
        String videoLink = ShopeeProductInfoUtil.getVideoLink(param.getSpu());
        shopeeTemplate.setVideo(videoLink);
        // 所有站点集合
        List<String> allSites = Arrays.stream(ShopeeCountryEnum.values()).map(ShopeeCountryEnum::getCode).collect(Collectors.toList());

        // 获取账号配置 毛利率
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByAccounts(shopeeTemplate.getAccounts());
        if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
            throw new RuntimeException(shopeeTemplate.getAccounts() + "账号配置为空 无法获取毛利重新算价");
        }
        ShopeeAccountConfig firstAccountConfig = shopeeAccountConfigs.get(0);
        if (null == firstAccountConfig.getProfit()) {
            throw new RuntimeException(firstAccountConfig.getAccount() + "账号未配置毛利率");
        }

        //生成子属性sku
        List<ShopeeSku> shopeeSkus = generateShopeeSkus(skuInfoList, publicImages, firstAccountConfig.getProfit(), firstAccountConfig.getSite());

        // 满足条件则按照上架配置过滤sku
        if (StringUtils.isNotBlank(param.getConfigSource()) && ConfigSourceEnum.ONLINE_CONFIG.getCode().equals(param.getConfigSource())) {
            // 店铺配置刊登店铺只会有一个
            shopeeSkus = checkPublishSkuListByOnlineConfig(shopeeTemplate.getAccounts().get(0), param.getRuleJson(), shopeeSkus);
        } else {
            // 过滤所有站点禁售子SKU
            shopeeSkus = ShopeeProductInfoUtil.filterForbidden(shopeeSkus, skuInfoList, allSites);
            // 过滤所有站点侵权子SKU
            shopeeSkus = ShopeeProductInfoUtil.filterInfringement(shopeeSkus, allSites);
        }


        if (refTemplate != null) {
            // 子属性sku信息
            List<ShopeeSku> dbShopeeSkus = JSONArray.parseArray(refTemplate.getShopeeSkusStr(), ShopeeSku.class);
            ShopeeProductInfoUtil.variantSetColorAndSize(shopeeSkus, dbShopeeSkus);
        }
        if (CollectionUtils.isEmpty(shopeeSkus)) {
            throw new RuntimeException("SKU信息为空，无可刊登的子属性SKU");
        }

        String publishType = param.getPublishType();
        if (StringUtils.isNotBlank(publishType) && StringUtils.equalsIgnoreCase(ShopeePublishTypeEnum.SPU_GP_TIMING.getCode(), publishType)) {
            // 排除客户定制
            List<ShopeeSku> passShopeeSkus = new ArrayList<>();
            for (ShopeeSku skus : shopeeSkus) {
                boolean sign = true;
                String sonSku = skus.getSku();
                SingleItemEs skuInfo = singleItemEsService.getSkuInfo(sonSku);
                if (skuInfo != null) {
                    List<SpecialGoods> specialGoodsList = skuInfo.getSpecialGoodsList();
                    if (CollectionUtils.isNotEmpty(specialGoodsList)) {
                        for (SpecialGoods specialGoods : specialGoodsList) {
                            Integer specialType = specialGoods.getSpecialType();
                            if (specialType != null && specialType.intValue() == SpecialTagEnum.s_2001.code) {
                                sign = false;
                                break;
                            }
                        }
                    }
                }
                if (sign) {
                    passShopeeSkus.add(skus);
                }
            }
            shopeeSkus = passShopeeSkus;
        }

        if (CollectionUtils.isEmpty(shopeeSkus)) {
            ApiResult.newError("子属性SKU的单品状态全为停产，存档，废弃或禁售，侵权或者客户定制不能创建模板！");
        }

        if (shopeeSkus.size() <= 1) {
            // 判断是否color和size都为空
            ShopeeSku shopeeSku = shopeeSkus.get(0);
            if (StringUtils.isBlank(shopeeSku.getColor()) && StringUtils.isBlank(shopeeSku.getSize())) {
                shopeeSku.setColor("as shown");
            }
        }
        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));

        // 验证不可刊登sku 所有店铺都不可刊登则拦截
        if (ShopeeGlobalTemplateUtil.allAccountContainNonPublishableSku(shopeeAccountConfigs, shopeeTemplate)) {
            throw new RuntimeException("所有店铺都存在店铺不可刊登sku");
        }

        // 重量在最重sku的基础上加10g
        Optional<ShopeeSku> max = shopeeSkus.stream()
                .filter(o -> o.getShippingWeight() != null)
                .max((o1, o2) -> o1.getShippingWeight() > o2.getShippingWeight() ? 1 : -1);
        max.ifPresent(shopeeSku -> shopeeTemplate.setWeight(NumberUtils.format((shopeeSku.getShippingWeight() + 0.01), "0.###")));

        // 设置尺码图表
        Integer categoryId = shopeeTemplate.getCategoryId();
        if (categoryId != null) {
            // 判断分类是否为使用分类
            // 判断分类id是否要求传尺码表
            LambdaQueryWrapper<ShopeeSizeChartApplicableCategory> applicableCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            applicableCategoryLambdaQueryWrapper.eq(ShopeeSizeChartApplicableCategory::getCategoryId, categoryId);
            int count = shopeeSizeChartApplicableCategoryService.count(applicableCategoryLambdaQueryWrapper);
            if (count > 0) {
                ShopeeSizeChartTemplates sizeChartTemplates = null;
                List<ShopeeSizeChartTemplates> shopeeSizeChartTemplates = shopeeSizeChartTemplatesService.listSizeChart(accountNumber, categoryId);
                if (CollectionUtils.isNotEmpty(shopeeSizeChartTemplates)) {
                    sizeChartTemplates = shopeeSizeChartTemplates.get(0);
                }
                if (sizeChartTemplates != null) {
                    Integer sizeChartId = sizeChartTemplates.getSizeChartId();
                    ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                    sizeChartInfo.setSizeChartId(sizeChartId);
                    shopeeTemplate.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                } else {
                    // 不存在，则去取 图片
                    if (CollectionUtils.isNotEmpty(sizeChartImages)) {
                        String sizeChartImage = sizeChartImages.get(0);
                        ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                        sizeChartInfo.setSizeChart(sizeChartImage);
                        shopeeTemplate.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                    }
                }
            }
        }

        shopeeTemplate.setCondition("NEW");
        shopeeTemplate.setIsParent(false);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.AUTO_PUBLISH.getCode());
        //默认销售刊登
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        //默认产品系统数据来源
        shopeeTemplate.setDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());

        return shopeeTemplate;
    }

    public static List<ProductInfo> filterStatus(boolean isNnShop, List<ProductInfo> skuInfoList) {
        if (CollectionUtils.isEmpty(skuInfoList)) {
            return skuInfoList;
        }
        List<ProductInfo> list = new ArrayList<>();
        for (ProductInfo productInfo : skuInfoList) {
            String itemStatus = productInfo.getItemStatus();
            List<Integer> specialTypeList = productInfo.getSpecialTypeList();
            if (ShopeeProductInfoUtil.filterStatus(isNnShop, itemStatus, specialTypeList)) {
                continue;
            }
            list.add(productInfo);
        }
        return list;
    }

    /**
     * 根据规则id过滤模板可刊登的sku
     *
     * @param ruleJson
     * @param shopeeSkus
     * @return
     */
    private List<ShopeeSku> checkPublishSkuListByOnlineConfig(String account, String ruleJson, List<ShopeeSku> shopeeSkus) {
        ShopeeListingOnlineConfigDTO.RuleConfigJson ruleConfigJson = ShopeeListingOnlineConfigDTO.reconvertRuleConfig(ruleJson);
        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, false);
        Boolean lazadaBigbagAuthStatus = saleAccount.getShopeeColBool3();
        List<String> skuList = shopeeSkus.stream().map(ShopeeSku::getSku).collect(Collectors.toList());

        // 过滤掉非NORMAL状态的SKU
        List<String> filterNormalSku = filterNormalSku(account, skuList);
        if (CollectionUtils.isEmpty(filterNormalSku)) {
            throw new RuntimeException("当前店铺SKU均为NORMAL状态,无可刊登SKU信息");
        }

        // 查询产品系统基础信息
        SingleItemEsRequest singleItemEsRequest = new SingleItemEsRequest();
        singleItemEsRequest.setSkuList(skuList);
        List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(singleItemEsRequest);
        if (CollectionUtils.isEmpty(singleItemEsList)) {
            throw new RuntimeException("获取管理单品列表为空" + skuList.toString());
        }
        List<Integer> unsalableLevels = ruleConfigJson.getUnsalableLevels();
        Map<String, Integer> unsalableLeveAndTriggeredMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(unsalableLevels)) {
            Map<String, Integer> skuUnsalableLevelMap = unsalableSkuService.getSkuUnsalableLevelMap(skuList);
            unsalableLeveAndTriggeredMap.putAll(skuUnsalableLevelMap);
        }
        Map<String, SingleItemEs> sonSkuMap = singleItemEsList.stream().filter(t -> StringUtils.isNotBlank(t.getSonSku())).collect(Collectors.toMap(k -> k.getSonSku(), v -> v, (k1, k2) -> k1));
        List<ShopeeSku> newShopeeSkus = shopeeSkus.stream()
                // 过滤是否滞销
                .filter(shopeeSku -> {
                    if (lazadaBigbagAuthStatus == null) {
                        return true;
                    }
                    if (CollectionUtils.isEmpty(unsalableLevels)) {
                        return true;
                    }

                    int type = lazadaBigbagAuthStatus ? 3: 1;
                    Integer orDefault = unsalableLeveAndTriggeredMap.getOrDefault(shopeeSku.getSku() + "_" + type, 0);
                    return unsalableLevels.contains(orDefault);
                })
                .filter(shopeeSku -> {
                    if (filterNormalSku.contains(shopeeSku.getSku())) {
                        return true;
                    }
                    return false;
                })
                .filter(shopeeSku -> {
                    // 库存
                    Integer avableStock = SkuStockUtils.getAvableStock(shopeeSku.getSku());
                    if (Objects.isNull(avableStock)) {
                        return false;
                    }
                    if (avableStock >= ruleConfigJson.getAvailableStockStart() && avableStock < ruleConfigJson.getAvailableStockEnd()) {
                        return true;
                    }
                    return false;
                })
                .filter(shopeeSku -> {
                    SingleItemEs singleItemEs = sonSkuMap.get(shopeeSku.getSku());
                    if (Objects.isNull(singleItemEs)) {
                        return false;
                    }

                    // 销售成本价
                    if (Objects.nonNull(ruleConfigJson.getSaleCostStart()) && Objects.nonNull(ruleConfigJson.getSaleCostEnd())) {
                        BigDecimal saleCostBigDecimal = singleItemEs.getSaleCost();
                        if (Objects.isNull(saleCostBigDecimal)) {
                            return false;
                        }
                        Double saleCost = BigDecimal.valueOf(saleCostBigDecimal.doubleValue()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (!(saleCost >= ruleConfigJson.getSaleCostStart() && saleCost < ruleConfigJson.getSaleCostEnd())) {
                            return false;
                        }
                    }

                    // 过滤产品标签
                    if (CollectionUtils.isNotEmpty(ruleConfigJson.getProductCodes())) {
                        String tagCode = singleItemEs.getTagCode();
                        List<String> tagCodeList = com.estone.erp.common.util.CommonUtils.splitList(tagCode, ",");
                        List<String> codes = tagCodeList.stream().filter(code -> ruleConfigJson.getProductCodes().contains(code)).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(codes)) {
                            return false;
                        }
                    }

                    // 过滤单品状态
                    if (CollectionUtils.isNotEmpty(ruleConfigJson.getItemStatus())) {
                        String enNameByCode = SingleItemEnum.getEnNameByCode(singleItemEs.getItemStatus());
                        if (StringUtils.isBlank(enNameByCode)) {
                            return false;
                        }
                        if (!ruleConfigJson.getItemStatus().contains(enNameByCode)) {
                            return false;
                        }
                    }

                    // 检查季节性商品
                    List<String> seasonProduct = ruleConfigJson.getSeasonProduct();
                    if (CollectionUtils.isNotEmpty(seasonProduct)) {
                        String isSeasonNew = singleItemEs.getIsSeasonNew();
                        if (StringUtils.isBlank(isSeasonNew)) {
                            return false;
                        }

                        List<String> seasonNewCodeList = CommonUtils.splitList(isSeasonNew, ",");
                        List<String> codes = seasonNewCodeList.stream().filter(seasonProduct::contains).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(codes)) {
                            return false;
                        }
                    }

                    // 过滤禁售信息
                    List<ShopeeListingOnlineConfigDTO.RuleConfigJson.ProhibitedInfo> prohibitedInfoList = ruleConfigJson.getProhibitedInfo().stream()
                            .filter(prohibitedInfo -> StringUtils.isNotBlank(prohibitedInfo.getPlatform())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(prohibitedInfoList)) {
                        String salesProhibition = singleItemEs.getSalesProhibition();
                        if (StringUtils.isNotBlank(salesProhibition) && !salesProhibition.equals("[]")) {
                            // 产品系统禁售平台信息
                            List<SalesProhibitionsVo> prohibitionsVoList = JSON.parseObject(salesProhibition, new TypeReference<List<SalesProhibitionsVo>>() {
                            });

                            // 转换成Map
                            Map<String, List<Sites>> prohibitionsVoMap = prohibitionsVoList.stream().collect(Collectors.toMap(SalesProhibitionsVo::getPlat, SalesProhibitionsVo::getSites));

                            boolean anyMatch = prohibitedInfoList.stream().anyMatch(prohibitedInfo -> {
                                // 先判断平台是否匹配
                                List<Sites> sitesList = prohibitionsVoMap.get(prohibitedInfo.getPlatform());
                                if (CollectionUtils.isNotEmpty(sitesList)) {
                                    // 如果平台匹配则判断站点是否匹配
                                    List<Sites> newSitesList = sitesList.stream().filter(sites -> StringUtils.isNotBlank(sites.getSite())).collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(newSitesList)) {
                                        // 如果产品的站点不等于空，则判断我们的站点是否在产品的站点列表中
                                        boolean anied = prohibitedInfo.getSiteList().stream().anyMatch(site -> {
                                            List<String> sites = newSitesList.stream().map(Sites::getSite).collect(Collectors.toList());
                                            return sites.contains(site);
                                        });
                                        return anied;
                                    }
                                    return true;
                                }
                                // 不禁售
                                return false;
                            });
                            return !anyMatch;
                        } else {
                            return true;
                        }
                    }
                    return true;
                })
                .collect(Collectors.toList());
        return newShopeeSkus;
    }


    /**
     * 过滤正常SKU
     *
     * @param account
     * @param skuList
     * @return
     */
    public List<String> filterNormalSku(String account, List<String> skuList) {
        // 查询在线列表
        NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder();
        NativeSearchQuery nativeSearchQuery = searchQueryBuilder.withQuery(
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termQuery("itemSeller", account))
                                .must(QueryBuilders.termsQuery("articleNumber", skuList))
                                .must(QueryBuilders.termQuery("itemStatus", "NORMAL"))
                                .must(QueryBuilders.termQuery("isGoods", true))
                )
                .withSourceFilter(new FetchSourceFilter(null, new String[]{"logistics", "description", "variationOptions"}))
                .build();
        List<EsShopeeItem> esShopeeItemList = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, nativeSearchQuery, EsShopeeItem.class);
        if (CollectionUtils.isEmpty(esShopeeItemList)) {
            return skuList;
        }

        List<String> filterSkuList = esShopeeItemList.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
        return skuList.stream().filter(sku -> !filterSkuList.contains(sku)).collect(Collectors.toList());
    }

    /**
     * 检查模板是否可以刊登
     *
     * @param template 模板
     */
    @Override
    public ApiResult<String> checkCanPublish(EsShopeeGlobalTemplate template, String action) {
        List<ShopeeSku> shopeeSkuList = JSONArray.parseArray(template.getShopeeSkusStr(), ShopeeSku.class);
        List<String> skuList = shopeeSkuList.stream().map(ShopeeSku::getSku).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shopeeSkuList) && CollectionUtils.isNotEmpty(skuList)) {
            // 所有站点集合
            List<String> allSites = Arrays.stream(ShopeeCountryEnum.values()).map(ShopeeCountryEnum::getCode).collect(Collectors.toList());
            String[] fields = {"mainSku", "sonSku", "itemStatus", "salesProhibition", "specialGoodsList"};
            SingleItemEsRequest criteria = new SingleItemEsRequest();
            criteria.setSkuList(skuList);
            criteria.setFields(fields);
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
            List<ProductInfo> productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
            if (CollectionUtils.isNotEmpty(productInfoList)) {
                // 过滤 停产 存档 废弃状态
                List<String> accounts = template.getAccounts();
                SaleAccountAndBusinessResponse shopeeAccount = null;
                if (CollectionUtils.isNotEmpty(accounts)) {
                    shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, accounts.get(0), true);
                }
                ShopeeProductInfoUtil.filterIllegalStatus(shopeeAccount, shopeeSkuList, productInfoList);

                // 过滤禁售所有站点的产品
                ShopeeProductInfoUtil.filterForbidden(shopeeSkuList, productInfoList, allSites);
            }

            // 过滤侵权
            try {
                ShopeeProductInfoUtil.filterInfringement(shopeeSkuList, allSites);
            } catch (Exception e) {
                return ApiResult.newError(e.getMessage());
            }

            if (CollectionUtils.isEmpty(shopeeSkuList)) {
                return ApiResult.newError("所有子SKU侵权,禁售或状态非法！");
            }
            template.setShopeeSkusStr(JSON.toJSONString(shopeeSkuList));
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据账号配置利率重新算价
     *
     * @param sku    sku
     * @param profit 利率
     * @return
     */
    @Override
    public ApiResult<Map<String, Double>> calcPriceByProfit(String sku, Double profit, String site) {
        if (null == profit) {
            return ApiResult.newError("毛利率为空！");
        }
        List<ProductInfo> skuList = ProductUtils.findProductInfos(Collections.singletonList(sku));
        if (CollectionUtils.isEmpty(skuList)) {
            return ApiResult.newError("无效的货号！");
        }
        double priceConfig = ShopeeSystemParamUtil.getGlobalProductPriceConfig();

        BigDecimal sellerPaysFreight = null;
        Map<String, Double> skuPriceMap = new HashMap<>();
        for (ProductInfo info : skuList) {
            Double price = ShopeeProductInfoUtil.calcPriceForGlobal(info, priceConfig, profit);
            if (sellerPaysFreight == null) {
                try {
                    sellerPaysFreight = ShopeeProductInfoUtil.getSellerPaysFreight(skuList, site);
                } catch (Exception e) {
                    return ApiResult.newError(e.getMessage());
                }
            }
            BigDecimal bigDecimalPrice = BigDecimal.valueOf(price);
            double priceAll = bigDecimalPrice.add(sellerPaysFreight).doubleValue();
            skuPriceMap.put(info.getSonSku(), NumberUtils.round(priceAll, 2));
        }
        return ApiResult.newSuccess(skuPriceMap);
    }

    @Override
    public String getMainSku(EsShopeeGlobalTemplate template) {
        return ProductUtils.getMainSku(template.getSku());
    }

    @Override
    public boolean existForbidden(EsShopeeGlobalTemplate template, List<String> skuList, String shopSite) {
        String mainSku = getMainSku(template);
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Collections.singletonList(mainSku));
        return ShopeeProductInfoUtil.existForbidden(skuList, skuInfoList, shopSite);
    }

    @Override
    public TemplateCheckProductInfo getTemplateCheckProductInfo(EsShopeeGlobalTemplate template) {
        String mainSku = getMainSku(template);
        List<String> tagCodes = new ArrayList<>();
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(Collections.singletonList(mainSku));

        for (ProductInfo productInfo : skuInfoList) {
            String productTag = productInfo.getEnTag();
            if (StringUtils.isEmpty(productTag)) {
                continue;
            }
            List<String> productTagList = Arrays.asList(productTag.split(","));
            if (CollectionUtils.isNotEmpty(productTagList)) {
                tagCodes.addAll(productTagList);
            }
        }
        TemplateCheckProductInfo templateCheckProductInfo = new TemplateCheckProductInfo();
        templateCheckProductInfo.setTagCodes(tagCodes);
        if (CollectionUtils.isNotEmpty(skuInfoList)) {
            String s = skuInfoList.stream().map(ProductInfo::getFullpathcode).filter(StringUtils::isNotBlank).findFirst().orElse(null);
            templateCheckProductInfo.setFullPathCode(s);
        }
        return templateCheckProductInfo;
    }

    /**
     * 生成子属性sku 范本无账号全球商品价格使用默认系统配置利润
     */
    private List<ShopeeSku> generateShopeeSkus(List<ProductInfo> skuList, List<String> publicImages, Double accountProfit, String site) {
        try {
            return ShopeeProductInfoUtil.generateShopeeSkus(skuList, publicImages, accountProfit, site);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 设置模板标题描述和尺寸
     */
    private void setTitleAndDesc(EsShopeeGlobalTemplate shopeeTemplate, List<ProductInfo> skuList, Integer copyWritingType) {
        // 查询标题描述 长宽高
        String defaultTitle = null;
        Map<String, Double> dimension = new HashMap<>(3);
        for (ProductInfo productInfo : skuList) {
            // 默认标题
            if (StringUtils.isBlank(defaultTitle)) {
                defaultTitle = productInfo.getTitleEn();
            }
            //获取标题和描述
            /*
                标题取值优先级：
                    1-SPU长标题
                    1-SPU短标题
                    3-SKU标题
                描述取值优先级：
                    1-SKU描述新，在描述最前面插入标题。
                    2-SKU描述，在描述最前面插入标题
             */
            ApiResult<?> titleInfo = ShopeeProductInfoUtil.getTileAndDescription(productInfo.getMainSku(), defaultTitle, copyWritingType);
            if (titleInfo == null || !titleInfo.isSuccess()) {
                throw new RuntimeException(titleInfo == null ? "按规则获取标题和描述为空" : titleInfo.getErrorMsg());
            }
            Map<String, String> titleMap = (HashMap) titleInfo.getResult();
            if (titleMap != null && titleMap.size() > 0) {
                shopeeTemplate.setName(titleMap.get("title"));
                shopeeTemplate.setDescription(titleMap.get("description"));
                shopeeTemplate.setCopyWritingType(Integer.valueOf(titleMap.get("copyWritingType")));
            }
            if (productInfo.getLength() != null) {
                Double length = productInfo.getLength().doubleValue();
                if (length < 1) {
                    length = 1D;
                }
                dimension.putIfAbsent("package_length", length);
            }
            if (productInfo.getWide() != null) {
                Double wide = productInfo.getWide().doubleValue();
                if (wide < 1) {
                    wide = 1D;
                }
                dimension.putIfAbsent("package_width", wide);
            }
            if (productInfo.getHeight() != null) {
                Double height = productInfo.getHeight().doubleValue();
                if (height < 1) {
                    height = 1D;
                }
                dimension.putIfAbsent("package_height", height);
            }

            // json中标题和描述都有了就跳出
            if (StringUtils.isNotBlank(shopeeTemplate.getName())
                    && StringUtils.isNotBlank(shopeeTemplate.getDescription())
                    && dimension.size() >= 3) {
                break;
            }
        }
        if(dimension.size() > 0){
            shopeeTemplate.setDimension(JSON.toJSONString(dimension));
        }
    }
}
