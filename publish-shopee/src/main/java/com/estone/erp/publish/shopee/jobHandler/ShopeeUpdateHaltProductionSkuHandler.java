package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.FeedTaskTypeConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.component.ShopeeItemServiceCustom;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskMsgEnum;
import com.estone.erp.publish.shopee.service.ShopeeGlobalItemService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.ProductUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
//增量停产存档SKU库存调整为0定时任务
public class ShopeeUpdateHaltProductionSkuHandler extends AbstractJobHandler {

    @Autowired
    ProductClient productClient;
    @Autowired
    ShopeeItemEsService shopeeItemEsService;
    @Autowired
    ShopeeItemServiceCustom shopeeItemServiceCustom;
    @Autowired
    ShopeeGlobalItemService shopeeGlobalItemService;

    @Getter
    @Setter
    public static class InnerParam {
        private List<String> accountList;
    }
    Map<String,SaleAccountAndBusinessResponse> accountMap=new HashMap();

    public ShopeeUpdateHaltProductionSkuHandler() {
        super("ShopeeUpdateHaltProductionSkuHandler");
    }

    @Override
    @XxlJob("ShopeeUpdateHaltProductionSkuHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("获取参数--start");
        //获取参数供测试使用
        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                log.error("参数解析错误！");
                return ReturnT.FAIL;
            }
        }
        log.info("获取参数--end");
        if(null == innerParam) {
            innerParam = new InnerParam();
        }
        List<String> accountList = innerParam.getAccountList();

        log.info("增量停产存档SKU库存调整为0定时任务--start");
        updateStock(accountList);


        accountMap.clear();
        log.info("增量停产存档SKU库存调整为0定时任务--end");
        return ReturnT.SUCCESS;
    }



    /**
     * 修改库存
     * @param
     */
    private void updateStock(List<String> accountList) {
        Date now = new Date();

        //24小时前
        long begin = now.getTime() - (1000L * 60 * 60 * 24);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<String> changeStopArchivedSku = ProductUtils
                .getChangeStopArchivedSku(sdf.format(new Date(begin)), sdf.format(now));

        //调用接口获取停产存档的sku列表
        if(CollectionUtils.isEmpty(changeStopArchivedSku)) {
            XxlJobLogger.log("*****************当前24小时内新增的停产存档sku数量为0*****************");
            return ;
        }

        //如果有返回sku列表，则根据获取的sku列表，修改库存
        for (String sku : changeStopArchivedSku) {
            EsShopeeItemRequest request = new EsShopeeItemRequest();
            request.setArticleNumber(sku);
            request.setIsGoods(true);
            request.setFromStock(1);
            request.setQueryFields(new String[] {"id", "stock", "articleNumber", "itemSeller"});
            if(CollectionUtils.isNotEmpty(accountList)) {
                request.setItemSellerList(accountList);
            }
            List<EsShopeeItem> shopeeItems = shopeeItemEsService.getEsShopeeItems(request);

            for (EsShopeeItem shopeeItem : shopeeItems) {
                SaleAccountAndBusinessResponse account = accountMap.get(shopeeItem.getItemSeller());
                if(null == account)  {
                    account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.SHOPEE.getChannelName(), shopeeItem.getItemSeller(), true);
                    if(null == account) {
                        XxlJobLogger.log("店铺:{},redis未找到", shopeeItem.getItemSeller());
                        continue;
                    }
                    accountMap.put(shopeeItem.getItemSeller(), account);
                }
                if(StringUtils.isNotBlank(account.getAccountStatus()) && SaleAccountStastusEnum.FROZEN.getCode().equals(account.getAccountStatus())){
                    XxlJobLogger.log("店铺状态为禁用:{}, 不执行", account.getAccountStatus());
                    continue;
                }

                //批量修改库存为0
                if(null != account) {
                    shopeeItem.setStock(0);
                    shopeeGlobalItemService.updateShopeeStock(account, StrConstant.ADMIN,shopeeItem,
                            FeedTaskTypeConstant.JOB, ShopeeFeedTaskMsgEnum.STOP_ARCHIVED_TO_ZERO_ADD.getCode());
                }
            }
        }
    }
}
