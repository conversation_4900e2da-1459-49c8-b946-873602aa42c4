package com.estone.erp.publish.shopee.component.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.param.add.ShopeeSizeChartInfoParamTemplate;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.dto.ShopeeProductColumnInfo;
import com.estone.erp.publish.shopee.dto.ShopeeSpuGlobalPublishParam;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.enums.ShopeeNewPublishStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishRoleEnum;
import com.estone.erp.publish.shopee.enums.ShopeeTemplateTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAdminGlobalTemplate;
import com.estone.erp.publish.shopee.model.ShopeeAdminGlobalTemplateExample;
import com.estone.erp.publish.shopee.model.ShopeeCategoryV2;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.shopee.util.*;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartApplicableCategory;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartTemplates;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartApplicableCategoryService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartTemplatesService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组合
 *
 * <AUTHOR>
 * @date 2022-12-05 14:19
 */
@Slf4j
@Service
public class ShopeeComposeTemplateBuilderServiceImpl implements ShopeeGlobalTemplateBuilderService {

    private static final String LONG_TITLE_PREFIX = "longTitle";
    private static final String SHORT_TITLE_PREFIX = "shortTitle";

    @Autowired
    private ShopeeTemplateService shopeeTemplateService;

    @Autowired
    private ShopeeBrandV2Service shopeeBrandV2Service;

    @Autowired
    private ShopeeAdminGlobalTemplateService shopeeAdminGlobalTemplateService;

    @Autowired
    private CategoryMappingService categoryMappingService;

    @Autowired
    private ShopeeCategoryV2Service shopeeCategoryV2Service;

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private ShopeeSizeChartTemplatesService shopeeSizeChartTemplatesService;
    @Resource
    private ShopeeSizeChartApplicableCategoryService shopeeSizeChartApplicableCategoryService;

    /**
     * 构建全球模板
     *
     * @param sku             sku
     * @param copyWritingType 文案类型
     */
    @Override
    public EsShopeeGlobalTemplate buildSpGlobalTemplate(String sku, Integer copyWritingType) {
        ComposeSku composeProduct = ProductUtils.getComposeProduct(sku);
        if (composeProduct == null) {
            throw new NoSuchElementException(String.format("%s,未查询到组合套装", sku));
        }
        String fullpathcode = composeProduct.getFullPathCode();
        boolean b = ShopeeGlobalTemplateUtil.checkSystemFullPathCodeCanPublish(fullpathcode);
        if (!b) {
            throw new RuntimeException("该产品系统分类平台禁止刊登");
        }

        // 拦截：拦截组合状态为停产、存档、废弃的组合SPU；拦截组合SPU禁售信息中标记了shopee禁售的数据；拦截组合状态不为启用的组合SPU
        checkComposeState(composeProduct, "创建");
        // 图片迁移
        List<String> publicImages = shopeeTemplateService.getAllImagesBySku(sku);
        if (CollectionUtils.isNotEmpty(publicImages)) {
            Collections.shuffle(publicImages);
        }

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setSku(sku);
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());
        // 图片设置进json
        shopeeTemplate.setImagesList(publicImages);
        // 生成子属性sku 范本无账号全球商品价格使用默认系统配置利润
        List<ShopeeSku> shopeeSkus = generateShopeeSkus(composeProduct, publicImages, null, ShopeeCountryEnum.Singapore.getCode());

        //视频
        String videoLink = ShopeeProductInfoUtil.getVideoLink(sku);
        shopeeTemplate.setVideo(videoLink);

        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));
        // 标题描述
        setTitleAndDesc(shopeeTemplate, composeProduct, copyWritingType);
        ShopeeSku shopeeSku = shopeeSkus.get(0);
        // 重量在套装重量基础上加10g
        shopeeTemplate.setWeight(NumberUtils.format((shopeeSku.getShippingWeight() + 0.01), "0.###"));
        shopeeTemplate.setCondition("NEW");
//        shopeeTemplate.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        shopeeTemplate.setDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        return shopeeTemplate;
    }

    /**
     * 检查模板是否可以刊登
     *
     * @param template 模板
     */
    @Override
    public ApiResult<String> checkCanPublish(EsShopeeGlobalTemplate template, String action) {
        String sku = template.getSku();
        ComposeSku composeProduct = ProductUtils.getComposeProduct(sku);
        if (composeProduct == null) {
            return ApiResult.newError(String.format("%s,组合产品为空", sku));
        }
        try {
            checkComposeState(composeProduct, action);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 根据账号配置利率重新算价
     *
     * @param sku    sku
     * @param profit 利率
     * @return
     */
    @Override
    public ApiResult<Map<String, Double>> calcPriceByProfit(String sku, Double profit, String site) {
        if (null == profit) {
            return ApiResult.newError("毛利率为空！");
        }
        ComposeSku composeProduct = ProductUtils.getComposeProduct(sku);
        if (composeProduct == null) {
            return ApiResult.newError(String.format("%s,组合产品为空", sku));
        }
        double priceConfig = ShopeeSystemParamUtil.getGlobalProductPriceConfig();
        // 全球商品价格
        Double price = calcPriceForGlobal(composeProduct, priceConfig, profit);
        BigDecimal sellerPaysFreight = null;
        try {
            sellerPaysFreight = ShopeeProductInfoUtil.getSellerPaysFreightByWeight(composeProduct.getPackageWeight(), site);
        } catch (Exception e) {
            return ApiResult.newError(String.format("计算组合产品价格失败：%s", e.getMessage()));
        }
        BigDecimal bigDecimalPrice = BigDecimal.valueOf(price);
        double priceAll = bigDecimalPrice.add(sellerPaysFreight).doubleValue();
        Map<String, Double> skuPriceMap = new HashMap<>();
        skuPriceMap.put(composeProduct.getComposeSku(), NumberUtils.round(priceAll, 2));
        return ApiResult.newSuccess(skuPriceMap);
    }

    @Override
    public String getMainSku(EsShopeeGlobalTemplate template) {
        return template.getSku();
    }

    @Override
    public boolean existForbidden(EsShopeeGlobalTemplate template, List<String> skuList, String shopSite) {
        ComposeSku composeProduct = ProductUtils.getComposeProduct(template.getSku());
        if (composeProduct == null) {
            throw new NoSuchElementException(String.format("%s,未查询到组合套装", template.getSku()));
        }
        String infringementSaleProhibition = composeProduct.getInfringementSaleProhibition();
        if (StringUtils.isBlank(infringementSaleProhibition)) {
            return false;
        }
        List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
        List<SalesProhibitionsVo> prohibitionsVos = infringementSaleProhibitionVO.stream()
                .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        for (SalesProhibitionsVo prohibitionsVo : prohibitionsVos) {
            String plat = prohibitionsVo.getPlat();
            if (!SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(plat)) {
                continue;
            }

            List<Sites> siteslist = prohibitionsVo.getSites();
            List<String> sites = siteslist.stream().map(Sites::getSite).collect(Collectors.toList());
            if (sites.contains(shopSite)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public TemplateCheckProductInfo getTemplateCheckProductInfo(EsShopeeGlobalTemplate template) {
        ComposeSku composeProduct = ProductUtils.getComposeProduct(template.getSku());
        if (composeProduct == null) {
            throw new NoSuchElementException(String.format("%s,未查询到组合套装", template.getSku()));
        }
        String fullPathCode = composeProduct.getFullPathCode();
        TemplateCheckProductInfo checkProductInfo = new TemplateCheckProductInfo();
        checkProductInfo.setFullPathCode(fullPathCode);
        String tagCode = composeProduct.getTagCode();
        if (StringUtils.isNotBlank(tagCode)) {
            List<String> strings = CommonUtils.splitList(tagCode, ",");
            checkProductInfo.setTagCodes(strings);
        } else {
            checkProductInfo.setTagCodes(Lists.newArrayList());
        }
        return checkProductInfo;
    }

    /**
     * 拦截组合状态为停产、存档、废弃的组合SPU；拦截组合SPU禁售信息中标记了shopee禁售的数据；拦截组合状态不为启用的组合SPU
     *
     * @param composeProduct 组合产品
     * @param action         创建，保存，刊登
     */
    private void checkComposeState(ComposeSku composeProduct, String action) {
        if (composeProduct.getComposeStatus() == null) {
            throw new IllegalStateException("组合状态不能为空");
        }
        if (PublishCommonConstant.INTERCEPT_CODE_STATE_LIST.contains(composeProduct.getComposeStatus())) {
            throw new IllegalStateException(String.format("不允许%s, 停产、存档、废弃 状态的组合套装数据", action));
        }
        // 拦截组合状态为禁用的组合SPU
        if (ComposeCheckStepEnum.DISCARD.isTrue(composeProduct.getCheckStep())) {
            throw new IllegalStateException(String.format("不允许%s,流转状态为禁用的组合套装数据", action));
        }
        String infringementSaleProhibition = composeProduct.getInfringementSaleProhibition();
        if (StringUtils.isBlank(infringementSaleProhibition)) {
            return;
        }
        List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
        List<String> saleProhibitionPlat = infringementSaleProhibitionVO.stream()
                .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                .flatMap(Collection::stream)
                .map(SalesProhibitionsVo::getPlat)
                .collect(Collectors.toList());
        if (saleProhibitionPlat.contains(SaleChannelEnum.SHOPEE.getChannelName())) {
            throw new IllegalStateException("该组合产品在SHOPEE禁售！");
        }
    }

    private List<ShopeeSku> generateShopeeSkus(ComposeSku composeProduct, List<String> publicImages, Double accountProfit, String site) {
        double priceConfig = ShopeeSystemParamUtil.getGlobalProductPriceConfig();
        double priceUpgradeRatio;
        if(null == accountProfit) {
            priceUpgradeRatio = ShopeeSystemParamUtil.getGlobalProductSalePriceUpgradeRatio();
        } else {
            priceUpgradeRatio = accountProfit;
        }
        String itemStatus = SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus());
        ShopeeSku shopeeSku = new ShopeeSku();
        String articleNumber = composeProduct.getComposeSku();
        shopeeSku.setSku(articleNumber);
        shopeeSku.setProductStatus(itemStatus);
        shopeeSku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
        // 仅查看属性, 状态 标签
        String attribute = "状态：" + itemStatus + "<br/> 标签：" + (StringUtils.isNotBlank(composeProduct.getTag()) ? composeProduct.getTag() : "无");
        shopeeSku.setAttribute(attribute);

        StockKeepingUnitWithBLOBs bean = new StockKeepingUnitWithBLOBs();
        //自身净重
        bean.setWeight(composeProduct.getProductWeight());
        //包材重量
        Double packingWeight = composeProduct.getPackageWeight() == null ? 0.0 : composeProduct.getPackageWeight();
        bean.setPackingMaterialWeight(packingWeight);
        //搭配包材重量
        Double matchWeight = 0.0;
        bean.setCollocationPackingMaterialWeight(matchWeight);

        // 设置图片
        ShopeeImageUtil.setSkuImage(composeProduct.getComposeSku(), publicImages, bean::setImage, () -> {
            // publicImages为空的情况，由这里来提供图片
            List<String> elseSupplier = new ArrayList<>();
            if (StringUtils.isNotEmpty(composeProduct.getPic())) {
                elseSupplier.add(composeProduct.getPic());
            }
            return elseSupplier;
        });

        // 重量单位kg, 加快递包装3g
        shopeeSku.setShippingWeight(ShopeeProductInfoUtil.calcSkuWeight(bean));
        shopeeSku.setImage(bean.getImage());
        //默认库存9999
        if (shopeeSku.getQuantity() == null) {
            shopeeSku.setQuantity(9999);
        }
        // 全球商品价格
        Double price = calcPriceForGlobal(composeProduct, priceConfig, priceUpgradeRatio);
        BigDecimal sellerPaysFreight = null;
        try {
            sellerPaysFreight = ShopeeProductInfoUtil.getSellerPaysFreightByWeight(composeProduct.getPackageWeight(), site);
        } catch (Exception e) {
            throw new RuntimeException(String.format("计算组合产品价格失败：%s", e.getMessage()));
        }
        BigDecimal bigDecimalPrice = BigDecimal.valueOf(price);
        double priceAll = bigDecimalPrice.add(sellerPaysFreight).doubleValue();
        shopeeSku.setPrice(NumberUtils.round(priceAll, 2));
        return Lists.newArrayList(shopeeSku);
    }

    private Double calcPriceForGlobal(ComposeSku composeProduct, double priceConfig, double priceUpgradeRatio) {
        BigDecimal cost = composeProduct.getSaleCost() == null ? BigDecimal.ZERO : composeProduct.getSaleCost();
        BigDecimal price = cost.add(cost.multiply(BigDecimal.valueOf(priceConfig)));
        BigDecimal multiply = price.multiply(BigDecimal.valueOf(priceUpgradeRatio));
        price = price.add(multiply);
        return price.doubleValue();
    }

    /**
     * 根据套装文案信息设置模板标题描述
     */
    private void setTitleAndDesc(EsShopeeGlobalTemplate shopeeTemplate, ComposeSku composeProduct, Integer copyWritingType) {
        List<SpuOfficial> composeOfficials = composeProduct.getComposeOfficials();
        if (CollectionUtils.isEmpty(composeOfficials)) {
            return;
        }
        SpuOfficial spuOfficial = composeOfficials.get(0);
        if (spuOfficial == null) {
            return;
        }
        List<Object> longTitleList = new ArrayList<>();
        List<Object> shortTitleList = new ArrayList<>();

        try {
            // 兼容单品产品,设置长短标题集合
            Field[] fields = spuOfficial.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                if (name.startsWith(LONG_TITLE_PREFIX)) {
                    String value = (String) field.get(spuOfficial);
                    if (StringUtils.isNotBlank(value)) {
                        longTitleList.add(value);
                    }
                } else if (name.startsWith(SHORT_TITLE_PREFIX)) {
                    String value = (String) field.get(spuOfficial);
                    if (StringUtils.isNotBlank(value)) {
                        shortTitleList.add(value);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        spuOfficial.setShortTitleJson(JSON.toJSONString(shortTitleList));
        spuOfficial.setLongTitleJson(JSON.toJSONString(longTitleList));
        Map<String, String> titleMap = ShopeeProductInfoUtil.spuOfficialTitleAndDescResolver(spuOfficial, "", copyWritingType);
        shopeeTemplate.setName(titleMap.get("title"));
        shopeeTemplate.setDescription(titleMap.get("description"));
        shopeeTemplate.setCopyWritingType(Integer.valueOf(titleMap.get("copyWritingType")));
    }

    @Override
    public EsShopeeGlobalTemplate buildGlobalTemplate(ShopeeSpuGlobalPublishParam param) {
        ComposeSku composeProduct = ProductUtils.getComposeProduct(param.getSpu());
        if (composeProduct == null) {
            throw new NoSuchElementException(String.format("%s,未查询到组合套装", param.getSpu()));
        }
        String fullpathcode = composeProduct.getFullPathCode();
        boolean b = ShopeeGlobalTemplateUtil.checkSystemFullPathCodeCanPublish(fullpathcode);
        if (!b) {
            throw new RuntimeException("该产品系统分类平台禁止刊登");
        }
        // 拦截：拦截组合状态为停产、存档、废弃的组合SPU；拦截组合SPU禁售信息中标记了shopee禁售的数据；拦截组合状态不为启用的组合SPU
        checkComposeState(composeProduct, "创建");

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setSubAccount(param.getSubAccount());
        shopeeTemplate.setMerchant(param.getMerchant());
        shopeeTemplate.setMerchantId(param.getMerchantId());
        shopeeTemplate.setAccounts(CommonUtils.splitList(param.getAccounts(), ","));
        shopeeTemplate.setSku(param.getSpu());
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());
        //生成默认品牌
        shopeeTemplate.setBrand(JSON.toJSONString(shopeeBrandV2Service.generateDefaultBrand()));

        //类目属性
        ShopeeAdminGlobalTemplate refTemplate = null;
        if (param.getByTemplateId() != null) {
            refTemplate = shopeeAdminGlobalTemplateService.selectByPrimaryKey(param.getByTemplateId());
            if (refTemplate != null) {
                if (!StringUtils.equalsIgnoreCase(refTemplate.getSku(), param.getSpu())) {
                    //sku不同
                    throw new RuntimeException(String.format("admin范本的sku:%s 与 spu:%s 不同，请确认!", refTemplate.getSku(), param.getSpu()));
                }
                if (BooleanUtils.isFalse(refTemplate.getIsEnabled())) {
                    //范本禁用 不能使用
                    throw new RuntimeException(String.format("admin范本[%s]已被禁用，请重新生成模板!", refTemplate.getId()));
                }
                shopeeTemplate.setCategoryId(refTemplate.getCategoryId());
                shopeeTemplate.setAttributesStr(refTemplate.getAttributesStr());
                if (refTemplate.getBrand() != null) {
                    shopeeTemplate.setBrand(refTemplate.getBrand());
                }
            }
        } else {
            //随机一个admin范本
            ShopeeAdminGlobalTemplateExample ex = new ShopeeAdminGlobalTemplateExample();
            ex.createCriteria()
                    .andSkuEqualTo(param.getSpu())
                    .andIsEnabledEqualTo(true)
                    .andCategoryIdIsNotNull();
            List<ShopeeAdminGlobalTemplate> dbList = shopeeAdminGlobalTemplateService.selectByExample(ex);
            if (CollectionUtils.isNotEmpty(dbList)) {
                //过滤掉类目映射禁用的模板数据（新类目站点-过滤掉禁用类目）
                CategoryMappingExample mappingExample = new CategoryMappingExample();
                mappingExample.createCriteria()
                        .andPlatformEqualTo(Platform.Shopee.name())
                        .andSiteEqualTo(StrConstant.CROSSBAR)
                        .andSystemCategoryIdEqualTo(composeProduct.getFullPathCode())
                        .andApplyStateEqualTo(ApplyStatusEnum.NO.getCode());
                List<CategoryMapping> categoryMappingList = categoryMappingService.selectByExample(mappingExample);
                if (CollectionUtils.isNotEmpty(categoryMappingList)) {
                    List<String> disableList = categoryMappingList.stream().map(CategoryMapping::getPlatformCategoryId).collect(Collectors.toList());
                    dbList = dbList.stream().filter(o -> !disableList.contains(o.getCategoryId().toString())).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isNotEmpty(dbList)) {
                refTemplate = dbList.get(RandomUtils.nextInt(0, dbList.size()));
                shopeeTemplate.setCategoryId(refTemplate.getCategoryId());
                shopeeTemplate.setAttributesStr(refTemplate.getAttributesStr());
                if (refTemplate.getBrand() != null) {
                    shopeeTemplate.setBrand(refTemplate.getBrand());
                }
            }
        }

        //生成标题、描述
        ShopeeProductColumnInfo info = ShopeeProductInfoUtil.buildProductColumnInfo(shopeeTemplate.getSubAccount(), composeProduct, param.getCopyWritingType());
        String title = info.getTitle();
        String description = info.getDescription();
        if (title.length() > 120) {
            title = title.substring(0, 120);
        }
        if (description.length() > 3000) {
            description = description.substring(0, 3000);
        }
        shopeeTemplate.setName(title);
        shopeeTemplate.setDescription(description);
        shopeeTemplate.setTitleRule(JSON.toJSONString(info.getTitleRule()));
        shopeeTemplate.setCopyWritingType(info.getCopyWritingType());

        // 分类不存在 用标题搜索对应的分类
        if (shopeeTemplate.getCategoryId() == null) {
            ApiResult<List<ShopeeCategoryV2>> result = shopeeCategoryV2Service.categoryRecommend(shopeeTemplate.getName(), null);
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getResult())) {
                List<ShopeeCategoryV2> caList = result.getResult();
                shopeeTemplate.setCategoryId(caList.get(0).getCategoryId());
            }
        }

        //ES-7390 http://************:8080/browse/ES-7390 分类优先级 最后类目映射
        if (shopeeTemplate.getCategoryId() == null) {
            // 查询类目映射得到类目
            CategoryMappingExample mappingExample = new CategoryMappingExample();
            mappingExample.createCriteria()
                    .andPlatformEqualTo(Platform.Shopee.name())
                    .andSiteEqualTo(StrConstant.CROSSBAR)
                    .andSystemCategoryIdEqualTo(composeProduct.getFullPathCode())
                    .andApplyStateEqualTo(ApplyStatusEnum.YES.getCode());
            List<CategoryMapping> categoryMappingList = categoryMappingService.selectByExample(mappingExample);
            if (CollectionUtils.isNotEmpty(categoryMappingList)) {
                //随机取一个类目
                CategoryMapping categoryMapping = categoryMappingList.get(RandomUtils.nextInt(0, categoryMappingList.size()));
                shopeeTemplate.setCategoryId(Integer.valueOf(categoryMapping.getPlatformCategoryId()));
            }
        }

        //主图和特效图片
        List<String> publicImages;
        List<String> sizeImages = new ArrayList<>();
        try {
            publicImages = shopeeTemplateService.getAllImagesBySku(param.getSpu());
            if (CollectionUtils.isNotEmpty(publicImages)) {
                sizeImages.addAll(publicImages);
                shopeeTemplate.setMainImageNum(1);
                List<String> extraImages = ShopeeImageUtil.filterSpuExtraImages(param.getSpu(), publicImages);
                shopeeTemplate.setImagesList(extraImages);
            }
        } catch (Exception e) {
            log.error("查询图片池出错", e);
            throw new RuntimeException("查询图片池出错:" + e.getMessage());
        }
        List<String> sizeChartImages = ShopeeImageUtil.getSpuSizeChartImages(param.getSpu(), sizeImages);
        String videoLink = ShopeeProductInfoUtil.getVideoLink(param.getSpu());
        shopeeTemplate.setVideo(videoLink);

        // 获取账号配置 毛利率
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByAccounts(shopeeTemplate.getAccounts());
        if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
            throw new RuntimeException(shopeeTemplate.getAccounts() + "账号配置为空 无法获取毛利重新算价");
        }
        ShopeeAccountConfig firstAccountConfig = shopeeAccountConfigs.get(0);
        if (null == firstAccountConfig.getProfit()) {
            throw new RuntimeException(firstAccountConfig.getAccount() + "账号未配置毛利率");
        }

        //生成子属性sku
        List<ShopeeSku> shopeeSkus = null;
        shopeeSkus = generateShopeeSkus(composeProduct, publicImages, firstAccountConfig.getProfit(), firstAccountConfig.getSite());

        if (refTemplate != null) {
            // 子属性sku信息
            List<ShopeeSku> dbShopeeSkus = JSONArray.parseArray(refTemplate.getShopeeSkusStr(), ShopeeSku.class);
            ShopeeProductInfoUtil.variantSetColorAndSize(shopeeSkus, dbShopeeSkus);
            // 有模板的时候，直接取模板的包裹尺寸
            shopeeTemplate.setDimension(refTemplate.getDimension());
        }
        if (CollectionUtils.isEmpty(shopeeSkus)) {
            throw new RuntimeException("子属性SKU的单品状态全为停产，存档，废弃或禁售，侵权不能创建模板！");
        }

        if (CollectionUtils.isEmpty(shopeeSkus)) {
            ApiResult.newError("子属性SKU的单品状态全为停产，存档，废弃或禁售，侵权或者客户定制不能创建模板！");
        }

        if (shopeeSkus.size() <= 1) {
            // 判断是否color和size都为空
            ShopeeSku shopeeSku = shopeeSkus.get(0);
            if (StringUtils.isBlank(shopeeSku.getColor()) && StringUtils.isBlank(shopeeSku.getSize())) {
                shopeeSku.setColor("as shown");
            }
        }
        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));

        // 验证不可刊登sku 所有店铺都不可刊登则拦截
        if (ShopeeGlobalTemplateUtil.allAccountContainNonPublishableSku(shopeeAccountConfigs, shopeeTemplate)) {
            throw new RuntimeException("所有店铺都存在店铺不可刊登sku");
        }

        // 重量在最重sku的基础上加10g
        Optional<ShopeeSku> max = shopeeSkus.stream()
                .filter(o -> o.getShippingWeight() != null)
                .max((o1, o2) -> o1.getShippingWeight() > o2.getShippingWeight() ? 1 : -1);
        max.ifPresent(shopeeSku -> shopeeTemplate.setWeight(NumberUtils.format((shopeeSku.getShippingWeight() + 0.01), "0.###")));

        // 设置尺码图表
        Integer categoryId = shopeeTemplate.getCategoryId();
        if (categoryId != null) {
            // 判断分类id是否要求传尺码表
            LambdaQueryWrapper<ShopeeSizeChartApplicableCategory> applicableCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            applicableCategoryLambdaQueryWrapper.eq(ShopeeSizeChartApplicableCategory::getCategoryId, categoryId);
            int count = shopeeSizeChartApplicableCategoryService.count(applicableCategoryLambdaQueryWrapper);
            if (count > 0) {
                ShopeeSizeChartTemplates sizeChartTemplates = null;
                List<ShopeeSizeChartTemplates> shopeeSizeChartTemplates = shopeeSizeChartTemplatesService.listSizeChart(shopeeTemplate.getAccounts().get(0), categoryId);
                if (CollectionUtils.isEmpty(shopeeSizeChartTemplates)) {
                    sizeChartTemplates = shopeeSizeChartTemplates.get(0);
                }
                if (sizeChartTemplates != null) {
                    Integer sizeChartId = sizeChartTemplates.getSizeChartId();
                    ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                    sizeChartInfo.setSizeChartId(sizeChartId);
                    shopeeTemplate.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                } else {
                    // 不存在，则去取 图片
                    if (CollectionUtils.isNotEmpty(sizeChartImages)) {
                        String sizeChartImage = sizeChartImages.get(0);
                        ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                        sizeChartInfo.setSizeChart(sizeChartImage);
                        shopeeTemplate.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                    }
                }
            }
        }

        shopeeTemplate.setCondition("NEW");
//        shopeeTemplate.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
        shopeeTemplate.setIsParent(false);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.AUTO_PUBLISH.getCode());
        //默认销售刊登
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        //默认产品系统数据来源
        shopeeTemplate.setDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());

        return shopeeTemplate;
    }
}
