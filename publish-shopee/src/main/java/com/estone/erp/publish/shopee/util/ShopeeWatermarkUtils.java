package com.estone.erp.publish.shopee.util;

import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.ColorUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;

@Slf4j
public class ShopeeWatermarkUtils {
    //给图片加上文字水印
    //********废弃 不要用
    public static String createWatermark(String imageUrl, String markContent) {
        try {
            String imageType = imageUrl.substring(imageUrl.lastIndexOf(".") + 1);
            String destinyPath = new File("").getAbsolutePath() + "/" + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            ImageIcon imgIcon = new ImageIcon(new URL(imageUrl));
            Image theImg = imgIcon.getImage();
            int width = theImg.getWidth(null);
            int height = theImg.getHeight(null);
            BufferedImage bimage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Font font = new Font("幼圆", Font.ITALIC, 60);
            Graphics2D g = bimage.createGraphics();
            g.setFont(font);
            Color color = new Color(169,169,169,120);
            g.setColor(color);
            g.setBackground(Color.white);
            g.drawImage(theImg, 0, 0, null);
            g.rotate(10 * Math.PI / 180);
            g.drawString(markContent, width - markContent.length() * 40 + 50, height/3);
            g.drawString(markContent, width /5, height/5*4);
            g.dispose();
            //这里为了防止同一张图被多个账号加水印造成水印重叠，要在前面加上一个账号作为前缀路径
            String imageName = markContent + "_" + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            //FileOutputStream out = new FileOutputStream(destinyPath);
            //JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
            //JPEGEncodeParam param = encoder.getDefaultJPEGEncodeParam(bimage);
            boolean isUploadSuccess = ImageIO.write(bimage, imageType, new File(destinyPath));
            //param.setQuality(1, true);
            //encoder.encode(bimage, param);
            //out.close();
            if(isUploadSuccess) {
                File imageFile = new File(destinyPath);
                int retryN= 3;
                try {
                    do {
                        ApiResult<SeaweedFile> apiResult = FmsApiUtil.publishFileUpload(imageFile, imageName, "productInfo", WebUtils.getUserName());
                        SeaweedFile seaweedFile = apiResult.getResult();
                        String fileSystemUrl = seaweedFile.getUrl2();
                        if(fileSystemUrl != null){
                            return fileSystemUrl;
                        }
                    }while (--retryN > 0);
                }catch (Exception e){
                    log.error("图片上传出错：", e);
                }finally {
                    boolean isDeleted = imageFile.delete();
                    if (!isDeleted) {
                        log.error("文件删除失败: {}", imageFile.getAbsolutePath());
                    }
                }
            }
        } catch (Exception e) {
            log.error("图片处理失败", e);
        }

        return null;
    }

    //给图片加上文字水印，自动刊登和定时刊登走这个逻辑
    //********废弃 不要用
    public static String createWatermarkFromAccountConfig(ShopeeAccountConfig shopeeAccountConfig,String imageUrl) {
        String logoText = shopeeAccountConfig.getLogoText();
        String logoColor = shopeeAccountConfig.getLogoColor();
        String logoPosition = shopeeAccountConfig.getLogoPosition();
        String fontSize = shopeeAccountConfig.getFontSize();
        try {
            String imageType = imageUrl.substring(imageUrl.lastIndexOf(".") + 1);
            String destinyPath = new File("").getAbsolutePath() + "/" + String.valueOf(System.currentTimeMillis()) + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            ImageIcon imgIcon = new ImageIcon(new URL(imageUrl));
            Image theImg = imgIcon.getImage();
            int width = theImg.getWidth(null);
            int height = theImg.getHeight(null);
            BufferedImage bimage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Font font = new Font("幼圆", Font.ITALIC, Integer.valueOf(fontSize.replaceAll("px","")));
            Graphics2D g = bimage.createGraphics();
            g.setFont(font);
            Color color = ColorUtil.parseToColor(logoColor.split(",")[0].replace("#",""));
            g.setColor(color);
            g.setBackground(Color.white);
            g.drawImage(theImg, 0, 0, null);
            //设置透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, Float.valueOf(logoColor.split(",")[1])));
            int textWidth = getFontWidth("幼圆", Font.ITALIC, Integer.valueOf(fontSize.replaceAll("px","")), logoText);
            int x = 0, y = 0;
            if (StringUtils.equals(logoPosition, "left-top")) {
                x = 30;
                y = 50;
            } else if (StringUtils.equals(logoPosition, "right-top")) {
                x = width - textWidth - 30;
                y = 50;
            } else if (StringUtils.equals(logoPosition, "left-bottom")) {
                x += 30;
                y = height - 50;
            } else if (StringUtils.equals(logoPosition, "right-bottom")) {
                x = width - textWidth - 30;
                y = height - 50;
            } else {
                x = (width - textWidth) / 2;
                y = (height) / 2;
            }
            g.drawString(logoText, x, y);
            g.dispose();
            //这里为了防止同一张图被多个账号加水印造成水印重叠，要在前面加上一个账号作为前缀路径
//            String imageName = logoText.replace(" ", "") + "/" + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            String imageName = shopeeAccountConfig.getAccount().replace(".","") + "_" + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            boolean isUploadSuccess = ImageIO.write(bimage, imageType, new File(destinyPath));
            if(isUploadSuccess) {
                File imageFile = new File(destinyPath);
                int retryN= 3;
                try {
                    do {
                        ApiResult<SeaweedFile> apiResult = FmsApiUtil.publishFileUpload(imageFile, imageName, "productInfo", WebUtils.getUserName());
                        SeaweedFile seaweedFile = apiResult.getResult();
                        String fileSystemUrl = seaweedFile.getUrl2();
                        if(fileSystemUrl != null){
                            return fileSystemUrl;
                        }
                    }while (--retryN > 0);
                }catch (Exception e){
                    log.error("图片上传出错：", e);
                }finally {
                    boolean isDeleted = imageFile.delete();
                    if (!isDeleted) {
                        log.error("文件删除失败: {}", imageFile.getAbsolutePath());
                    }
                }
            }
        } catch (Exception e) {
            log.error("图片处理失败", e);
        }
        return null;
    }

    public static int getFontWidth(String fontName, int fontStyle, int fontSize, String pressText) {
        Font f = new Font(fontName, fontStyle, fontSize);
        //FontMetrics fm = sun.font.FontDesignMetrics.getMetrics(f);
        // 水印的宽高
        FontRenderContext frc = new FontRenderContext(new AffineTransform(), true, true);
        Rectangle rec = f.getStringBounds(pressText, frc).getBounds();
        double textWidth = rec.getWidth();

        return (int) textWidth;


    }

    /**
     * 给图片加上文字水印
     * @param imageUrl
     * @param markContent
     * @return
     */
    public static String addWaterMarkImg(String imageUrl, String markContent){
        Color color = new Color(169,169,169,120);
//        Color color = new Color(169,11,18,120);
        String waterMarkImg = pressText(imageUrl, markContent, "幼圆", Font.ITALIC,
                60, "left-bottom", color, Float.parseFloat("1"), markContent);

        return waterMarkImg;
    }

    /**
     *  根据账号配置添加图片水印
     * @param account
     * @param imageUrl
     * @return
     * @throws Exception
     */
    public static String addWaterMarkImg(ShopeeAccountConfig account, String imageUrl){
        if(account != null){
            String logoText = account.getLogoText();
            String logoPosition = account.getLogoPosition();
            String fontSize = account.getFontSize();
            String logoColor = account.getLogoColor();

            if(StringUtils.isNotBlank(logoText)){
                String size = fontSize.replace("px", "");
                String[] split = logoColor.split(",");
                //颜色
                String colorCode = split[0];
                colorCode = colorCode.replaceFirst("#", "");
                Color color = ColorUtil.parseToColor(colorCode);

                //透明度
                String transparency = split[1];

                String waterMarkImg = pressText(imageUrl, logoText, "幼圆", Font.ITALIC,
                        Integer.valueOf(size), logoPosition, color, Float.parseFloat(transparency), account.getAccount());

                return waterMarkImg;
            }
        }

        return "";
    }

    /**
     * 给图片加文字水印
     *  @param imageUrl       图片的网络地址
     * @param pressText s水印文字
     * @param fontName  字体名称
     * @param fontStyle 字体风格
     * @param fontSize  字体大小
     * @param location  字体位置:left-top：左上角，right-top：右上角，left-bottom：左下角，right-bottom：右下角
     * @param accountNumber
     */
    public static String pressText(String imageUrl, String pressText, String fontName, int fontStyle,
                                   int fontSize, String location, Color color, float composite, String accountNumber){

        String waterMarkImg = "";
        File imageFile = null;
        File fileTemp = null;
        try {
            accountNumber = accountNumber.replace(".", "");
//            String destinyPath = new File("").getAbsolutePath() + "/"+ accountNumber + "/"+ imageUrl.substring(imageUrl.lastIndexOf("/")+1);
//            fileTemp = File.createTempFile(accountNumber, imageUrl.substring(imageUrl.lastIndexOf(".")));

            int beginIndex = imageUrl.lastIndexOf(".");
            String suffixName = ".jpg";
            if(beginIndex == -1){
                fileTemp = File.createTempFile(accountNumber, suffixName);
            }else{
                String suffix = imageUrl.substring(beginIndex);
                if(suffix.equalsIgnoreCase("jpg") || suffix.equalsIgnoreCase("jpeg") || suffix.equalsIgnoreCase("png") ){
                    fileTemp = File.createTempFile(accountNumber, suffix);
                    suffixName = "."+suffix;
                }else{
                    fileTemp = File.createTempFile(accountNumber, suffixName);
                }
            }

            int textWidth = getFontWidth(fontName, fontStyle, fontSize, pressText);
            imageFile = toFile(imageUrl, fileTemp);
            Image src = ImageIO.read(imageFile);
            int width = src.getWidth(null);
            int height = src.getHeight(null);
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = image.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g.drawImage(src.getScaledInstance(width, height, Image.SCALE_SMOOTH), 0, 0, null);
            g.setColor(color);
            g.setFont(new Font(fontName, fontStyle, fontSize));
            g.setBackground(Color.white);
            //设置透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, composite));

            int x = 0, y = 0;
            if (StringUtils.equals(location, "left-top")) {
                x = 30;
                y = 50;
            } else if (StringUtils.equals(location, "right-top")) {
                x = width - textWidth - 30;
                y = 50;
            } else if (StringUtils.equals(location, "left-bottom")) {
                x += 30;
                y = height - 50;
            } else if (StringUtils.equals(location, "right-bottom")) {
                x = width - textWidth - 30;
                y = height - 50;
            } else {
                x = (width - textWidth) / 2;
                y = (height) / 2;
            }
            g.drawString(pressText, x, y);
            g.dispose();

//            String formatName = destinyPath.substring(destinyPath.lastIndexOf(".") + 1);
            String formatName = suffixName.substring(suffixName.lastIndexOf(".") + 1);
            ImageIO.write(image, formatName, imageFile);

//            String imageName = imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            String imageName = accountNumber + "_" + imageUrl.substring(imageUrl.lastIndexOf("/")+1);
            if(!imageName.endsWith(suffixName)){
                imageName += suffixName;
            }
            ApiResult<SeaweedFile> apiResult = FmsApiUtil.publishFileUpload(imageFile, imageName, "productInfo", WebUtils.getUserName());
            SeaweedFile seaweedFile = apiResult.getResult();
            waterMarkImg = seaweedFile.getUrl2();

        } catch (Exception e) {
            log.error("添加水印图片报错", e);
        }finally {
            try{
                boolean isDeleted = imageFile.delete();
                if (!isDeleted) {
                    log.error("文件删除失败: {}", imageFile.getAbsolutePath());
                }
//                fileTemp.delete();
            }catch (Exception e){
                log.error("file delete error", e);
            }
        }

        return waterMarkImg;
    }

    public static File toFile(String link, File file) {
        InputStream ins = null;
        OutputStream os = null;
        try {
            URL url = new URL(link);
            URLConnection uri = url.openConnection();
            //获取数据流
            ins = uri.getInputStream();
            os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            return file;
        }
        catch (Exception e) {
            return null;
        }finally {
            if(os != null){
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(ins != null){
                try {
                    ins.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
