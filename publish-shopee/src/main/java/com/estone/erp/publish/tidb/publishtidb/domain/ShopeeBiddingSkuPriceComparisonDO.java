package com.estone.erp.publish.tidb.publishtidb.domain;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: Shopee竞价SKU对比价格表DO
 * <AUTHOR>
 * @Date 2025/5/16 18:29
 */
@Data
public class ShopeeBiddingSkuPriceComparisonDO {

    /**
     * id
     */
    private Long id;

    /**
     * id集合
     */
    private List<Long> idList;

    /**
     * 竞价ID
     */
    private Long competitionId;

    /**
     * 紫鸟店铺名
     */
    private String ziniuShopName;

    /**
     * 店铺集合
     */
    private List<String> accountList;

    /**
     * SPU编号集合
     */
    private String spu;

    /**
     * SKU编码集合
     */
    private String sku;

    /**
     * Item ID集合
     */
    private String itemId;

    /**
     * Model ID集合
     */
    private String modelId;

    /**
     * 现SKU毛利率开始值
     */
    private BigDecimal currentSkuGrossMarginStart;

    /**
     * 现SKU毛利率结束值
     */
    private BigDecimal currentSkuGrossMarginEnd;

    /**
     * 改后价格开始值
     */
    private BigDecimal adjustedPriceStart;

    /**
     * 改后价格结束值
     */
    private BigDecimal adjustedPriceEnd;

    /**
     * 改后价格毛利率开始值
     */
    private BigDecimal adjustedPriceGrossMarginStart;

    /**
     * 改后价格毛利率结束值
     */
    private BigDecimal adjustedPriceGrossMarginEnd;

    /**
     * 导入时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime importTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime importTimeEnd;

    /**
     * 确认修改时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmationTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmationTimeEnd;

    /**
     * 上传时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTimeEnd;

    /**
     * 确认状态（0-确认不调整，1-待确认，2-已确认）
     */
    private Integer confirmationStatus;

    /**
     * 批量确认操作（0-确认不调整，1-待确认，2-已确认）
     */
    private Integer batchConfirmStatus;

    /**
     * 调价状态（1-成功，2-失败，3-处理中）
     */
    private Integer adjustmentStatus;

    /**
     * 创建时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAtStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAtEnd;

    /**
     * 更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAtStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAtEnd;

    /**
     * 创建人
     */
    private List<String> createdByList;

    /**
     * 修改人
     */
    private List<String> updatedByList;

    public List<String> toList(String data) {
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        return Arrays.stream(data.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }
}