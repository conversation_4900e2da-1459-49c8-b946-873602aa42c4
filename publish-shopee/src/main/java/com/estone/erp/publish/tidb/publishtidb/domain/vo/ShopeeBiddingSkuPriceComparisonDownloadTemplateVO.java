package com.estone.erp.publish.tidb.publishtidb.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/5/26 14:58
 */
@Data
public class ShopeeBiddingSkuPriceComparisonDownloadTemplateVO implements Serializable {

    /**
     * 紫鸟店铺名
     */
    @ExcelProperty("紫鸟店铺名")
    private String ziniuShopName;

    /**
     * 商家
     */
    @ExcelProperty("商家")
    private String merchant;

    /**
     * 竞品ID
     */
    @ExcelProperty("竞品ID")
    private Long competitionId;

    /**
     * 链接属性值
     */
    @ExcelProperty("链接属性值")
    private String linkAttributes;

    /**
     * 店铺
     */
    @ExcelProperty("店铺")
    private String account;

    /**
     * SPU编号
     */
    @ExcelProperty("SPU")
    private String spu;

    /**
     * SKU编码
     */
    @ExcelProperty("SKU")
    private String sku;

    /**
     * Item ID
     */
    @ExcelProperty("Item ID")
    private String itemId;

    /**
     * Model ID
     */
    @ExcelProperty("Model ID")
    private String modelId;

    /**
     * 现SKU价格
     */
    @ExcelProperty("现SKU价格")
    private BigDecimal currentSkuPrice;

}
