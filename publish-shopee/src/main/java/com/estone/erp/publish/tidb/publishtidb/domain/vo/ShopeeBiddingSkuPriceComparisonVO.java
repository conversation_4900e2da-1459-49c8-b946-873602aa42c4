package com.estone.erp.publish.tidb.publishtidb.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.AdjustmentStatusConverter;
import com.estone.erp.publish.component.converter.ConfirmationStatusConverter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: Shopee竞价SKU对比价格表VO
 * <AUTHOR>
 * @Date 2025/5/16 18:29
 */
@Data
public class ShopeeBiddingSkuPriceComparisonVO implements Serializable {

    /**
     * 主键ID
     */
    @ExcelIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 竞品ID
     */
    @ExcelIgnore
    private Long competitionId;

    /**
     * 紫鸟店铺名
     */
    @ExcelProperty("紫鸟店铺名")
    private String ziniuShopName;

    /**
     * 商家
     */
    @ExcelProperty("商家")
    private String merchant;

    /**
     * 店铺
     */
    @ExcelProperty("店铺")
    private String account;

    /**
     * SPU编号
     */
    @ExcelProperty("SPU")
    private String spu;

    /**
     * SKU编码
     */
    @ExcelProperty("SKU")
    private String sku;

    /**
     * Item ID
     */
    @ExcelProperty("Item ID")
    private String itemId;

    /**
     * Model ID
     */
    @ExcelProperty("Model ID")
    private String modelId;

    /**
     * 现SKU价格
     */
    @ExcelProperty("现SKU价格")
    private BigDecimal currentSkuPrice;

    /**
     * 现SKU毛利率（百分比）
     */
    @ExcelProperty("现SKU毛利率")
    private BigDecimal currentSkuGrossMargin;

    /**
     * 竞品链接价格
     */
    @ExcelProperty("竞品链接价格")
    private BigDecimal competitorLinkPrice;

    /**
     * 竞品链接库存
     */
    @ExcelProperty("竞品链接库存")
    private Integer competitorLinkStock;

    /**
     * 竞品链接
     */
    @ExcelProperty("竞品链接")
    private String competitorLink;

    /**
     * 竞品属性值
     */
    @ExcelProperty("竞品属性值")
    private String competitorAttributes;

    /**
     * 链接属性值
     */
    @ExcelProperty("链接属性值")
    private String linkAttributes;

    /**
     * 改后价格
     */
    @ExcelProperty("改后价格")
    private BigDecimal adjustedPrice;

    /**
     * 改后价格毛利率（百分比）
     */
    @ExcelProperty("改后价格毛利率")
    private BigDecimal adjustedPriceGrossMargin;

    /**
     * 导入时间
     */
    @ExcelProperty("导入时间")
    private LocalDateTime importTime;

    /**
     * 确认状态（0-确认不调整，1-待确认，2-已确认）
     * @see com.estone.erp.publish.shopee.enums.ShopeeConfirmationStatusEnum
     */
    @ExcelProperty(value = "确认状态", converter = ConfirmationStatusConverter.class)
    private Integer confirmationStatus;

    /**
     * 确认修改时间
     */
    @ExcelProperty("确认修改时间")
    private LocalDateTime confirmationTime;

    /**
     * 调价状态（1-成功，2-失败，3-处理中）
     * @see com.estone.erp.publish.shopee.enums.ShopeeAdjustmentStatusEnum
     */
    @ExcelProperty(value = "调价状态" , converter = AdjustmentStatusConverter.class)
    private Integer adjustmentStatus;

    /**
     * 上传时间
     */
    @ExcelProperty("上传时间")
    private LocalDateTime uploadTime;

    /**
     * 调价失败备注
     */
    @ExcelProperty("调价失败备注")
    private String adjustmentFailureRemark;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createdBy;

    /**
     * 修改时间
     */
    @ExcelIgnore
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @ExcelIgnore
    private String updatedBy;
}