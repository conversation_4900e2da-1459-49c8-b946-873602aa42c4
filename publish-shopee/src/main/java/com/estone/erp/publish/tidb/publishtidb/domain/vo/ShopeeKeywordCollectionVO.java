package com.estone.erp.publish.tidb.publishtidb.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: Shopee竞价SKU关键词采集表VO
 * <AUTHOR>
 * @Date 2025/5/20 18:29
 */
@Data
public class ShopeeKeywordCollectionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty("竞品ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * SPU编号
     */
    @ExcelProperty("SPU")
    private String spu;

    /**
     * 站点
     */
    @ExcelProperty("站点")
    private String site;


    /**
     * 竞价SKU关键词
     */
    @ExcelProperty("竞价SKU关键词")
    private String keyword;

    /**
     * 竞品链接
     */
    @ExcelProperty("竞品链接")
    private String competitorLink;

    /**
     * 价格
     */
    @ExcelProperty("价格")
    private BigDecimal price;

    /**
     * 库存
     */
    @ExcelProperty("库存")
    private Integer stock;

    /**
     * 采集时间
     */
    @ExcelProperty("采集时间")
    private LocalDateTime collectionTime;

    /**
     * 竞品属性
     */
    @ExcelProperty("竞品属性")
    private String variations;

    /**
     * 创建时间
     */
    @ExcelIgnore
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createdBy;

    /**
     * 修改时间
     */
    @ExcelIgnore
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @ExcelIgnore
    private String updatedBy;
}
