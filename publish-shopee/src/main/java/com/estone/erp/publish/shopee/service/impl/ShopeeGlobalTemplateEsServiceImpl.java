package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplateShop;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeGlobalTemplateRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeGlobalTemplateService;
import com.estone.erp.publish.platform.enums.ShopeeSkuDataSourceEnum;
import com.estone.erp.publish.platform.model.PmsSku;
import com.estone.erp.publish.platform.model.PmsSkuExample;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.shopee.api.v2.param.add.ShopeeSizeChartInfoParamTemplate;
import com.estone.erp.publish.shopee.bo.EsShopeeGlobalTemplateBo;
import com.estone.erp.publish.shopee.bo.ShopeePublishContext;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.component.ShopeeGlobalPublishComponent;
import com.estone.erp.publish.shopee.component.template.ShopeeTemplateBuilderComponent;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.shopee.dto.EsShopeeGlobalTemplateResponse;
import com.estone.erp.publish.shopee.dto.ShopeeGlobalTemplateEsExtend;
import com.estone.erp.publish.shopee.enums.ShopeeGlobalPublishStepEnum;
import com.estone.erp.publish.shopee.enums.ShopeeNewPublishStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeePublishRoleEnum;
import com.estone.erp.publish.shopee.enums.ShopeeTemplateTypeEnum;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeCategoryV2Service;
import com.estone.erp.publish.shopee.service.ShopeeGlobalTemplateEsService;
import com.estone.erp.publish.shopee.service.ShopeeTemplateService;
import com.estone.erp.publish.shopee.util.*;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataDO;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataRequest;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartApplicableCategory;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeSizeChartTemplates;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductSitesService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartApplicableCategoryService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeSizeChartTemplatesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Shopee全球刊登模板（es）业务层
 *
 * @Auther yucm
 * @Date 2022/9/19
 */
@Slf4j
@Service("shopeeGlobalTemplateEsService")
public class ShopeeGlobalTemplateEsServiceImpl implements ShopeeGlobalTemplateEsService {
    @Resource
    private EsShopeeGlobalTemplateService esShopeeGlobalTemplateService;
    @Resource
    private ShopeeCategoryV2Service shopeeCategoryV2Service;
    @Resource
    private PmsSkuService pmsSkuService;
    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private ShopeeGlobalPublishComponent shopeeGlobalPublishComponent;
    @Resource
    private ShopeeTemplateBuilderComponent shopeeTemplateBuilderComponent;

    @Resource
    private ShopeeNewProductSitesService shopeeNewProductSitesService;

    @Resource
    private ShopeeTemplateService shopeeTemplateService;

    @Resource
    private ShopeeOnlineUtils shopeeOnlineUtils;

    @Resource
    private ShopeeSizeChartApplicableCategoryService shopeeSizeChartApplicableCategoryService;

    @Resource
    private ShopeeSizeChartTemplatesService shopeeSizeChartTemplatesService;

    @Override
    public List<SpTemplateDataDO> getSPTemplateData(SpTemplateDataRequest request) {
        EsShopeeGlobalTemplateRequest templateRequest = new EsShopeeGlobalTemplateRequest();
        templateRequest.setSku(request.getSpu());
        templateRequest.setPublishStatus(3);
        templateRequest.setIsParent(false);
        templateRequest.setQueryFields(new String[]{"id", "SKU", "name","description"});
        List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates = this.getEsShopeeGlobalTemplates(templateRequest);
        List<SpTemplateDataDO> spTemplateDataDOS = esShopeeGlobalTemplates.stream().map(esShopeeGlobalTemplate -> {
            SpTemplateDataDO spTemplateDataDO = new SpTemplateDataDO();
            spTemplateDataDO.setSpu(esShopeeGlobalTemplate.getSku());
            spTemplateDataDO.setTitle(esShopeeGlobalTemplate.getName());
            spTemplateDataDO.setDescription(esShopeeGlobalTemplate.getDescription());
            return spTemplateDataDO;
        }).collect(Collectors.toList());
        return spTemplateDataDOS;
    }

    @Override
    public EsShopeeGlobalTemplate findAllById(Long id) {
        if (null == id) {
            return null;
        }
        return esShopeeGlobalTemplateService.findAllById(id);
    }

    @Override
    public void updateByShop(EsShopeeGlobalTemplateShop esShopeeGlobalTemplateShop) {
        if (esShopeeGlobalTemplateShop == null || null == esShopeeGlobalTemplateShop.getTemplateId()
                || StringUtils.isBlank(esShopeeGlobalTemplateShop.getAccountNumber())) {
            return;
        }

        EsShopeeGlobalTemplate templateEs = this.findAllById(esShopeeGlobalTemplateShop.getTemplateId());
        if (templateEs == null) {
            return;
        }

        Boolean update = false;
        List<EsShopeeGlobalTemplateShop> newShops = new ArrayList<>();
        for (EsShopeeGlobalTemplateShop shopeeGlobalTemplateShop : templateEs.getEsShopeeGlobalTemplateShops()) {
            if (StringUtils.equals(esShopeeGlobalTemplateShop.getSite(), shopeeGlobalTemplateShop.getSite())) {
                update = true;
                if (null == esShopeeGlobalTemplateShop.getLastUpdateDate()) {
                    esShopeeGlobalTemplateShop.setLastUpdateDate(new Date());
                }
                if (StringUtils.isBlank(esShopeeGlobalTemplateShop.getCreatedBy())) {
                    esShopeeGlobalTemplateShop.setLastUpdatedBy(WebUtils.getUserName());
                }
                shopeeGlobalTemplateShop = esShopeeGlobalTemplateShop;
            }
            newShops.add(shopeeGlobalTemplateShop);
        }

        if (update) {
            templateEs.setEsShopeeGlobalTemplateShops(newShops);
            this.save(templateEs);
        }
    }

    @Override
    public void updateByShops(List<EsShopeeGlobalTemplateShop> esShopeeGlobalTemplateShops) {
        if (CollectionUtils.isEmpty(esShopeeGlobalTemplateShops) || null == esShopeeGlobalTemplateShops.get(0).getTemplateId()) {
            return;
        }

        EsShopeeGlobalTemplate templateEs = this.findAllById(esShopeeGlobalTemplateShops.get(0).getTemplateId());
        if (templateEs == null) {
            return;
        }

        for (EsShopeeGlobalTemplateShop esShopeeGlobalTemplateShop : esShopeeGlobalTemplateShops) {
            esShopeeGlobalTemplateShop.setLastUpdateDate(new Date());
        }

        // 按站点分组
        Map<String, EsShopeeGlobalTemplateShop> siteShopMap = esShopeeGlobalTemplateShops.stream().collect(Collectors.toMap(o -> o.getSite(), o -> o, (k1, k2) -> k1));
        List<EsShopeeGlobalTemplateShop> newShops = new ArrayList<>();
        for (EsShopeeGlobalTemplateShop shopeeGlobalTemplateShop : templateEs.getEsShopeeGlobalTemplateShops()) {
            // 根据站点替换es中的数据 无对应站点保留原数据
            EsShopeeGlobalTemplateShop updateTemplateShop = siteShopMap.get(shopeeGlobalTemplateShop.getSite());
            if (null != updateTemplateShop) {
                siteShopMap.remove(updateTemplateShop.getSite());
                updateTemplateShop.setLastUpdateDate(new Date());
                if (StringUtils.isBlank(updateTemplateShop.getCreatedBy())) {
                    updateTemplateShop.setLastUpdatedBy(WebUtils.getUserName());
                }
                shopeeGlobalTemplateShop = updateTemplateShop;
            }
            newShops.add(shopeeGlobalTemplateShop);
        }

        // 之前没有的数据要新增
        if (MapUtils.isNotEmpty(siteShopMap)) {
            newShops.addAll(new ArrayList<>(siteShopMap.values()));
        }

        templateEs.setEsShopeeGlobalTemplateShops(newShops);
        this.save(templateEs);
    }

    @Override
    public void save(EsShopeeGlobalTemplate esShopeeGlobalTemplate) {
        if (esShopeeGlobalTemplate == null) {
            return;
        }

        // id为空新增 redis获取自增id
        if (null == esShopeeGlobalTemplate.getId()) {
            esShopeeGlobalTemplate.setId(ShopeeCommonUtils.getShopeeGlobalTemplateId());
            List<EsShopeeGlobalTemplateShop> templateShops = esShopeeGlobalTemplate.getEsShopeeGlobalTemplateShops();
            if (CollectionUtils.isNotEmpty(templateShops)) {
                for (EsShopeeGlobalTemplateShop templateShop : templateShops) {
                    templateShop.setTemplateId(esShopeeGlobalTemplate.getId());
                }
            }
        }

        if (null == esShopeeGlobalTemplate.getCreateDate()) {
            esShopeeGlobalTemplate.setCreateDate(new Date());
        }
        if (StringUtils.isBlank(esShopeeGlobalTemplate.getCreatedBy())) {
            esShopeeGlobalTemplate.setCreatedBy(WebUtils.getUserName());
        }

        esShopeeGlobalTemplate.setLastUpdateDate(new Date());
        if (StringUtils.isBlank(esShopeeGlobalTemplate.getCreatedBy())) {
            esShopeeGlobalTemplate.setLastUpdatedBy(WebUtils.getUserName());
        }

        esShopeeGlobalTemplateService.save(esShopeeGlobalTemplate);
    }

    @Override
    public void saveAll(List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates) {
        if (CollectionUtils.isEmpty(esShopeeGlobalTemplates)) {
            return;
        }
        for (EsShopeeGlobalTemplate esShopeeGlobalTemplate : esShopeeGlobalTemplates) {
            // id为空新增 redis获取自增id
            if (null == esShopeeGlobalTemplate.getId()) {
                esShopeeGlobalTemplate.setId(ShopeeCommonUtils.getShopeeGlobalTemplateId());
                List<EsShopeeGlobalTemplateShop> templateShops = esShopeeGlobalTemplate.getEsShopeeGlobalTemplateShops();
                if (CollectionUtils.isNotEmpty(templateShops)) {
                    for (EsShopeeGlobalTemplateShop templateShop : templateShops) {
                        templateShop.setTemplateId(esShopeeGlobalTemplate.getId());
                    }
                }
            }

            if (null == esShopeeGlobalTemplate.getCreateDate()) {
                esShopeeGlobalTemplate.setCreateDate(new Date());
            }
            if (StringUtils.isBlank(esShopeeGlobalTemplate.getCreatedBy())) {
                esShopeeGlobalTemplate.setCreatedBy(WebUtils.getUserName());
            }

            if (null == esShopeeGlobalTemplate.getLastUpdateDate()) {
                esShopeeGlobalTemplate.setLastUpdateDate(new Date());
            }

            if (StringUtils.isBlank(esShopeeGlobalTemplate.getCreatedBy())) {
                esShopeeGlobalTemplate.setLastUpdatedBy(WebUtils.getUserName());
            }
        }
        esShopeeGlobalTemplateService.saveAll(esShopeeGlobalTemplates);
    }

    @Override
    public void deleteById(Long id) {
        esShopeeGlobalTemplateService.deleteById(id);
    }

    @Override
    public EsShopeeGlobalTemplateResponse search(CQuery<EsShopeeGlobalTemplateRequest> cquery) {
        EsShopeeGlobalTemplateRequest request = cquery.getSearch();
        Page<EsShopeeGlobalTemplate> esShopeeGlobalTemplates = esShopeeGlobalTemplateService.page(request, cquery.getLimit(), cquery.getOffset());

        EsShopeeGlobalTemplateResponse response = new EsShopeeGlobalTemplateResponse();
        response.setEsShopeeGlobalTemplatePage(esShopeeGlobalTemplates);
        if (null != esShopeeGlobalTemplates) {
            Map<Long, ShopeeGlobalTemplateEsExtend> esExtendMap = handelPageExtend(esShopeeGlobalTemplates.getContent());
            response.setExtendMap(esExtendMap);
        }

        return response;
    }

    @Override
    public Page<EsShopeeGlobalTemplate> page(EsShopeeGlobalTemplateRequest request, int pageSize, Integer offset) {
        return esShopeeGlobalTemplateService.page(request, pageSize, offset);
    }

    @Override
    public List<EsShopeeGlobalTemplate> getEsShopeeGlobalTemplates(EsShopeeGlobalTemplateRequest request) {
        return esShopeeGlobalTemplateService.getEsShopeeGlobalTemplates(request);
    }

    @Override
    public String deleteShopeeTemplate(List<Long> idList, Boolean isParent) {
        if (CollectionUtils.isEmpty(idList)) {
            return "ids不可以为空!";
        }

        List<Integer> statusList = Arrays.asList(ShopeeNewPublishStatusEnum.PUBLISHING.getCode(),
                ShopeeNewPublishStatusEnum.SUCCESS.getCode(),
                ShopeeNewPublishStatusEnum.PARTIAL_SUCCESS.getCode());
        if (BooleanUtils.isTrue(isParent)) {
            //范本关联的模板为刊登中 刊登成功不能删除
            EsShopeeGlobalTemplateRequest request = new EsShopeeGlobalTemplateRequest();
            request.setParentIds(idList);
            request.setPublishStatusList(statusList);
            request.setIsParent(false);
            request.setQueryFields(new String[]{"id", "publishStatus"});
            List<EsShopeeGlobalTemplate> tempList = this.getEsShopeeGlobalTemplates(request);

            if (CollectionUtils.isNotEmpty(tempList)) {
                ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
                if (!superAdminOrEquivalent.isSuccess()) {
                    return superAdminOrEquivalent.getErrorMsg();
                }
                if (!superAdminOrEquivalent.getResult()) {
                    List<Long> failIds = tempList.stream().map(o -> o.getParentId()).collect(Collectors.toList());
                    return "id为" + failIds + "的范本，关联模板存在刊登中、部分成功、刊登成功，不能删除";
                }
            }
        } else {
            EsShopeeGlobalTemplateRequest request = new EsShopeeGlobalTemplateRequest();
            request.setIds(idList);
            request.setPublishStatusList(statusList);
            request.setIsParent(false);
            request.setQueryFields(new String[]{"id", "publishStatus"});
            List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates = this.getEsShopeeGlobalTemplates(request);

            if (CollectionUtils.isNotEmpty(esShopeeGlobalTemplates)) {
                List<Long> hasPublishTemplateIdList = esShopeeGlobalTemplates.stream().map(o -> o.getId()).collect(Collectors.toList());
                return "id为" + hasPublishTemplateIdList + "的模板为刊登中、部分成功、刊登成功，不能删除";
            }
        }

        for (Long id : idList) {
            this.deleteById(id);
        }
        return null;
    }

    @Override
    public void batchCopyTemplate(Map<Long, Integer> maps) {
        if (MapUtils.isEmpty(maps)) {
            return;
        }

        String userName = WebUtils.getUserName();
        Map<String, List<String>> skuMap = new HashMap<>();
        maps.forEach((id, quantity) -> {
            EsShopeeGlobalTemplate template = this.findAllById(id);
            if (null == template) {
                return;// 不存在则跳出
            }

            // 判断尺码表信息是否存在，存在则滞空
            if (StringUtils.isNotBlank(template.getSizeChartInfo())) {
                template.setSizeChartInfo(null);
            }

            String articleNumber = template.getSku();
            List<String> allImages = shopeeTemplateService.getAllImagesBySku(articleNumber);

            if (!skuMap.containsKey(articleNumber)) {
                ApiResult<List<String>> titleResult = ShopeeProductInfoUtil.getSkuAllTitle(articleNumber);
                if (titleResult.isSuccess() && CollectionUtils.isNotEmpty(titleResult.getResult())) {
                    skuMap.put(articleNumber, titleResult.getResult());
                }
            }

            for (Integer i = 0; i < quantity; i++) {
                EsShopeeGlobalTemplate bean = new EsShopeeGlobalTemplate();
                BeanUtils.copyProperties(template, bean);

                // 重置店铺等信息
                ShopeeGlobalTemplateEsUtils.resetEsShopeeGlobalTemplate(bean);
                // 重置附图
                ShopeeImageUtil.resetExtraImages(template, bean, allImages);

                //另存的模板随机取标题
                List<String> titleList = skuMap.get(articleNumber);
                if (CollectionUtils.isNotEmpty(titleList)) {
                    bean.setName(titleList.get(RandomUtils.nextInt(0, titleList.size())));
                }

                if (BooleanUtils.isNotFalse(template.getIsParent())) {
                    bean.setIsParent(false);
                }
                bean.setParentId(template.getId());
                bean.setCreatedBy(userName);
                bean.setCreateDate(new Date());
                bean.setLastUpdatedBy(userName);
                bean.setLastUpdateDate(new Date());

                //重新查一遍sku的单品状态并设置
                if (StringUtils.isNotEmpty(bean.getShopeeSkusStr())) {
                    List<ShopeeSku> shopeeSkus = ShopeeGlobalTemplateEsUtils.parseShopeeSkus(bean.getShopeeSkusStr());
                    for (ShopeeSku sku : shopeeSkus) {
                        PmsSkuExample pmsSkuExample = new PmsSkuExample();
                        pmsSkuExample.createCriteria().andArticleNumberEqualTo(sku.getSku());
                        List<PmsSku> pmsSkuList = pmsSkuService.selectByExample(pmsSkuExample);
                        if (CollectionUtils.isNotEmpty(pmsSkuList)) {
                            PmsSku pmsSku = pmsSkuList.get(0);
                            String skuCode = SkuStatusEnum.getCodeById(pmsSku.getStatus());
                            sku.setProductStatus(skuCode);
                        }
                    }
                    bean.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));
                }
                this.save(bean);
            }
        });
    }

    /**
     * 范本 保存并刊登
     *
     * @param parentTemplate
     * @return
     */
    @Override
    public ApiResult<String> persistParentTemplate(EsShopeeGlobalTemplateBo parentTemplate) {
        String userName = WebUtils.getUserName();
        // 验证输入参数
        ApiResult apiResult = ShopeeGlobalTemplateUtil.checkTemplateParam(parentTemplate);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }

        //验证范本是否已存在
        ApiResult<String> existParentResult = ShopeeGlobalTemplateEsUtils.checkExistParent(parentTemplate);
        if (!existParentResult.isSuccess()) {
            return existParentResult;
        }

        //做店铺验证
        ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(parentTemplate);
        if (!accountResult.isSuccess()) {
            return ApiResult.newError(accountResult.getErrorMsg());
        }

        // 过滤未填写的属性值
        parentTemplate.setAttributesStr(ShopeeAttributesUtils.filterBlank(parentTemplate.getAttributesStr()));

        List<ShopeeAccountConfig> accList = accountResult.getResult();
        List<String> accounts = accList.stream().map(ShopeeAccountConfig::getAccount).collect(Collectors.toList());
        parentTemplate.setAccounts(accounts);

        //新增
        if (parentTemplate.getType() == null) {
            parentTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
        }

        // 范本复制模板
        EsShopeeGlobalTemplate template = new EsShopeeGlobalTemplate();
        BeanUtils.copyProperties(parentTemplate, template);

        String sizeChartInfoStr = template.getSizeChartInfo();
        if (StringUtils.isBlank(sizeChartInfoStr)) {
            Integer categoryId = template.getCategoryId();
            if (categoryId != null) {
                // 判断分类是否为使用分类
                // 判断分类id是否要求传尺码表
                LambdaQueryWrapper<ShopeeSizeChartApplicableCategory> applicableCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                applicableCategoryLambdaQueryWrapper.eq(ShopeeSizeChartApplicableCategory::getCategoryId, categoryId);
                int count = shopeeSizeChartApplicableCategoryService.count(applicableCategoryLambdaQueryWrapper);
                if (count > 0) {
                    ShopeeSizeChartInfoParamTemplate shopeeSizeChartInfoParamTemplate = JSON.parseObject(sizeChartInfoStr, ShopeeSizeChartInfoParamTemplate.class);
                    if (shopeeSizeChartInfoParamTemplate == null ||
                            (StringUtils.isBlank(shopeeSizeChartInfoParamTemplate.getSizeChart()) && shopeeSizeChartInfoParamTemplate.getSizeChartId() == null)) {
                        ShopeeSizeChartTemplates sizeChartTemplates = null;
                        List<ShopeeSizeChartTemplates> shopeeSizeChartTemplates = shopeeSizeChartTemplatesService.listSizeChart(template.getAccounts().get(0), categoryId);
                        if (CollectionUtils.isNotEmpty(shopeeSizeChartTemplates)) {
                            sizeChartTemplates = shopeeSizeChartTemplates.get(0);
                        }
                        if (sizeChartTemplates != null) {
                            Integer sizeChartId = sizeChartTemplates.getSizeChartId();
                            ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                            sizeChartInfo.setSizeChartId(sizeChartId);
                            template.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                        } else {
                            List<String> allImages = shopeeTemplateService.getAllImagesBySku(template.getSku());
                            List<String> sizeChartImages = ShopeeImageUtil.getSpuSizeChartImages(template.getSku(), allImages);
                            if (CollectionUtils.isNotEmpty(sizeChartImages)) {
                                String sizeChartImage = sizeChartImages.get(0);
                                ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                                sizeChartInfo.setSizeChart(sizeChartImage);
                                template.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                            }
                        }
                    }
                }
            }
        }

        // 设置模板
        template.setIsParent(false);
        template.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        // 重新生长随机mtsku
        template.setMtsku(template.getSku() + CNSCPublishUtil.getRandomChar());
        if (!parentTemplate.getDataSource().equals(ShopeeSkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode())) {
            // 根据账号重新计算价格
            ApiResult<String> recalculatePriceApiResult = shopeeTemplateBuilderComponent.recalculatePriceByAccount(template, null);
            if (!recalculatePriceApiResult.isSuccess()) {
                return recalculatePriceApiResult;
            }
        }

        // 范本创建人创建时间信息保留
        if (null != parentTemplate.getId()) {
            EsShopeeGlobalTemplate dbParentTemplate = this.findAllById(parentTemplate.getId());
            if (dbParentTemplate != null) {
                parentTemplate.setCreatedBy(dbParentTemplate.getCreatedBy());
                parentTemplate.setCreateDate(dbParentTemplate.getCreateDate());
                parentTemplate.setLastUpdateDate(new Date());
                parentTemplate.setLastUpdatedBy(userName);
            }
        }

        // 保存范本 范本账号信息置空
        parentTemplate.setSubAccount(null);
        parentTemplate.setMerchant(null);
        parentTemplate.setMerchantId(null);
        parentTemplate.setAccounts(null);

        // insert 重新复制对象 避免子类的属性保存到es
        EsShopeeGlobalTemplate newParentTemplate = new EsShopeeGlobalTemplate();
        BeanUtils.copyProperties(parentTemplate, newParentTemplate);
        this.save(newParentTemplate);

        // 保存模板
        template.setId(null);
        template.setParentId(parentTemplate.getId());
        template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
        template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
        // insert
        ShopeeGlobalTemplateEsUtils.initTemplateShop(template, accList, parentTemplate.getSiteList(), userName);
        this.save(template);
        Long templateId = template.getId();
        CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
            EsShopeeGlobalTemplate temp = this.findAllById(template.getId());
            //可重复刊登
            publishTemplateByPublishStepHandle(temp, userName);
            return temp.getId();
        });

        return ApiResult.newSuccess("范本保存成功 正在刊登中请稍后查看模板和处理报告！");
    }

    @Override
    public ApiResult<String> persistTemplate(EsShopeeGlobalTemplateBo template, String operateType) {
        if (template.getMainImageNum() == null) {
            template.setMainImageNum(1);
        }

        // 校验该sku是否已存在范本
        if (BooleanUtils.isTrue(template.getIsParent())) {
            ApiResult<String> existParentResult = ShopeeGlobalTemplateEsUtils.checkExistParent(template);
            if (!existParentResult.isSuccess()) {
                return existParentResult;
            }
        }
        String userName = WebUtils.getUserName();

        //验证
        ApiResult apiResult = ShopeeGlobalTemplateUtil.checkTemplateParam(template);
        if (!apiResult.isSuccess()) {
            return apiResult;
        }

        // 校验不刊登类目
        if (BooleanUtils.isTrue(template.getUpload())) {
            boolean checkNoPublishCategory = ShopeeGlobalTemplateUtil.checkNoPublishCategory(template.getAccounts(), template.getCategoryId());
            if (checkNoPublishCategory) {
                return ApiResult.newError("该站点禁止刊登该分类，请重新选择分类");
            }
        }

        // 过滤未填写的属性值
        template.setAttributesStr(ShopeeAttributesUtils.filterBlank(template.getAttributesStr()));

        log.info("执行刊登：尺码表信息：{}", template.getSizeChartInfo());
        if ("add".equalsIgnoreCase(operateType)) {
            //新增
            if (template.getType() == null) {
                template.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
            }
            // 创建人
            template.setCreatedBy(userName);
            // 创建时间
            template.setCreateDate(new Timestamp(System.currentTimeMillis()));
            // 修改人
            template.setLastUpdatedBy(userName);
            // 修改时间
            template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));

            if (BooleanUtils.isTrue(template.getUpload())) { // spu生成模板 保存并刊登
                //做店铺验证
                ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(template);
                if (!accountResult.isSuccess()) {
                    return ApiResult.newError(accountResult.getErrorMsg());
                }

                List<ShopeeAccountConfig> accList = accountResult.getResult();
                List<String> accounts = accList.stream().map(o -> o.getAccount()).collect(Collectors.toList());
                template.setAccounts(accounts);
                template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
                template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());

                ShopeeGlobalTemplateEsUtils.initTemplateShop(template, accList, template.getSiteList(), userName);
                // insert 重新复制对象 避免子类的属性保存到es
                EsShopeeGlobalTemplate newTemplate = new EsShopeeGlobalTemplate();
                BeanUtils.copyProperties(template, newTemplate);
                this.save(newTemplate);

                // 保存模版同步新品模版数据（模版id）
                shopeeNewProductSitesService.updateNewProductByGlobalTemplate(template.getNewProductId(), newTemplate.getId(), accList);

                CNSCPublishUtil.asyncPublishGlobalTemplate(newTemplate.getId(), () -> {
                    EsShopeeGlobalTemplate temp = this.findAllById(newTemplate.getId());
                    //可重复刊登
                    publishTemplateByPublishStepHandle(temp, userName);

                    return temp.getId();
                });
            } else { // 范本新增
                // 新增只有范本
                template.setIsParent(true);
                // insert 重新复制对象 避免子类的属性保存到es
                EsShopeeGlobalTemplate newTemplate = new EsShopeeGlobalTemplate();
                BeanUtils.copyProperties(template, newTemplate);
                this.save(newTemplate);
            }
        } else if ("update".equalsIgnoreCase(operateType)) {// 范本模板编辑 模板保存并刊登
            //修改
            if (template.getId() == null) {
                return ApiResult.newError("修改失败：参数ID为空！");
            }
            EsShopeeGlobalTemplate dbTemplate = this.findAllById(template.getId());
            if (null == dbTemplate) {
                return ApiResult.newError("修改失败：模板不存在！");
            }
            if (!StringUtils.equalsIgnoreCase(userName, dbTemplate.getCreatedBy())) {
                ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
                if (!superAdminOrEquivalent.isSuccess()) {
                    return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                }
                if (!superAdminOrEquivalent.getResult()) {
                    return ApiResult.newError("只有创建者，超管，最高权限者才能进行编辑");
                }
            }
            // 模板才做验证
            if (BooleanUtils.isFalse(dbTemplate.getIsParent())) {
                if (dbTemplate.getPublishStatus() != null
                        && (dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PUBLISHING.getCode()
                        || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.SUCCESS.getCode()
                        || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PARTIAL_SUCCESS.getCode())) {
                    return ApiResult.newError("模板状态为刊登中、部分成功或刊登成功，不能修改！");
                }

                int count = 0;
                if (CollectionUtils.isNotEmpty(dbTemplate.getEsShopeeGlobalTemplateShops())) {
                    count = dbTemplate.getEsShopeeGlobalTemplateShops().size();
                }

                // 不是待刊登、刊登失败的模板 无店铺信息可以修改 其他不可以
                if (dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode()
                        || dbTemplate.getPublishStatus() == ShopeeNewPublishStatusEnum.FAIL.getCode()
                        || count == 0) {
                    ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(template);
                    if (!accountResult.isSuccess()) {
                        return ApiResult.newError(accountResult.getErrorMsg());
                    }

                    List<ShopeeAccountConfig> accList = accountResult.getResult();
                    List<String> accounts = accList.stream().map(o -> o.getAccount()).collect(Collectors.toList());
                    template.setAccounts(accounts);

                    ShopeeGlobalTemplateEsUtils.initTemplateShop(template, accList, template.getSiteList(), userName);

                    // 保存模版同步新品模版数据（模版id）
                    shopeeNewProductSitesService.updateNewProductByGlobalTemplate(template.getNewProductId(), template.getId(), accList);
                } else {
                    //不是待刊登、刊登失败的模板  不能修改店铺
                    template.setAccounts(dbTemplate.getAccounts());
                }

                //如果是上传直接改成刊登中...
                if (BooleanUtils.isTrue(template.getUpload())) {
                    //子账号：销售仅能选择自己关联权限的子账号；模板刊登状态为刊登中、刊登成功、刊登失败、部分成功的不可修改
                    template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
                    template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
                }
            }

            //设置是否范本及范本id
            template.setIsParent(dbTemplate.getIsParent());
            template.setParentId(dbTemplate.getParentId());
            template.setCreatedBy(dbTemplate.getCreatedBy());
            template.setCreateDate(dbTemplate.getCreateDate());
            template.setLastUpdatedBy(userName);
            template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            // 重新复制对象 避免子类的属性保存到es
            EsShopeeGlobalTemplate newTemplate = new EsShopeeGlobalTemplate();
            BeanUtils.copyProperties(template, newTemplate);
            this.save(newTemplate);


            //是否刊登
            if (BooleanUtils.isTrue(template.getUpload())) {
                Long templateId = template.getId();
                CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {

                    EsShopeeGlobalTemplate temp = this.findAllById(template.getId());

                    //可重复刊登
                    publishTemplateByPublishStepHandle(temp, userName);

                    return temp.getId();
                });
            }
        }
        return ApiResult.newSuccess();
    }

    @Override
    public ApiResult<String> batchPublish(EsShopeeGlobalTemplateBo templateBo) {
        ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(templateBo);
        if (!accountResult.isSuccess()) {
            return ApiResult.newError(accountResult.getErrorMsg());
        }

        List<ShopeeAccountConfig> accList = accountResult.getResult();
        List<String> accounts = accList.stream().map(o -> o.getAccount()).collect(Collectors.toList());
        templateBo.setAccounts(accounts);

        EsShopeeGlobalTemplateRequest esShopeeGlobalTemplateRequest = new EsShopeeGlobalTemplateRequest();
        esShopeeGlobalTemplateRequest.setIds(templateBo.getIds());
        esShopeeGlobalTemplateRequest.setQueryFields(null);
        List<EsShopeeGlobalTemplate> localEsTemplates = this.getEsShopeeGlobalTemplates(esShopeeGlobalTemplateRequest);
        localEsTemplates = localEsTemplates.stream()
                .filter(o -> (o.getPublishStatus() != null && ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode() == o.getPublishStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(localEsTemplates)) {
            return ApiResult.newError("选择的刊登模板未找到，请确认是否待刊登状态！");
        }

        // 获取账号配置 毛利率 根据店铺毛利率重新计算价格
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByAccounts(templateBo.getAccounts());
        if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
            return ApiResult.newError(templateBo.getAccounts() + "账号配置为空 无法获取毛利重新算价");
        }
        ShopeeAccountConfig firstAccountConfig = shopeeAccountConfigs.get(0);
        Double profit = firstAccountConfig.getProfit();
        if (null == profit) {
            return ApiResult.newError(firstAccountConfig.getAccount() + "账号未配置毛利率");
        }

        String userName = WebUtils.getUserName();
        for (EsShopeeGlobalTemplate template : localEsTemplates) {
            template.setSubAccount(templateBo.getSubAccount());
            template.setMerchant(templateBo.getMerchant());
            template.setMerchantId(templateBo.getMerchantId());
            template.setAccounts(templateBo.getAccounts());
            template.setPublishStatus(ShopeeNewPublishStatusEnum.PUBLISHING.getCode());
            template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
            Long templateId = template.getId();
            // 根据账号重新计算价格
            ApiResult<String> recalculatePriceApiResult = shopeeTemplateBuilderComponent.recalculatePriceByAccount(template, profit);
            if (!recalculatePriceApiResult.isSuccess()) {
                // 算价失败 记录处理报告记录状态
                template.setPublishStatus(ShopeeNewPublishStatusEnum.FAIL.getCode());
                ShopeeGlobalTemplateEsUtils.initTemplateShop(template, accList, templateBo.getSiteList(), userName, ShopeeNewPublishStatusEnum.FAIL.getCode());
                this.save(template);
                ShopeeFeedTaskHandleUtil.insertFinishFeedTask(task -> {
                    task.setAssociationId(templateId.toString());
                    task.setAccountNumber(template.getSubAccount());
                    task.setArticleNumber(template.getSku());
                    task.setCreatedBy(userName);
                    task.setResultMsg("根据选择店铺算价错误 " + recalculatePriceApiResult.getErrorMsg());
                    task.setAttribute5(template.getSubAccount());
                });
                continue;
            }
            Integer categoryId = template.getCategoryId();
            String sizeChartInfoStr = template.getSizeChartInfo();
            if (categoryId != null) {
                // 判断分类是否为使用分类
                // 判断分类id是否要求传尺码表
                LambdaQueryWrapper<ShopeeSizeChartApplicableCategory> applicableCategoryLambdaQueryWrapper = new LambdaQueryWrapper<>();
                applicableCategoryLambdaQueryWrapper.eq(ShopeeSizeChartApplicableCategory::getCategoryId, categoryId);
                int count = shopeeSizeChartApplicableCategoryService.count(applicableCategoryLambdaQueryWrapper);
                if (count > 0) {
                    ShopeeSizeChartInfoParamTemplate shopeeSizeChartInfoParamTemplate = JSON.parseObject(sizeChartInfoStr, ShopeeSizeChartInfoParamTemplate.class);
                    if (shopeeSizeChartInfoParamTemplate == null ||
                            (StringUtils.isBlank(shopeeSizeChartInfoParamTemplate.getSizeChart()) && shopeeSizeChartInfoParamTemplate.getSizeChartId() == null)) {
                        ShopeeSizeChartTemplates sizeChartTemplates = null;
                        List<ShopeeSizeChartTemplates> shopeeSizeChartTemplates = shopeeSizeChartTemplatesService.listSizeChart(template.getAccounts().get(0), categoryId);
                        if (CollectionUtils.isNotEmpty(shopeeSizeChartTemplates)) {
                            sizeChartTemplates = shopeeSizeChartTemplates.get(0);
                        }
                        if (sizeChartTemplates != null) {
                            Integer sizeChartId = sizeChartTemplates.getSizeChartId();
                            ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                            sizeChartInfo.setSizeChartId(sizeChartId);
                            template.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                        } else {
                            List<String> allImages = shopeeTemplateService.getAllImagesBySku(template.getSku());
                            List<String> sizeChartImages = ShopeeImageUtil.getSpuSizeChartImages(template.getSku(), allImages);
                            if (CollectionUtils.isNotEmpty(sizeChartImages)) {
                                String sizeChartImage = sizeChartImages.get(0);
                                ShopeeSizeChartInfoParamTemplate sizeChartInfo = new ShopeeSizeChartInfoParamTemplate();
                                sizeChartInfo.setSizeChart(sizeChartImage);
                                template.setSizeChartInfo(JSON.toJSONString(sizeChartInfo));
                            }
                        }
                    }
                }
            }

            // insert
            ShopeeGlobalTemplateEsUtils.initTemplateShop(template, accList, templateBo.getSiteList(), userName);
            this.save(template);

            CNSCPublishUtil.asyncPublishGlobalTemplate(templateId, () -> {
                // 重新查询 异步可能等待比较久时间
                EsShopeeGlobalTemplate temp = this.findAllById(templateId);

                //可重复刊登
                publishTemplateByPublishStepHandle(temp, userName);
                return temp.getId();
            });
        }

        return ApiResult.newSuccess("批量刊登中，请稍后查看处理报告！");
    }

    @Override
    public void publishTemplateByPublishStepHandle(EsShopeeGlobalTemplate temp, String userName) {
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask(task -> {
            task.setAssociationId(temp.getId() + "");
            task.setAccountNumber(temp.getSubAccount());
            task.setArticleNumber(temp.getSku());
            task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            task.setCreatedBy(userName);
            if (CollectionUtils.isNotEmpty(temp.getAccounts())) {
                task.setAttribute3(temp.getAccounts().stream().collect(Collectors.joining(",")));
            }
            task.setAttribute5(temp.getSubAccount());
        });

        boolean canPublish = true;
        String msg = "";
        //重新查询数据库得到最新模板
        EsShopeeGlobalTemplate template = this.findAllById(temp.getId());
        if (template == null) {
            canPublish = false;
            msg = String.format("模板%s,不存在！", temp.getId());
        }

        if (!canPublish) {
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), msg);
            return;
        }

        //按步骤刊登
        ApiResult<ShopeePublishContext> result;
        try {
            result = publishTemplateByPublishStep(template, userName, null);
        } catch (Exception e) {
            result = ApiResult.newError("刊登出错：" + e.getMessage());
            log.error("publishTemplateByPublishStep publish error ：", e);
        }

        // 更新模板刊登状态
        ShopeeGlobalTemplateUtil.updateGlobalTemplaeStatus(template, result, feedTask);
    }

    @Override
    public ApiResult<ShopeePublishContext> publishTemplateByPublishStep(EsShopeeGlobalTemplate template, String userName, String ruleName) {
        ApiResult<ShopeePublishContext> resp = ApiResult.newSuccess();

        SaleAccountAndBusinessResponse cnscAccount = null;
        boolean canPublish = true;
        String msg = "";
        if (canPublish) {
            //获取账号
            List<String> accounts = template.getAccounts();
            if (CollectionUtils.isEmpty(accounts)) {
                msg = template.getId() + "模板店铺为空！";
            }
            try {
                cnscAccount = CNSCPublishUtil.getCnscAccount(accounts.get(0));
                // 海外仓店铺使用本地刊登
                if (BooleanUtils.isTrue(cnscAccount.getOverseaWarehouse())) {
                    canPublish = false;
                    msg = "海外仓店铺，需使用本地刊登！";
                }
            } catch (Exception e) {
                log.error(String.format("account %s, get error", template.getAccounts()), e);
                msg = String.format("account:%s, get info error:%s", template.getAccounts(), e.getMessage());
            }
            if (cnscAccount == null) {
                canPublish = false;
                msg = StringUtils.isBlank(msg) ? "cnsc账号获取失败为空！" : msg;
            }
        }

        // 验证刊登店铺(不查询店铺为空的 和 成功的shop)
        List<EsShopeeGlobalTemplateShop> shopList = template.getEsShopeeGlobalTemplateShops().stream()
                .filter(o -> (StringUtils.isNotBlank(o.getAccountNumber()) && ShopeeNewPublishStatusEnum.SUCCESS.getCode() != o.getLocalStatus()))
                .collect(Collectors.toList());
        List<String> failAccounts = new ArrayList<>();
        if (canPublish) {
            //能刊登 再验证店铺是否已刊登过
            for (EsShopeeGlobalTemplateShop shop : shopList) {
                ApiResult<?> shopResult = ShopeeGlobalTemplateUtil.checkShopSkuPublished(template, shop);
                if (!shopResult.isSuccess()) {
                    failAccounts.add(shop.getAccountNumber() + ": " + shopResult.getErrorMsg());
                }
            }

            if (shopList.size() == failAccounts.size()) {
                // 店铺全部不能刊登 置为失败
                canPublish = false;
                msg = "不满足刊登条件得店铺：" + JSON.toJSONString(failAccounts);
            }

            // 检查在线列表信息
            if (StringUtils.isNotBlank(ruleName)) {
                ShopeeOnlineGlobalConfig config = shopeeOnlineUtils.getConfig();
                boolean b = shopeeOnlineUtils.checkCanPublish(config, template.getSku(), template.getId());
                if (!b) {
                    canPublish = false;
                    msg = "该SPU近30天出单量大于等于" + config.getOrderCountCycle() + "且SPU在线链接数大于等于" + config.getPublishNumber() + "，刊登失败";
                }
            }
        }

        if (StringUtils.isBlank(template.getShopeeSkusStr())) {
            canPublish = false;
            msg = "不存在sku信息！";
        } else {
            // --如果不是多属性产品的话，直接在这里设置价格
            List<ShopeeSku> shopeeSkus = ShopeeGlobalTemplateEsUtils.parseShopeeSkus(template.getShopeeSkusStr());
            if (CollectionUtils.isNotEmpty(shopeeSkus)) {
                for (ShopeeSku skus : shopeeSkus) {
                    if (skus.getPrice() == null || skus.getPrice() <= 0) {
                        msg = skus.getSku() + " 价格异常！";
                        canPublish = false;
                        break;
                    }
                }
            }
        }

        // 校验不刊登类目
        boolean checkNoPublishCategory = ShopeeGlobalTemplateUtil.checkNoPublishCategory(template.getAccounts(), template.getCategoryId());
        if (checkNoPublishCategory) {
            canPublish = false;
            msg = "该站点禁止刊登该分类，请重新选择分类";
        }

        // 不能刊登
        if (!canPublish) {
            return ApiResult.newError(msg);
        }
        List<String> accountList = shopList.stream().filter(o -> StringUtils.isNotBlank(o.getAccountNumber())).map(o -> o.getAccountNumber()).collect(Collectors.toList());
        //判断刊登步骤
        if (template.getPublishSteps() == null) {
            if (template.getGlobalItemId() == null) {
                template.setPublishSteps(ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode());
            } else {
                template.setPublishSteps(ShopeeGlobalPublishStepEnum.GLOBAL_ITEM_PUBLISH_TO_SHOP.getCode());
            }
        }

        // 一、PUBLISH_GLOBAL_ITEM 校验
        Map<String, String> imgMappingMap = new HashMap<>();
        if (ShopeeGlobalPublishStepEnum.PUBLISH_GLOBAL_ITEM.getCode().equalsIgnoreCase(template.getPublishSteps())) {
            // 1.1 不存在globalItemId 或者 发布的店铺不存在成功状态的数据
            List<EsShopeeGlobalTemplateShop> successTemplateShops = template.getEsShopeeGlobalTemplateShops().stream()
                    .filter(o -> ShopeeNewPublishStatusEnum.SUCCESS.getCode() == o.getLocalStatus())
                    .collect(Collectors.toList());

            int successCount = 0;
            if (CollectionUtils.isNotEmpty(successTemplateShops)) {
                successCount = successTemplateShops.size();
            }
            if (template.getGlobalItemId() == null || successCount == 0) {
                try {
                    resp = shopeeGlobalPublishComponent.addGlobalItem(template, cnscAccount, imgMappingMap, accountList);
                } catch (Exception e) {
                    log.error(String.format("模板:%s 刊登出错", template.getId()), e);
                    resp.setErrorMsg("上传全球产品出错" + e.getMessage());
                    resp.setSuccess(false);
                }
            } else {
                //暂时没业务...
            }
        }
        if (!resp.isSuccess()) {
            //如果上传产品失败
            return resp;
        }

        ShopeePublishContext publishContext = resp.getResult();
        String mainSku;
        Long globalItemId;
        if (publishContext != null) {
            mainSku = publishContext.getMainSku();
            globalItemId = publishContext.getTemporaryId();
        } else {
            mainSku = template.getSku();
            if (mainSku.contains("-")) {
                //重新查询主sku
                mainSku = shopeeTemplateBuilderComponent.getMainSku(template);
            }
            globalItemId = template.getGlobalItemId();
        }

        // 二、GLOBAL_ITEM_PUBLISH_TO_SHOP 校验
        Map<String, FeedTask> feedTaskMap = new HashMap<>();
        if (ShopeeGlobalPublishStepEnum.GLOBAL_ITEM_PUBLISH_TO_SHOP.getCode().equalsIgnoreCase(template.getPublishSteps())) {
            // 2.1 创建发布任务
            shopeeGlobalPublishComponent.createPublishTaskBefore(template, userName, mainSku, cnscAccount, imgMappingMap, globalItemId, feedTaskMap, shopList, ruleName);
        }

        // 三、UPLOAD_VIDEO 校验
        if (ShopeeGlobalPublishStepEnum.UPLOAD_VIDEO.getCode().equalsIgnoreCase(template.getPublishSteps())) {
            //暂时不做...
        }

        // 四、获取发布结果
        for (EsShopeeGlobalTemplateShop shopTp : shopList) {
            shopeeGlobalPublishComponent.getShopPublishResult(cnscAccount, feedTaskMap, shopTp);
        }

        // 五、更新刊登结果 (店铺不为空的刊登结果)
//        shopeeGlobalPublishComponent.updatePublishResult(template, feedTask, shopList);

        return resp;
    }

    @Override
    public ApiResult<?> publishGlobalItemToShop(EsShopeeGlobalTemplateBo param) {
        Long templateId = param.getId();
        if (templateId == null) {
            return ApiResult.newError("模板id不能为空！");
        }
        List<String> accounts = param.getAccounts();
        if (CollectionUtils.isEmpty(accounts)) {
            return ApiResult.newError("店铺不能为空！");
        }
        accounts = accounts.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accounts)) {
            return ApiResult.newError("店铺不能为空！");
        }
        param.setAccounts(accounts);
        //做店铺验证
        ApiResult<List<ShopeeAccountConfig>> accountResult = shopeeAccountConfigService.getAccountConfigAndCheck(param);
        if (!accountResult.isSuccess()) {
            return ApiResult.newError(accountResult.getErrorMsg());
        }

        List<ShopeeAccountConfig> accList = accountResult.getResult();

        //查询数据库模板
        EsShopeeGlobalTemplate template = this.findAllById(templateId);
        if (template == null) {
            return ApiResult.newError("模板不存在！");
        }
        if (ShopeeNewPublishStatusEnum.PARTIAL_SUCCESS.getCode() != template.getPublishStatus() && ShopeeNewPublishStatusEnum.FAIL.getCode() != template.getPublishStatus()) {
            return ApiResult.newError("模板状态不是刊登失败、部分成功，不能操作！");
        }
        String userName = WebUtils.getUserName();

        List<EsShopeeGlobalTemplateShop> shopList = template.getEsShopeeGlobalTemplateShops();
        Map<String, EsShopeeGlobalTemplateShop> siteShop = shopList.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSite()))
                .collect(Collectors.toMap(o -> o.getSite().toUpperCase(), o -> o, (o1, o2) -> o1));
        List<EsShopeeGlobalTemplateShop> publishList = new ArrayList<>(accList.size());
        List<EsShopeeGlobalTemplateShop> saveList = new ArrayList<>(accList.size());
        for (ShopeeAccountConfig account : accList) {
            if (siteShop.containsKey(account.getSite())) {
                //校验状态 只有待刊登、刊登失败的才能进行覆盖
                EsShopeeGlobalTemplateShop dbShop = siteShop.get(account.getSite());
                if (StringUtils.isBlank(dbShop.getAccountNumber())
                        || dbShop.getLocalStatus() == ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode()
                        || dbShop.getLocalStatus() == ShopeeNewPublishStatusEnum.FAIL.getCode()) {
                    dbShop.setAccountNumber(account.getAccount());
                    dbShop.setShopId(account.getShopId());
                    dbShop.setPublishTaskId("");
                    dbShop.setLocalStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
                    publishList.add(dbShop);
                }
            } else {
                //新增
                EsShopeeGlobalTemplateShop shop = new EsShopeeGlobalTemplateShop();
                shop.setTemplateId(template.getId());
                shop.setAccountNumber(account.getAccount());
                shop.setShopId(account.getShopId());
                shop.setSite(account.getSite());
                shop.setSku(template.getSku());
                shop.setLocalStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
                shop.setCreatedBy(userName);
                shop.setCreateDate(new Timestamp(System.currentTimeMillis()));
                siteShop.put(shop.getSite(), shop);
                saveList.add(shop);
                publishList.add(shop);
            }
        }
        if (publishList.isEmpty()) {
            return ApiResult.newError("没有可以发布的店铺！");
        }

        List<EsShopeeGlobalTemplateShop> newShops = new ArrayList<>(publishList);
        template.setEsShopeeGlobalTemplateShops(newShops);

        EsShopeeGlobalTemplate update = new EsShopeeGlobalTemplate();
        update.setId(template.getId());
        //源成功店铺
        List<String> sourceAccounts = shopList.stream()
                .filter(o -> o.getLocalStatus() == ShopeeNewPublishStatusEnum.PUBLISHING.getCode() || o.getLocalStatus() == ShopeeNewPublishStatusEnum.SUCCESS.getCode())
                .map(o -> o.getAccountNumber())
                .collect(Collectors.toList());

        //新店铺
        List<String> newAccounts = publishList.stream().map(o -> o.getAccountNumber()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceAccounts)) {
            sourceAccounts = newAccounts;
        } else {
            sourceAccounts.addAll(newAccounts);
        }
        template.setAccounts(sourceAccounts);
        this.save(template);

        // 保存模版同步新品模版数据（模版id）
        shopeeNewProductSitesService.updateNewProductByGlobalTemplate(template.getNewProductId(), template.getId(), accList);

        Long id = template.getId();
        CNSCPublishUtil.asyncPublishGlobalTemplate(id, () -> {
            EsShopeeGlobalTemplate dbTemplate = this.findAllById(id);
            //可重复刊登方法
            this.publishTemplateByPublishStepHandle(dbTemplate, userName);
            return dbTemplate.getId();
        });

        return ApiResult.newSuccess();
    }

    @Override
    public Map<String, Integer> getPublishRoleByGlobalItemIds(List<String> globalItemIds) {
        if (CollectionUtils.isEmpty(globalItemIds)) {
            return Collections.emptyMap();
        }
        List<Long> globalItemIdLongs = new ArrayList<>();
        for (String globalItemId : globalItemIds) {
            try {
                if (StringUtils.isBlank(globalItemId)) {
                    continue;
                }
                Long id = Long.valueOf(globalItemId);
                globalItemIdLongs.add(id);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        if (CollectionUtils.isEmpty(globalItemIdLongs)) {
            return Collections.emptyMap();
        }

        EsShopeeGlobalTemplateRequest request = new EsShopeeGlobalTemplateRequest();
        request.setIsParent(false);
        request.setGlobalItemIds(globalItemIdLongs);
        request.setQueryFields(new String[]{"id", "globalItemId", "publishRole"});
        List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates = this.getEsShopeeGlobalTemplates(request);

        Map<String, Integer> publishRoleMap = esShopeeGlobalTemplates.stream()
                .filter(o -> (null != o.getGlobalItemId() && null != o.getPublishRole()))
                .collect(Collectors.toMap(o -> o.getGlobalItemId().toString(), o -> o.getPublishRole(), (k1, k2) -> k1));

        return publishRoleMap;
    }

    /**
     * 设置扩展信息
     *
     * @param esShopeeGlobalTemplates
     * @return
     */
    @Override
    public Map<Long, ShopeeGlobalTemplateEsExtend> handelPageExtend(List<EsShopeeGlobalTemplate> esShopeeGlobalTemplates) {
        if (CollectionUtils.isEmpty(esShopeeGlobalTemplates)) {
            return Collections.emptyMap();
        }

        Map<Long, ShopeeGlobalTemplateEsExtend> extendMap = new HashMap<>();
        List<Integer> cateIds = esShopeeGlobalTemplates.stream()
                .filter(o -> o.getCategoryId() != null)
                .map(o -> o.getCategoryId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cateIds)) {
            ShopeeCategoryV2Example v2Ex = new ShopeeCategoryV2Example();
            v2Ex.createCriteria().andCategoryIdIn(cateIds).andSiteEqualTo(ShopeeConstants.CNSC);
            List<ShopeeCategoryV2> v2List = shopeeCategoryV2Service.selectByExample(v2Ex);

            // 设置分类全路径名称
            Map<Integer, String> idV2Map = v2List.stream().collect(Collectors.toMap(o -> o.getCategoryId(), o -> o.getNamePath(), (o1, o2) -> o1));
            esShopeeGlobalTemplates.parallelStream().forEach(o -> {
                ShopeeGlobalTemplateEsExtend extend = new ShopeeGlobalTemplateEsExtend();
                extend.setCategoryNamePath(idV2Map.get(o.getCategoryId()));
                extendMap.put(o.getId(), extend);
            });
        }

        return extendMap;
    }

    @Override
    public Long getMaxId() {
        Long maxId = null;
        EsShopeeGlobalTemplateRequest request = new EsShopeeGlobalTemplateRequest();
        request.setPageFields(new String[]{"id"});
        request.setOrderBy("createDate");
        request.setSequence("DESC");
        Page<EsShopeeGlobalTemplate> page = this.page(request, 1, 0);
        if (null == page || CollectionUtils.isEmpty(page.getContent())) {
            return maxId;
        }

        EsShopeeGlobalTemplate esShopeeGlobalTemplate = page.getContent().get(0);
        if (null != esShopeeGlobalTemplate) {
            return esShopeeGlobalTemplate.getId();
        }

        return maxId;
    }

    @Override
    public Map<String, Integer> getCreatedByTemplateSumMap(EsShopeeGlobalTemplateRequest request) {
        if (null == request) {
            return Collections.emptyMap();
        }
        return esShopeeGlobalTemplateService.getCreatedByTemplateSumMap(request);
    }

    @Override
    public List<ShopeeDatastatistics> getAccountNumberLocalStatusShopSumMap(EsShopeeGlobalTemplateRequest request) {
        if (null == request) {
            return Collections.emptyList();
        }

        List<ShopeeDatastatistics> datastatisticsList = new ArrayList<>();
        AggregatedPage<EsShopeeGlobalTemplate> search = esShopeeGlobalTemplateService.getAccountNumberLocalStatusShopSumMap(request);
        if (null == search) {
            return datastatisticsList;
        }

        Nested shopsGroup = search.getAggregations().get("shopsGroup");
        if (null != shopsGroup) {
            Terms accountNumberTerms = shopsGroup.getAggregations().get("accountNumberGroup");
            if (null != accountNumberTerms) {
                for (MultiBucketsAggregation.Bucket accountNumberBucket : accountNumberTerms.getBuckets()) {
                    String accountNumber = (String) accountNumberBucket.getKey();
                    if (StringUtils.isBlank(accountNumber)) continue;
                    Terms localStatusTerms = accountNumberBucket.getAggregations().get("localStatusGroup");
                    if (null == localStatusTerms) continue;

                    ShopeeDatastatistics shopeeDatastatistics = new ShopeeDatastatistics();
                    shopeeDatastatistics.setAccount(accountNumber);
                    shopeeDatastatistics.setAmount(new Long(accountNumberBucket.getDocCount()).intValue());

                    int normalAmount = 0;
                    for (MultiBucketsAggregation.Bucket localStatusBucket : localStatusTerms.getBuckets()) {
                        Long localStatus = (Long) localStatusBucket.getKey();
                        if (null == localStatus) continue;
                        int localStatusInteger = localStatus.intValue();
                        if (ShopeeNewPublishStatusEnum.SUCCESS.getCode() == localStatusInteger) {
                            normalAmount += new Long(localStatusBucket.getDocCount()).intValue();
                        }

                        try {
                            TopHits top = localStatusBucket.getAggregations().get("top");
                            for (SearchHit hit : top.getHits()) {
                                Map<String, Object> fieldMap = hit.getSourceAsMap();
                                String site = fieldMap.get("site").toString();
                                String createdBy = fieldMap.get("createdBy").toString();
                                shopeeDatastatistics.setSite(site);
                                shopeeDatastatistics.setUserId(createdBy);
                            }
                        } catch (Exception e) {
                            log.error(accountNumber + " " + localStatus + "处理top信息错误" + e.getMessage());
                        }
                    }
                    shopeeDatastatistics.setNormalAmount(normalAmount);
                    datastatisticsList.add(shopeeDatastatistics);
                }
            }
        }

        return datastatisticsList;
    }
}
