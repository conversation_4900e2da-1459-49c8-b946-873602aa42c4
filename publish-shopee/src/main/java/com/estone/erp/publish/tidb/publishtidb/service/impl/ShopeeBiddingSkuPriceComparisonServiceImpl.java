package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.enums.ShopeeConfirmationStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeBiddingSkuPriceComparisonDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeBiddingSkuPriceComparisonDownloadTemplateVO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeBiddingSkuPriceComparisonVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBiddingKeywordCollectionMapper;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBiddingSkuPriceComparisonMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingKeywordCollection;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingSkuPriceComparison;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBiddingSkuPriceComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * Shopee竞价SKU对比价格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Slf4j
@Service
public class ShopeeBiddingSkuPriceComparisonServiceImpl extends ServiceImpl<ShopeeBiddingSkuPriceComparisonMapper, ShopeeBiddingSkuPriceComparison> implements ShopeeBiddingSkuPriceComparisonService {

    private final Random RANDOM = new Random();

    @Autowired
    private ExcelDownloadLogService excelDownloadLogService;

    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Autowired
    private ShopeeBiddingKeywordCollectionMapper shopeeBiddingKeywordCollectionMapper;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Override
    public CQueryResult<ShopeeBiddingSkuPriceComparisonVO> queryPage(CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        CQueryResult<ShopeeBiddingSkuPriceComparisonVO> result = new CQueryResult<>();
        ShopeeBiddingSkuPriceComparisonDO search = query.getSearch();
        try {
            // 构建查询条件
            LambdaQueryWrapper<ShopeeBiddingSkuPriceComparison> lambdaQueryWrapper = getShopeeBiddingSkuPriceComparisonInfoLambdaQueryWrapper(search);

            // 分页查询
            IPage<ShopeeBiddingSkuPriceComparison> page = new Page<>(query.getPage(), query.getLimit());
            IPage<ShopeeBiddingSkuPriceComparison> pageResult = this.page(page, lambdaQueryWrapper);

            // 转换为VO
            List<ShopeeBiddingSkuPriceComparisonVO> voList = pageResult.getRecords().stream()
                    .map(info -> BeanUtil.copyProperties(info, ShopeeBiddingSkuPriceComparisonVO.class))
                    .collect(Collectors.toList());

            result.setRows(voList);
            result.setSuccess(true);
            result.setTotal(pageResult.getTotal());
            result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        } catch (Exception e) {
            log.error("查询竞价SKU对比价格表失败", e);
            return CQueryResult.failResult(e.getMessage());
        }
        return result;
    }

    @Override
    public ApiResult<String> download(CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        try {
            // 构造导出日志
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            downloadLog.setType(ShopeeDownloadTypeEnums.BIDDING_SKU_PRICE_COMPARISON.getType());
            downloadLog.setQueryCondition(JSON.toJSONString(query.getSearch()));
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(WebUtils.getUserName());
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            log.error("导出竞价SKU对比价格表失败", e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> importData(MultipartFile file) {
        // 校验文件
        if (file == null || file.isEmpty()) {
            return ApiResult.newError("导入文件不能为空");
        }

        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFilename) || !originalFilename.toLowerCase().endsWith(".xlsx")) {
            return ApiResult.newError("只支持 .xlsx 格式的 Excel 文件");
        }

        List<ShopeeBiddingSkuPriceComparisonDownloadTemplateVO> shopeeBiddingSkuPriceComparisonVOList = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream()) {
            // 读取 Excel 文件
            EasyExcel.read(inputStream, ShopeeBiddingSkuPriceComparisonVO.class, new PageReadListener<ShopeeBiddingSkuPriceComparisonDownloadTemplateVO>(shopeeBiddingSkuPriceComparisonVOList::addAll))
                    .sheet(0)
                    .head(ShopeeBiddingSkuPriceComparisonDownloadTemplateVO.class)
                    .doRead();
            if (CollectionUtils.isEmpty(shopeeBiddingSkuPriceComparisonVOList)) {
                return ApiResult.newError("导入数据为空");
            }

            // 校验数据
            for (ShopeeBiddingSkuPriceComparisonDownloadTemplateVO vo : shopeeBiddingSkuPriceComparisonVOList) {
                // 校验必填字段
                if (StringUtils.isAllBlank(vo.getZiniuShopName(), vo.getMerchant(), vo.getAccount(), vo.getSpu(), vo.getSku(), vo.getItemId(), vo.getModelId(), vo.getLinkAttributes()) || vo.getCurrentSkuPrice() == null || vo.getCompetitionId() == null) {
                    return ApiResult.newError("紫鸟店铺名、商家、店铺、SPU、SKU、Item ID、Model ID、现SKU价格、竞品ID、链接属性值均为必填项");
                }
            }

            // 获取当前用户可访问的店铺列表（如果authorizedAccounts不为空，表示用户不是超管、平台销售主管或数据支持部门，需要进行权限过滤）
            List<String> authorizedAccounts = permissionsHelper.getCurrentUserPermission(null, null, null, null, SaleChannel.CHANNEL_SHOPEE, true);
            if (CollectionUtils.isNotEmpty(authorizedAccounts)) {
                // 过滤掉用户无权限的店铺数据
                List<ShopeeBiddingSkuPriceComparisonDownloadTemplateVO> filteredList = shopeeBiddingSkuPriceComparisonVOList.stream()
                        .filter(vo -> authorizedAccounts.contains(vo.getAccount()))
                        .collect(Collectors.toList());

                // 如果过滤后没有数据，说明用户没有权限导入任何数据
                if (CollectionUtils.isEmpty(filteredList)) {
                    return ApiResult.newError("您没有权限导入这些店铺的数据");
                }

                // 更新列表为过滤后的数据
                shopeeBiddingSkuPriceComparisonVOList = filteredList;
            }

            //计算竞价信息
            List<ShopeeBiddingSkuPriceComparison> insertList = calculationComparisonList(shopeeBiddingSkuPriceComparisonVOList);
            if (insertList.isEmpty()) {
                return ApiResult.newSuccess("导入成功，新增: 0 条");
            }

            // 计算改前改后毛利率
            for (int i = 0; i < 2; i++) {
                String type = i == 0 ? "before" : "after";
                Map<String, BatchPriceCalculatorResponse> stringBatchPriceCalculatorResponseMap = batchPriceCalculator(type, insertList);
                for (ShopeeBiddingSkuPriceComparison priceComparison : insertList) {
                    BatchPriceCalculatorResponse batchPriceCalculatorResponse = stringBatchPriceCalculatorResponseMap.get(priceComparison.getAccount() + "_" + priceComparison.getSku() + "_" + priceComparison.getItemId());
                    if (Objects.isNull(batchPriceCalculatorResponse) || Objects.isNull(batchPriceCalculatorResponse.getGrossProfitRate())) {
                        continue;
                    }
                    if (i == 0) {
                        priceComparison.setCurrentSkuGrossMargin(BigDecimal.valueOf(batchPriceCalculatorResponse.getGrossProfitRate()));
                        continue;
                    }
                    priceComparison.setAdjustedPriceGrossMargin(BigDecimal.valueOf(batchPriceCalculatorResponse.getGrossProfitRate()));
                }
            }
            this.saveBatch(insertList);
        } catch (Exception e) {
            log.error("导入竞价SKU对比价格表失败，文件名: {}", originalFilename, e);
            throw new RuntimeException(e);
        }
        return ApiResult.newSuccess("导入成功");
    }

    /**
     * 计算竞价信息
     *
     * @param shopeeBiddingSkuPriceComparisonVOList
     * @return
     */
    private List<ShopeeBiddingSkuPriceComparison> calculationComparisonList(List<ShopeeBiddingSkuPriceComparisonDownloadTemplateVO> shopeeBiddingSkuPriceComparisonVOList) {
        // 数据处理
        List<ShopeeBiddingSkuPriceComparison> insertList = new ArrayList<>();

        String username = WebUtils.getUserName() != null && !WebUtils.getUserName().trim().isEmpty() ? WebUtils.getUserName() : DataContextHolder.getUsername();
        LocalDateTime now = LocalDateTime.now();

        // 按照店铺名称分组
        Map<String, ShopeeAccountConfig> accountConfigMap = shopeeBiddingSkuPriceComparisonVOList.stream()
                .map(ShopeeBiddingSkuPriceComparisonDownloadTemplateVO::getAccount).distinct().collect(Collectors.toMap(account -> account, shopeeAccountConfigService::getAccountConfigCache));

        // 查询竞价SKU关键词采集列表
        Map<Long, ShopeeBiddingKeywordCollection> shopeeBiddingKeywordCollectionsMap = getLongShopeeBiddingKeywordCollectionMap(shopeeBiddingSkuPriceComparisonVOList.stream().map(ShopeeBiddingSkuPriceComparisonDownloadTemplateVO::getCompetitionId));

        // 获取所有 SKU+ItemID 组合
        for (ShopeeBiddingSkuPriceComparisonDownloadTemplateVO skuPriceComparisonVO : shopeeBiddingSkuPriceComparisonVOList) {
            ShopeeAccountConfig shopeeAccountConfig = accountConfigMap.get(skuPriceComparisonVO.getAccount());
            if (Objects.isNull(shopeeAccountConfig)) {
                continue;
            }

            ShopeeBiddingSkuPriceComparison shopeeBiddingSkuPriceComparison = new ShopeeBiddingSkuPriceComparison();
            // 设置基本字段(不适用拷贝，因为有的字段需要计算，如果计算失败需要跳过)
            shopeeBiddingSkuPriceComparison.setZiniuShopName(skuPriceComparisonVO.getZiniuShopName());
            shopeeBiddingSkuPriceComparison.setAccount(skuPriceComparisonVO.getAccount());
            shopeeBiddingSkuPriceComparison.setSpu(skuPriceComparisonVO.getSpu());
            shopeeBiddingSkuPriceComparison.setSku(skuPriceComparisonVO.getSku());
            shopeeBiddingSkuPriceComparison.setItemId(skuPriceComparisonVO.getItemId());
            shopeeBiddingSkuPriceComparison.setModelId(skuPriceComparisonVO.getModelId());
            shopeeBiddingSkuPriceComparison.setCurrentSkuPrice(skuPriceComparisonVO.getCurrentSkuPrice());
            shopeeBiddingSkuPriceComparison.setImportTime(now);
            shopeeBiddingSkuPriceComparison.setLinkAttributes(skuPriceComparisonVO.getLinkAttributes());
            // 店铺信息
            shopeeBiddingSkuPriceComparison.setSubAccount(shopeeAccountConfig.getSubAccount());
            shopeeBiddingSkuPriceComparison.setMerchant(shopeeAccountConfig.getMerchant());
            shopeeBiddingSkuPriceComparison.setMerchantId(shopeeAccountConfig.getMerchantId());
            shopeeBiddingSkuPriceComparison.setShopId(shopeeAccountConfig.getShopId());
            shopeeBiddingSkuPriceComparison.setSite(shopeeAccountConfig.getSite());
            // 设置确认状态为待确认
            shopeeBiddingSkuPriceComparison.setConfirmationStatus(1);

            // 计算字段
            // 1. 竞品链接价格和库存
            try {
                ShopeeBiddingKeywordCollection bookingKeyword = shopeeBiddingKeywordCollectionsMap.get(skuPriceComparisonVO.getCompetitionId());
                if (Objects.isNull(bookingKeyword)) {
                    // 获取价格最低的记录
                    LambdaQueryWrapper<ShopeeBiddingKeywordCollection> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ShopeeBiddingKeywordCollection::getModelId, skuPriceComparisonVO.getModelId())
                            .gt(ShopeeBiddingKeywordCollection::getStock, 100)
                            .orderByAsc(ShopeeBiddingKeywordCollection::getPrice)
                            .last("limit 1");
                    ShopeeBiddingKeywordCollection competitor = shopeeBiddingKeywordCollectionMapper.selectOne(queryWrapper);
                    if (Objects.isNull(competitor)) {
                        continue;
                    }
                    shopeeBiddingSkuPriceComparison.setCompetitorLink(competitor.getCompetitorLink());
                    shopeeBiddingSkuPriceComparison.setCompetitorAttributes(competitor.getVariations());
                    shopeeBiddingSkuPriceComparison.setCompetitorLinkPrice(competitor.getPrice());
                    shopeeBiddingSkuPriceComparison.setCompetitorLinkStock(competitor.getStock());
                } else {
                    shopeeBiddingSkuPriceComparison.setCompetitorLink(bookingKeyword.getCompetitorLink());
                    shopeeBiddingSkuPriceComparison.setCompetitorAttributes(bookingKeyword.getVariations());
                    shopeeBiddingSkuPriceComparison.setCompetitorLinkPrice(bookingKeyword.getPrice());
                    shopeeBiddingSkuPriceComparison.setCompetitorLinkStock(bookingKeyword.getStock());
                }

                // 2. 改后价格 - 根据店铺站点读取参数配置，对改后价格进行随机减数处理
                if (skuPriceComparisonVO.getCurrentSkuPrice() != null && shopeeBiddingSkuPriceComparison.getCompetitorLinkPrice() != null) {
                    BigDecimal adjustedPrice = calculateAdjustedPrice(shopeeAccountConfig, shopeeBiddingSkuPriceComparison.getCompetitorLinkPrice());
                    shopeeBiddingSkuPriceComparison.setAdjustedPrice(adjustedPrice.setScale(2, RoundingMode.HALF_UP));
                }
            } catch (Exception e) {
                throw new RuntimeException(String.format("获取竞品链接信息失败，SKU: %s, 错误: %s", skuPriceComparisonVO.getSku(), e.getMessage()));
            }
            shopeeBiddingSkuPriceComparison.setCreatedAt(now);
            shopeeBiddingSkuPriceComparison.setCreatedBy(username);
            insertList.add(shopeeBiddingSkuPriceComparison);
        }
        return insertList;
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("竞价SKU对比价格表导入模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建模板数据
            List<ShopeeBiddingSkuPriceComparisonDownloadTemplateVO> list = new ArrayList<>();
            ShopeeBiddingSkuPriceComparisonDownloadTemplateVO template = new ShopeeBiddingSkuPriceComparisonDownloadTemplateVO();
            template.setZiniuShopName("紫鸟店铺名示例");
            template.setMerchant("商家示例");
            template.setAccount("店铺示例");
            template.setSpu("SPU示例");
            template.setSku("SKU示例");
            template.setItemId("Item ID示例");
            template.setModelId("Model ID示例");
            template.setCurrentSkuPrice(BigDecimal.valueOf(100.00));
            list.add(template);

            // 写入响应
            EasyExcel.write(response.getOutputStream(), ShopeeBiddingSkuPriceComparisonDownloadTemplateVO.class).sheet("模板").doWrite(list);
        } catch (IOException e) {
            log.error("下载模板失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> batchConfirm(CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        try {
            ShopeeBiddingSkuPriceComparisonDO search = query.getSearch();
            if (search == null) {
                return ApiResult.newError("未提供筛选条件，无法进行批量确认");
            }
            if (Objects.isNull(search.getBatchConfirmStatus())) {
                return ApiResult.newError("批量确认状态不能为空");
            }

            // 构建查询条件
            LambdaUpdateWrapper<ShopeeBiddingSkuPriceComparison> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ShopeeBiddingSkuPriceComparison::getConfirmationStatus, 1);
            // 添加ID条件（如果有）
            if (CollectionUtils.isNotEmpty(search.getIdList())) {
                updateWrapper.in(ShopeeBiddingSkuPriceComparison::getId, search.getIdList());
            } else {
                // 添加其他查询条件
                updateWrapper.like(StringUtils.isNotEmpty(search.getZiniuShopName()), ShopeeBiddingSkuPriceComparison::getZiniuShopName, search.getZiniuShopName())
                        .in(CollectionUtils.isNotEmpty(search.getAccountList()), ShopeeBiddingSkuPriceComparison::getAccount, search.getAccountList())
                        .in(StringUtils.isNotEmpty(search.getSpu()), ShopeeBiddingSkuPriceComparison::getSpu, search.toList(search.getSpu()))
                        .in(StringUtils.isNotEmpty(search.getSku()), ShopeeBiddingSkuPriceComparison::getSku, search.toList(search.getSku()))
                        .in(StringUtils.isNotEmpty(search.getItemId()), ShopeeBiddingSkuPriceComparison::getItemId, search.toList(search.getItemId()))
                        .in(StringUtils.isNotEmpty(search.getModelId()), ShopeeBiddingSkuPriceComparison::getModelId, search.toList(search.getModelId()))
                        .ge(ObjectUtils.isNotEmpty(search.getCurrentSkuGrossMarginStart()), ShopeeBiddingSkuPriceComparison::getCurrentSkuGrossMargin, search.getCurrentSkuGrossMarginStart())
                        .le(ObjectUtils.isNotEmpty(search.getCurrentSkuGrossMarginEnd()), ShopeeBiddingSkuPriceComparison::getCurrentSkuGrossMargin, search.getCurrentSkuGrossMarginEnd())
                        .ge(ObjectUtils.isNotEmpty(search.getAdjustedPriceStart()), ShopeeBiddingSkuPriceComparison::getAdjustedPrice, search.getAdjustedPriceStart())
                        .le(ObjectUtils.isNotEmpty(search.getAdjustedPriceEnd()), ShopeeBiddingSkuPriceComparison::getAdjustedPrice, search.getAdjustedPriceEnd())
                        .ge(ObjectUtils.isNotEmpty(search.getAdjustedPriceGrossMarginStart()), ShopeeBiddingSkuPriceComparison::getAdjustedPriceGrossMargin, search.getAdjustedPriceGrossMarginStart())
                        .le(ObjectUtils.isNotEmpty(search.getAdjustedPriceGrossMarginEnd()), ShopeeBiddingSkuPriceComparison::getAdjustedPriceGrossMargin, search.getAdjustedPriceGrossMarginEnd())
                        .between(search.getImportTimeStart() != null && search.getImportTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getImportTime, search.getImportTimeStart(), search.getImportTimeEnd())
                        .between(search.getConfirmationTimeStart() != null && search.getConfirmationTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getConfirmationTime, search.getConfirmationTimeStart(), search.getConfirmationTimeEnd())
                        .between(search.getUploadTimeStart() != null && search.getUploadTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getUploadTime, search.getUploadTimeStart(), search.getUploadTimeEnd());
            }

            // 设置更新的字段
            updateWrapper.set(ShopeeBiddingSkuPriceComparison::getConfirmationStatus, search.getBatchConfirmStatus());
            updateWrapper.set(ShopeeBiddingSkuPriceComparison::getConfirmationTime, LocalDateTime.now());
            updateWrapper.set(ShopeeBiddingSkuPriceComparison::getUpdatedAt, LocalDateTime.now());
            updateWrapper.set(ShopeeBiddingSkuPriceComparison::getUpdatedBy, WebUtils.getUserName() != null && !WebUtils.getUserName().trim().isEmpty() ? WebUtils.getUserName() : DataContextHolder.getUsername());

            // 执行更新并获取更新的记录数
            int count = this.baseMapper.update(null, updateWrapper);
            if (count > 0) {
                String message = search.getBatchConfirmStatus() == 0 ? "批量确认不调整成功" : "批量确认成功";
                return ApiResult.newSuccess(String.format("%s，共确认 %d 条数据", message, count));
            }
            return ApiResult.newError("没有符合条件的待确认数据");
        } catch (Exception e) {
            log.error("批量确认操作失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> batchUpdatePrice(List<ShopeeBiddingSkuPriceComparison> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return ApiResult.newError("更新列表不能为空");
        }
        try {
            // 获取当前用户和时间
            LocalDateTime now = LocalDateTime.now();
            String username = WebUtils.getUserName() != null && !WebUtils.getUserName().trim().isEmpty() ? WebUtils.getUserName() : DataContextHolder.getUsername();

            // 获取所有ID
            List<Long> idList = updateList.stream().map(ShopeeBiddingSkuPriceComparison::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(idList)) {
                return ApiResult.newError("更新列表中没有有效的ID");
            }
            Map<Long, ShopeeBiddingSkuPriceComparison> updateListMap = updateList.stream().collect(Collectors.toMap(ShopeeBiddingSkuPriceComparison::getId, Function.identity()));

            // 查询数据库中的记录
            List<ShopeeBiddingSkuPriceComparison> existingList = this.list(
                    new LambdaQueryWrapper<ShopeeBiddingSkuPriceComparison>().eq(ShopeeBiddingSkuPriceComparison::getConfirmationStatus, 1).in(ShopeeBiddingSkuPriceComparison::getId, idList)
            );
            if (CollectionUtils.isEmpty(existingList)) {
                return ApiResult.newError("未找到需要更新的记录");
            }

            // 更新改后价格并记录日志
            for (ShopeeBiddingSkuPriceComparison shopeeBiddingSkuPriceComparison : existingList) {
                ShopeeBiddingSkuPriceComparison comparison = updateListMap.get(shopeeBiddingSkuPriceComparison.getId());
                if (Objects.isNull(comparison)) {
                    continue;
                }
                shopeeBiddingSkuPriceComparison.setAdjustedPrice(comparison.getAdjustedPrice());
            }

            // 计算毛利率
            Map<String, BatchPriceCalculatorResponse> stringBatchPriceCalculatorResponseMap = batchPriceCalculator("after", existingList);

            Map<Long, ShopeeBiddingSkuPriceComparison> existingMap = existingList.stream().collect(Collectors.toMap(ShopeeBiddingSkuPriceComparison::getId, Function.identity()));
            for (ShopeeBiddingSkuPriceComparison priceComparison : existingList) {
                BatchPriceCalculatorResponse batchPriceCalculatorResponse = stringBatchPriceCalculatorResponseMap.get(priceComparison.getAccount() + "_" + priceComparison.getSku() + "_" + priceComparison.getItemId());
                if (Objects.isNull(batchPriceCalculatorResponse) || Objects.isNull(batchPriceCalculatorResponse.getGrossProfitRate())) {
                    continue;
                }
                priceComparison.setAdjustedPriceGrossMargin(BigDecimal.valueOf(batchPriceCalculatorResponse.getGrossProfitRate()));

                ShopeeBiddingSkuPriceComparison shopeeBiddingSkuPriceComparison = existingMap.get(priceComparison.getId());
                if (Objects.isNull(shopeeBiddingSkuPriceComparison)) {
                    continue;
                }

                priceComparison.setAdjustedPrice(shopeeBiddingSkuPriceComparison.getAdjustedPrice());
                priceComparison.setConfirmationStatus(ShopeeConfirmationStatusEnum.CONFIRMED.getCode());
                priceComparison.setConfirmationTime(now);
                priceComparison.setUpdatedAt(now);
                priceComparison.setUpdatedBy(username);
            }

            return ApiResult.newSuccess(this.updateBatchById(existingList) ? "批量修改价格成功" : "批量修改价格失败");
        } catch (Exception e) {
            log.error("批量修改价格失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 构造查询条件
     *
     * @param search
     * @return
     */
    private LambdaQueryWrapper<ShopeeBiddingSkuPriceComparison> getShopeeBiddingSkuPriceComparisonInfoLambdaQueryWrapper(ShopeeBiddingSkuPriceComparisonDO search) {
        return new LambdaQueryWrapper<ShopeeBiddingSkuPriceComparison>().like(StringUtils.isNotEmpty(search.getZiniuShopName()), ShopeeBiddingSkuPriceComparison::getZiniuShopName, search.getZiniuShopName())
                .in(CollectionUtils.isNotEmpty(search.getAccountList()), ShopeeBiddingSkuPriceComparison::getAccount, search.getAccountList())
                .in(StringUtils.isNotEmpty(search.getSpu()), ShopeeBiddingSkuPriceComparison::getSpu, search.toList(search.getSpu()))
                .in(StringUtils.isNotEmpty(search.getSku()), ShopeeBiddingSkuPriceComparison::getSku, search.toList(search.getSku()))
                .in(StringUtils.isNotEmpty(search.getItemId()), ShopeeBiddingSkuPriceComparison::getItemId, search.toList(search.getItemId()))
                .in(StringUtils.isNotEmpty(search.getModelId()), ShopeeBiddingSkuPriceComparison::getModelId, search.toList(search.getModelId()))
                .ge(ObjectUtils.isNotEmpty(search.getCurrentSkuGrossMarginStart()), ShopeeBiddingSkuPriceComparison::getCurrentSkuGrossMargin, search.getCurrentSkuGrossMarginStart())
                .le(ObjectUtils.isNotEmpty(search.getCurrentSkuGrossMarginEnd()), ShopeeBiddingSkuPriceComparison::getCurrentSkuGrossMargin, search.getCurrentSkuGrossMarginEnd())
                .ge(ObjectUtils.isNotEmpty(search.getAdjustedPriceStart()), ShopeeBiddingSkuPriceComparison::getAdjustedPrice, search.getAdjustedPriceStart())
                .le(ObjectUtils.isNotEmpty(search.getAdjustedPriceEnd()), ShopeeBiddingSkuPriceComparison::getAdjustedPrice, search.getAdjustedPriceEnd())
                .ge(ObjectUtils.isNotEmpty(search.getAdjustedPriceGrossMarginStart()), ShopeeBiddingSkuPriceComparison::getAdjustedPriceGrossMargin, search.getAdjustedPriceGrossMarginStart())
                .le(ObjectUtils.isNotEmpty(search.getAdjustedPriceGrossMarginEnd()), ShopeeBiddingSkuPriceComparison::getAdjustedPriceGrossMargin, search.getAdjustedPriceGrossMarginEnd())
                .eq(ObjectUtils.isNotEmpty(search.getConfirmationStatus()), ShopeeBiddingSkuPriceComparison::getConfirmationStatus, search.getConfirmationStatus())
                .eq(ObjectUtils.isNotEmpty(search.getAdjustmentStatus()), ShopeeBiddingSkuPriceComparison::getAdjustmentStatus, search.getAdjustmentStatus())
                .between(search.getImportTimeStart() != null && search.getImportTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getImportTime, search.getImportTimeStart(), search.getImportTimeEnd())
                .between(search.getConfirmationTimeStart() != null && search.getConfirmationTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getConfirmationTime, search.getConfirmationTimeStart(), search.getConfirmationTimeEnd())
                .between(search.getUploadTimeStart() != null && search.getUploadTimeEnd() != null, ShopeeBiddingSkuPriceComparison::getUploadTime, search.getUploadTimeStart(), search.getUploadTimeEnd())
                .orderByDesc(ShopeeBiddingSkuPriceComparison::getImportTime);
    }

    /**
     * 批量算价
     *
     * @param type
     * @param existingList
     * @return
     */
    private Map<String, BatchPriceCalculatorResponse> batchPriceCalculator(String type, List<ShopeeBiddingSkuPriceComparison> existingList) {
        List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = new ArrayList<>();
        // 按照店铺名称分组
        Map<String, List<ShopeeBiddingSkuPriceComparison>> shopeeBiddingSkuPriceComparisonMap = existingList.stream()
                .collect(Collectors.groupingBy(ShopeeBiddingSkuPriceComparison::getAccount));
        shopeeBiddingSkuPriceComparisonMap.forEach((account, shopeeBiddingSkuPriceComparisons) -> {
            ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.getAccountConfigCache(account);
            if (shopeeAccountConfig == null) {
                return;
            }

            // 获取物流信息
            Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic(shopeeAccountConfig.getAccount());
            String logisticCode = siteLogisticMap.get(shopeeAccountConfig.getSite());
            if (StringUtils.isBlank(logisticCode)) {
                return;
            }

            for (ShopeeBiddingSkuPriceComparison skuPriceComparison : shopeeBiddingSkuPriceComparisons) {
                BatchPriceCalculatorRequest req = new BatchPriceCalculatorRequest();
                req.setId(skuPriceComparison.getAccount() + "_" + skuPriceComparison.getSku() + "_" + skuPriceComparison.getItemId());
                req.setArticleNumber(skuPriceComparison.getSku());
                req.setQuantity(1);
                req.setSaleChannel(Platform.Shopee.name());
                req.setSite(shopeeAccountConfig.getSite());
                req.setCountryCode(shopeeAccountConfig.getSite());
                req.setShippingMethod(logisticCode);
                req.setSalePrice("before".equals(type) ? skuPriceComparison.getCurrentSkuPrice().doubleValue() : skuPriceComparison.getAdjustedPrice().doubleValue());
                batchPriceCalculatorRequestList.add(req);
            }
        });
        List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = new ArrayList<>();
        // 请求试算器
        List<List<BatchPriceCalculatorRequest>> partition = Lists.partition(batchPriceCalculatorRequestList, 100);
        for (List<BatchPriceCalculatorRequest> batchPriceCalculatorRequests : partition) {
            ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequests, 3);
            if (!listApiResult.isSuccess()) {
                continue;
            }
            batchPriceCalculatorResponses.addAll(listApiResult.getResult());
        }

        // 分区聚合id
        Map<String, BatchPriceCalculatorResponse> batchPriceCalculatorResponseMap = batchPriceCalculatorResponses.stream()
                .collect(Collectors.toMap(
                        BatchPriceCalculatorResponse::getId,
                        Function.identity(),
                        (o1, o2) -> o1
                ));
        return batchPriceCalculatorResponseMap;
    }

    /**
     * 计算改后价格
     *
     * @param shopeeAccountConfig 店铺配置
     * @param competitorLinkPrice       改后改后价
     * @return 改后价格
     */
    private BigDecimal calculateAdjustedPrice(ShopeeAccountConfig shopeeAccountConfig, BigDecimal competitorLinkPrice) {
        try {
            // 获取站点对应的减值配置 虾皮竞价对比价格站点配置
            String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "SHOPEE_BIDDING_COMPARISON_PRICE", "SITE_PRICE", 3);
            if (StringUtils.isEmpty(paramValue)) {
                log.warn("无法获取站点对应的减值配置, 站点: {}", shopeeAccountConfig.getSite());
                return competitorLinkPrice;
            }
            // 转成map
            Map<String, List<BigDecimal>> sitePrice = JSONObject.parseObject(paramValue, new TypeReference<Map<String, List<BigDecimal>>>() {
            });
            List<BigDecimal> reductionRange = sitePrice.get(shopeeAccountConfig.getSite());
            if (CollectionUtils.isEmpty(reductionRange) || reductionRange.size() < 2) {
                return competitorLinkPrice;
            }

            // 解析bigDecimalList并生成随机减值
            BigDecimal minReduction = reductionRange.get(0);
            BigDecimal maxReduction = reductionRange.get(1);
            if (minReduction == null || maxReduction == null || minReduction.compareTo(maxReduction) > 0) {
                return competitorLinkPrice;
            }
            BigDecimal range = maxReduction.subtract(minReduction);
            double randomFactor = RANDOM.nextDouble();
            BigDecimal finalPrice = competitorLinkPrice.subtract(minReduction.add(range.multiply(new BigDecimal(randomFactor))));

            // 确保价格不小于0
            return finalPrice.compareTo(BigDecimal.ZERO) > 0 ? finalPrice : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("计算改后价格失败, 站点: {}, 错误: {}", shopeeAccountConfig.getSite(), e.getMessage(), e);
            return competitorLinkPrice;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> batchDelete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ApiResult.newError("删除ID不能为空");
        }

        try {
            // 获取当前用户的下级用户列表
            List<String> underlingPermissionUsers = permissionsHelper.getUnderlingPermissionUsers(SaleChannel.CHANNEL_SHOPEE);

            // 如果underlingPermissionUsers为空，说明是超级管理员，可以删除所有记录
            if (CollectionUtils.isEmpty(underlingPermissionUsers)) {
                boolean success = this.removeByIds(ids);
                if (success) {
                    return ApiResult.newSuccess("删除成功");
                } else {
                    return ApiResult.newError("删除失败");
                }
            }

            // 否则，获取下级用户的详细信息
            List<String> authorizedUsers = new ArrayList<>(underlingPermissionUsers);

            // 查询要删除的记录
            Collection<ShopeeBiddingSkuPriceComparison> recordsCollection = this.listByIds(ids);
            if (CollectionUtils.isEmpty(recordsCollection)) {
                return ApiResult.newError("未找到要删除的记录");
            }
            List<ShopeeBiddingSkuPriceComparison> recordsToDelete = new ArrayList<>(recordsCollection);

            // 验证权限：只能删除自己和下级导入的数据
            List<Long> authorizedIds = new ArrayList<>();
            List<Long> unauthorizedIds = new ArrayList<>();

            for (ShopeeBiddingSkuPriceComparison record : recordsToDelete) {
                if (authorizedUsers.contains(record.getCreatedBy())) {
                    authorizedIds.add(record.getId());
                } else {
                    unauthorizedIds.add(record.getId());
                }
            }
            // 如果有未授权的记录，返回错误
            if (CollectionUtils.isNotEmpty(unauthorizedIds)) {
                return ApiResult.newError("您没有权限删除ID为 " + unauthorizedIds + " 的记录");
            }

            // 执行删除操作
            if (CollectionUtils.isNotEmpty(authorizedIds)) {
                boolean success = this.removeByIds(authorizedIds);
                if (success) {
                    return ApiResult.newSuccess("删除成功");
                } else {
                    return ApiResult.newError("删除失败");
                }
            } else {
                return ApiResult.newError("没有可删除的记录");
            }
        } catch (Exception e) {
            log.error("批量删除竞价SKU基本信息失败", e);
            return ApiResult.newError("删除失败：" + e.getMessage());
        }
    }

    @Override
    public ApiResult<String> batchMatchCompetitor(List<ShopeeBiddingSkuPriceComparisonDO> competitorList) {
        List<Long> idList = competitorList.stream().map(ShopeeBiddingSkuPriceComparisonDO::getId).collect(Collectors.toList());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(idList)) {
            return ApiResult.newError("ID列表不能为空");
        }
        // 按照id分组
        Map<Long, ShopeeBiddingSkuPriceComparison> shopeeBiddingSkuPriceComparisonsMap = this.listByIds(idList).stream().collect(Collectors.toMap(ShopeeBiddingSkuPriceComparison::getId, info -> info));

        // 获取竞品信息
        Map<Long, ShopeeBiddingKeywordCollection> shopeeBiddingKeywordCollectionsMap = getLongShopeeBiddingKeywordCollectionMap(competitorList.stream().map(ShopeeBiddingSkuPriceComparisonDO::getCompetitionId));

        for (ShopeeBiddingSkuPriceComparisonDO comparisonDO : competitorList) {
            ShopeeBiddingKeywordCollection bookingKeyword = shopeeBiddingKeywordCollectionsMap.get(comparisonDO.getCompetitionId());
            if (Objects.isNull(bookingKeyword)) {
                continue;
            }

            // 如果不不存在则更新
            ShopeeBiddingSkuPriceComparison shopeeBiddingSkuPriceComparison = shopeeBiddingSkuPriceComparisonsMap.get(comparisonDO.getId());
            if (Objects.isNull(shopeeBiddingSkuPriceComparison)) {
                continue;
            }


            if (Objects.isNull(shopeeBiddingSkuPriceComparison.getCompetitorLink()) || Objects.isNull(shopeeBiddingSkuPriceComparison.getCompetitorAttributes()) ||
                    Objects.isNull(shopeeBiddingSkuPriceComparison.getCompetitorLinkPrice()) || Objects.isNull(shopeeBiddingSkuPriceComparison.getCompetitorLinkStock())) {
                shopeeBiddingSkuPriceComparison.setCompetitorLink(bookingKeyword.getCompetitorLink());
                shopeeBiddingSkuPriceComparison.setCompetitorAttributes(bookingKeyword.getVariations());
                shopeeBiddingSkuPriceComparison.setCompetitorLinkPrice(bookingKeyword.getPrice());
                shopeeBiddingSkuPriceComparison.setCompetitorLinkStock(bookingKeyword.getStock());
            }
            this.updateById(shopeeBiddingSkuPriceComparison);
        }
        return ApiResult.newSuccess();

    }

    /**
     * 获取竞价SKU关键词采集列表
     *
     * @param competitorList
     * @return
     */
    private Map<Long, ShopeeBiddingKeywordCollection> getLongShopeeBiddingKeywordCollectionMap(Stream<Long> competitorList) {
        // 查询竞价SKU关键词采集列表
        Map<Long, ShopeeBiddingKeywordCollection> shopeeBiddingKeywordCollectionsMap = new HashMap<>();
        List<Long> competitionIdList = competitorList.filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(competitionIdList)) {
            shopeeBiddingKeywordCollectionsMap = shopeeBiddingKeywordCollectionMapper.selectBatchIds(competitionIdList).stream().collect(Collectors.toMap(ShopeeBiddingKeywordCollection::getId, Function.identity()));
        }
        return shopeeBiddingKeywordCollectionsMap;
    }
}
