package com.estone.erp.publish.shopee.jobHandler.once;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.shopee.dto.ShopeeUpdateTaskMqMessage;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * shopee 调价任务
 *
 * <AUTHOR>
 * @date 2025年7月8日14:39:55
 */
@Component
public class ShopeeUpdatePriceTaskJobHandler extends AbstractJobHandler {

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    public ShopeeUpdatePriceTaskJobHandler() {
        super(ShopeeUpdatePriceTaskJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private List<String> accountNumbers;
        private List<String> itemIds;
        private List<String> sites;
        /**
         * STATISTICS_ORIGIN_PRICE_PROFIT_RATE : 统计原折扣价毛利率
         * STATISTICS_NEW_PRICE_PROFIT_RATE : 统计新折扣价毛利率
         * UPDATE : 更新价格
         */
        private String taskType;
        /**
         * 物流方式 Shopee-SLS-MY
         */
        private String logisticsCode;
        private boolean update;
    }

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("ShopeeUpdatePriceTaskJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数不能为空");
            return ReturnT.FAIL;
        }

        if (innerParam.isUpdate()) {
            // 更新价格
            Long total = processUpdateTask(innerParam);
            XxlJobLogger.log("发送更新价格任务数量:{}", total);
        } else {
            // 统计链接
            Long total = statisticsAccount(innerParam);
            XxlJobLogger.log("发送统计任务数量:{}", total);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 处理更新价格任务
     */
    private Long processUpdateTask(InnerParam innerParam) {
        // 通过系统参数获取待调价店铺
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SHOPEE, "task", "update_my_price_accounts", 10);
        if (StringUtils.isBlank(systemParamValue)) {
            XxlJobLogger.log("系统参数未配置");
            return 0L;
        }
        innerParam.setAccountNumbers(CommonUtils.splitList(systemParamValue, ","));
        return sendTaskToMq(innerParam);
    }

    /**
     * 统计链接
     */
    private Long statisticsAccount(InnerParam innerParam) {
        return sendTaskToMq(innerParam);
    }

    /**
     * 发送任务到MQ
     */
    private Long sendTaskToMq(InnerParam innerParam) {
        List<String> sites = innerParam.getSites();
        if (sites == null || sites.isEmpty()) {
            XxlJobLogger.log("站点列表为空");
            return 0L;
        }

        ShopeeAccountConfigExample example = new ShopeeAccountConfigExample();
        ShopeeAccountConfigExample.Criteria criteria = example.createCriteria();
        criteria.andSiteIn(sites);
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            criteria.andAccountIn(innerParam.getAccountNumbers());
        }
        example.setColumns("account,site");
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectCustomColumnByExample(example);

        if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
            XxlJobLogger.log("没有查询到有效的账号");
            return 0L;
        }
        for (ShopeeAccountConfig shopeeAccountConfig : shopeeAccountConfigs) {
            String account = shopeeAccountConfig.getAccount();
            String site = shopeeAccountConfig.getSite();
            ShopeeUpdateTaskMqMessage message = new ShopeeUpdateTaskMqMessage();
            message.setAccount(account);
            message.setSite(site);
            message.setTaskType(innerParam.getTaskType());
            message.setItemIds(innerParam.getItemIds());
            message.setLogisticsCode(innerParam.getLogisticsCode());
            message.setUpdate(innerParam.isUpdate());
            try {
                // 发送消息到MQ
                rabbitMqSender.publishShopeeVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_UPDATE_TASK_QUEUE_KEY, message);
                XxlJobLogger.log("发送{}任务到MQ，店铺:{}, 站点:{}", innerParam.taskType, account, site);
            } catch (Exception e) {
                XxlJobLogger.log("发送{}任务到MQ失败: {}", innerParam.getTaskType(), e.getMessage());
            }
        }

        return (long) shopeeAccountConfigs.size();
    }
}
