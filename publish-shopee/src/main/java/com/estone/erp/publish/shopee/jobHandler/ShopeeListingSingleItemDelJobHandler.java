package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.LogPrintUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.service.ShopeeOperateLogService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuSystemStock;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ES-7790
 * 定时频率：一天一次
 * <p>
 * 定时时间：每天早上八点
 * <p>
 * 定时内容：sku单品状态为停产，存档，且多属性为否的链接，系统自动删除，记录对一个处理报告，备注为：停产存档且为单属性，系统自动删除
 * <p>
 * 备注：下架前需查询产品系统SKU单品状态是否为停产或存档，不是，则跳过不下架。
 */
@Slf4j
@Component
public class ShopeeListingSingleItemDelJobHandler extends AbstractJobHandler {

    public ShopeeListingSingleItemDelJobHandler() {
        super("shopeeListingSingleItemDelJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<String> skuList;
        private List<String> accountNumberList;
    }


    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Autowired
    private ShopeeItemEsService shopeeItemEsService;
    @Autowired
    private ShopeeOperateLogService shopeeOperateLogService;


    @XxlJob("shopeeListingSingleItemDelJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = new InnerParam();
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析错误！" + param);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        List<String> accountNumberList = innerParam.getAccountNumberList();
        List<String> skuList = innerParam.getSkuList();

        ShopeeAccountConfigExample shopeeAccountConfigExample = new ShopeeAccountConfigExample();
        shopeeAccountConfigExample.setColumns("id, account, percent_pre_order_target, percent_pre_order_actual, account_type");
        ShopeeAccountConfigExample.Criteria criteria = shopeeAccountConfigExample.createCriteria();
        criteria.andAccountIsNotNull();
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountIn(accountNumberList);
        }
        List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectCustomColumnByExample(shopeeAccountConfigExample);
        for (ShopeeAccountConfig shopeeAccountConfig : shopeeAccountConfigs) {
            ShopeeExecutors.executeAccountDeleteItem(() -> {
                String account = shopeeAccountConfig.getAccount();
                if (StringUtils.isBlank(account)) {
                    return;
                }
                XxlJobLogger.log("account:{} 开始删除停单存档且为单属性的", account);

                Map<String, String> mapStatus = new HashMap<>();
                Set<String> existDeleteItemIds = new HashSet<>();

                try {
                    EsShopeeItemRequest request = new EsShopeeItemRequest();
                    request.setSkuStatusList(List.of(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.DISCARD.getCode()));
                    if (CollectionUtils.isNotEmpty(skuList)) {
                        request.setArticleNumberList(skuList);
                    }
                    request.setQueryFields(new String[]{"id", "itemId", "articleNumber", "isGoods", "isFather"});
                    request.setItemSeller(shopeeAccountConfig.getAccount());
                    request.setOrderBy("id");
                    request.setIsGoods(true);
                    shopeeItemEsService.scrollQueryExecutorTask(request, listings -> {
                        if (CollectionUtils.isEmpty(listings)) {
                            return;
                        }

                        for (EsShopeeItem esShopeeItem : listings) {
                            JSONObject itemInfo = new JSONObject();
                            itemInfo.put("itemId", esShopeeItem.getItemId());
                            itemInfo.put("itemSeller", esShopeeItem.getItemSeller());

                            try {
                                if (esShopeeItem.getIsFather()) {

                                    // 检查 sku的 可用+在途-待发=0 就删除
                                    String articleNumber = esShopeeItem.getArticleNumber();
                                    if (StringUtils.isBlank(articleNumber)) {
                                        continue;
                                    }
                                    itemInfo.put("articleNumber", articleNumber);

                                    SkuSystemStock systemStock = SkuStockUtils.getMultipleSkuSystemStocks(articleNumber);
                                    itemInfo.put("usableStock", systemStock.getUsableStock());
                                    itemInfo.put("pendingStock", systemStock.getPendingStock());
                                    itemInfo.put("waitingOnWayStock", systemStock.getWaitingOnWayStock());
                                    Integer skuSystemStock = systemStock.getUsableWaitingPendingStock();
                                    if (skuSystemStock > 0) {
                                        continue;
                                    }
                                    String skuStatus = "";
                                    if (mapStatus.containsKey(articleNumber)) {
                                        skuStatus = mapStatus.get(articleNumber);
                                    } else {
                                        ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                                        skuStatus = productInfoVO.getSkuStatus();
                                        if (StringUtils.isBlank(skuStatus)) {
                                            continue;
                                        }
                                        mapStatus.put(articleNumber, skuStatus);
                                    }
                                    itemInfo.put("skuStatus", skuStatus);
                                    shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, itemInfo.toJSONString());
                                    if (skuStatus.equals(SkuStatusEnum.STOP.getCode()) || skuStatus.equals(SkuStatusEnum.ARCHIVED.getCode()) || skuStatus.equals(SkuStatusEnum.DISCARD.getCode())) {
                                        shopeeItemEsService.deleteItem(esShopeeItem.getItemId(), "admin", "停产存档废弃且为单属性，系统自动删除");
                                    }
                                } else {
                                    if (existDeleteItemIds.contains(esShopeeItem.getItemId())) {
                                        continue;
                                    }
                                    EsShopeeItemRequest itemRequest = new EsShopeeItemRequest();
                                    itemRequest.setQueryFields(new String[]{"id", "itemId", "articleNumber", "isGoods", "isFather", "skuStatus"});
                                    itemRequest.setItemSeller(shopeeAccountConfig.getAccount());
                                    itemRequest.setItemId(esShopeeItem.getItemId());
                                    itemRequest.setIsGoods(true);
                                    List<EsShopeeItem> esServiceEsShopeeItems = shopeeItemEsService.getEsShopeeItems(itemRequest);
                                    for (EsShopeeItem item : esServiceEsShopeeItems) {
                                        String articleNumber = item.getArticleNumber();
                                        itemInfo.put("articleNumber", articleNumber);
                                        SkuSystemStock systemStock = SkuStockUtils.getMultipleSkuSystemStocks(articleNumber);
                                        itemInfo.put("usableStock", systemStock.getUsableStock());
                                        itemInfo.put("pendingStock", systemStock.getPendingStock());
                                        itemInfo.put("waitingOnWayStock", systemStock.getWaitingOnWayStock());
                                        Integer skuSystemStock = systemStock.getUsableWaitingPendingStock();
                                        if (skuSystemStock > 0) {
                                            throw new BusinessException("库存大于0或者空,货号：" + articleNumber);
                                        }
                                        if (!SkuStatusEnum.isContainIllegalStatus(item.getSkuStatus())) {
                                            throw new BusinessException("刊登系统商品状态不满足停产、存档、废弃，不允许删除，货号：" + articleNumber);
                                        }

                                        String skuStatus = "";
                                        if (mapStatus.containsKey(articleNumber)) {
                                            skuStatus = mapStatus.get(articleNumber);
                                        } else {
                                            ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
                                            skuStatus = productInfoVO.getSkuStatus();
                                            if (StringUtils.isBlank(skuStatus)) {
                                                continue;
                                            }
                                            mapStatus.put(articleNumber, skuStatus);
                                        }
                                        itemInfo.put("skuStatus", skuStatus);
                                        shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, itemInfo.toJSONString());

                                        if (!SkuStatusEnum.isContainIllegalStatus(skuStatus)) {
                                            throw new BusinessException("产品系统商品状态不满足停产、存档、废弃，不允许删除，货号：" + articleNumber);
                                        }

                                    }
                                    existDeleteItemIds.add(esShopeeItem.getItemId());
                                    shopeeItemEsService.deleteItem(esShopeeItem.getItemId(), "admin", "停产存档废弃且为多属性，系统自动删除");
                                }
                            } catch (Exception e) {
                                shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, e.getMessage());
                            }
                        }
                    });
                } catch (Exception e) {
                    XxlJobLogger.log("account:{}, 处理异常：{}", account, LogPrintUtil.getMinimumReverseStackCause(e));
                    shopeeOperateLogService.createOperateLog("LISTING_SINGLE_ITEM_DEL", account, e.getMessage());
                }
            });
        }
        return ReturnT.SUCCESS;
    }
}
