package com.estone.erp.publish.tidb.publishtidb.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeBiddingSkuPriceComparisonDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeBiddingSkuPriceComparisonVO;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingSkuPriceComparison;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBiddingSkuPriceComparisonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * Shopee竞价SKU对比价格表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Api(tags = "竞价SKU对比价格表")
@RestController
@RequestMapping("/shopeeBiddingSkuPriceComparison")
public class ShopeeBiddingSkuPriceComparisonController {

    @Autowired
    private ShopeeBiddingSkuPriceComparisonService shopeeBiddingSkuPriceComparisonService;

    @ApiOperation(value = "分页查询")
    @PostMapping()
    public CQueryResult<ShopeeBiddingSkuPriceComparisonVO> queryPage(@RequestBody(required = true) CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        return shopeeBiddingSkuPriceComparisonService.queryPage(query);
    }

    @ApiOperation(value = "导出")
    @PostMapping("/download")
    public ApiResult<String> download(@RequestBody(required = true) CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        return shopeeBiddingSkuPriceComparisonService.download(query);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/import")
    public ApiResult<String> importData(@RequestParam("file") MultipartFile file) {
        try {
            return shopeeBiddingSkuPriceComparisonService.importData(file);
        } catch (Exception e) {
            return ApiResult.newError("导入失败");
        }
    }

    @ApiOperation(value = "导出模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        shopeeBiddingSkuPriceComparisonService.downloadTemplate(response);
    }

    @ApiOperation(value = "批量确认调整")
    @PostMapping("/batchConfirm")
    public ApiResult<String> batchConfirm(@RequestBody(required = true) CQuery<ShopeeBiddingSkuPriceComparisonDO> query) {
        try {
            return shopeeBiddingSkuPriceComparisonService.batchConfirm(query);
        } catch (Exception e) {
            return ApiResult.newError("批量确认异常，稍后重试！");
        }
    }

    @ApiOperation(value = "批量修改价格")
    @PostMapping("/batchUpdatePrice")
    public ApiResult<String> batchUpdatePrice(@RequestBody(required = true) List<ShopeeBiddingSkuPriceComparison> updateList) {
        try {
            return shopeeBiddingSkuPriceComparisonService.batchUpdatePrice(updateList);
        } catch (Exception e) {
            return ApiResult.newError("批量修改价格，稍后重试！");
        }
    }

    @ApiOperation(value = "批量匹配竞品")
    @PostMapping("/batchMatchCompetitor")
    public ApiResult<String> batchMatchCompetitor(@RequestBody(required = true) List<ShopeeBiddingSkuPriceComparisonDO> competitorList) {
        try {
            return shopeeBiddingSkuPriceComparisonService.batchMatchCompetitor(competitorList);
        } catch (Exception e) {
            return ApiResult.newError("操作异常，稍后重试！");
        }
    }

    @ApiOperation(value = "批量删除")
    @PostMapping("/delete")
    public ApiResult<String> delete(@RequestBody(required = true) List<Long> ids){
        return shopeeBiddingSkuPriceComparisonService.batchDelete(ids);
    }

}
