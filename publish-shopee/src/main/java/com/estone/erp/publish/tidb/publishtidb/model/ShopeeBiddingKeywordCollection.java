package com.estone.erp.publish.tidb.publishtidb.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Shopee竞价SKU关键词采集表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shopee_bidding_keyword_collection")
public class ShopeeBiddingKeywordCollection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * SPU
     */
    private String spu;

    /**
     * itemId
     */
    private String itemId;

    /**
     * Model ID
     */
    private String modelId;

    /**
     * 竞价SKU关键词
     */
    private String biddingKeyword;

    /**
     * 竞品链接
     */
    private String competitorLink;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 变体信息
     */
    private String variations;

    /**
     * 采集时间
     */
    private LocalDateTime collectionTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 更新日志
     */
    private String updateLog;


}
