package com.estone.erp.publish.shopee.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.CountryEnum;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.WenAnTypeEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeGlobalTemplate;
import com.estone.erp.publish.platform.bo.SpuTitleRule;
import com.estone.erp.publish.platform.enums.ShopeeSkuDataSourceEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.platform.util.SpuTitleRuleUtil;
import com.estone.erp.publish.shopee.bo.ShopeeCreateTemplateJson;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.bo.ShopeeTemplateNewBo;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.shopee.dto.ShopeeGlobalPriceDo;
import com.estone.erp.publish.shopee.dto.ShopeeProductColumnInfo;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpDas.esModel.EsAttrInfo;
import com.estone.erp.publish.system.erpDas.esModel.EsVariationsInfo;
import com.estone.erp.publish.system.erpDas.esModel.ali1688.EsAli1688AccountProductInfo;
import com.estone.erp.publish.system.erpDas.esModel.ali1688.EsAli1688VariationsInfo;
import com.estone.erp.publish.system.erpDas.esModel.ebay.EsAttrInfos;
import com.estone.erp.publish.system.erpDas.esModel.ebay.EsVariationsInfos;
import com.estone.erp.publish.system.erpDas.esModel.shopee.ShopeeSpIncrementInfo;
import com.estone.erp.publish.system.erpDas.esModel.shopee.ShopeeSpProductInfo;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.fms.enums.VideoTypeEnum;
import com.estone.erp.publish.system.fms.model.VideoDetail;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.*;
import com.estone.erp.publish.system.product.bean.forbidden.SalesInfringementVo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.util.StopWatch;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/2/27 10:14
 * @description 从新产品系统获取sku信息 构建模板详情
 */
@Slf4j
public class ShopeeProductInfoUtil {

    private static ShopeeTemplateService shopeeTemplateService = SpringUtils.getBean(ShopeeTemplateService.class);
    private static ShopeeTemplateNewService shopeeTemplateNewService = SpringUtils.getBean(ShopeeTemplateNewService.class);
    private static ShopeeBrandV2Service shopeeBrandV2Service = SpringUtils.getBean(ShopeeBrandV2Service.class);
    private static ShopeeAdminGlobalTemplateService shopeeAdminGlobalTemplateService = SpringUtils.getBean(ShopeeAdminGlobalTemplateService.class);
    private static ShopeeCategoryV2Service shopeeCategoryV2Service = SpringUtils.getBean(ShopeeCategoryV2Service.class);
    private static ShopeeAttributeV2Service shopeeAttributeV2Service = SpringUtils.getBean(ShopeeAttributeV2Service.class);
    private static CategoryMappingService categoryMappingService = SpringUtils.getBean(CategoryMappingService.class);
    private static SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
    private static ShopeeAccountConfigService shopeeAccountConfigService = SpringUtils.getBean(ShopeeAccountConfigService.class);
    private static final String color = "color";
    private static final String size = "size";
    /**
     * 子sku分隔符
     */
    private static final String SKU_SPLIT_STR = "-";

    /**
     * 子sku属性分隔符
     */
    private static final String PROP_VALUES_SPLIT_STR = ":";

    /**
     * 构建产品详情
     *
     * @param productArticleNumber
     * @return
     */
    public static ApiResult buildProductInfo(String productArticleNumber, String site) {
        List<ProductInfo> skuList = ProductUtils.findProductInfos(Arrays.asList(productArticleNumber));
        if (CollectionUtils.isEmpty(skuList)) {
            return ApiResult.newError("无效的货号！");
        }
        ShopeeCreateTemplateJson shopeeTemplate = new ShopeeCreateTemplateJson();
        // 图片迁移
        List<String> publicImages = shopeeTemplateService.getAllImagesBySku(productArticleNumber);
        if (CollectionUtils.isEmpty(publicImages)) {
            return ApiResult.newError("无图片的货号！");
        }
        // 图片设置进json
        shopeeTemplate.setProductImages(publicImages);

        //为了利用旧方法，进行对象转换（在里面进行随机选择主图）
        List<StockKeepingUnitWithBLOBs> skus = convertSkuInfo(skuList, publicImages);

        //获取子属性sku
        List<ShopeeSku> shopeeSkus = getShopeeChildSkus(skus, skuList);

        // 过滤当前站点禁售
        shopeeSkus = filterForbidden(shopeeSkus, skuList, Arrays.asList(site));

        // 过滤侵权
        try {
            shopeeSkus = filterInfringement(shopeeSkus, Arrays.asList(site));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        if (CollectionUtils.isEmpty(shopeeSkus)) {
            return ApiResult.newError("子属性SKU的单品状态全为停产，存档，废弃或禁售，侵权不能创建模板！");
        }
        shopeeTemplate.setSkus(shopeeSkus);

        //根据sku，国家 查询刊登成功模板的color size
        ShopeeTemplateNewBo bo = matchSkuColorSizeBySkuSite(productArticleNumber, site);
        matchSkuColorSize(shopeeSkus, bo);

        //StockKeepingUnitWithBLOBs skuObject = skus.get(0);
        String defaultTitle = null;
        // 查询标题描述
        for (ProductInfo productInfo : skuList) {
//            //英文标题
//            if (StringUtils.isNotBlank(productInfo.getTitleEn())) {
//                shopeeTemplate.setName(productInfo.getTitleEn());
//            }
//            //英文描述
//            if (StringUtils.isNotBlank(productInfo.getDesEn())) {
//                shopeeTemplate.setDescription(productInfo.getDesEn());
//            }
            // 默认标题
            if (StringUtils.isBlank(defaultTitle)) {
                defaultTitle = productInfo.getTitleEn();
            }
            //获取标题和描述
                /*
                    标题取值优先级：
                        1-SPU长标题
                        1-SPU短标题
                        3-SKU标题
                    描述取值优先级：
                        1-SKU描述新，在描述最前面插入标题。
                        2-SKU描述，在描述最前面插入标题
                 */
            ApiResult<?> titleInfo = getTileAndDescription(productInfo.getMainSku(), defaultTitle, WenAnTypeEnum.defaultWenAn.getCode());
            if (titleInfo == null || !titleInfo.isSuccess()) return ApiResult.newError(titleInfo.getErrorMsg());
            Map<String, String> titleMap = (HashMap) titleInfo.getResult();
            if (titleMap != null && titleMap.size() > 0) {
                shopeeTemplate.setName(titleMap.get("title"));
                shopeeTemplate.setDescription(titleMap.get("description"));
            }
            //中文标题
            if (StringUtils.isNotBlank(productInfo.getTitleCn())) {
                shopeeTemplate.setChineseName(productInfo.getTitleCn());
            }
            // json中标题和描述都有了就跳出
            if (StringUtils.isNotBlank(shopeeTemplate.getName()) && StringUtils.isNotBlank(shopeeTemplate.getDescription()) && StringUtils.isNotBlank(shopeeTemplate.getChineseName())) {
                break;
            }
            //产品系统中旧的数据可能没有中文描述（titleCn），这时候要用产品名称(name)来代替
            if (StringUtils.isBlank(shopeeTemplate.getChineseName())) {
                shopeeTemplate.setChineseName(skuList.get(0).getName());
            }
        }

        // 重量在最重sku的基础上加10g
        shopeeTemplate.setWeight(NumberUtils.format((shopeeTemplate.getSkus().get(0).getShippingWeight() + 0.01), "0.###"));

        return ApiResult.of(true, shopeeTemplate, "sku信息集合！");
    }

    /**
     * 过滤非法状态 停产 存档 废弃
     *
     * @param shopeeSkus
     * @param skuList
     * @return
     */
    public static List<ShopeeSku> filterIllegalStatus(SaleAccountAndBusinessResponse response, List<ShopeeSku> shopeeSkus, List<ProductInfo> skuList) {
        if (CollectionUtils.isEmpty(shopeeSkus) || CollectionUtils.isEmpty(skuList)) {
            return shopeeSkus;
        }
        Boolean isNnShop = Optional.ofNullable(response).map(a -> a.getShopeeColBool3()).orElse(false);

        Map<String, String> sonSkuStatusMap = skuList.stream().collect(Collectors.toMap(ProductInfo::getSonSku, ProductInfo::getItemStatus));
        Map<String, List<Integer>> sonSkuAndSpecialTypeMap = skuList.stream().filter(a -> CollectionUtils.isNotEmpty(a.getSpecialTypeList())).collect(Collectors.toMap(ProductInfo::getSonSku, ProductInfo::getSpecialTypeList));
        if (MapUtils.isEmpty(sonSkuStatusMap)) {
            return shopeeSkus;
        }

        Iterator<ShopeeSku> it = shopeeSkus.iterator();
        while (it.hasNext()) {
            ShopeeSku shopeeSku = it.next();
            String sonSku = shopeeSku.getSku();
            if (StringUtils.isBlank(sonSku)) {
                continue;
            }

            if (filterStatus(isNnShop, sonSkuStatusMap.get(sonSku), sonSkuAndSpecialTypeMap.get(sonSku))) {
                it.remove();
            }
        }

        return shopeeSkus;
    }


    /**
     * 子SKU 过滤禁售
     *
     * @param shopeeSkus
     * @param skuList
     * @param filterSites
     * @return
     */
    public static List<ShopeeSku> filterForbidden(List<ShopeeSku> shopeeSkus, List<ProductInfo> skuList, List<String> filterSites) {
        if (CollectionUtils.isEmpty(shopeeSkus) || CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(filterSites)) {
            return shopeeSkus;
        }

        Map<String, List<SalesProhibitionsVo>> sonSkuProhibitionsVoMap = skuList.stream()
                .collect(Collectors.toMap(o -> o.getSonSku(), o -> CollectionUtils.isEmpty(o.getSalesProhibitionsVos()) ? new ArrayList<>() : o.getSalesProhibitionsVos()));
        if (MapUtils.isEmpty(sonSkuProhibitionsVoMap)) {
            return shopeeSkus;
        }

        Iterator<ShopeeSku> it = shopeeSkus.iterator();
        while (it.hasNext()) {
            ShopeeSku shopeeSku = it.next();
            String sonSku = shopeeSku.getSku();
            if (StringUtils.isBlank(sonSku)) {
                continue;
            }

            List<SalesProhibitionsVo> salesProhibitionsVos = sonSkuProhibitionsVoMap.get(sonSku);
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                continue;
            }

            for (SalesProhibitionsVo salesProhibitionsVo : salesProhibitionsVos) {
                String plat = salesProhibitionsVo.getPlat();
                if (!SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(plat)) {
                    continue;
                }

                List<Sites> siteslist = salesProhibitionsVo.getSites();
                List<String> sites = siteslist.stream().map(Sites::getSite).collect(Collectors.toList());
                if (sites.containsAll(filterSites)) {
                    it.remove();
                    break;
                }
            }
        }

        return shopeeSkus;
    }

    /**
     * 子SKU 过滤侵权
     *
     * @param shopeeSkus
     * @param filterSites
     * @return
     */
    public static List<ShopeeSku> filterInfringement(List<ShopeeSku> shopeeSkus, List<String> filterSites) {
        if (CollectionUtils.isEmpty(shopeeSkus) || CollectionUtils.isEmpty(filterSites)) {
            return shopeeSkus;
        }

        List<String> sonSkus = shopeeSkus.stream().map(ShopeeSku::getSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonSkus)) {
            return shopeeSkus;
        }

        Map<String, SalesInfringementVo> infringementSkuMap;
        try {
            infringementSkuMap = ProductInfringementForbiddenSaleUtils.getInfringementProSaleChannelBySku(sonSkus);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException("查询ES禁售信息失败！" + e.getMessage());
        }
        if (null == infringementSkuMap) {
            infringementSkuMap = new HashMap<>();
        }
        Iterator<ShopeeSku> it = shopeeSkus.iterator();
        while (it.hasNext()) {
            ShopeeSku shopeeSku = it.next();
            String sonSku = shopeeSku.getSku();
            if (StringUtils.isBlank(sonSku)) {
                continue;
            }

            SalesInfringementVo salesInfringementVo = infringementSkuMap.get(sonSku);
            if (null == salesInfringementVo) {
                continue;
            }

            Map<String, List<String>> platSiteMap = salesInfringementVo.getPlatSiteMap();
            if (MapUtils.isEmpty(platSiteMap)) {
                continue;
            }

            List<String> sites = platSiteMap.get(SaleChannel.CHANNEL_SHOPEE);
            if (CollectionUtils.isNotEmpty(sites) && sites.containsAll(filterSites)) {
                it.remove();
            }
        }

        return shopeeSkus;
    }

    /**
     * 是否存在禁售
     *
     * @param sonSkus
     * @param filterSite
     * @return
     */
    public static Boolean existForbidden(List<String> sonSkus, List<ProductInfo> skuList, String filterSite) {
        if (CollectionUtils.isEmpty(sonSkus) || CollectionUtils.isEmpty(skuList) || StringUtils.isBlank(filterSite)) {
            return false;
        }

        Map<String, List<SalesProhibitionsVo>> sonSkuProhibitionsVoMap = skuList.stream()
                .collect(Collectors.toMap(o -> o.getSonSku(), o -> CollectionUtils.isEmpty(o.getSalesProhibitionsVos()) ? new ArrayList<>() : o.getSalesProhibitionsVos()));
        if (MapUtils.isEmpty(sonSkuProhibitionsVoMap)) {
            return false;
        }

        for (String sonSku : sonSkus) {
            if (StringUtils.isBlank(sonSku)) {
                continue;
            }

            List<SalesProhibitionsVo> salesProhibitionsVos = sonSkuProhibitionsVoMap.get(sonSku);
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                continue;
            }

            for (SalesProhibitionsVo salesProhibitionsVo : salesProhibitionsVos) {
                String plat = salesProhibitionsVo.getPlat();
                if (!SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(plat)) {
                    continue;
                }

                List<Sites> siteslist = salesProhibitionsVo.getSites();
                List<String> sites = siteslist.stream().map(Sites::getSite).collect(Collectors.toList());
                if (sites.contains(filterSite)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 是否存在侵权
     *
     * @param sonSkus
     * @param filterSite
     * @return
     * @throws Exception
     */
    public static Boolean existInfringement(List<String> sonSkus, String filterSite) throws Exception {
        if (CollectionUtils.isEmpty(sonSkus) || StringUtils.isBlank(filterSite)) {
            return false;
        }

        Map<String, String> skuSiteMap = new HashMap<>();
        sonSkus.forEach(sku -> {
            skuSiteMap.put(sku, filterSite);
        });

        Map<String, Boolean> infringementSkuMap;
        try {
            infringementSkuMap = ProductInfringementForbiddenSaleUtils.checkSkuInfringementBySite(SaleChannel.CHANNEL_SHOPEE, skuSiteMap);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception("查询ES侵权信息失败！" + e.getMessage());
        }

        for (String sonSku : sonSkus) {
            if (MapUtils.isNotEmpty(infringementSkuMap) && BooleanUtils.isTrue(infringementSkuMap.get(sonSku))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 自动刊登数据过滤 不合法（停产存档废弃）状态
     *
     * @param spuToCodeMap
     * @return
     */
    public static Map<String, SkuListAndCode> filterItemStatus(boolean isNnShop, Map<String, SkuListAndCode> spuToCodeMap) {
        Map<String, SkuListAndCode> checkStatusMap = new HashMap<>();
        if (MapUtils.isEmpty(spuToCodeMap)) {
            return checkStatusMap;
        }

        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();
            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if (null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkStatusMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()) {
                SonSkuFewInfo sonSkuFewInfo = it.next();
                String itemStatus = sonSkuFewInfo.getItemStatus();
                if (filterStatus(isNnShop, itemStatus, sonSkuFewInfo.getSpecialTypeList())) {
                    it.remove();
                    continue;
                }
            }

            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                //log.warn(String.format("自动刊登 spu %s 所有子SKU 停产，存档，废弃不允许刊登", spu));
            } else {
                checkStatusMap.put(spu, skuListAndCode);
            }
        }
        return checkStatusMap;
    }

    /**
     * 自动刊登数据过滤禁售
     *
     * @param spuToCodeMap
     * @param filterSites
     * @return
     */
    public static Map<String, SkuListAndCode> filterForbidden(Map<String, SkuListAndCode> spuToCodeMap, List<String> filterSites) {
        Map<String, SkuListAndCode> checkForbiddenMap = new HashMap<>();
        if (MapUtils.isEmpty(spuToCodeMap)) {
            return checkForbiddenMap;
        }
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();

            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if (null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkForbiddenMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()) {

                SonSkuFewInfo sonSkuFewInfo = it.next();
                List<SalesProhibitionsVo> salesProhibitionsVos = sonSkuFewInfo.getSalesProhibitionsVos();
                if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                    continue;
                }

                for (SalesProhibitionsVo salesProhibitionsVo : salesProhibitionsVos) {
                    String plat = salesProhibitionsVo.getPlat();
                    if (!SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(plat)) {
                        continue;
                    }

                    List<Sites> siteslist = salesProhibitionsVo.getSites();
                    List<String> sites = siteslist.stream().map(Sites::getSite).collect(Collectors.toList());
                    if (sites.containsAll(filterSites)) {
                        it.remove();
                        break;
                    }
                }
            }

            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                //log.warn(String.format("自动刊登 spu %s 平台禁售，不允许刊登", spu));
            } else {
                checkForbiddenMap.put(spu, skuListAndCode);
            }
        }

        return checkForbiddenMap;
    }

    /**
     * 自动刊登数据过滤侵权
     *
     * @param spuToCodeMap
     * @param filterSites
     * @return
     */
    public static Map<String, SkuListAndCode> filterInfringement(Map<String, SkuListAndCode> spuToCodeMap, List<String> filterSites) {
        Map<String, SkuListAndCode> checkInfringementMap = new HashMap<>();
        if (MapUtils.isEmpty(spuToCodeMap)) {
            return checkInfringementMap;
        }

        Set<String> spusSets = spuToCodeMap.keySet();
        if (CollectionUtils.isEmpty(spusSets)) {
            return checkInfringementMap;
        }

        Map<String, SalesInfringementVo> infringementSkuMap = null;
        try {
            infringementSkuMap = ProductInfringementForbiddenSaleUtils.getInfringementProSaleChannelBySku(new ArrayList<>(spusSets));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if (null == infringementSkuMap) {
            log.error("filterInfringement infringementSkuMap is null");
            infringementSkuMap = new HashMap<>();
        }
        for (Map.Entry<String, SkuListAndCode> skuListAndCodeEntry : spuToCodeMap.entrySet()) {
            String spu = skuListAndCodeEntry.getKey();
            SkuListAndCode skuListAndCode = skuListAndCodeEntry.getValue();
            if (null == skuListAndCode || StringUtils.isBlank(skuListAndCode.getCode())) {
                continue;
            }

            // 存在分类不存在扩展信息不过滤 后续刊登还需过滤
            List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();
            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                checkInfringementMap.put(spu, skuListAndCode);
                continue;
            }

            Iterator<SonSkuFewInfo> it = sonSkuFewInfos.iterator();
            while (it.hasNext()) {
                SonSkuFewInfo sonSkuFewInfo = it.next();
                String sonSku = sonSkuFewInfo.getSonSku();
                if (StringUtils.isBlank(sonSku)) {
                    continue;
                }

                SalesInfringementVo salesInfringementVo = infringementSkuMap.get(sonSku);
                if (null == salesInfringementVo) {
                    continue;
                }

                Map<String, List<String>> platSiteMap = salesInfringementVo.getPlatSiteMap();
                if (MapUtils.isEmpty(platSiteMap)) {
                    continue;
                }

                List<String> sites = platSiteMap.get(SaleChannel.CHANNEL_SHOPEE);
                if (CollectionUtils.isNotEmpty(sites) && sites.containsAll(filterSites)) {
                    it.remove();
                }
            }

            if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
                //log.warn(String.format("自动刊登 spu %s 平台侵权，不允许刊登", spu));
            } else {
                checkInfringementMap.put(spu, skuListAndCode);
            }
        }
        return checkInfringementMap;
    }


    /**
     * 过滤不可刊登sku (主sku直接拦截 子sku过滤 过滤后为空则拦截)
     *
     * @param shopeeSkuList
     * @param nonPublishableSkuList
     * @return
     */
    public static List<ShopeeSku> filterNonPublishableSku(List<ShopeeSku> shopeeSkuList, String templateSku, List<String> nonPublishableSkuList) throws Exception {
        if (CollectionUtils.isEmpty(nonPublishableSkuList)) {
            return shopeeSkuList;
        }

        // 主sku 为不可刊登sku 直接报错
        for (String nonPublishableSku : nonPublishableSkuList) {
            if (StringUtils.equalsIgnoreCase(nonPublishableSku, templateSku)) {
                throw new Exception("产品为店铺配置不可以刊登SKU " + templateSku);
            }
        }

        if (CollectionUtils.isEmpty(shopeeSkuList)) {
            return shopeeSkuList;
        }

        List<String> skuList = shopeeSkuList.stream().map(ShopeeSku::getSku).collect(Collectors.toList());
        Iterator<ShopeeSku> it = shopeeSkuList.iterator();
        while (it.hasNext()) {
            ShopeeSku shopeeSku = it.next();
            String sonSku = shopeeSku.getSku();
            if (StringUtils.isBlank(sonSku)) {
                continue;
            }
            for (String nonPublishableSku : nonPublishableSkuList) {
                if (StringUtils.equalsIgnoreCase(nonPublishableSku, sonSku)) {
                    it.remove();
                    break;
                }
            }
        }
        if (CollectionUtils.isEmpty(shopeeSkuList)) {
            throw new Exception("所有子sku为店铺配置不可以刊登SKU " + JSON.toJSONString(skuList));
        }
        return shopeeSkuList;
    }

    /**
     * 按一定规则获取标题和描述
     *
     * @param fatherSku:父SKU
     * @param copyWritingType 文案类型
     * @return
     */
    public static ApiResult<?> getTileAndDescription(String fatherSku, String defaultTitle, Integer copyWritingType) {
        List<String> spuList = new ArrayList<>(1);
        spuList.add(fatherSku);
        //根据主spu获取spu标题
        ResponseJson resp = ProductUtils.getSpuTitles(spuList);
        if (resp != null && resp.isSuccess()) {
            List<SpuOfficial> spuOfficialList = (List<SpuOfficial>) resp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isNotEmpty(spuOfficialList)) {
                SpuOfficial spuOfficial = spuOfficialList.get(0);
                Map<String, String> titleMap = spuOfficialTitleAndDescResolver(spuOfficial, defaultTitle, copyWritingType);
                return ApiResult.newSuccess(titleMap);
            } else {
                return ApiResult.newError("查询产品数据为空");
            }
        } else {
            return ApiResult.newError("调用获取spu标题接口失败！");
        }
    }

    public static SpuInfo getSpuInfo(String spu) {
        ResponseJson spuResponse = ProductUtils.findSpuInfo(spu);
        if (!spuResponse.isSuccess()) {
            return null;
        }
        List<SpuInfo> spuInfoList = (List<SpuInfo>) spuResponse.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(spuInfoList)) {
            return null;
        }
        return spuInfoList.get(0);
    }

    /**
     * 根据文案信息获取标题描述
     */
    public static Map<String, String> spuOfficialTitleAndDescResolver(SpuOfficial spuOfficial, String defaultTitle, Integer copyWritingType) {
        if (copyWritingType == null) {
            copyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
        }
        spuOfficial.setNeedWenAnType(copyWritingType);

        Map<String, String> titleMap = new HashMap<>();
        String title = "";
        String description = "";
        Integer resCopyWritingType;
        //如果长标题有数据
        if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
            //去除可能存在的空值（，“”）
            JSONArray longJson = JSON.parseArray(spuOfficial.getLongTitleJson().replaceAll(",\"\"", ""));
            if (CollectionUtils.isNotEmpty(longJson)) {
                //随机获取其中的某个标题
                title = longJson.get(RandomUtils.nextInt(0, longJson.size())).toString();
            }

        }
        //如果长标题没取到，就取短标题
        if (StringUtils.isBlank(title)) {
            if (StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
                //去除可能存在的空值（，“”）
                JSONArray skuJson = JSON.parseArray(spuOfficial.getShortTitleJson().replaceAll(",\"\"", ""));
                if (CollectionUtils.isNotEmpty(skuJson)) {
                    //随机获取其中的某个标题
                    title = skuJson.get(RandomUtils.nextInt(0, skuJson.size())).toString();
                }
            }
        }
        //如果短标题没取到，就取sku标题
        if (StringUtils.isBlank(title)) {
            if (StringUtils.isNotBlank(spuOfficial.getTitle())) {
                //去除可能存在的空值（，“”）
                JSONArray skuJson = JSON.parseArray(spuOfficial.getTitle().replaceAll(",\"\"", ""));
                if (null != skuJson && skuJson.size() > 0) {
                    //随机获取其中的某个标题
                    title = skuJson.get(RandomUtils.nextInt(0, skuJson.size())).toString();
                }
            }
        }
        //如果都没取到，就取默认标题
        if (StringUtils.isBlank(title)) {
            title = defaultTitle;
        }
                /* 描述取值优先级：
                    1-SKU描述新，在描述最前面插入标题。
                    2-SKU描述，在描述最前面插入标题
                */
        String newDescription = spuOfficial.getNewDescription();
        if (StringUtils.isNotBlank(newDescription)) {
            description = title + "\n\n" + newDescription;
        }
        if (StringUtils.isBlank(description)) {
            String desc = StrUtil.objectToStr(spuOfficial.getDescription());
            List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {
            });
            if (CollectionUtils.isNotEmpty(list)) {
                for (String s : list) {
                    if (StringUtils.isNotBlank(s)) {
                        description = title + "\n\n" + s;
                        break;
                    }
                }
            }
        }
        resCopyWritingType = spuOfficial.getNeedWenAnType();
        if (spuOfficial.getNeedWenAnType() == WenAnTypeEnum.unlimitedWenAn.getCode() && !spuOfficial.isHasNoLimitWenAn()) {
            resCopyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
        }
        titleMap.put("title", title);
        titleMap.put("description", description);
        titleMap.put("copyWritingType", resCopyWritingType.toString());
        return titleMap;
    }


    public static List<StockKeepingUnitWithBLOBs> convertSkuInfo(List<ProductInfo> skuList, List<String> publicImages) {
        List<StockKeepingUnitWithBLOBs> list = new ArrayList<>(skuList.size());
        for (ProductInfo info : skuList) {
            StockKeepingUnitWithBLOBs bean = new StockKeepingUnitWithBLOBs();
            //主sku
            bean.setProductarticlenumber(info.getMainSku());
            //子sku
            bean.setArticleNumber(info.getSonSku());
            //单品状态
            bean.setSkulifecyclephase(info.getItemStatus());
            //name
            bean.setName(info.getName());
            //标签
            bean.setSkutagcode(info.getTag());
            //自身净重
            bean.setWeight(info.getProductWeight());
            //包材重量
            Double packingWeight = info.getPackingWeight() == null ? 0.0 : info.getPackingWeight().doubleValue();
            bean.setPackingMaterialWeight(packingWeight);
            //搭配包材重量
            Double matchWeight = info.getMatchWeight() == null ? 0.0 : info.getMatchWeight().doubleValue();
            bean.setCollocationPackingMaterialWeight(matchWeight);
            // 设置sku主图
            ShopeeImageUtil.setSkuImage(info.getSonSku(), publicImages, bean::setImage, () -> {
                // publicImages为空的情况，由这里来提供图片
                String firstImage = info.getFirstImage();
                List<String> elseSupplierList = new ArrayList<>();
                if (StringUtils.isNotBlank(firstImage)) {
                    elseSupplierList.add(firstImage);
                } else {
                    String firstImageAdd = info.getFirstImageAdd();
                    if (StringUtils.isNotBlank(firstImageAdd)) {
                        elseSupplierList.add(firstImageAdd);
                    }
                    List<String> firstImageList = JSONArray.parseArray(info.getFirstImageAdd(), String.class);
                    if (CollectionUtils.isNotEmpty(firstImageList)) {
                        elseSupplierList.addAll(firstImageList);
                    }
                }
                return elseSupplierList;
            });

            list.add(bean);
        }
        return list;
    }

    public static List<ShopeeSku> getShopeeChildSkus(List<StockKeepingUnitWithBLOBs> skus, List<ProductInfo> skuInfoList) {
        // 有主货号字段的, 解析color size属性
        //SkuTheme parse = ShopeeCommonUtils.parseWithBLOBs(skus);

        List<ShopeeSku> shopeeSkus = new ArrayList<ShopeeSku>();
        for (StockKeepingUnitWithBLOBs sku : skus) {
            ShopeeSku shopeeSku = new ShopeeSku();
            String articleNumber = sku.getArticleNumber();
            shopeeSku.setSku(articleNumber);
            shopeeSku.setProductStatus(sku.getSkulifecyclephase());

            for (ProductInfo productInfo : skuInfoList) {
                if (productInfo.getSonSku().equalsIgnoreCase(articleNumber)) {
                    try {
                        List<ProductSaleAtts> saleAtts = productInfo.parseSaleAtts();
                        if (CollectionUtils.isNotEmpty(saleAtts)) {
                            for (ProductSaleAtts saleAtt : saleAtts) {
                                if (saleAtt.getEnName().equalsIgnoreCase("color")) {
                                    shopeeSku.setColor(StringUtils.substring(saleAtt.getEnValue(), 0, 30));
                                }
                                if (saleAtt.getEnName().equalsIgnoreCase("size")) {
                                    shopeeSku.setSize(StringUtils.substring(saleAtt.getEnValue(), 0, 30));
                                }
                                if (saleAtt.getCnName().equalsIgnoreCase("颜色")) {
                                    shopeeSku.setChineseColor(saleAtt.getCnValue());
                                }
                                if (saleAtt.getCnName().equalsIgnoreCase("尺寸")) {
                                    shopeeSku.setChineseSize(saleAtt.getCnValue());
                                }
                            }
                        }

                    } catch (Exception e) {
                        //因为产品那边有可能属性的json格式错了，把错的数据打印出来
                        System.out.println("==============" + productInfo.getSaleAtts() + "属性JSON解析错误=================");
                    }
                    break;
                }
            }

            // 带"-"的货号
//            /*if (articleNumber.indexOf("-") > -1 && articleNumber.split("-").length > 1) {
//                String attr = articleNumber.substring(articleNumber.lastIndexOf("-") + 1);
//                if (null != parse) {
//                    Map<String, List<NameValue>> variants = parse.getVariants();
//                    // 查找颜色
//                    List<NameValue> list = variants.get(attr);
//                    for (NameValue nameValue : list) {
//                        if ("Color".equalsIgnoreCase(nameValue.getName())) {
//                            // color属性
//                            shopeeSku.setColor(nameValue.getValue());
//                            Color color = Color.getColor(nameValue.getValue());
//                            // 在枚举中能找到全名就写全名
//                            if (null != color) {
//                                shopeeSku.setColor(color.getName());
//                            }
//                        }
//                        if ("Size".equalsIgnoreCase(nameValue.getName())) {
//                            // size属性
//                            shopeeSku.setSize(nameValue.getValue());
//                            Size size = Size.getSize(nameValue.getValue());
//                            // 在枚举中能找到全名就写全名
//                            if (null != size) {
//                                shopeeSku.setSize(size.getName());
//                            }
//                        }
//                    }
//                }
//            }
//            // 中文属性
//            String chineseColor = shopeeSku.getChineseColor();
//            if (StringUtils.isBlank(chineseColor)) {
//                String name = sku.getName() == null ? "" : sku.getName();
//                if (name.indexOf("——") != -1) {
//                    String color = name.substring(name.indexOf("——") + 1);
//                    shopeeSku.setChineseColor(color);
//                }
//                else if (name.indexOf("-") != -1) {
//                    String color = name.substring(name.indexOf("-") + 1);
//                    shopeeSku.setChineseColor(color);
//                }
//            }*/

            // 仅查看属性, 状态 标签
            String attribute = "状态：" + sku.getSkulifecyclephase() + "<br/> 标签："
                    + (StringUtils.isNotBlank(sku.getSkutagcode()) ? sku.getSkutagcode() : "无");
            shopeeSku.setAttribute(attribute);
            // 重量单位kg, 重量加10g, 加快递包装3g, 在母板的weight中加
            shopeeSku.setShippingWeight(calcSkuWeight(sku));
            shopeeSku.setImage(sku.getImage());
            //默认库存999
            if (shopeeSku.getQuantity() == null) {
                shopeeSku.setQuantity(999);
            }

            //设置中文繁体颜色
            if (StringUtils.isNotBlank(shopeeSku.getColor())) {
                String cnColor = ShopeeColorEnum.mappingCNColor(shopeeSku.getColor());
                if (StringUtils.isNotBlank(cnColor)) {
                    shopeeSku.setChineseColor(cnColor);
                }
            }
            shopeeSkus.add(shopeeSku);
        }
        return shopeeSkus;
    }

    //计算重量（自身净重+包材重量+搭配包材重量+面单重量3g）,注意Shopee的重量是以kg做单位并且保留三位小数
    public static Double calcSkuWeight(StockKeepingUnitWithBLOBs sku) {
        //自身净重
        double skuWeight = sku.getWeight() == null ? 0.0 : sku.getWeight();
        //包材重量
        double packingMaterialWeight = sku.getPackingMaterialWeight() == null ? 0.0 : sku.getPackingMaterialWeight();
        //搭配包材重量
        double collocationPackingMaterialWeight = sku.getCollocationPackingMaterialWeight() == null ? 0.0 : sku.getCollocationPackingMaterialWeight();

        return NumberUtils.format((skuWeight + packingMaterialWeight + collocationPackingMaterialWeight + 3) / 1000, "0.###");
    }


    /**
     * 根据sku，国家 查询刊登成功模板的color size
     *
     * @param sku
     * @param site
     * @return
     */
    public static ShopeeTemplateNewBo matchSkuColorSizeBySkuSite(String sku, String site) {
        ShopeeTemplateNewBo bo = new ShopeeTemplateNewBo();
        String orderBy = "id desc limit 1";
        ShopeeTemplateNewExample ex = new ShopeeTemplateNewExample();
        ex.setOrderByClause(orderBy);
        ex.createCriteria()
                .andSkuEqualTo(sku)
                .andSiteEqualTo(site)
                .andIsParentEqualTo(false)
                .andPublishStatusEqualTo(ShopeeNewPublishStatusEnum.SUCCESS.getCode());
        ex.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
        List<ShopeeTemplateNew> list = shopeeTemplateNewService.selectByExample(ex);
        if (CollectionUtils.isNotEmpty(list)) {
            ShopeeTemplateNew tp = list.get(0);
            List<ShopeeSku> shopeeSkus = JSON.parseObject(tp.getShopeeSkusStr(), new TypeReference<List<ShopeeSku>>() {
            });
            bo.setId(tp.getId());
            bo.setShopeeSkus(shopeeSkus);
        } else {
            //查询其他站点的模板
            ex.clear();
            ex.setOrderByClause(orderBy);
            ShopeeTemplateNewExample.Criteria criteria = ex.createCriteria();
            criteria.andSkuEqualTo(sku)
                    .andIsParentEqualTo(false)
                    .andPublishStatusEqualTo(ShopeeNewPublishStatusEnum.SUCCESS.getCode());
            if (!ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(site)) {
                //非台湾的，排除台湾
                criteria.andSiteNotEqualTo(ShopeeCountryEnum.Taiwan.getCode());
            }
            ex.setTable(ShopeeTemplateTableEnum.SHOPEE_TEMPLATE_NEW.getCode());
            list = shopeeTemplateNewService.selectByExample(ex);
            if (CollectionUtils.isNotEmpty(list)) {
                ShopeeTemplateNew tp = list.get(0);
                List<ShopeeSku> shopeeSkus = JSON.parseObject(tp.getShopeeSkusStr(), new TypeReference<List<ShopeeSku>>() {
                });
                bo.setId(tp.getId());
                bo.setShopeeSkus(shopeeSkus);
                //如果是台湾要翻译
                if (ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(site)) {
                    for (ShopeeSku shopeeSku : shopeeSkus) {
                        //设置中文繁体颜色
                        if (StringUtils.isNotBlank(shopeeSku.getColor())) {
                            String cnColor = ShopeeColorEnum.mappingCNColor(shopeeSku.getColor());
                            if (StringUtils.isNotBlank(cnColor)) {
                                shopeeSku.setColor(StringUtils.substring(cnColor, 0, 30));
                            }
                        }
                    }

//                    long count = shopeeSkus.stream().filter(o -> StringUtils.isNotBlank(o.getColor())).count();
//                    if(count > 0){
//                        List<String> csList = new ArrayList<>(shopeeSkus.size());
//                        for (ShopeeSku o : shopeeSkus) {
//                            if(StringUtils.isBlank(o.getColor())){
//                                csList.add("");
//                            }else{
//                                csList.add(o.getColor());
//                            }
//                        }
//                        String result = GoogleTranslateUtils.googleTranslateText(csList, "en", GoogleTranslateUtils.changeDestLang(site));
//                        if(StringUtils.isNotBlank(result)){
//                            try {
//                                List<String> transList = JSON.parseArray(result, String.class);
//                                for (int i = 0; i < shopeeSkus.size(); i++) {
//                                    ShopeeSku shopeeSku = shopeeSkus.get(i);
//                                    String color = transList.get(i);
//                                    if(StringUtils.isNotBlank(color)){
//                                        shopeeSku.setColor(color);
//                                    }
//                                }
//                            }catch (Exception e){
//                                log.error(String.format("sku：%s,翻译解析出错：", sku), e);
//                            }
//                        }
//                    }
                }
            } else {
                //查询产品系统
//                List<ProductInfo> skuInfoList = ProductServiceUtils.findSkuInfos(Arrays.asList(sku));
//                if (CollectionUtils.isNotEmpty(skuInfoList)) {
//                    List<StockKeepingUnitWithBLOBs> skuList = ShopeeProductInfoUtil.convertSkuInfo(skuInfoList);
//                    //获取子属性sku
//                    List<ShopeeSku> shopeeSkus = ShopeeProductInfoUtil.getShopeeChildSkus(skuList,skuInfoList);
//                    if(ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(site)){
//                        for (ShopeeSku o : shopeeSkus) {
//                            o.setSize(o.getChineseSize());
//                            o.setColor(o.getChineseColor());
//                        }
//                    }
//                    bo.setShopeeSkus(shopeeSkus);
//                }
            }
        }

        return bo;
    }

    /**
     * 匹配颜色尺寸
     *
     * @param shopeeSkus
     * @param bo
     */
    public static void matchSkuColorSize(List<ShopeeSku> shopeeSkus, ShopeeTemplateNewBo bo) {
        if (CollectionUtils.isNotEmpty(bo.getShopeeSkus())) {
            for (ShopeeSku matchSku : bo.getShopeeSkus()) {
                for (ShopeeSku shopeeSku : shopeeSkus) {
                    if (matchSku.getSku().equalsIgnoreCase(shopeeSku.getSku())) {
//                        if(StringUtils.isNotBlank(matchSku.getColor())){
                        shopeeSku.setColor(StringUtils.substring(matchSku.getColor(), 0, 30));
//                        }
//                        if(StringUtils.isNotBlank(matchSku.getSize())){
                        shopeeSku.setSize(StringUtils.substring(matchSku.getSize(), 0, 30));
//                        }
                        break;
                    }
                }
            }
        }
    }

    public static List<ShopeeSku> generateShopeeSkus(List<ProductInfo> skuList, List<String> publicImages, Double accountProfit, String site) {
        double priceConfig = ShopeeSystemParamUtil.getGlobalProductPriceConfig();
        double priceUpgradeRatio;
        if (null == accountProfit) {
            priceUpgradeRatio = ShopeeSystemParamUtil.getGlobalProductSalePriceUpgradeRatio();
        } else {
            priceUpgradeRatio = accountProfit;
        }

        BigDecimal sellerPaysFreight = null;
        List<ShopeeSku> shopeeSkus = new ArrayList<>();

        for (ProductInfo info : skuList) {
            ShopeeSku shopeeSku = new ShopeeSku();
            String articleNumber = info.getSonSku();
            shopeeSku.setSku(articleNumber);
            shopeeSku.setProductStatus(info.getItemStatus());
            shopeeSku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
            try {
                List<ProductSaleAtts> saleAtts = info.parseSaleAtts();
                if (CollectionUtils.isNotEmpty(saleAtts)) {
                    for (ProductSaleAtts saleAtt : saleAtts) {
                        if (saleAtt.getEnName().equalsIgnoreCase("color")) {
                            shopeeSku.setColor(StringUtils.substring(saleAtt.getEnValue(), 0, 30));
                        }
                        if (saleAtt.getEnName().equalsIgnoreCase("size")) {
                            shopeeSku.setSize(StringUtils.substring(saleAtt.getEnValue(), 0, 30));
                        }
                        if (saleAtt.getCnName().equalsIgnoreCase("颜色")) {
                            shopeeSku.setChineseColor(saleAtt.getCnValue());
                        }
                        if (saleAtt.getCnName().equalsIgnoreCase("尺寸")) {
                            shopeeSku.setChineseSize(saleAtt.getCnValue());
                        }
                    }
                }

            } catch (Exception e) {
                //因为产品那边有可能属性的json格式错了，把错的数据打印出来
                System.out.println("==============" + info.getSaleAtts() + "属性JSON解析错误=================");
            }

            // 仅查看属性, 状态 标签
            String attribute = "状态：" + info.getItemStatus() + "<br/> 标签：" + (StringUtils.isNotBlank(info.getTag()) ? info.getTag() : "无");
            shopeeSku.setAttribute(attribute);


            StockKeepingUnitWithBLOBs bean = new StockKeepingUnitWithBLOBs();
            //自身净重
            bean.setWeight(info.getProductWeight());
            //包材重量
            Double packingWeight = info.getPackingWeight() == null ? 0.0 : info.getPackingWeight().doubleValue();
            bean.setPackingMaterialWeight(packingWeight);
            //搭配包材重量
            Double matchWeight = info.getMatchWeight() == null ? 0.0 : info.getMatchWeight().doubleValue();
            bean.setCollocationPackingMaterialWeight(matchWeight);
            //产品系统会返回子sku主图
            ShopeeImageUtil.setSkuImage(info.getSonSku(), publicImages, bean::setImage, () -> {
                // publicImages为空的情况，由这里来提供图片
                String firstImage = info.getFirstImage();
                List<String> elseSupplierList = new ArrayList<>();
                if (StringUtils.isNotBlank(firstImage)) {
                    elseSupplierList.add(firstImage);
                } else {
                    String firstImageAdd = info.getFirstImageAdd();
                    if (StringUtils.isNotBlank(firstImageAdd)) {
                        elseSupplierList.add(firstImageAdd);
                    }
                    List<String> firstImageList = JSONArray.parseArray(info.getFirstImageAdd(), String.class);
                    if (CollectionUtils.isNotEmpty(firstImageList)) {
                        elseSupplierList.addAll(firstImageList);
                    }
                }
                return elseSupplierList;
            });

            // 重量单位kg, 重量加10g, 加快递包装3g, 在母板的weight中加
            shopeeSku.setShippingWeight(calcSkuWeight(bean));
            shopeeSku.setImage(bean.getImage());

            //默认库存9999
            if (shopeeSku.getQuantity() == null) {
                shopeeSku.setQuantity(9999);
            }

            // 全球商品价格
            Double price = calcPriceForGlobal(info, priceConfig, priceUpgradeRatio);
            if (sellerPaysFreight == null) {
                sellerPaysFreight = getSellerPaysFreight(skuList, site);
            }
            BigDecimal bigDecimalPrice = price == null ? BigDecimal.ZERO : BigDecimal.valueOf(price);
            Double priceAll = bigDecimalPrice.add(sellerPaysFreight).doubleValue();
            shopeeSku.setPrice(NumberUtils.round(priceAll, 2));

            shopeeSkus.add(shopeeSku);
        }

        return shopeeSkus;
    }

    public static List<ShopeeGlobalPriceDo> getPrice(String accountNumber, List<ProductInfo> skuList, Double accountProfit, String site) {
        double priceConfig = ShopeeSystemParamUtil.getGlobalProductPriceConfig();
        double priceUpgradeRatio;
        List<ShopeeGlobalPriceDo> list = new ArrayList<>();
        boolean isNnShop = false;
        if (StringUtils.isNotBlank(accountNumber)) {
            SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, accountNumber);
            isNnShop = Optional.ofNullable(saleAccountByAccountNumber).map(SaleAccountAndBusinessResponse::getShopeeColBool3).orElse(false);
        }

        if (null == accountProfit) {
            priceUpgradeRatio = ShopeeSystemParamUtil.getGlobalProductSalePriceUpgradeRatio();
        } else {
            priceUpgradeRatio = accountProfit;
        }
        ShopeeGlobalPriceDo sellerPaysFreightPro = null;
        for (ProductInfo info : skuList) {
            // 全球商品价格
            ShopeeGlobalPriceDo shopeeGlobalPriceDo = calcPriceForGlobalPro(info, priceConfig, priceUpgradeRatio);
            if (sellerPaysFreightPro == null) {
                sellerPaysFreightPro = getSellerPaysFreightPro(isNnShop, skuList, site);
            }

            BigDecimal productWeight = info.getProductWeight() == null ? BigDecimal.ZERO : BigDecimal.valueOf(info.getProductWeight());
            BigDecimal packingWeight = info.getPackingWeight() == null ? BigDecimal.ZERO : info.getPackingWeight();
            BigDecimal matchWeight = info.getMatchWeight() == null ? BigDecimal.ZERO : info.getMatchWeight();

            shopeeGlobalPriceDo.setFilter(filterStatus(isNnShop, info.getItemStatus(), info.getSpecialTypeList()));
            shopeeGlobalPriceDo.setProductWeight(productWeight);
            shopeeGlobalPriceDo.setPackingWeight(packingWeight);
            shopeeGlobalPriceDo.setMatchWeight(matchWeight);

            shopeeGlobalPriceDo.setSellerPaysFreight(sellerPaysFreightPro.getSellerPaysFreight());
            shopeeGlobalPriceDo.setMaxWeight(sellerPaysFreightPro.getMaxWeight());
            shopeeGlobalPriceDo.setAddWeight(sellerPaysFreightPro.getAddWeight());
            shopeeGlobalPriceDo.setMaxWeightSku(sellerPaysFreightPro.getMaxWeightSku());
            BigDecimal bigDecimalPrice = shopeeGlobalPriceDo.getPrice() == null ? BigDecimal.ZERO : BigDecimal.valueOf(shopeeGlobalPriceDo.getPrice());
            double priceAll = bigDecimalPrice.add(sellerPaysFreightPro.getSellerPaysFreight()).doubleValue();
            shopeeGlobalPriceDo.setPrice(NumberUtils.round(priceAll, 2));
            list.add(shopeeGlobalPriceDo);
        }
        return list;
    }

    /**
     * 全球商品计算价格
     *
     * @param info
     * @param priceConfig
     * @param ratio
     * @return
     */
    public static Double calcPriceForGlobal(ProductInfo info, double priceConfig, double ratio) {

        /*
        最新规则：
        全球商品价格：成本价+成本价*店铺设置毛利率+对应站点卖家支付运费（卖家支付运费最小为1）
        成本价：销售成本价+包材成本+搭配包材成本+销售成本价*12%（12%为参数配置值，取系统设置-参数，配置权限仅开放主管和超级管理员）
        利率：范本时系统配置默认30% 模板根据账号配置利率（多个账号根据站点优先级）
        对应站点卖家支付运费：全球刊登各站点统一价格使用sg站点 公式和参数计算卖家支付运费（最重的一个变体SKU重量计算 所以卖家支付运费在外层算一次即可）
         */
        BigDecimal cost = info.getSaleCost() == null ? BigDecimal.ZERO : info.getSaleCost();
        BigDecimal packingPrice = info.getPackingPrice() == null ? BigDecimal.ZERO : info.getPackingPrice();
        BigDecimal matchPrice = info.getMatchPrice() == null ? BigDecimal.ZERO : info.getMatchPrice();
        BigDecimal price = cost.add(packingPrice).add(matchPrice).add(cost.multiply(BigDecimal.valueOf(priceConfig)));
        BigDecimal multiply = price.multiply(BigDecimal.valueOf(ratio));
        price = price.add(multiply);

        return price.doubleValue();
    }

    /**
     * 全球商品计算价格
     *
     * @param info
     * @param priceConfig
     * @param ratio
     * @return
     */
    public static ShopeeGlobalPriceDo calcPriceForGlobalPro(ProductInfo info, double priceConfig, double ratio) {

        /*
        最新规则：
        全球商品价格：成本价+成本价*店铺设置毛利率+对应站点卖家支付运费（卖家支付运费最小为1）
        成本价：销售成本价+包材成本+搭配包材成本+销售成本价*12%（12%为参数配置值，取系统设置-参数，配置权限仅开放主管和超级管理员）
        利率：范本时系统配置默认30% 模板根据账号配置利率（多个账号根据站点优先级）
        对应站点卖家支付运费：全球刊登各站点统一价格使用sg站点 公式和参数计算卖家支付运费（最重的一个变体SKU重量计算 所以卖家支付运费在外层算一次即可）
         */
        ShopeeGlobalPriceDo shopeeGlobalPriceDo = new ShopeeGlobalPriceDo();
        shopeeGlobalPriceDo.setSku(info.getSonSku());
        shopeeGlobalPriceDo.setPriceConfig(priceConfig);
        shopeeGlobalPriceDo.setRatio(ratio);
        BigDecimal cost = info.getSaleCost() == null ? BigDecimal.ZERO : info.getSaleCost();
        shopeeGlobalPriceDo.setSalePrice(cost);
        BigDecimal packingPrice = info.getPackingPrice() == null ? BigDecimal.ZERO : info.getPackingPrice();
        shopeeGlobalPriceDo.setPackingPrice(packingPrice);
        BigDecimal matchPrice = info.getMatchPrice() == null ? BigDecimal.ZERO : info.getMatchPrice();
        shopeeGlobalPriceDo.setMatchPrice(matchPrice);
        BigDecimal price = cost.add(packingPrice).add(matchPrice).add(cost.multiply(BigDecimal.valueOf(priceConfig)));
        BigDecimal multiply = price.multiply(BigDecimal.valueOf(ratio));
        price = price.add(multiply);

        shopeeGlobalPriceDo.setPrice(price.doubleValue());
        return shopeeGlobalPriceDo;
    }

    /**
     * 卖家支付的运费
     *
     * @param productInfoList
     * @param site
     * @return
     */
    public static BigDecimal getSellerPaysFreight(List<ProductInfo> productInfoList, String site) {
        if (CollectionUtils.isEmpty(productInfoList) || StringUtils.isBlank(site)) {
            return null;
        }

        // 取有效产品最重的重量
        BigDecimal maxWeight = null;
        for (ProductInfo info : productInfoList) {
            BigDecimal productWeight = info.getProductWeight() == null ? BigDecimal.ZERO : BigDecimal.valueOf(info.getProductWeight());
            BigDecimal packingWeight = info.getPackingWeight() == null ? BigDecimal.ZERO : info.getPackingWeight();
            BigDecimal matchWeight = info.getMatchWeight() == null ? BigDecimal.ZERO : info.getMatchWeight();
            BigDecimal estimateWeight = productWeight.add(packingWeight).add(matchWeight);
            if (null == maxWeight || maxWeight.compareTo(estimateWeight) == -1) {
                maxWeight = estimateWeight;
            }
        }
        if (null == maxWeight) {
            throw new RuntimeException("该货号无有效sku信息 无重量 无法算出卖家支付运费");
        }

        // 产品重量（净重+包材+包装材料+面单3g+10g）
        // g 10位向上取整 kg为保留两位小数 100g就是0.1kg，但是101g，就是110g，就是0.11kg
        BigDecimal weight = (maxWeight.add(BigDecimal.valueOf(13))).divide(BigDecimal.valueOf(1000), 2, RoundingMode.UP);

        // 计算卖家运费
        return getSellerPaysFreight(weight.doubleValue(), site);
    }

    /**
     * 卖家支付的运费
     *
     * @param productInfoList
     * @param site
     * @return
     */
    public static ShopeeGlobalPriceDo getSellerPaysFreightPro(boolean isNnShop, List<ProductInfo> productInfoList, String site) {
        if (CollectionUtils.isEmpty(productInfoList) || StringUtils.isBlank(site)) {
            return null;
        }
        ShopeeGlobalPriceDo shopeeGlobalPriceDo = new ShopeeGlobalPriceDo();
        // 取有效产品最重的重量
        BigDecimal maxWeight = null;

        for (ProductInfo info : productInfoList) {
            // 过滤停产(Stop)、存档(Archived)、废弃(Discard)的产品
            if (filterStatus(isNnShop, info.getItemStatus(), info.getSpecialTypeList())) {
                continue;
            }

            BigDecimal productWeight = info.getProductWeight() == null ? BigDecimal.ZERO : BigDecimal.valueOf(info.getProductWeight());
            BigDecimal packingWeight = info.getPackingWeight() == null ? BigDecimal.ZERO : info.getPackingWeight();
            BigDecimal matchWeight = info.getMatchWeight() == null ? BigDecimal.ZERO : info.getMatchWeight();
            BigDecimal estimateWeight = productWeight.add(packingWeight).add(matchWeight);
            if (null == maxWeight || maxWeight.compareTo(estimateWeight) == -1) {
                shopeeGlobalPriceDo.setMaxWeightSku(info.getSonSku());
                maxWeight = estimateWeight;
            }
        }
        if (null == maxWeight) {
            throw new RuntimeException("该货号无有效sku信息 无重量 无法算出卖家支付运费");
        }
        // 产品重量（净重+包材+包装材料+面单3g+10g）
        // g 10位向上取整 kg为保留两位小数 100g就是0.1kg，但是101g，就是110g，就是0.11kg
        shopeeGlobalPriceDo.setAddWeight(BigDecimal.valueOf(13));
        BigDecimal weight = (maxWeight.add(BigDecimal.valueOf(13))).divide(BigDecimal.valueOf(1000), 2, RoundingMode.UP);
        shopeeGlobalPriceDo.setMaxWeight(weight);

        // 计算卖家运费
        BigDecimal sellerPaysFreight = getSellerPaysFreight(weight.doubleValue(), site);
        shopeeGlobalPriceDo.setSellerPaysFreight(sellerPaysFreight);
        return shopeeGlobalPriceDo;
    }

    /**
     * 根据公式计算出买家支付运费 目前使用sg 其他站点启用需测试
     *
     * @param weight kg
     * @param site
     * @return
     */
    private static BigDecimal getSellerPaysFreight(Double weight, String site) {
        BigDecimal sellerPaysFreight = null;
        if (null == weight || StringUtils.isBlank(site)) {
            return sellerPaysFreight;
        }

        BigDecimal one = BigDecimal.valueOf(1);
        String siteCurrency = ShopeeCountryEnum.getCurrencyByCode(site);
        ApiResult<Double> cnyRateResult = PriceCalculatedUtil.getExchangeRate(siteCurrency, "CNY");
        if (!cnyRateResult.isSuccess() || null == cnyRateResult.getResult()) {
            throw new RuntimeException(String.format("%s获取汇率失败：%s", siteCurrency, cnyRateResult.getErrorMsg()));
        }
        Double exchangeRate = cnyRateResult.getResult();

        // my(重量上限/0.01-1)*0.15*汇率 续重价格0.15/10g
        if (ShopeeCountryEnum.Malaysia.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(0.15)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // sg(重量上限/0.01-1)*0.15*汇率
        else if (ShopeeCountryEnum.Singapore.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(0.15)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // th(重量上限/0.01-1)*1*汇率
        else if (ShopeeCountryEnum.Thailand.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(one).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // ph(重量上限/0.01-1)*4.5*汇率
        else if (ShopeeCountryEnum.Philippines.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(4.5)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // vn(重量上限/0.01-1)*900*汇率
        else if (ShopeeCountryEnum.Vietnam.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(900)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // tw(重量上限/0.1-1)*5*汇率
        else if (ShopeeCountryEnum.Taiwan.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.1)).subtract(one))
                    .multiply(BigDecimal.valueOf(5)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // id(重量上限/0.01-1)*1200*汇率
        else if (ShopeeCountryEnum.Indonesia.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(1200)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // pl(重量上限/0.01-1)*0.4*汇率
        else if (ShopeeCountryEnum.Poland.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(0.4)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // br((重量上限/0.01-1)*0.9*汇率 )/2
        else if (ShopeeCountryEnum.Brazil.getCode().equals(site)) {
            sellerPaysFreight = ((BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(0.9)).multiply(BigDecimal.valueOf(exchangeRate)))
                    .divide(BigDecimal.valueOf(2));
        }
        // co((重量上限/0.01-1)*800*汇率 )/2
        else if (ShopeeCountryEnum.Colombia.getCode().equals(site)) {
            sellerPaysFreight = ((BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(800)).multiply(BigDecimal.valueOf(exchangeRate)))
                    .divide(BigDecimal.valueOf(2));
        }
        // cl(重量上限/0.01-1)*190*汇率
        else if (ShopeeCountryEnum.Chile.getCode().equals(site)) {
            sellerPaysFreight = (BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(190)).multiply(BigDecimal.valueOf(exchangeRate));
        }
        // mx((重量上限/0.01-1)*4*汇率 )/2
        else if (ShopeeCountryEnum.Mexico.getCode().equals(site)) {
            sellerPaysFreight = ((BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(0.01)).subtract(one))
                    .multiply(BigDecimal.valueOf(4)).multiply(BigDecimal.valueOf(exchangeRate)))
                    .divide(BigDecimal.valueOf(2));
        }

        // es fr不做了暂无相对应的公式
        //Spain("ES"),
        //France("FR"),

        // 卖家支付运费最小为1
        if (sellerPaysFreight != null && sellerPaysFreight.compareTo(one) == -1) {
            sellerPaysFreight = one;
        }
        return sellerPaysFreight;
    }

    /**
     * 获取sku下面所有可用标题
     *
     * @param sku
     * @return
     */
    public static ApiResult<List<String>> getSkuAllTitle(String sku) {
        List<ProductInfo> skuList = ProductUtils.findProductInfos(Arrays.asList(sku));
        if (CollectionUtils.isEmpty(skuList)) {
            return ApiResult.newError("无效的货号！");
        }

        List<String> titles = new ArrayList<>();

        // 查询标题
        String defaultTitle = null;
        for (ProductInfo productInfo : skuList) {
            // 默认标题
            if (StringUtils.isBlank(defaultTitle) && StringUtils.isNotBlank(productInfo.getTitleEn())) {
                defaultTitle = productInfo.getTitleEn();
                break;
            }
        }
        if (StringUtils.isNotBlank(defaultTitle)) {
            titles.add(defaultTitle);
        }

        List<String> spuList = new ArrayList<>(1);
        spuList.add(skuList.get(0).getMainSku());
        //根据主spu获取spu标题
        ResponseJson resp = ProductUtils.getSpuTitles(spuList);
        if (resp != null && resp.isSuccess()) {
            List<SpuOfficial> spuOfficialList = (List<SpuOfficial>) resp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isNotEmpty(spuOfficialList)) {
                SpuOfficial spuOfficial = spuOfficialList.get(0);
                //长标题
                if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
                    //去除可能存在的空值（，“”）
                    JSONArray longJson = JSON.parseArray(spuOfficial.getLongTitleJson().replaceAll(",\"\"", ""));
                    if (CollectionUtils.isNotEmpty(longJson)) {
                        for (int i = 0; i < longJson.size(); i++) {
                            //搜集所有长标题
                            if (StringUtils.isNotBlank(longJson.getString(i))) {
                                titles.add(longJson.getString(i));
                            }
                        }
                    }

                }

                //短标题
                if (StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
                    //去除可能存在的空值（，“”）
                    JSONArray skuJson = JSON.parseArray(spuOfficial.getShortTitleJson().replaceAll(",\"\"", ""));
                    if (CollectionUtils.isNotEmpty(skuJson)) {
                        for (int i = 0; i < skuJson.size(); i++) {
                            //搜集所有短标题
                            if (StringUtils.isNotBlank(skuJson.getString(i))) {
                                titles.add(skuJson.getString(i));
                            }
                        }
                    }
                }

                //sku标题
                if (StringUtils.isNotBlank(spuOfficial.getTitle())) {
                    //去除可能存在的空值（，“”）
                    JSONArray skuJson = JSON.parseArray(spuOfficial.getTitle().replaceAll(",\"\"", ""));
                    if (null != skuJson && skuJson.size() > 0) {
                        for (int i = 0; i < skuJson.size(); i++) {
                            //搜集所有sku标题
                            if (StringUtils.isNotBlank(skuJson.getString(i))) {
                                titles.add(skuJson.getString(i));
                            }
                        }
                    }
                }
            }
        }

        return ApiResult.of(true, titles, "sku所有可用标题集合！");
    }


    /**
     * 构建产品字段信息
     *
     * @param account
     * @param spu
     * @param skuInfoList
     * @param copyWritingType 文案类型
     * @return
     */
    public static ShopeeProductColumnInfo buildProductColumnInfo(String account, SpuInfo spu, List<ProductInfo> skuInfoList, Integer copyWritingType) {
        ShopeeProductColumnInfo bean = new ShopeeProductColumnInfo();

        String title = "";
        String description = "";
        Integer resCopyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
        try {
            ResponseJson resp = ProductUtils.getSpuTitles(CommonUtils.arrayAsList(spu.getSpu()));
            if (resp != null && resp.isSuccess()) {
                List<SpuOfficial> spuOfficialList = (List<SpuOfficial>) resp.getBody().get(ProductUtils.resultKey);
                if (CollectionUtils.isNotEmpty(spuOfficialList)) {
                    SpuOfficial spuOfficial = spuOfficialList.get(0);
                    spuOfficial.setNeedWenAnType(copyWritingType);
                    ApiResult<Map<String, Object>> apiResult = SpuTitleRuleUtil.generateTitle(Platform.Shopee, spuOfficial, (newTitle) -> {
                        //其他操作...
                        return ApiResult.newSuccess(newTitle);
                    });
                    //记录标题的取值规则
                    Object titleRuleVal = apiResult.getResult().get(SpuTitleRuleUtil.titleRuleKey);
                    if (titleRuleVal != null) {
                        SpuTitleRule rule = (SpuTitleRule) titleRuleVal;
                        rule.setIsFirst(true);
                        bean.setTitleRule(rule);
                    }
                    //标题
                    Object titleNew = apiResult.getResult().get(SpuTitleRuleUtil.titleKey);
                    if (titleNew != null && StringUtils.isNotBlank(titleNew.toString())) {
                        title = titleNew.toString().trim();
                    }
                    /* 描述取值优先级：
                        1-SKU描述新，在描述最前面插入标题。
                        2-SKU描述，在描述最前面插入标题
                    */
                    String newDescription = spuOfficial.getNewDescription();
                    if (StringUtils.isNotBlank(newDescription)) {
                        description = title + "\n" + newDescription;
                    }
                    if (StringUtils.isBlank(description)) {
                        String desc = StrUtil.objectToStr(spu.getDescEn());
                        if (StringUtils.isNotBlank(desc)) {
                            List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {
                            });
                            for (String s : list) {
                                if (StringUtils.isNotBlank(s)) {
                                    description = title + "\n" + s;
                                    break;
                                }
                            }
                        }
                    }
                    resCopyWritingType = spuOfficial.getNeedWenAnType();
                    if (spuOfficial.getNeedWenAnType() == WenAnTypeEnum.unlimitedWenAn.getCode() && !spuOfficial.isHasNoLimitWenAn()) {
                        resCopyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
                    }
                }
            }
        } catch (Exception e) {
            log.error(String.format("account: %s,spu%s,生成标题描述出错", account, spu.getSpu()), e);
        }
        bean.setTitle(title);
        bean.setDescription(description);
        bean.setCopyWritingType(resCopyWritingType);

        //长宽高
        Map<String, Double> dimension = new HashMap<>(3);
        for (ProductInfo productInfo : skuInfoList) {
            if (productInfo.getLength() != null) {
                Double length = productInfo.getLength().doubleValue();
                if (length < 1) {
                    length = 1D;
                }
                dimension.putIfAbsent("package_length", length);
            }
            if (productInfo.getWide() != null) {
                Double wide = productInfo.getWide().doubleValue();
                if (wide < 1) {
                    wide = 1D;
                }
                dimension.putIfAbsent("package_width", wide);
            }
            if (productInfo.getHeight() != null) {
                Double height = productInfo.getHeight().doubleValue();
                if (height < 1) {
                    height = 1D;
                }
                dimension.putIfAbsent("package_height", height);
            }

            // json中标题和描述都有了就跳出
            if (dimension.size() >= 3) {
                break;
            }
        }
        bean.setDimension(dimension);

        return bean;
    }

    public static ShopeeProductColumnInfo buildProductColumnInfo(String account, ComposeSku composeProduct, Integer copyWritingType) {
        ShopeeProductColumnInfo bean = new ShopeeProductColumnInfo();

        String title = "";
        String description = "";
        Integer resCopyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
        try {
            List<SpuOfficial> spuOfficialList = composeProduct.getComposeOfficials();
            if (CollectionUtils.isEmpty(spuOfficialList)) {
                throw new RuntimeException("组合信息文案缺失");
            }
            if (CollectionUtils.isNotEmpty(spuOfficialList)) {
                SpuOfficial spuOfficial = spuOfficialList.get(0);
                spuOfficial.setSpu(composeProduct.getComposeSku());
                spuOfficial.setNeedWenAnType(copyWritingType);
                ApiResult<Map<String, Object>> apiResult = SpuTitleRuleUtil.generateTitle(Platform.Shopee, spuOfficial, (newTitle) -> {
                    //其他操作...
                    return ApiResult.newSuccess(newTitle);
                });
                //记录标题的取值规则
                Object titleRuleVal = apiResult.getResult().get(SpuTitleRuleUtil.titleRuleKey);
                if (titleRuleVal != null) {
                    SpuTitleRule rule = (SpuTitleRule) titleRuleVal;
                    rule.setIsFirst(true);
                    bean.setTitleRule(rule);
                }
                //标题
                Object titleNew = apiResult.getResult().get(SpuTitleRuleUtil.titleKey);
                if (titleNew != null && StringUtils.isNotBlank(titleNew.toString())) {
                    title = titleNew.toString().trim();
                }
                    /* 描述取值优先级：
                        1-SKU描述新，在描述最前面插入标题。
                        2-SKU描述，在描述最前面插入标题
                    */
                String newDescription = spuOfficial.getNewDescription();
                if (StringUtils.isNotBlank(newDescription)) {
                    description = title + "\n" + newDescription;
                }
                if (StringUtils.isBlank(description)) {
                    String desc = StrUtil.objectToStr(spuOfficial.getDescription());
                    if (StringUtils.isNotBlank(desc)) {
                        List<String> list = JSON.parseObject(desc, new TypeReference<List<String>>() {
                        });
                        for (String s : list) {
                            if (StringUtils.isNotBlank(s)) {
                                description = title + "\n" + s;
                                break;
                            }
                        }
                    }
                }
                resCopyWritingType = spuOfficial.getNeedWenAnType();
                if (spuOfficial.getNeedWenAnType() == WenAnTypeEnum.unlimitedWenAn.getCode() && !spuOfficial.isHasNoLimitWenAn()) {
                    resCopyWritingType = WenAnTypeEnum.defaultWenAn.getCode();
                }
            }
        } catch (Exception e) {
            log.error(String.format("account: %s,spu%s,生成标题描述出错", account, composeProduct.getComposeSku()), e);
        }
        bean.setTitle(title);
        bean.setDescription(description);
        bean.setCopyWritingType(resCopyWritingType);
        return bean;
    }

    /**
     * 构建试卖-全球模板
     *
     * @param mainSku
     * @param product
     * @return
     */
    public static ApiResult<?> buildSpGlobalTemplate(String mainSku, ShopeeSpProductInfo product) {
        List<ShopeeSpIncrementInfo> incrementInfoList = product.getIncrementInfo();
        ShopeeSpIncrementInfo incrementInfo = incrementInfoList.get(0);
        List<String> imageUrls = incrementInfo.getImageUrls();

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setCopyWritingType(WenAnTypeEnum.defaultWenAn.getCode());
        shopeeTemplate.setSku(mainSku);
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());
        // 图片设置进json
        shopeeTemplate.setImagesList(imageUrls);

        // 泰国、越南、巴西 采集的数据，标题和商品，skuColor，skuSize 描述需要自动翻译为英文
        CountryEnum targetCountryEnum = null;
        if (StringUtils.isNotBlank(product.getCountry())) {
            if (product.getCountry().equalsIgnoreCase(CountryEnum.TH.getCode())) {
                targetCountryEnum = CountryEnum.TH;
            } else if (product.getCountry().equalsIgnoreCase(CountryEnum.VN.getCode())) {
                targetCountryEnum = CountryEnum.VN;
            } else if (product.getCountry().equalsIgnoreCase(CountryEnum.BR.getCode())) {
                targetCountryEnum = CountryEnum.BR;
            }
        }

        //构建子属性sku集合
        List<ShopeeSku> shopeeSkus = new ArrayList<>();
        List<EsVariationsInfos> variationsInfos = incrementInfo.getVariationsInfos();
        if (CollectionUtils.isNotEmpty(variationsInfos)) {
            for (EsVariationsInfos var : variationsInfos) {
                ShopeeSku sku = new ShopeeSku();
                sku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
                sku.setSku(mainSku);
                List<EsAttrInfos> attrInfos = var.getAttrInfos();
                if (CollectionUtils.isNotEmpty(attrInfos)) {
                    for (EsAttrInfos attrInfo : attrInfos) {
                        if (ShopeeConstants.COLOR.equalsIgnoreCase(attrInfo.getName())) {
                            sku.setColor(StringUtils.substring(attrInfo.getValue(), 0, 30));
                        } else if (ShopeeConstants.SIZE.equalsIgnoreCase(attrInfo.getName())) {
                            sku.setSize(StringUtils.substring(attrInfo.getValue(), 0, 30));
                        } else {
                            //如果color size 都匹配不到，可能是其他层级标记, 则先设置color 再设置 size
                            if (StringUtils.isBlank(sku.getColor())) {
                                sku.setColor(StringUtils.substring(attrInfo.getValue(), 0, 30));
                                sku.setColorSourceName(attrInfo.getName());
                            } else if (StringUtils.isBlank(sku.getSize())) {
                                sku.setSize(StringUtils.substring(attrInfo.getValue(), 0, 30));
                                sku.setSizeSourceName(attrInfo.getName());
                            }
                        }
                    }

                    if (targetCountryEnum != null) {
                        List<String> list = new ArrayList<>(2);
                        list.add(sku.getColor() == null ? "" : sku.getColor());
                        list.add(sku.getSize() == null ? "" : sku.getSize());
                        String result = GoogleTranslateUtils.googleTranslateText(list, targetCountryEnum.getLanguage(), CountryEnum.US.getLanguage());
                        if (StringUtils.isNotBlank(result)) {
                            try {
                                List<String> transList = JSON.parseArray(result, String.class);
                                if (transList.size() == list.size()) {
                                    sku.setColor("".equals(transList.get(0)) ? null : transList.get(0));
                                    sku.setSize("".equals(transList.get(1)) ? null : transList.get(1));
                                }
                            } catch (Exception e) {
                                log.error(String.format("试卖主sku：%s, 生成子sku时翻译出错：", sku.getSku()), e);
                                return ApiResult.newError(String.format("试卖主sku：%s, 生成子sku时翻译出错：", sku.getSku()));
                            }
                        } else {
                            log.error(String.format("试卖主sku：%s, 生成子sku时翻译为空", sku.getSku()));
                            return ApiResult.newError(String.format("试卖主sku：%s, 生成子sku时翻译出错：", sku.getSku()));
                        }
                    }

                    //设置变体sku名称
                    if (StringUtils.isNotBlank(sku.getColor())) {
                        // color 只保留字母、数字、横杆
                        String color = sku.getColor().replaceAll("[^a-zA-Z0-9-]", "");
                        sku.setSku(String.format("%s-%s", sku.getSku(), color));
                    }
                    if (StringUtils.isNotBlank(sku.getSize())) {
                        // size 只保留字母、数字、横杆
                        String size = sku.getSize().replaceAll("[^a-zA-Z0-9-]", "");
                        sku.setSku(String.format("%s-%s", sku.getSku(), size));
                    }
                }

                if (StringUtils.isNotBlank(sku.getSku())) {
                    sku.setSku(sku.getSku().toUpperCase());
                }

                //sku里面的原价
                sku.setPrice(var.getOriginalPrice());
                if (sku.getPrice() == null || sku.getPrice() == 0) {
                    //设置最大原价
                    sku.setPrice(incrementInfo.getOriginalMaxPrice());
                }
                if (sku.getPrice() == null || sku.getPrice() == 0) {
                    //进入这里表示产品没有打折
                    sku.setPrice(incrementInfo.getMaxPrice());
                }
                // 图片是无规则图片，不做过滤处理
                String skuImagePath = var.getSkuImagePath();
                if (StringUtils.isNotBlank(skuImagePath)) {
                    try {
                        List<String> list = JSON.parseArray(skuImagePath, String.class);
                        if (CollectionUtils.isNotEmpty(list)) {
                            sku.setImage(list.size() >= 2 ? list.get(1) : list.get(0));
                        }
                    } catch (Exception e) {
                        log.error(String.format("%s转换图片集合出错", skuImagePath), e);
                    }
                }
                sku.setQuantity(9999);
                shopeeSkus.add(sku);
            }
        } else {
            //单体
            ShopeeSku sku = new ShopeeSku();
            if (StringUtils.isNotBlank(mainSku)) {
                sku.setSku(mainSku.toUpperCase());
            }
            sku.setPrice(incrementInfo.getOriginalMaxPrice());
            if (sku.getPrice() == null || sku.getPrice() == 0) {
                sku.setPrice(incrementInfo.getMaxPrice());
            }
            sku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
            sku.setImage(imageUrls.get(0));
            sku.setQuantity(9999);
            shopeeSkus.add(sku);
        }

        //获取汇率 汇率转换
        String currency = incrementInfo.getCurrency();
        ApiResult<Double> apiResult = PriceCalculatedUtil.getExchangeRate(currency, CurrencyConstant.CNY);
        if (apiResult.isSuccess()) {
            Double cnyRate = apiResult.getResult();
            // 这里要改成用缓存处理
            double trialSaleGlobalProductSalePriceUpgradeRatio = ShopeeSystemParamUtil.getTrialSaleGlobalProductSalePriceUpgradeRatio();
            shopeeSkus.parallelStream().forEach(sku -> {
                // 试卖sku价格按链接价格*汇率（汇兑成RMB）* 115%
                double price = sku.getPrice() * cnyRate * (trialSaleGlobalProductSalePriceUpgradeRatio + 1);
                sku.setPrice(NumberUtils.round(price, 2));
            });
        } else {
            return ApiResult.newError(String.format("%s获取汇率失败：%s", currency, apiResult.getErrorMsg()));
        }
        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));


        //确定类目属性
        List<String> cateIds = product.getCateIds();
        if (CollectionUtils.isNotEmpty(cateIds)) {
            Integer cateId = Integer.valueOf(cateIds.get(cateIds.size() - 1));
            ShopeeCategoryV2Example cateEx = new ShopeeCategoryV2Example();
            cateEx.createCriteria().andSiteEqualTo(StrConstant.CNSC).andCategoryIdEqualTo(cateId);
            List<ShopeeCategoryV2> category = shopeeCategoryV2Service.selectByExample(cateEx);
            if (CollectionUtils.isNotEmpty(category)) {
                shopeeTemplate.setCategoryId(category.get(0).getCategoryId());

                if (StringUtils.isNotBlank(incrementInfo.getSpecifications())) {
                    JSONObject attrJson = JSON.parseObject(incrementInfo.getSpecifications());

                    //查询属性
                    ShopeeAttributeV2Example attributeExample = new ShopeeAttributeV2Example();
                    attributeExample.createCriteria()
                            .andCategoryIdEqualTo(shopeeTemplate.getCategoryId())
                            .andIsMandatoryEqualTo(true);
                    List<ShopeeAttributeV2> attributes = shopeeAttributeV2Service.selectByExample(attributeExample);
                    //根据属性id 去重
                    attributes = attributes.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getAttributeId()))), ArrayList::new));
                    for (ShopeeAttributeV2 attr : attributes) {
                        String val = attrJson.getString(attr.getOriginalAttributeName());
                        if (StringUtils.isBlank(val)) {
                            val = attrJson.getString(attr.getDisplayAttributeName());
                        }
                        if (StringUtils.isNotBlank(val)
                                && StringUtils.isNotBlank(attr.getAttributeValueList())
                                && "[]".equals(attr.getAttributeValueList())) {
                            List<Map<String, Object>> attList = new ArrayList<>();

                            JSONArray valList = JSON.parseArray(attr.getAttributeValueList());
                            for (int i = 0; i < valList.size(); i++) {
                                JSONObject jsonObject = valList.getJSONObject(i);
                                if (StringUtils.equalsIgnoreCase(jsonObject.getString("name"), val)) {
                                    Map<String, Object> bean = new HashMap<>(3);
                                    bean.put("attributeId", attr.getAttributeId());
                                    bean.put("displayAttributeName", attr.getDisplayAttributeName());
                                    bean.put("attributeValueList", Arrays.asList(jsonObject));
                                    attList.add(bean);
                                }
                            }
                            shopeeTemplate.setAttributesStr(JSON.toJSONString(attList));
                        }
                    }
                }
            }
        }

        // 标题和商品描述过滤特殊字符
        String description = StrUtil.removeNonBmpUnicodes(incrementInfo.getDescription());
        String title = StrUtil.removeNonBmpUnicodes(incrementInfo.getTitle());

        if (targetCountryEnum != null) {
            List<String> list = new ArrayList<>(2);
            list.add(title == null ? "" : title);
            list.add(description == null ? "" : description);
            String result = GoogleTranslateUtils.googleTranslateText(list, targetCountryEnum.getLanguage(), CountryEnum.US.getLanguage());
            if (StringUtils.isNotBlank(result)) {
                try {
                    List<String> transList = JSON.parseArray(result, String.class);
                    if (transList.size() == list.size()) {
                        title = "".equals(transList.get(0)) ? null : transList.get(0);
                        description = "".equals(transList.get(1)) ? null : transList.get(1);
                    }
                } catch (Exception e) {
                    log.error(String.format("试卖主sku：%s, 翻译出错：", mainSku), e);
                    return ApiResult.newError(String.format("试卖主sku：%s, 标题描述翻译出错：", mainSku));
                }
            } else {
                log.error(String.format("试卖主sku：%s, 翻译为空", mainSku));
                return ApiResult.newError(String.format("试卖主sku：%s, 标题描述翻译出错：", mainSku));
            }
        }

        shopeeTemplate.setName(title);
        shopeeTemplate.setDescription(description);
        shopeeTemplate.setBrand(incrementInfo.getBrand());
        //木有重量
        shopeeTemplate.setCondition("NEW");
        shopeeTemplate.setIsParent(true);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        shopeeTemplate.setDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());

        return ApiResult.newSuccess(shopeeTemplate);
    }

    /**
     * 构建试卖-全球模板
     *
     * @param mainSku
     * @param product
     * @return
     */
    public static ApiResult<?> build1688GlobalTemplate(String mainSku, EsAli1688AccountProductInfo product) {
        List<EsAli1688VariationsInfo> incrementInfoList = product.getIncrementInfo();
        EsAli1688VariationsInfo incrementInfo = incrementInfoList.get(0);
        List<String> imageUrls = incrementInfo.getImageUrls();

        EsShopeeGlobalTemplate shopeeTemplate = new EsShopeeGlobalTemplate();
        shopeeTemplate.setCopyWritingType(WenAnTypeEnum.defaultWenAn.getCode());
        shopeeTemplate.setSku(mainSku);
        shopeeTemplate.setMtsku(shopeeTemplate.getSku() + CNSCPublishUtil.getRandomChar());
        // 图片设置进json
        shopeeTemplate.setImagesList(imageUrls);

        //构建子属性sku集合
        List<ShopeeSku> shopeeSkus = new ArrayList<>();
        List<EsVariationsInfo> variationsInfos = incrementInfo.getVariationsInfos();

        if (CollectionUtils.isNotEmpty(variationsInfos)) {
            variationsInfos = variationsInfos.stream().limit(50).collect(Collectors.toList());
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("1688试卖属性翻译");
            Map<String, String> cnAndEnMap = translateSpAttrInfos(variationsInfos);
            stopWatch.stop();
            log.info(stopWatch.prettyPrint());
            ShopeeSku shopeeSku = null;
            for (EsVariationsInfo variationsInfo : variationsInfos) {
                shopeeSku = new ShopeeSku();
                shopeeSku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
                // 子SKU
                String sonSku = mainSku;
                // 子SKU属性
                List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
                if(CollectionUtils.isNotEmpty(attrInfos)) {
                    for (EsAttrInfo attrInfo : attrInfos) {
                        // 1.颜色尺码翻译为英文，只保留字母和数字和-  Color size 需要加s
                        String name = attrInfo.getName() == null ? "": attrInfo.getName();
                        String value =  attrInfo.getValue() == null ? "": attrInfo.getValue();

                        String enValue = cnAndEnMap.get(value);
                        if (enValue != null && enValue.length() > 65 ){
                            enValue = enValue.substring(0, 65);
                        }
                        // 过滤字符 只保留字母和数字和-
                        enValue = enValue.replaceAll("[^a-zA-Z0-9-]", "");
                        sonSku = sonSku + SKU_SPLIT_STR + enValue;

                        if (ShopeeConstants.COLOR.equalsIgnoreCase(name) || "颜色".equals(name)) {
                            shopeeSku.setColor(enValue);
                        } else if (ShopeeConstants.SIZE.equalsIgnoreCase(name) || "尺寸".equals(name)) {
                            shopeeSku.setSize(enValue);
                        } else {
                            //如果color size 都匹配不到，可能是其他层级标记, 则先设置color 再设置 size
                            if (StringUtils.isBlank(shopeeSku.getColor())) {
                                shopeeSku.setColor(enValue);
                                shopeeSku.setColorSourceName(name);
                            } else if (StringUtils.isBlank(shopeeSku.getSize())) {
                                shopeeSku.setSize(enValue);
                                shopeeSku.setSizeSourceName(name);
                            }
                        }
                    }
                }

                List<String> skuImagePaths = variationsInfo.getSkuImagePath();
                if(CollectionUtils.isNotEmpty(skuImagePaths)) {
                    skuImagePaths = skuImagePaths.stream()
                            .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                            .filter(ShopeeProductInfoUtil::filterLowPixelImg)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(skuImagePaths)) {
                        shopeeSku.setImage(StringUtils.isBlank(skuImagePaths.get(0)) ? "" : skuImagePaths.get(0));
                    }
                }
                shopeeSku.setSku(StringUtils.upperCase(sonSku));
                shopeeSku.setQuantity(9999);
                shopeeSkus.add(shopeeSku);
            }
        } else {
            //单体
            ShopeeSku sku = new ShopeeSku();
            sku.setSku(StringUtils.upperCase(mainSku));
            sku.setImage(imageUrls.get(0));
            sku.setQuantity(9999);
            sku.setDaysToShip(ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP);
            shopeeSkus.add(sku);
        }

        shopeeTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));

        // 标题和商品描述过滤特殊字符
//        String description = StrUtil.removeNonBmpUnicodes(incrementInfo.getDescription());
        String title = StrUtil.removeNonBmpUnicodes(incrementInfo.getTitle());

        List<String> list = new ArrayList<>(2);
        list.add(title == null ? "" : title);
//        list.add(description == null ? "" : description);
        String result = GoogleTranslateUtils.googleTranslateText(list, CountryEnum.CN.getLanguage(), CountryEnum.US.getLanguage());
        if (StringUtils.isNotBlank(result)) {
            try {
                List<String> transList = JSON.parseArray(result, String.class);
                if (transList.size() == list.size()) {
                    title = "".equals(transList.get(0)) ? null : transList.get(0);
//                    description = "".equals(transList.get(1)) ? null : transList.get(1);
                }
            } catch (Exception e) {
                log.error(String.format("试卖主sku：%s, 翻译出错：", mainSku), e);
                return ApiResult.newError(String.format("试卖主sku：%s, 标题描述翻译出错：", mainSku));
            }
        } else {
            log.error(String.format("试卖主sku：%s, 翻译为空", mainSku));
            return ApiResult.newError(String.format("试卖主sku：%s, 标题描述翻译出错：", mainSku));
        }
        String description = ShopeeDescriptionUtil.buildTemplateDesc("", incrementInfo.getDescImgUrls());

        shopeeTemplate.setName(title);
        shopeeTemplate.setDescription(description);
//        shopeeTemplate.setBrand(incrementInfo.getBrand());
        //木有重量
        shopeeTemplate.setCondition("NEW");
        shopeeTemplate.setIsParent(true);
        shopeeTemplate.setMainImageNum(1);
        shopeeTemplate.setPublishStatus(ShopeeNewPublishStatusEnum.PRE_PUBLISH.getCode());
        shopeeTemplate.setType(ShopeeTemplateTypeEnum.NORMAL.getCode());
        shopeeTemplate.setPublishRole(ShopeePublishRoleEnum.SALE.getCode());
        shopeeTemplate.setDataSource(ShopeeSkuDataSourceEnum.ERP_DATA_SYSTEM_1688.getCode());
        List<String> videoUrls = incrementInfo.getVideoUrls();
        if (CollectionUtils.isNotEmpty(videoUrls)) {
            shopeeTemplate.setVideo(videoUrls.get(0));
        }
        return ApiResult.newSuccess(shopeeTemplate);
    }

    /**
     * 根据admin范本多属性 设置模板的 color 和 size
     *
     * @param shopeeSkus
     * @param shopeeSkus
     */
    public static void variantSetColorAndSize(List<ShopeeSku> shopeeSkus, List<ShopeeSku> adminShopeeSkus) {
        if (CollectionUtils.isEmpty(shopeeSkus) || CollectionUtils.isEmpty(adminShopeeSkus)) {
            return;
        }
        Map<String, ShopeeSku> adminShopeeSkuMap = adminShopeeSkus.stream().collect(Collectors.toMap(o -> o.getSku(), o -> o, (k1, k2) -> k1));
        for (ShopeeSku shopeeSku : shopeeSkus) {
            String sonSku = shopeeSku.getSku();
            ShopeeSku adminShopeeSku = adminShopeeSkuMap.get(sonSku);
            if (null == adminShopeeSku) {
                continue;
            }

            String color = adminShopeeSku.getColor();
            if (StringUtils.isNotBlank(color)) {
                shopeeSku.setColor(StringUtils.substring(color, 0, 30));
            }
            String chineseColor = adminShopeeSku.getChineseColor();
            if (StringUtils.isNotBlank(chineseColor)) {
                shopeeSku.setChineseColor(chineseColor);
            }
            String size = adminShopeeSku.getSize();
            if (StringUtils.isNotBlank(size)) {
                shopeeSku.setSize(StringUtils.substring(size, 0, 30));
            }
            String chineseSize = adminShopeeSku.getChineseSize();
            if (StringUtils.isNotBlank(chineseSize)) {
                shopeeSku.setChineseSize(chineseSize);
            }
        }
    }

    public static BigDecimal getSellerPaysFreightByWeight(Double weight, String site) throws Exception {
        if (weight == null || StringUtils.isBlank(site)) {
            return null;
        }
        // 产品重量（净重+包材+包装材料+面单3g+10g）
        // g 10位向上取整 kg为保留两位小数 100g就是0.1kg，但是101g，就是110g，就是0.11kg
        BigDecimal packageWeight = BigDecimal.valueOf(weight);
        BigDecimal calcWeight = (packageWeight.add(BigDecimal.valueOf(13))).divide(BigDecimal.valueOf(1000), 2, RoundingMode.UP);

        // 计算卖家运费
        return getSellerPaysFreight(calcWeight.doubleValue(), site);
    }

    /**
     * 按照 产品系统生成  销售上传 刊登系统生成的 视频优先级获取视频
     *
     * @param spu
     * @return
     */
    public static String getVideoLink(String spu) {
        // 获取产品系统视频
        String videoLink = getProductVideoLink(spu);
        if (StringUtils.isBlank(videoLink)) {
            // 不存在则查询 销售制作 刊登系统生成的 视频
            videoLink = getPublishVideoLink(spu);
        }

        return videoLink;
    }

    /**
     * 获取产品系统制作视频
     *
     * @param spu
     * @return
     */
    public static String getProductVideoLink(String spu) {
        ApiResult<List<VideoDetail>> apiResult = FmsUtils.getVideoDetailBySpu(spu);
        if (apiResult.isSuccess() && CollectionUtils.isNotEmpty(apiResult.getResult())) {
            List<VideoDetail> videoDetails = apiResult.getResult();
            List<String> videoList = videoDetails.stream().filter(o -> StringUtils.isNotBlank(o.getVideoLink())).map(o -> o.getVideoLink()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(videoList)) {
                Collections.shuffle(videoList);
                return videoList.get(0);
            }
        }
        return null;
    }

    /**
     * 查询 销售制作 刊登系统生成的 视频
     *
     * @param spu
     * @return
     */
    public static String getPublishVideoLink(String spu) {
        List<String> videoList = null;
        // 查询是否有销售
        try {
            String filePath = FmsUtils.getFilePath(SaleChannel.CHANNEL_SHOPEE, VideoTypeEnum.sale.getCode());
            ApiResult<List<String>> publishVideoResult = FmsUtils.getPublishVideo(spu, filePath);
            if (!publishVideoResult.isSuccess()) {
                log.error(String.format("[%s]获取文件系统销售视频异常:[%s]", spu, publishVideoResult.getErrorMsg()));
            }
            videoList = publishVideoResult.getResult();
        } catch (Exception e) {
            log.error(String.format("[%s]获取文件系统视频销售异常Exception:[%s]", spu, e.getMessage()), e);
        }

        // 查询刊登生成的视频
        try {
            String filePath = FmsUtils.getFilePath(SaleChannel.CHANNEL_SHOPEE, VideoTypeEnum.system.getCode());
            ApiResult<List<String>> publishVideoResult = FmsUtils.getPublishVideo(spu, filePath);
            if (!publishVideoResult.isSuccess()) {
                log.error(String.format("[%s]获取文件系统刊登生成的视频异常:[%s]", spu, publishVideoResult.getErrorMsg()));
            }
            videoList = publishVideoResult.getResult();
        } catch (Exception e) {
            log.error(String.format("[%s]获取文件系统刊登生成的视频异常Exception:[%s]", spu, e.getMessage()), e);
        }

        if (CollectionUtils.isNotEmpty(videoList)) {
            Collections.shuffle(videoList);
            return videoList.get(0);
        }
        return null;
    }


    /**
     * 翻译为英文 name 和 value
     * 默认使用color size 属性
     * @param variationsInfos
     * @return
     */
    private static Map<String, String> translateSpAttrInfos(List<EsVariationsInfo> variationsInfos) {
        if(CollectionUtils.isEmpty(variationsInfos)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> cnAndEnMap = new HashMap<>();
        Set<String> cnSet = new HashSet<>();
        for (EsVariationsInfo variationsInfo : variationsInfos) {
            List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
            if (CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfo attrInfo : attrInfos) {
                    String name = attrInfo.getName() == null ? "": attrInfo.getName();
                    String value =  attrInfo.getValue() == null ? "": attrInfo.getValue();
                    cnSet.add(name);
                    cnSet.add(value);
                }
            }
        }

        List<String> cnList = new ArrayList<>(cnSet);
        String result = GoogleTranslateUtils.googleTranslateText(cnList, CountryEnum.CN.getLanguage(), CountryEnum.US.getLanguage());
        if(StringUtils.isNotBlank(result)){
            try {
                List<String> transList = JSON.parseArray(result, String.class);
                if(transList.size() == cnList.size()){
                    for (int i = 0; i < cnList.size(); i++) {
                        String cn = cnList.get(i);
                        String en = transList.get(i);
                        cnAndEnMap.put(cn, en);
                    }
                }
            } catch (Exception e){
                log.error("试卖翻译子sku属性出错" + e.getMessage(), e);
                throw new RuntimeException("试卖翻译子sku属性出错" + e.getMessage());
            }
        } else {
            log.error("试卖翻译子sku属性出错!");
            throw new RuntimeException("试卖翻译子sku属性出错!");
        }

        Set<String> enNameSet = new HashSet<>();
        Set<String> cnNameSet = new HashSet<>();
        for (EsVariationsInfo variationsInfo : variationsInfos) {
            List<EsAttrInfo> attrInfos = variationsInfo.getAttrInfos();
            if (CollectionUtils.isNotEmpty(attrInfos)) {
                for (EsAttrInfo attrInfo : attrInfos) {
                    // 1.颜色尺码翻译为英文，只保留字母和数字和-  Color size 需要加s
                    String name = attrInfo.getName() == null ? "" : attrInfo.getName();
                    cnNameSet.add(name);
                    enNameSet.add(cnAndEnMap.get(name));
                }
            }
        }

        Boolean existColor = false;
        Boolean existSize = false;
        for (String enName : enNameSet) { // 产品经理特意要求 默认使用 color size 超过两个再使用翻译的语言
            if(StringUtils.equalsIgnoreCase(enName, color)) {
                existColor = true;
            }
            if(StringUtils.equalsIgnoreCase(enName, size)) {
                existSize = true;
            }
        }

        // 默认使用 color size 超过两个再使用翻译的语言 (找出两个color size  不存在则使用其他转为 color size)
        List<String> cnNameList = new ArrayList<>(cnNameSet);
        for (int i = 0; i < cnNameList.size(); i++) {
            if (BooleanUtils.isTrue(existColor) && BooleanUtils.isTrue(existSize)) {
                break;
            }
            if(BooleanUtils.isNotTrue(existColor)) {
                cnAndEnMap.put(cnNameList.get(i), color);
                existColor = true;
            }else {
                cnAndEnMap.put(cnNameList.get(i), size);
                existSize = true;
            }
        }

        return cnAndEnMap;
    }

    /**
     * 过滤低像素图片
     * @param imgUrl
     * @return
     */
    public static boolean filterLowPixelImg(String imgUrl) {
        if(org.apache.commons.lang3.StringUtils.isBlank(imgUrl)) {
            return false;
        }
        if(imgUrl.contains("32x32")) {
            return false;
        }
        try {
            URL url = new URL(imgUrl);
            BufferedImage image = ImageIO.read(url);
            int width = image.getWidth();
            int height = image.getHeight();
            if (width < 800 || height < 800) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 过滤状态
     * @param isNnShop 是否南宁
     * @param skuStatus sku状态
     * @param specialTypeList 特殊标签
     * @return
     */
    public static boolean filterStatus(boolean isNnShop, String skuStatus, List<Integer> specialTypeList) {
        if (isNnShop) {
            return nNShopIsContainIllegalStatus(specialTypeList, skuStatus) || nnShopFilterSpecialType(true, specialTypeList);
        } else {
            return SkuStatusEnum.isContainIllegalStatus(skuStatus);
        }
    }
    /**
     * 南宁店铺过滤
     * @param specialTypeList 特殊标签
     * @param skuStatus sku状态
     * @return true 移除、 false 保留
     */
    public static boolean nNShopIsContainIllegalStatus(List<Integer> specialTypeList, String skuStatus) {
        if (SkuStatusEnum.isContainIllegalStatus(skuStatus)) {
            // 判断是否有包含nn滞销标签，有的话就不需要过滤
            return !CollectionUtils.isNotEmpty(specialTypeList) || !specialTypeList.contains(SpecialTagEnum.s_2036.code);
        }
        return false;
    }

    /**
     * 南宁店铺过滤南宁禁止标签
     * @param isNnShop 是否南宁店铺
     * @param specialTypeList 禁止标签
     * @return 是否过滤
     */
    public static boolean nnShopFilterSpecialType(boolean isNnShop, List<Integer> specialTypeList) {
        if (isNnShop && CollectionUtils.isNotEmpty(specialTypeList)) {
            return specialTypeList.contains(SpecialTagEnum.s_2041.code);
        }
        return false;
    }

}
