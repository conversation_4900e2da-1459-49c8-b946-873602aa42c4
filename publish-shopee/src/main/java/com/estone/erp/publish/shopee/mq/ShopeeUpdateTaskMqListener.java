package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.call.v2.ShopeeDiscountCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeUpdateStockPriceCallV2;
import com.estone.erp.publish.shopee.component.ShopeeItemEsBulkProcessor;
import com.estone.erp.publish.shopee.dto.ShopeeUpdateTaskMqMessage;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeItemStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeDiscountItem;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeItemEsService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeUpdateTaskRecord;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeUpdateTaskRecordService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: Shopee调价任务消息监听器
 * <AUTHOR>
 * @Date 2025/7/8 14:39:55
 */
@Slf4j
@Component
public class ShopeeUpdateTaskMqListener implements ChannelAwareMessageListener {

    @Resource
    private ShopeeUpdateTaskRecordService shopeeUpdateTaskRecordService;

    @Resource
    private ShopeeItemEsService shopeeItemEsService;

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private ShopeeItemEsBulkProcessor shopeeItemEsBulkProcessor;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到Shopee调价任务消息：{}", body);

            ShopeeUpdateTaskMqMessage taskMqMessage = JSON.parseObject(body, new TypeReference<>() {
            });

            doService(taskMqMessage);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("处理Shopee调价任务消息失败：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void doService(ShopeeUpdateTaskMqMessage taskMqMessage) {
        try {
            processShopeeUpdateTask(taskMqMessage);
        } catch (Exception e) {
            log.error("处理店铺调价任务失败，账号：{}，站点：{}，错误：{}", taskMqMessage.getAccount(), taskMqMessage.getSite(), e.getMessage(), e);
        }
    }

    private void processShopeeUpdateTask(ShopeeUpdateTaskMqMessage param) {
        String account = param.getAccount();
        String site = param.getSite();
        String taskType = param.getTaskType();
        Boolean update = param.getUpdate();

        log.info("开始处理店铺调价任务，账号：{}，站点：{}，任务类型：{}，是否更新：{}", account, site, taskType, update);

        // 验证账号是否存在
        ShopeeAccountConfig accountConfig = shopeeAccountConfigService.selectByAccountNumber(account);
        if (accountConfig == null) {
            log.warn("未找到对应的账号配置，账号：{}，站点：{}", account, site);
            return;
        }

        try {
            if (Boolean.TRUE.equals(update)) {
                // 执行价格更新逻辑
                executeUpdatePrice(param);
            } else {
                // 根据任务类型执行不同的统计逻辑
                switch (taskType) {
                    case "STATISTICS_ORIGIN_PRICE_PROFIT_RATE":
                        executeStatisticsOriginPrice(param, accountConfig);
                        break;
                    case "STATISTICS_NEW_PRICE_PROFIT_RATE":
                        executeStatisticsNewPrice(param, accountConfig);
                        break;
                    default:
                        log.warn("未知的任务类型：{}，账号：{}，站点：{}", taskType, account, site);
                        break;
                }
            }
        } catch (Exception e) {
            log.error("执行调价任务失败，账号：{}，站点：{}，错误：{}", account, site, e.getMessage(), e);
        }
    }

    private void executeUpdatePrice(ShopeeUpdateTaskMqMessage param) {
        log.info("执行价格更新，账号：{}，站点：{}", param.getAccount(), param.getSite());

        LambdaQueryWrapper<ShopeeUpdateTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopeeUpdateTaskRecord::getAccountNumber, param.getAccount())
                .in(CollectionUtils.isNotEmpty(param.getItemIds()), ShopeeUpdateTaskRecord::getItemId, param.getItemIds())
                .eq(ShopeeUpdateTaskRecord::getTaskType, 1)
                .gt(ShopeeUpdateTaskRecord::getRuleType, 0)
                .in(ShopeeUpdateTaskRecord::getStatus, List.of(1, 3));

        // 使用 TiDB 分页获取记录
        List<TidbPageMeta<Long>> pageMetaList = shopeeUpdateTaskRecordService.getPageMetaListByWrapper(wrapper);

        if (CollectionUtils.isEmpty(pageMetaList)) {
            log.info("未找到需要处理的价格更新记录，账号：{}，站点：{}", param.getAccount(), param.getSite());
            return;
        }

        for (TidbPageMeta<Long> pageMeta : pageMetaList) {
            try {
                updatePricePage(pageMeta, param);
            } catch (Exception e) {
                log.error("处理的价格更新记录失败，页码：{}，账号：{}，错误：{}",
                        pageMeta.getPageNum(), param.getAccount(), e.getMessage(), e);
            }
        }

    }

    private void updatePricePage(TidbPageMeta<Long> pageMeta, ShopeeUpdateTaskMqMessage param) {
        LambdaQueryWrapper<ShopeeUpdateTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopeeUpdateTaskRecord::getAccountNumber, param.getAccount())
                .in(CollectionUtils.isNotEmpty(param.getItemIds()), ShopeeUpdateTaskRecord::getItemId, param.getItemIds())
                .eq(ShopeeUpdateTaskRecord::getTaskType, 1)
                .gt(ShopeeUpdateTaskRecord::getRuleType, 0)
                .in(ShopeeUpdateTaskRecord::getStatus, List.of(1, 3))
                .ge(ShopeeUpdateTaskRecord::getId, pageMeta.getStartKey())
                .le(ShopeeUpdateTaskRecord::getId, pageMeta.getEndKey());


        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), param.getAccount(), true);


        List<ShopeeUpdateTaskRecord> records = shopeeUpdateTaskRecordService.list(queryWrapper);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Function<ShopeeUpdateTaskRecord, String> groupByFunction = record -> {
            if (record.getRuleType() == 2) {
                return "DISCOUNT_PRICE";
            }
            return "PRICE";
        };

        Map<String, List<ShopeeUpdateTaskRecord>> priceTypeGroups = records.stream().collect(Collectors.groupingBy(groupByFunction));
        priceTypeGroups.forEach((priceType, groupRecords) -> {
            ShopeeExecutors.productUpdateSku(() -> {
                try {
                    if ("PRICE".equals(priceType)) {
                        // 修改新原价
                        updateOriginalPrice(groupRecords, saleAccount);
                    } else if ("DISCOUNT_PRICE".equals(priceType)) {
                        // 修改新折扣价
                        updateDiscountPrice(groupRecords, saleAccount);
                    }
                } catch (Exception e) {
                    log.error("处理价格更新记录失败，记录类型：{}，错误：{}", priceType, e.getMessage(), e);
                    groupRecords.forEach(record -> {
                        recordCalculationFailure(record, "处理价格更新记录失败：" + e.getMessage());
                    });
                    shopeeUpdateTaskRecordService.updateBatchById(groupRecords, 300);
                }
            });
        });


    }

    private void updateDiscountPrice(List<ShopeeUpdateTaskRecord> recordList, SaleAccountAndBusinessResponse account) {
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }
        getShopeeDiscountItems(recordList);
        recordList.forEach(record -> {
            if (record.getDiscountId() == null) {
                recordCalculationFailure(record, "未获取到折扣信息");
                return;
            }
        });

        Map<Long, List<ShopeeUpdateTaskRecord>> discountIdGroupedRecords = recordList.stream()
                .filter(record -> Objects.nonNull(record.getDiscountId()))
                .collect(Collectors.groupingBy(ShopeeUpdateTaskRecord::getDiscountId));

        discountIdGroupedRecords.forEach((discountId, groupedRecords) -> {
            try {
                // 批量修改折扣价格
                batchUpdateDiscountPrice(discountId, groupedRecords, account);
            } catch (Exception e) {
                log.error("批量修改折扣价格失败，折扣ID：{}，错误：{}", discountId, e.getMessage(), e);
                groupedRecords.forEach(record -> {
                    recordCalculationFailure(record, "批量修改折扣价格失败：" + e.getMessage());
                });
                shopeeUpdateTaskRecordService.updateBatchById(groupedRecords, 300);
            }
        });

    }

    private void getShopeeDiscountItems(List<ShopeeUpdateTaskRecord> recordList) {
        List<String> itemIdList = recordList.stream().map(ShopeeUpdateTaskRecord::getItemId).distinct().collect(Collectors.toList());

        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemIdList(itemIdList);
        request.setIsGoods(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setQueryFields(new String[]{"itemId", "discountId"});
        List<EsShopeeItem> esShopeeItems = shopeeItemEsService.getEsShopeeItems(request);
        Map<String, EsShopeeItem> esShopeeItemMap = esShopeeItems.stream()
                .filter(item -> Objects.nonNull(item.getDiscountId()))
                .collect(Collectors.toMap(EsShopeeItem::getItemId, Function.identity(), (oldV, newV) -> newV));

        recordList.forEach(record -> {
            EsShopeeItem esShopeeItem = esShopeeItemMap.get(record.getItemId());
            if (esShopeeItem == null) {
                return;
            }
            record.setDiscountId(esShopeeItem.getDiscountId());
        });


    }

    private void batchUpdateDiscountPrice(Long discountId, List<ShopeeUpdateTaskRecord> groupedRecords, SaleAccountAndBusinessResponse account) {
        List<ShopeeDiscountItem> discountItems = groupedRecords.stream().map(item -> {
            ShopeeDiscountItem shopeeDiscountItem = new ShopeeDiscountItem();
            shopeeDiscountItem.setDiscountId(item.getDiscountId());
            shopeeDiscountItem.setItemId(Long.valueOf(item.getItemId()));
            if (item.getModelId() != null) {
                shopeeDiscountItem.setModelId(Long.valueOf(item.getModelId()));
            }
            shopeeDiscountItem.setArticleNumber(item.getSku());
            shopeeDiscountItem.setItemOriginalPrice(item.getOriginalDiscountPrice().doubleValue());
            shopeeDiscountItem.setItemPromotionPrice(item.getNewDiscountPrice().doubleValue());
            shopeeDiscountItem.setCreatedBy("admin-MY-调价任务");
            // 记录链接Id 用于更新折扣价格
            shopeeDiscountItem.setUpdateBy(String.valueOf(item.getId()));
            return shopeeDiscountItem;
        }).collect(Collectors.toList());

        // 处理报告
        List<FeedTask> feedTasks = createFeedTask(groupedRecords, ShopeeFeedTaskEnum.UPDATE_DISCOUNT.getValue());
        ApiResult<String> apiResult = callUpdateDiscountPrice(account, discountId, discountItems);
        boolean isSuccess = apiResult.isSuccess();
        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTasks, isSuccess ? FeedTaskResultStatusEnum.SUCCESS.getResultStatus() : FeedTaskResultStatusEnum.FAIL.getResultStatus(), apiResult.getErrorMsg());

        groupedRecords.forEach(record -> {
            if (isSuccess) {
                record.setStatus(2);
            } else {
                record.setStatus(3);
                Map<String, String> extraData = new HashMap<>();
                extraData.put("errorMsg", apiResult.getErrorMsg());
                record.setExtraData(JSON.toJSONString(extraData));
            }
            record.setUpdateTime(LocalDateTime.now());
        });
        shopeeUpdateTaskRecordService.updateBatchById(groupedRecords, 300);
    }

    private ApiResult<String> callUpdateDiscountPrice(SaleAccountAndBusinessResponse account, Long discountId, List<ShopeeDiscountItem> discountItems) {
        try {
            ShopeeResponse shopeeResponse = ShopeeDiscountCallV2.updateDiscountItem(account, discountId, discountItems);
            if (StringUtils.isNotBlank(shopeeResponse.getError())) {
                return ApiResult.newError(JSON.toJSONString(shopeeResponse));
            }
            // 同步Listing折扣价
            discountItems.forEach(item -> {
                shopeeItemEsBulkProcessor.updateItemDiscountPrice(item);
            });
            return ApiResult.newSuccess(shopeeResponse.getResponse());
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    private void updateOriginalPrice(List<ShopeeUpdateTaskRecord> recordList, SaleAccountAndBusinessResponse account) {
        getShopeeDiscountItems(recordList);
        recordList.forEach(record -> {
            if (record.getDiscountId() != null) {
                Long modelId = null;
                if (record.getModelId() != null) {
                    modelId = Long.valueOf(record.getModelId());
                }
                ShopeeResponse shopeeResponse = ShopeeDiscountCallV2.deleteDiscountItem(account, Long.valueOf(record.getItemId()), modelId, record.getDiscountId());
                if (StringUtils.isNotBlank(shopeeResponse.getError())) {
                    recordCalculationFailure(record, "删除折扣失败：" + shopeeResponse.getError());
                }
            }

        });


        Map<String, List<ShopeeUpdateTaskRecord>> itemIdToRecordList = recordList.stream().collect(Collectors.groupingBy(ShopeeUpdateTaskRecord::getItemId));
        itemIdToRecordList.forEach((itemId, itemList) -> {
            try {
                callUpdateOriginalPrice(itemList, account);
            } catch (Exception e) {
                log.error("修改原价失败，itemId：{}，错误：{}", itemId, e.getMessage(), e);
                recordList.forEach(record -> {
                    recordCalculationFailure(record, "修改原价失败：" + e.getMessage());
                });
                shopeeUpdateTaskRecordService.updateBatchById(recordList, 300);
            }
        });


    }

    private void callUpdateOriginalPrice(List<ShopeeUpdateTaskRecord> itemList, SaleAccountAndBusinessResponse account) {
        BiFunction<String, String, String> esIdFunction = (itemId, modelId) -> {
            if (StringUtils.isBlank(modelId)) {
                return itemId;
            }
            return itemId + "_" + modelId;
        };

        List<FeedTask> feedTask = createFeedTask(itemList, ShopeeFeedTaskEnum.UPDATE_PRICE.getValue());
        List<EsShopeeItem> shopeeItemList = itemList.stream().map(item -> {
            EsShopeeItem updateItem = new EsShopeeItem();
            updateItem.setId(esIdFunction.apply(item.getItemId(), item.getModelId()));
            updateItem.setItemId(item.getItemId());
            updateItem.setOriginalPrice(item.getModifiedOriginalPrice().doubleValue());
            updateItem.setVariationId(item.getModelId());
            return updateItem;
        }).collect(Collectors.toList());

        ShopeeResponse shopeeResponse = ShopeeUpdateStockPriceCallV2.updatePriceCall(account, shopeeItemList);
        if (StringUtils.isNotBlank(shopeeResponse.getError())) {
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(shopeeResponse));
            itemList.forEach(record -> {
                recordCalculationFailure(record, "修改原价失败：" + shopeeResponse.getError());
            });
            shopeeUpdateTaskRecordService.updateBatchById(itemList, 300);
            return;
        }

        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), JSON.toJSONString(shopeeResponse));
        itemList.forEach(record -> {
            record.setStatus(2);
            record.setUpdateTime(LocalDateTime.now());
        });
        shopeeUpdateTaskRecordService.updateBatchById(itemList, 300);
    }

    private void executeStatisticsOriginPrice(ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        log.info("执行统计逻辑，账号：{}，站点：{}", param.getAccount(), param.getSite());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(List.of(accountConfig.getAccount()), SaleChannel.CHANNEL_SHOPEE);
        Triple<String, String, String> triple = saleSuperiorMap.get(param.getAccount());
        if (triple != null && StringUtils.isNotBlank(triple.getLeft())) {
            String sellerName = triple.getLeft();
            accountConfig.setSaleIds(List.of(sellerName));
        }
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(param.getAccount());
        request.setSite(param.getSite());
        request.setIsGoods(true);
        request.setItemStatus(ShopeeItemStatusEnum.NORMAL.getCode());
        request.setQueryFields(new String[]{"id", "itemId", "variationId", "articleNumber", "spu", "itemSeller", "itemStatus", "skuStatus", "price", "originalPrice"});
        request.setOrderBy("id");
        shopeeItemEsService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            List<EsShopeeItem> shopeeItemList = listings.stream()
                    .filter(listing -> Objects.nonNull(listing.getPrice()))
                    .filter(listing -> StringUtils.isNotBlank(listing.getArticleNumber()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shopeeItemList)) {
                return;
            }
            Lists.partition(shopeeItemList, 100).forEach(shopeeItems -> {
                try {
                    calculatorOriginPriceRate(shopeeItems, param, accountConfig);
                } catch (Exception e) {
                    log.error("计算毛利率失败，账号：{}，站点：{}，错误：{}", param.getAccount(), param.getSite(), e.getMessage(), e);
                }
            });
        });
    }

    private void executeStatisticsNewPrice(ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        log.info("执行新折扣价统计逻辑，账号：{}，站点：{}", param.getAccount(), param.getSite());

        // 查询当前店铺 STATISTICS_ORIGIN_PRICE_PROFIT_RATE 执行的记录
        LambdaQueryWrapper<ShopeeUpdateTaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShopeeUpdateTaskRecord::getAccountNumber, param.getAccount())
                .eq(ShopeeUpdateTaskRecord::getTaskType, 1)
                .eq(ShopeeUpdateTaskRecord::getStatus, 0);

        // 使用 TiDB 分页获取记录
        List<TidbPageMeta<Long>> pageMetaList = shopeeUpdateTaskRecordService.getPageMetaListByWrapper(wrapper);

        if (CollectionUtils.isEmpty(pageMetaList)) {
            log.info("未找到需要处理的原折扣价记录，账号：{}，站点：{}", param.getAccount(), param.getSite());
            return;
        }

        for (TidbPageMeta<Long> pageMeta : pageMetaList) {
            try {
                processNewPricePage(pageMeta, param, accountConfig);
            } catch (Exception e) {
                log.error("处理新折扣价分页数据失败，页码：{}，账号：{}，错误：{}",
                        pageMeta.getPageNum(), param.getAccount(), e.getMessage(), e);
            }
        }
    }

    private void processNewPricePage(TidbPageMeta<Long> pageMeta, ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        // 根据分页元数据查询具体记录
        LambdaQueryWrapper<ShopeeUpdateTaskRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopeeUpdateTaskRecord::getAccountNumber, param.getAccount())
                .eq(ShopeeUpdateTaskRecord::getTaskType, 1)
                .eq(ShopeeUpdateTaskRecord::getStatus, 0)
                .ge(ShopeeUpdateTaskRecord::getId, pageMeta.getStartKey())
                .le(ShopeeUpdateTaskRecord::getId, pageMeta.getEndKey());

        List<ShopeeUpdateTaskRecord> records = shopeeUpdateTaskRecordService.list(queryWrapper);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        log.info("开始批量计算新折扣价和毛利率，账号：{}，站点：{}，记录数：{}",
                param.getAccount(), param.getSite(), records.size());

        // 批量计算新折扣价和毛利率
        batchCalculateNewPriceAndProfitRate(records, param, accountConfig);
    }

    /**
     * 批量计算新折扣价和毛利率
     * 优化点：
     * 1. 使用批量计算提高效率
     * 2. 对计算失败的数据记录失败信息至extraData
     * 3. 过滤计算失败的数据，只对成功的数据执行后续业务逻辑
     *
     * @param records       待计算的记录列表
     * @param param         消息参数
     * @param accountConfig 账号配置
     */
    private void batchCalculateNewPriceAndProfitRate(List<ShopeeUpdateTaskRecord> records, ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        // 过滤有效记录并计算新折扣价
        List<ShopeeUpdateTaskRecord> validRecords = new ArrayList<>();
        List<ShopeeUpdateTaskRecord> invalidRecords = new ArrayList<>();
        List<BatchPriceCalculatorRequest> batchRequests = new ArrayList<>();

        for (ShopeeUpdateTaskRecord record : records) {
            try {
                // 计算新折扣价：原折扣价 + 0.54
                BigDecimal originalDiscountPrice = record.getOriginalDiscountPrice();
                if (originalDiscountPrice == null) {
                    log.warn("原折扣价为空，跳过处理，记录ID：{}", record.getId());
                    recordCalculationFailure(record, "原折扣价为空");
                    invalidRecords.add(record);
                    continue;
                }

                if (StringUtils.isBlank(record.getSku())) {
                    log.warn("SKU为空，跳过处理，记录ID：{}", record.getId());
                    recordCalculationFailure(record, "SKU为空");
                    invalidRecords.add(record);
                    continue;
                }

                BigDecimal newDiscountPrice = originalDiscountPrice.add(BigDecimal.valueOf(0.54));
                record.setNewDiscountPrice(newDiscountPrice);

                // 准备批量计算请求
                BatchPriceCalculatorRequest request = new BatchPriceCalculatorRequest();
                request.setId(record.getId().toString());
                request.setArticleNumber(record.getSku());
                request.setQuantity(1);
                request.setSaleChannel(SaleChannel.CHANNEL_SHOPEE);
                request.setSite(param.getSite());
                request.setCountryCode(param.getSite());
                request.setSalePrice(newDiscountPrice.doubleValue());
                request.setShippingMethod(param.getLogisticsCode());

                batchRequests.add(request);
                validRecords.add(record);

            } catch (Exception e) {
                log.error("准备计算数据失败，记录ID：{}，错误：{}", record.getId(), e.getMessage(), e);
                recordCalculationFailure(record, "准备计算数据失败：" + e.getMessage());
                invalidRecords.add(record);
            }
        }

        // 执行批量毛利率计算
        List<ShopeeUpdateTaskRecord> successRecords = new ArrayList<>();
        List<ShopeeUpdateTaskRecord> failedRecords = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(batchRequests)) {
            try {
                ApiResult<List<BatchPriceCalculatorResponse>> result = PriceCalculatedUtil.batchPriceCalculator(batchRequests, 3);

                if (!result.isSuccess() || CollectionUtils.isEmpty(result.getResult())) {
                    log.error("批量计算毛利率失败，错误：{}", result.getErrorMsg());
                    // 将所有有效记录标记为失败
                    for (ShopeeUpdateTaskRecord record : validRecords) {
                        recordCalculationFailure(record, "批量计算毛利率失败：" + result.getErrorMsg());
                        failedRecords.add(record);
                    }
                } else {
                    // 处理批量计算结果
                    Map<String, BatchPriceCalculatorResponse> responseMap = result.getResult().stream()
                            .collect(Collectors.toMap(BatchPriceCalculatorResponse::getId, Function.identity(), (o1, o2) -> o1));

                    for (ShopeeUpdateTaskRecord record : validRecords) {
                        try {
                            BatchPriceCalculatorResponse response = responseMap.get(record.getId().toString());
                            if (response == null) {
                                recordCalculationFailure(record, "未找到对应的计算结果");
                                failedRecords.add(record);
                                continue;
                            }

                            if (BooleanUtils.isFalse(response.getIsSuccess())) {
                                recordCalculationFailure(record, "毛利率计算失败：" + response.getErrorMsg());
                                failedRecords.add(record);
                                continue;
                            }

                            Double grossProfitRate = response.getGrossProfitRate();
                            if (grossProfitRate == null) {
                                recordCalculationFailure(record, "新折扣价毛利率为空");
                                failedRecords.add(record);
                                continue;
                            }

                            BigDecimal newProfitRate = BigDecimal.valueOf(response.getGrossProfitRate()).setScale(2, RoundingMode.HALF_UP);
                            // 保存新毛利率并应用业务规则
                            record.setNewProfitRate(newProfitRate);
                            applyBusinessRules(record, accountConfig, param);
                            record.setUpdateTime(LocalDateTime.now());
                            successRecords.add(record);

                        } catch (Exception e) {
                            log.error("处理计算结果失败，记录ID：{}，错误：{}", record.getId(), e.getMessage(), e);
                            recordCalculationFailure(record, "处理计算结果失败：" + e.getMessage());
                            failedRecords.add(record);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("批量计算毛利率异常，错误：{}", e.getMessage(), e);
                // 将所有有效记录标记为失败
                for (ShopeeUpdateTaskRecord record : validRecords) {
                    recordCalculationFailure(record, "批量计算异常：" + e.getMessage());
                    failedRecords.add(record);
                }
            }
        }

        // 批量更新记录
        List<ShopeeUpdateTaskRecord> allUpdatedRecords = new ArrayList<>();
        allUpdatedRecords.addAll(successRecords);
        allUpdatedRecords.addAll(failedRecords);
        allUpdatedRecords.addAll(invalidRecords);

        if (CollectionUtils.isNotEmpty(allUpdatedRecords)) {
            shopeeUpdateTaskRecordService.updateBatchById(allUpdatedRecords);
            log.info("批量更新记录完成，总数：{}，成功：{}，失败：{}",
                    allUpdatedRecords.size(), successRecords.size(), failedRecords.size() + invalidRecords.size());
        }
    }

    private void recordCalculationFailure(ShopeeUpdateTaskRecord record, String errorMsg) {
        String extraData = record.getExtraData();
        if (StringUtils.isNotBlank(extraData)) {
            Map<String, String> extraDataMap = JSON.parseObject(extraData, new TypeReference<>() {
            });
            if (extraDataMap == null) {
                extraDataMap = new HashMap<>();
            }

            String msg = extraDataMap.getOrDefault("errorMsg", "");
            extraDataMap.put("errorMsg", msg + errorMsg);
            record.setExtraData(JSON.toJSONString(extraDataMap));
        } else {
            Map<String, String> extraDataMap = new HashMap<>();
            extraDataMap.put("errorMsg", errorMsg);
            record.setExtraData(JSON.toJSONString(extraData));
        }
        record.setStatus(3); // 设置为失败状态
        record.setUpdateTime(LocalDateTime.now());
    }


    private void applyBusinessRules(ShopeeUpdateTaskRecord record, ShopeeAccountConfig accountConfig, ShopeeUpdateTaskMqMessage param) {
        BigDecimal newDiscountPrice = record.getNewDiscountPrice();
        BigDecimal originalPrice = record.getOriginalPrice();
        BigDecimal newProfitRate = record.getNewProfitRate();

        if (newDiscountPrice == null || originalPrice == null || newProfitRate == null) {
            log.warn("价格或毛利率信息不完整，跳过规则应用，记录ID：{}", record.getId());
            recordCalculationFailure(record, "价格或毛利率信息不完整，无法应用业务规则");
            return;
        }

        // 判断新折扣价是否小于原价
        boolean isNewPriceLowerThanOriginal = newDiscountPrice.compareTo(originalPrice) < 0;

        // 5.4 当新折扣价≥原价，按店铺配置毛利率改原价
        if (!isNewPriceLowerThanOriginal) {
            record.setRuleType(4);
            record.setStatus(1); // 需要调价
            calculateTargetPriceByConfigProfitRate(record, accountConfig, param);
            return;
        }

        // 以下处理新折扣价小于原价的情况
        // 5.1 当新折扣价＜原价，且，新折扣价的毛利率＞30%，不调价
        // 毛利率阈值常量 (小数形式，0.3 = 30%, 0.15 = 15%)
        BigDecimal profit_rate_threshold_high = BigDecimal.valueOf(0.3);
        BigDecimal profit_rate_threshold_low = BigDecimal.valueOf(0.15);


        if (newProfitRate.compareTo(profit_rate_threshold_high) > 0) {
            recordCalculationFailure(record, "新折扣价小于原价且毛利率>30%，不调价");
            record.setRuleType(1);
            record.setStatus(-1); // 不调价
            return;
        }

        // 5.2 当新折扣价＜原价，且，15%＜新折扣价毛利率≤30%，按新折扣价调折扣价
        if (newProfitRate.compareTo(profit_rate_threshold_low) > 0 &&
                newProfitRate.compareTo(profit_rate_threshold_high) <= 0) {
            record.setRuleType(2);
            record.setStatus(1); // 需要调价
            record.setNewDiscountPrice(newDiscountPrice);
            return;
        }

        // 5.3 当新折扣价＜原价，且，新折扣价毛利率≤15%，按店铺配置毛利率改原价
        record.setRuleType(3);
        record.setStatus(1); // 需要调价
        calculateTargetPriceByConfigProfitRate(record, accountConfig, param);
    }

    private void calculateTargetPriceByConfigProfitRate(ShopeeUpdateTaskRecord record, ShopeeAccountConfig accountConfig, ShopeeUpdateTaskMqMessage param) {
        try {
            BigDecimal accountProfitRate = record.getAccountRate();
            if (accountProfitRate == null) {
                log.warn("店铺配置毛利率为空，使用默认值，记录ID：{}", record.getId());
                recordCalculationFailure(record, "店铺配置毛利率为空，使用默认值");
                return;
            }
            BatchPriceCalculatorRequest priceCalculatorRequest = new BatchPriceCalculatorRequest();
            priceCalculatorRequest.setId(String.valueOf(record.getId()));
            priceCalculatorRequest.setArticleNumber(record.getSku());
            priceCalculatorRequest.setQuantity(1);
            priceCalculatorRequest.setSaleChannel(SaleChannel.CHANNEL_SHOPEE);
            priceCalculatorRequest.setSite(accountConfig.getSite());
            priceCalculatorRequest.setCountryCode(accountConfig.getSite());
            priceCalculatorRequest.setGrossProfitRate(accountProfitRate.doubleValue());
            priceCalculatorRequest.setShippingMethod(param.getLogisticsCode());

            ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(List.of(priceCalculatorRequest), 3);
            if (!listApiResult.isSuccess()) {
                recordCalculationFailure(record, "调用试算器失败，错误：" + listApiResult.getErrorMsg());
                return;
            }
            List<BatchPriceCalculatorResponse> result = listApiResult.getResult();
            if (CollectionUtils.isEmpty(result)) {
                recordCalculationFailure(record, "试算器返回结果为空");
                return;
            }
            BatchPriceCalculatorResponse batchPriceCalculatorResponse = result.get(0);
            if (!batchPriceCalculatorResponse.getIsSuccess()) {
                recordCalculationFailure(record, "试算器计算失败，错误：" + batchPriceCalculatorResponse.getErrorMsg());
                return;
            }
            BigDecimal bigDecimal = BigDecimal.valueOf(batchPriceCalculatorResponse.getForeignPrice()).setScale(2, RoundingMode.UP);
            record.setModifiedOriginalPrice(bigDecimal);
            record.setAccountRate(accountProfitRate);
        } catch (Exception e) {
            log.error("计算目标价格失败，记录ID：{}，错误：{}", record.getId(), e.getMessage(), e);
            recordCalculationFailure(record, "计算目标价格失败：" + e.getMessage());
        }
    }

    private void calculatorOriginPriceRate(List<EsShopeeItem> shopeeItems, ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        List<BatchPriceCalculatorRequest> batchPriceCalculatorRequestList = shopeeItems.stream()
                .map(listing -> {
                    BatchPriceCalculatorRequest batchPriceCalculatorRequest = new BatchPriceCalculatorRequest();
                    batchPriceCalculatorRequest.setId(listing.getId());
                    batchPriceCalculatorRequest.setArticleNumber(listing.getArticleNumber());
                    batchPriceCalculatorRequest.setQuantity(1);
                    batchPriceCalculatorRequest.setSaleChannel(SaleChannel.CHANNEL_SHOPEE);
                    batchPriceCalculatorRequest.setSite(param.getSite());
                    batchPriceCalculatorRequest.setCountryCode(param.getSite());
                    batchPriceCalculatorRequest.setSalePrice(listing.getPrice());
                    batchPriceCalculatorRequest.setShippingMethod(param.getLogisticsCode());
                    return batchPriceCalculatorRequest;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(batchPriceCalculatorRequestList)) {
            return;
        }

        ApiResult<List<BatchPriceCalculatorResponse>> listApiResult = PriceCalculatedUtil.batchPriceCalculator(batchPriceCalculatorRequestList, 3);
        if (!listApiResult.isSuccess()) {
            log.error("调用试算器失败, param:{}, error:{}", JSON.toJSONString(param), listApiResult.getErrorMsg());
            return;
        }

        List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = listApiResult.getResult();
        if (CollectionUtils.isEmpty(batchPriceCalculatorResponses)) {
            return;
        }

        Map<String, BatchPriceCalculatorResponse> priceCalculatorResponseMap = batchPriceCalculatorResponses.stream().collect(Collectors.toMap(BatchPriceCalculatorResponse::getId, Function.identity(), (o1, o2) -> o1));
        List<ShopeeUpdateTaskRecord> recordList = shopeeItems.stream().map(item -> {
            BatchPriceCalculatorResponse calculatorResponse = priceCalculatorResponseMap.get(item.getId());
            if (BooleanUtils.isFalse(calculatorResponse.getIsSuccess())) {
                ShopeeUpdateTaskRecord taskRecord = addTaskRecord(item, null, param, accountConfig);
                Map<String, String> extraData = new HashMap<>();
                extraData.put("resultMsg", calculatorResponse.getErrorMsg());
                taskRecord.setStatus(3);
                taskRecord.setExtraData(JSON.toJSONString(extraData));
                return taskRecord;
            }

            double grossProfitRate = BigDecimal.valueOf(calculatorResponse.getGrossProfitRate()).setScale(2, RoundingMode.HALF_UP).doubleValue();
            return addTaskRecord(item, grossProfitRate, param, accountConfig);
        }).collect(Collectors.toList());
        shopeeUpdateTaskRecordService.saveBatch(recordList, 300);
    }

    private ShopeeUpdateTaskRecord addTaskRecord(EsShopeeItem item, Double grossProfitRate, ShopeeUpdateTaskMqMessage param, ShopeeAccountConfig accountConfig) {
        ShopeeUpdateTaskRecord taskRecord = new ShopeeUpdateTaskRecord();
        taskRecord.setAccountNumber(item.getItemSeller());
        taskRecord.setTaskType(1);
        taskRecord.setRuleType(0);
        if (CollectionUtils.isNotEmpty(accountConfig.getSaleIds())) {
            taskRecord.setSellerName(accountConfig.getSaleIds().get(0));
        }
        taskRecord.setItemId(item.getItemId());
        taskRecord.setModelId(item.getVariationId());
        taskRecord.setSpu(item.getSpu());
        taskRecord.setSku(item.getArticleNumber());
        if (accountConfig.getProfit() != null) {
            taskRecord.setAccountRate(BigDecimal.valueOf(accountConfig.getProfit()));
        }
        if (item.getOriginalPrice() != null) {
            taskRecord.setOriginalPrice(BigDecimal.valueOf(item.getOriginalPrice()));
        }
        if (item.getPrice() != null) {
            taskRecord.setOriginalDiscountPrice(BigDecimal.valueOf(item.getPrice()));
        }
        if (grossProfitRate != null) {
            taskRecord.setOriginalProfitRate(BigDecimal.valueOf(grossProfitRate));
        }
        taskRecord.setStatus(0);
        taskRecord.setCreateTime(LocalDateTime.now());
        taskRecord.setUpdateTime(LocalDateTime.now());
        return taskRecord;

    }

    private List<FeedTask> createFeedTask(List<ShopeeUpdateTaskRecord> itemPools, String taskType) {
        return itemPools.stream().map(item -> {
            FeedTask feedTask = ShopeeFeedTaskHandleUtil.createFeedTask(item.getAccountNumber(), taskType, item.getItemId(), item.getSku(), "admin");
            if (taskType.equals(ShopeeFeedTaskEnum.UPDATE_DISCOUNT.getValue())) {
                String resultMsg = "修改前折扣价格：" + item.getOriginalDiscountPrice() + "，修改后折扣价格：" + item.getNewDiscountPrice();
                feedTask.setResultMsg(resultMsg);
            } else {
                String resultMsg = "修改前价格：" + item.getOriginalPrice() + "，修改后价格：" + item.getModifiedOriginalPrice();
                feedTask.setResultMsg(resultMsg);
            }
            feedTask.setAttribute1("MY-调价任务");
            return feedTask;
        }).collect(Collectors.toList());
    }
} 