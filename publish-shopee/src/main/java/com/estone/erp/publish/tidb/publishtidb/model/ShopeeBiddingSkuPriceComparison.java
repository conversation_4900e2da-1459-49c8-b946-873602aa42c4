package com.estone.erp.publish.tidb.publishtidb.model;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Shopee竞价SKU对比价格表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shopee_bidding_sku_price_comparison")
public class ShopeeBiddingSkuPriceComparison implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 紫鸟店铺名
     */
    private String ziniuShopName;

    /**
     * 子账号
     */
    private String subAccount;

    /**
     * 商家
     */
    private String merchant;

    /**
     * 商家id
     */
    private String merchantId;

    /**
     * 店铺
     */
    private String account;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 站点
     */
    private String site;

    /**
     * SPU编号
     */
    private String spu;

    /**
     * SKU编码
     */
    private String sku;

    /**
     * Item ID
     */
    private String itemId;

    /**
     * Model ID
     */
    private String modelId;

    /**
     * 现SKU价格
     */
    private BigDecimal currentSkuPrice;

    /**
     * 现SKU毛利率（百分比）
     */
    private BigDecimal currentSkuGrossMargin;

    /**
     * 竞品链接价格
     */
    private BigDecimal competitorLinkPrice;

    /**
     * 竞品链接库存
     */
    private Integer competitorLinkStock;

    /**
     * 竞品链接
     */
    private String competitorLink;

    /**
     * 竞品属性值
     */
    private String competitorAttributes;

    /**
     * 链接属性值
     */
    private String linkAttributes;

    /**
     * 改后价格
     */
    private BigDecimal adjustedPrice;

    /**
     * 改后价格毛利率（百分比）
     */
    private BigDecimal adjustedPriceGrossMargin;

    /**
     * 导入时间
     */
    private LocalDateTime importTime;

    /**
     * 确认状态（0-确认不调整，1-待确认，2-已确认）
     * @see com.estone.erp.publish.shopee.enums.ShopeeConfirmationStatusEnum
     */
    private Integer confirmationStatus;

    /**
     * 确认修改时间
     */
    private LocalDateTime confirmationTime;

    /**
     * 调价状态（1-成功，2-失败，3-处理中）
     * @see com.estone.erp.publish.shopee.enums.ShopeeAdjustmentStatusEnum
     */
    private Integer adjustmentStatus;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 调价失败备注
     */
    private String adjustmentFailureRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    private String updatedBy;


}
