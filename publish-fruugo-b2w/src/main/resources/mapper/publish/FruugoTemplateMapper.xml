<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.fruugo.mapper.FruugoTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.fruugo.model.FruugoTemplate" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="product_type" property="productType" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="system_category" property="systemCategory" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="VARCHAR" />
    <result column="prohibitions" property="prohibitions" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="tags" property="tags" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_name" property="categoryName" jdbcType="VARCHAR" />
    <result column="category_attribute_configs" property="categoryAttributeConfigs" jdbcType="VARCHAR" />
    <result column="manufacture" property="manufacture" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="specifications" property="specifications" jdbcType="VARCHAR" />
    <result column="specification_details" property="specificationDetails" jdbcType="VARCHAR" />
    <result column="product_images" property="productImages" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="template_status" property="templateStatus" jdbcType="INTEGER" />
    <result column="correlation_id" property="correlationId" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="ce_mark" property="ceMark" jdbcType="VARCHAR" />
    <result column="safety_warnings" property="safetyWarnings" jdbcType="VARCHAR" />
    <result column="ingredients" property="ingredients" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="VARCHAR" />

  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, article_number, product_type, `status`, system_category, `state`, 
    prohibitions, remark, tags, category_id, category_name, category_attribute_configs, 
    manufacture, brand, specifications, specification_details, product_images, created_by, 
    create_time, update_by, update_time, template_status, correlation_id, title, ce_mark,
    safety_warnings, ingredients, product_id
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.fruugo.model.FruugoTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List"/>
      </otherwise>
    </choose>
    from ${tableName}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from fruugo_template
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.fruugo.model.FruugoTemplate" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ${tableName} (account_number, article_number, product_type,
      `status`, system_category, `state`, 
      prohibitions, remark, tags, 
      category_id, category_name, category_attribute_configs, 
      manufacture, brand, specifications, 
      specification_details, product_images, created_by, 
      create_time, update_by, update_time, template_status,
      correlation_id,title,ce_mark,safety_warnings,ingredients,product_id)
    values (#{accountNumber,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{productType,jdbcType=INTEGER},
      #{status,jdbcType=INTEGER}, #{systemCategory,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR},
      #{prohibitions,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR},
      #{categoryId,jdbcType=INTEGER}, #{categoryName,jdbcType=VARCHAR}, #{categoryAttributeConfigs,jdbcType=VARCHAR},
      #{manufacture,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR},
      #{specificationDetails,jdbcType=VARCHAR}, #{productImages,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{templateStatus,jdbcType=INTEGER},#{correlationId,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR},
        #{ceMark,jdbcType=VARCHAR},#{safetyWarnings,jdbcType=VARCHAR},#{ingredients,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR})
  </insert>
  <insert id="batchInsert">
      insert into fruugo_template (account_number, article_number, product_type,
      `status`, system_category, `state`,
      prohibitions, remark, tags,
      category_id, category_name, category_attribute_configs,
      manufacture, brand, specifications,
      specification_details, product_images, created_by,
      create_time, update_by, update_time,
      template_status,correlation_id,title,ce_mark,safety_warnings,ingredients,product_id
      )
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.accountNumber,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR},
          #{item.productType,jdbcType=INTEGER},
          #{item.status,jdbcType=INTEGER}, #{item.systemCategory,jdbcType=VARCHAR}, #{item.state,jdbcType=VARCHAR},
          #{item.prohibitions,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.tags,jdbcType=VARCHAR},
          #{item.categoryId,jdbcType=INTEGER}, #{item.categoryName,jdbcType=VARCHAR},
          #{item.categoryAttributeConfigs,jdbcType=VARCHAR},
          #{item.manufacture,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.specifications,jdbcType=VARCHAR},
          #{item.specificationDetails,jdbcType=VARCHAR}, #{item.productImages,jdbcType=VARCHAR},
          #{item.createdBy,jdbcType=VARCHAR},
          #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
          #{item.updateTime,jdbcType=TIMESTAMP}, #{item.templateStatus,jdbcType=INTEGER},
           #{item.correlationId,jdbcType=VARCHAR},#{item.title,jdbcType=VARCHAR},
           #{item.ceMark,jdbcType=VARCHAR},#{item.safetyWarnings,jdbcType=VARCHAR},
           #{item.ingredients,jdbcType=VARCHAR},#{item.productId,jdbcType=VARCHAR}
          )
      </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.fruugo.model.FruugoTemplateExample" resultType="java.lang.Integer" >
    select count(*) from ${tableName}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="selectAccountNumberBySpuList" resultType="java.lang.String">
    select distinct article_number from fruugo_template_model
    where article_number in
    <foreach collection="spuList" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
    </select>
  <select id="getPublishSpuModelPageTotal" resultType="java.lang.Integer">
    select count(distinct article_number)
    from fruugo_template_model
    <where>
    <if test="request.isEnable != null">
      and template_status = #{request.isEnable}
    </if>
    <if test="request.starUpdateTime != null and request.endUpdateTime != null">
      and update_time between #{request.starUpdateTime} and #{request.endUpdateTime}
    </if>
    </where>
  </select>
  <select id="getPublishSpuModelPage" resultMap="BaseResultMap">
    select article_number, group_concat(template_status) as remark
    from fruugo_template_model
    <where>
      <if test="request.isEnable != null">
        and template_status = #{request.isEnable}
      </if>
      <if test="request.starUpdateTime != null and request.endUpdateTime != null">
        and update_time between #{request.starUpdateTime} and #{request.endUpdateTime}
      </if>
    </where>
    group by article_number limit #{offset}, #{limit};
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ${example.tableName}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null" >
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.systemCategory != null" >
        system_category = #{record.systemCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null" >
        `state` = #{record.state,jdbcType=VARCHAR},
      </if>
      <if test="record.prohibitions != null" >
        prohibitions = #{record.prohibitions,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null" >
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryName != null" >
        category_name = #{record.categoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryAttributeConfigs != null" >
        category_attribute_configs = #{record.categoryAttributeConfigs,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacture != null" >
        manufacture = #{record.manufacture,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null" >
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.specificationDetails != null" >
        specification_details = #{record.specificationDetails,jdbcType=VARCHAR},
      </if>
      <if test="record.productImages != null" >
        product_images = #{record.productImages,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.templateStatus != null" >
        template_status = #{record.templateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.correlationId != null" >
        correlation_id = #{record.correlationId,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.ceMark != null" >
        ce_mark = #{record.ceMark,jdbcType=VARCHAR},
      </if>
      <if test="record.safetyWarnings != null" >
        safety_warnings = #{record.safetyWarnings,jdbcType=VARCHAR},
      </if>
      <if test="record.ingredients != null" >
        ingredients = #{record.ingredients,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=VARCHAR}
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.fruugo.model.FruugoTemplate" >
    update ${tableName}
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="productType != null" >
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="systemCategory != null" >
        system_category = #{systemCategory,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        `state` = #{state,jdbcType=VARCHAR},
      </if>
      <if test="prohibitions != null" >
        prohibitions = #{prohibitions,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="tags != null" >
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="categoryAttributeConfigs != null" >
        category_attribute_configs = #{categoryAttributeConfigs,jdbcType=VARCHAR},
      </if>
      <if test="manufacture != null" >
        manufacture = #{manufacture,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null" >
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="specificationDetails != null" >
        specification_details = #{specificationDetails,jdbcType=VARCHAR},
      </if>
      <if test="productImages != null" >
        product_images = #{productImages,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateStatus != null" >
        template_status = #{templateStatus,jdbcType=INTEGER},
      </if>
      <if test="correlationId != null" >
        correlation_id = #{correlationId,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="ceMark != null" >
        ce_mark = #{ceMark,jdbcType=VARCHAR},
      </if>
      <if test="safetyWarnings != null" >
        safety_warnings = #{safetyWarnings,jdbcType=VARCHAR},
      </if>
      <if test="ingredients != null" >
        ingredients = #{ingredients,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=VARCHAR}
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateModelStatus">
    update fruugo_template_model
    <set>
      <if test="dto.enable != null">
        template_status = #{dto.enable},
      </if>
      <if test="dto.username != null">
        update_by = #{dto.username},
      </if>
      <if test="dto.createTime != null">
        update_time = #{dto.createTime},
      </if>
    </set>
    where id in
    <foreach collection="dto.ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>
