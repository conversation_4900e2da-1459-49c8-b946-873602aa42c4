package com.estone.erp.publish.fruugo.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class FruugoTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键,模板编号
     */
    private Integer id;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 商品类型 0:管理单品
     */
    private Integer productType;

    /**
     * 状态 0:待刊登 1:刊登成功 2:刊登失败 3:刊登中
     */
    private Integer status;

    /**
     * 系统分类
     */
    private String systemCategory;

    /**
     * 单品状态
     */
    private String state;

    /**
     * 禁售信息
     */
    private String prohibitions;

    /**
     * 刊登备注
     */
    private String remark;

    /**
     * 产品标签
     */
    private String tags;

    /**
     * 平台分类ID
     */
    private Integer categoryId;

    /**
     * 平台分类名称
     */
    private String categoryName;

    /**
     * 产品属性
     */
    private String categoryAttributeConfigs;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 规格详情信息
     */
    private String specificationDetails;

    /**
     * 商品媒体信息
     */
    private String productImages;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 模版状态 0-禁用，1-启用
     */
    private Integer templateStatus;

    /**
     * 关联id
     */
    private String correlationId;

    /**
     * 表名
     */
    private  String tableName;

    /**
     * 标题
     */
    private String title;


    //定时队列id，table_false_exist
    private Integer queueId;

    /**
     * ce标识
     */
    private String ceMark;

    /**
     * 安全警告
     */
    private String safetyWarnings;

    /**
     * 原材料
     */
    private String ingredients;


    /**
     * 提交平台productID
     */
    private String productId;



}