package com.estone.erp.publish.fruugo.model.vo;


import com.estone.erp.publish.fruugo.model.dto.FruugoTemplateProductDetailDto;
import com.estone.erp.publish.fruugo.model.dto.FruugoTemplateSkuImageDto;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Data
public class FruugoTemplateInfoVO {
    /**
     * 主键,模板编号
     */
    private Integer id;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 店铺后缀
     */
    private String accountSite;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * ean码前缀
     */
    private String eanPrefix;

    /**
     * 商品类型 0:管理单品
     */
    private Integer productType;

    /**
     * 状态 0:待刊登 1:刊登成功 2:刊登失败 3:刊登中
     */
    private Integer status;

    /**
     * 系统分类
     */
    private String systemCategory;

    /**
     * 单品状态
     */
    private String state;

    /**
     * 禁售信息
     */
    private String prohibitions;

    /**
     * 刊登备注
     */
    private String remark;

    /**
     * 产品标签
     */
    private String tags;

    /**
     * 平台分类ID
     */
    private Integer categoryId;

    /**
     * 平台分类名称
     */
    private String categoryName;

    /**
     * 产品属性
     */
    private String categoryAttributeConfigs;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 规格
     */
    private String specifications;


    /**
     * 产品详情
     */
    private List<FruugoTemplateProductDetailDto> details;

    /**
     * 图片信息
     */

    private List<FruugoTemplateSkuImageDto> productImages;


    /**
     * 禁售信息
     */
    private Map<String,List<String>> forbiddenInfoMap;

    /**
     * 图片池
     */
    private List<String> imagePool;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Timestamp updateTime;


    /**
     * 队列id
     */
    private Integer queueId;


    /**
     * ce标识
     */
    private String ceMark;

    /**
     * 安全警告
     */
    private String safetyWarnings;

    /**
     * 原材料
     */
    private String ingredients;

    /**
     * productId
     */
    private String productId;

}



