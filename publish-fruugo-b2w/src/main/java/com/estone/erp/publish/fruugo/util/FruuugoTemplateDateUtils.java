package com.estone.erp.publish.fruugo.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.MapUtil;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.PublishRedissonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.fruugo.constant.FruugoRedisConstant;
import com.estone.erp.publish.fruugo.model.dto.FruugoTemplateProductDetailDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.platform.util.SimilarityRatioUtils;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringementWordSource;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.util.CommonMatchProdInfoUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class FruuugoTemplateDateUtils {


    private static final InfringementWordService infringementWordService = SpringUtils.getBean(InfringementWordService.class);

    /**
     * 标题：
     * 优先级：长标题>短标题>sku，将SKU对应的color和size加到标题最后，
     * 超出限制时进行截取（加完color和size后，超出限制再截取）
     * 限制1-150字符
     * 描述：
     * 优先级：描述新>描述，限制1-150字符
     *
     * @param spuOfficial
     * @param fruugoTemplateProductDetailDto
     * @param productInfo
     */
    public static void setTitleAndDesc(SpuOfficial spuOfficial, FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto, ProductInfo productInfo) {
        List<String> titleList = null;

        if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
            titleList = parsingStrList(spuOfficial.getLongTitleJson());
        }

        if (CollectionUtils.isEmpty(titleList) && StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
            titleList = parsingStrList(spuOfficial.getShortTitleJson());
        }
        if (CollectionUtils.isEmpty(titleList) && StringUtils.isNotBlank(spuOfficial.getTitle())) {
            titleList = parsingStrList(spuOfficial.getTitle());
        }
        String title = null;
        if (CollectionUtils.isNotEmpty(titleList)) {
            Collections.shuffle(titleList);
            title = titleList.get(0);
        } else {
            title = productInfo.getTitleEn();
        }

        if (StringUtils.isNotBlank(title)) {
            //标题差异化
            title = fruugoTitleDifferentiation(spuOfficial.getSpu(), title, productInfo);
            //拼接尺寸颜色
            String colorAndSize = getColorAndSize(productInfo.getSaleAtts());
            //不为空就拼接
            if (StringUtils.isNotBlank(colorAndSize)) {
                title = title + " " + colorAndSize;
            }else {
                title = title + " " + "Multicolor";
            }

            title = truncateToMaxLength(title, 150);
            log.info("拼接尺寸颜色后：" + title);
            title = truncateToMaxLength(title, 150);
            log.info("截取之后title：" + title);


        }
        fruugoTemplateProductDetailDto.setTitle(title);

        String desc = spuOfficial.getNewDescription();
        desc = StringUtils.isNotBlank(desc) ? desc : spuOfficial.getDescription();
        desc = truncateToMaxLength(desc, 5000);
        fruugoTemplateProductDetailDto.setDescription(desc);
    }


    /**
     * fruugo标题差异化：相同程度大于90%的判断，大于90%就去取词增加关键词，过滤包含数字的关键词
     */
    private static String fruugoTitleDifferentiation(String spu, String title, ProductInfo productInfo) {

        String desEn = productInfo.getDesEn();
        if (StringUtils.isBlank(desEn)) {
            return title;
        }

        float similarityRatio = SimilarityRatioUtils.getSimilarityRatio(desEn.toLowerCase().toLowerCase(), title.toLowerCase());

        if ( similarityRatio <= 0.9) {
            return title;
        }

        //-- 查询spu信息
        ResponseJson rsp = ProductUtils.findSpuInfo(spu);
        if (!rsp.isSuccess()) {
            throw new RuntimeException(rsp.getMessage());
        }
        List<SpuInfo> spuInfos = (List<SpuInfo>) rsp.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(spuInfos)) {
            throw new RuntimeException("获取spu信息为空！");
        }
        SpuInfo spuInfo = spuInfos.get(0);

        Set<String> searchDatasSet = new HashSet<>();

        if (StringUtils.isNotEmpty(spuInfo.getPublishKeyWord())) {
            List<String> searchDataList = JSONArray.parseArray(spuInfo.getPublishKeyWord(), String.class);
            for (String string : searchDataList) {
                String s = string.replaceAll("\n", ",").replaceAll("\r", ",");
                List<String> strings1 = CommonUtils.splitList(s, ",");
                for (String s1 : strings1) {
                    if (!s1.contains(":")) {
                        searchDatasSet.add(s1);
                    }
                }
            }
        }

        // 沃尔玛标题差异化自动组合关键词
        String result = CommonMatchProdInfoUtil.diffTitleAutoKeyword(title, searchDatasSet);
        if (StringUtils.isNotBlank(title)) {
            return result;
        }
        return title;
    }

    private static String getColorAndSize(String saleAtts) {
        StringBuilder colorAndSize = new StringBuilder();
        JSONArray attributeArray = JSON.parseArray(saleAtts);
        if (CollectionUtils.isEmpty(attributeArray)) {
            return colorAndSize.toString();
        }
        for (int i = 0; i < attributeArray.size(); i++) {
            JSONObject attributeJson = attributeArray.getJSONObject(i);
            String enName = attributeJson.getString("enName");
            String enValue = attributeJson.getString("enValue");
            if (StringUtils.isNotBlank(enName) && StringUtils.isNotBlank(enValue) && ("color".equalsIgnoreCase(enName) || "size".equalsIgnoreCase(enName))) {
                colorAndSize.append(" ").append(enValue);
            }
        }
        return colorAndSize.toString();
    }

    private static List<String> parsingStrList(String titleJson) {
        List<String> longTitles = JSON.parseObject(titleJson, new TypeReference<>() {
        });
        return longTitles.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    public static String truncateToMaxLength(String input, int maxLength) {

        if (input == null || input.length() <= maxLength) {
            return input;
        }

        String truncated = input.substring(0, maxLength);

        int lastSpaceIndex = truncated.lastIndexOf(" ");

        if (lastSpaceIndex != -1) {
            return truncated.substring(0, lastSpaceIndex);
        } else {
            return truncated;
        }
    }

    public static String generateGtin(String type, String prefix, String sku) {
        StringBuilder stringBuilder = new StringBuilder();
        switch (type) {
            case "EAN":
//                stringBuilder.append("1");
                stringBuilder.append(prefix);
                withSkuId(stringBuilder, sku);
                withCheckCode(stringBuilder);
                break;
        }
        return stringBuilder.toString();
    }

    private static void withSkuId(StringBuilder sb, String sku) {
        ResponseJson rsp = ProductUtils.findSkuInfo(sku);
        if (rsp.isSuccess()) {
            ProductInfo productInfo = (ProductInfo) rsp.getBody().get(ProductUtils.resultKey);
            String id = productInfo.getId();
            id = handleNum(id);
            sb.append(id);
        }
    }

    /**
     * 字符串位数不够补0
     *
     * @param str
     * @return
     */
    private static String handleNum(String str) {
        // 位数限制
        int length = 8;
        for (int i = str.length(); i < length; i++) {
            str = "0" + str;
        }
        return str;
    }


    public static void withCheckCode(StringBuilder sb) {
        // 非ean-13码不做校验
        if (sb.length() != 12) {
            return;
        }

        Integer[] bits = new Integer[sb.length()];
        for (int i = 0; i < sb.length(); i++) {
            bits[i] = Integer.valueOf(String.valueOf(sb.charAt(i)));
        }

        int c1 = (bits[1] + bits[3] + bits[5] + bits[7] + bits[9] + bits[11]) * 3;
        int c2 = bits[0] + bits[2] + bits[4] + bits[6] + bits[8] + bits[10];
        int cc = (c1 + c2) % 10;

        sb.append(cc == 0 ? 0 : 10 - cc);
    }

    /**
     * 匹配主图,默认从子SKU-00，子SKU-000，-KD，-kd，SKU同名图片中随机取一张图片作为主图
     *
     * @param sonSku
     * @param fruugoImages
     * @return
     */
    public static String matchMainImage(String sonSku, List<String> fruugoImages) {
        if (CollectionUtils.isEmpty(fruugoImages)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        for (String url : fruugoImages) {
            int lastSlashIndex = url.lastIndexOf("/");
            if (lastSlashIndex != -1) {
                String fileName = url.substring(lastSlashIndex + 1);
                fileName = fileName.substring(0, fileName.length() - 4);
                map.put(fileName, url);
            }
        }

        List<String> candidateImages = new ArrayList<>();
        if (StringUtils.isNotBlank(map.get(sonSku + "-00"))) {
            candidateImages.add(map.get(sonSku + "-00"));
        }
        if (StringUtils.isNotBlank(map.get(sonSku + "-000"))) {
            candidateImages.add(map.get(sonSku + "-000"));
        }
        if (StringUtils.isNotBlank(map.get(sonSku + "-KD"))) {
            candidateImages.add(map.get(sonSku + "-KD"));
        }
        if (StringUtils.isNotBlank(map.get(sonSku + "-kd"))) {
            candidateImages.add(map.get(sonSku + "-kd"));
        }
        if (StringUtils.isNotBlank(map.get(sonSku))) {
            candidateImages.add(map.get(sonSku));
        }
        if (!candidateImages.isEmpty()) {
            Random random = new Random();
            return candidateImages.get(random.nextInt(candidateImages.size()));
        }

        return fruugoImages.get(new Random().nextInt(fruugoImages.size()));
    }

    /**
     * 限制附图最多5张，选取5张除主图外的图片；
     * 附图顺序：第一张取以effect结尾的图片
     * 第二张取cmb结尾的图片
     * 其余三张随机取除主图外的图片补全
     * 若无effect或cmb图片，使用其他图片进行补全
     *
     * @param fruugoImages
     * @param articleNumber
     * @return
     */
    public static List<String> matchAttachImage(List<String> fruugoImages, String articleNumber) {
        List<String> imagList = new ArrayList<>(fruugoImages);
        //获取SKU的所有主图
        List<String> mainImageList = getMainImageList(imagList, articleNumber);
        // 排除主图
        if (CollectionUtils.isNotEmpty(mainImageList)) {
            imagList.removeAll(mainImageList);
        }

        List<String> selectedImages = new ArrayList<>();

        // 1. 选择以"effect"结尾的图片
        String effectImage = imagList.stream()
                .filter(image -> image.contains("-effect"))
                .findAny()
                .orElse(null);
        if (effectImage != null) {
            selectedImages.add(effectImage);
            imagList.remove(effectImage);
        }

        // 2. 选择以"cmb"结尾的图片
        String cmbImage = imagList.stream()
                .filter(image -> image.contains("-cmb"))
                .findAny()
                .orElse(null);
        if (cmbImage != null) {
            selectedImages.add(cmbImage);
            imagList.remove(cmbImage);
        }

        // 3. 随机选择其他图片补全
        Random random = new Random();
        while (selectedImages.size() < 5 && !imagList.isEmpty()) {
            int randomIndex = random.nextInt(imagList.size());
            selectedImages.add(imagList.get(randomIndex));
            imagList.remove(randomIndex);
        }
        return selectedImages;

    }

    private static List<String> getMainImageList(List<String> fruugoImages, String articleNumber) {
        List<String> mainImageList = new ArrayList<>();
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber));
        List<String> skuList = productInfoList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        Set<String> skuSet = new HashSet<>(skuList);
        Set<String> skuVariants = new HashSet<>();

        for (String sku : skuList) {
            skuVariants.add(sku + "-00");
            skuVariants.add(sku + "-000");
        }

        for (String url : fruugoImages) {
            int lastSlashIndex = url.lastIndexOf("/");
            if (lastSlashIndex != -1) {
                String fileName = url.substring(lastSlashIndex + 1);
                fileName = fileName.substring(0, fileName.length() - 4);

                if (skuSet.contains(fileName)) {
                    mainImageList.add(url);
                }
                if (fileName.equals(articleNumber)) {
                    mainImageList.add(url);
                }
                if (skuVariants.contains(fileName)) {
                    mainImageList.add(url);
                }
            }
        }

        return mainImageList;
    }


    /**
     * 检验标题描述属性值是有有侵权词
     *
     * @param templateInfoVO
     */
    public static Map<String, String> checkInfringmentWord(FruugoTemplateInfoVO templateInfoVO) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();
            if (CollectionUtils.isEmpty(details)) {
                throw new BusinessException("没有相关SKU商品信息");
            }

            //标题
            CompletableFuture<Void> titleFuture = CompletableFuture.runAsync(() -> {
                String titleString = details.stream().map(FruugoTemplateProductDetailDto::getTitle).collect(Collectors.joining(StrConstant.CHECK_INFRING_WORD_SPLIT));
                Map<String, Integer> infringWordAndBrand = getInfringWordAndBrand(titleString);
                if (MapUtil.isNotEmpty(infringWordAndBrand)) {
                    resultMap.put("标题", StringUtils.join(infringWordAndBrand.keySet(), ","));
                }
            }, FruugoExecutors.CHECK_INFRINGEMENT_WORDS_POOL);

            //描述
            CompletableFuture<Void> descFuture = CompletableFuture.runAsync(() -> {
                String descString = details.stream().map(FruugoTemplateProductDetailDto::getDescription).collect(Collectors.joining(StrConstant.CHECK_INFRING_WORD_SPLIT));
                Map<String, Integer> infringWordAndBrand = getInfringWordAndBrand(descString);
                if (MapUtil.isNotEmpty(infringWordAndBrand)) {
                    resultMap.put("描述", StringUtils.join(infringWordAndBrand.keySet(), ","));
                }
            }, FruugoExecutors.CHECK_INFRINGEMENT_WORDS_POOL);


            //属性
            CompletableFuture<Void> attributeFuture = CompletableFuture.runAsync(() -> {
                String attributes = details.stream()  // 遍历 details 列表
                        .filter(detail -> detail.getAttributes() != null)
                        .flatMap(detail -> detail.getAttributes().stream())
                        .map(attribute -> attribute.getName() + StrConstant.CHECK_INFRING_WORD_SPLIT + attribute.getValue())
                        .collect(Collectors.joining(StrConstant.CHECK_INFRING_WORD_SPLIT));
                Map<String, Integer> infringWordAndBrand = getInfringWordAndBrand(attributes);
                if (MapUtil.isNotEmpty(infringWordAndBrand)) {
                    resultMap.put("属性", StringUtils.join(infringWordAndBrand.keySet(), ","));
                }

            }, FruugoExecutors.CHECK_INFRINGEMENT_WORDS_POOL);
            //品牌
            CompletableFuture<Void> brandFuture = CompletableFuture.runAsync(() -> {
                Map<String, Integer> infringWordAndBrand = getInfringWordAndBrand(templateInfoVO.getBrand());
                if (MapUtil.isNotEmpty(infringWordAndBrand)) {
                    resultMap.put("品牌", StringUtils.join(infringWordAndBrand.keySet(), ","));
                }
            }, FruugoExecutors.CHECK_INFRINGEMENT_WORDS_POOL);

            CompletableFuture.allOf(titleFuture, descFuture, attributeFuture, brandFuture).join();

            return resultMap;

        } catch (Exception e) {
            log.error("校验侵权词错误", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public static Map<String, Integer> getInfringWordAndBrand(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        //过滤白名单
        String afterText = filterWhiteWords(text);
        if (StringUtils.isBlank(afterText)) {
            return null;
        } else {
            text = afterText;
        }

        Map<String, Integer> results = new HashMap<>();
        SearchVo searchVo = new SearchVo();
        searchVo.setPlatform(SaleChannel.CHANNEL_FRUUGO);
        searchVo.setText(text);
        ApiResult<InfringmentResponse> apiResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
        if (!apiResult.isSuccess()) {
            throw new RuntimeException(apiResult.getErrorMsg());
        }
        InfringmentResponse result = apiResult.getResult();
        Map<String, Integer> infringementMap = result.getInfringementMap();
        if (MapUtils.isNotEmpty(infringementMap)) {
            results.putAll(infringementMap);
        }
        Map<String, InfringementWordSource> brandWordSourceMap = result.getBrandWordSourceMap();
        if (MapUtils.isNotEmpty(brandWordSourceMap)) {
            Map<String, Integer> transformedMap = brandWordSourceMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().getIsIgnore()
                    ));
            results.putAll(transformedMap);
        }
        return results;
    }

    private static String filterWhiteWords(String text) {
        //获取白名单
        String whiteString = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_FRUUGO, "FRUUGO", "brand_word_white_list", 10);
        String afterText = "";
        //过滤白名单
        if (StringUtils.isNotBlank(whiteString)) {
            List<String> whiteList = List.of(whiteString.split("[,，]"));
            //从text中取掉whiteList存在的
            String patternString = String.join("|", whiteList.stream()
                    .map(word -> "\\b" + Pattern.quote(word) + "\\b")
                    .collect(Collectors.toList()));
            afterText = text.replaceAll("(?i)" + patternString, "");
        }
        return afterText;
    }


    public static void filterInfringementWord(FruugoTemplateInfoVO templateInfoVO) {
        Map<String, String> stringIntegerMap = FruuugoTemplateDateUtils.checkInfringmentWord(templateInfoVO);
        if (org.apache.commons.collections.MapUtils.isEmpty(stringIntegerMap)) {
            return;
        }
        List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();
        for (FruugoTemplateProductDetailDto detail : details) {
            //过滤侵权词
            String newTitle = infringementWordService.delInfringementWord(detail.getTitle(), FruuugoTemplateDateUtils.getInfringWordAndBrand(detail.getTitle()));
            String newDescription = infringementWordService.delInfringementWord(detail.getDescription(), FruuugoTemplateDateUtils.getInfringWordAndBrand(detail.getDescription()));
            detail.setTitle(newTitle);
            detail.setDescription(newDescription);
        }
    }


    /**
     * 检查是否重复刊登了
     *
     * @param accountNumber 店铺
     * @param spu           spu
     */
    public static void checkLock(String accountNumber, String spu) {
        // spu 分布式锁
        String key = FruugoRedisConstant.publishLimitKey + "::" + accountNumber + "::" + spu;
        boolean b = PublishRedissonUtils.tryLock(key, TimeUnit.MINUTES, 0, 3);
        if (!b) {
            throw new RuntimeException("重复刊登");
        }
    }

    public static void deleteLock(String accountNumber, String spu) {
        // spu 分布式锁
        String key = FruugoRedisConstant.publishLimitKey + "::" + accountNumber + "::" + spu;
        PublishRedisClusterUtils.del(key);
    }
}
