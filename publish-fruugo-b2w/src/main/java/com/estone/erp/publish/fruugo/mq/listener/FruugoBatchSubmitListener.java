package com.estone.erp.publish.fruugo.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PublishRedissonUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.fruugo.call.FruugoCallV1Service;
import com.estone.erp.publish.fruugo.call.model.request.CreateProductRequest;
import com.estone.erp.publish.fruugo.constant.FruugoRedisConstant;
import com.estone.erp.publish.fruugo.enums.FruugoFeedTaskEnum;
import com.estone.erp.publish.fruugo.model.FruugoBatchPublish;
import com.estone.erp.publish.fruugo.model.FruugoBatchPublishExample;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.service.*;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.fruugo.mq.listener
 * @Author: sj
 * @CreateTime: 2025-06-03  15:07
 * @Description: TODO
 */

@Slf4j
@Component
public class FruugoBatchSubmitListener implements ChannelAwareMessageListener {

    @Resource
    private FruugoBatchPublishService fruugoBatchPublishService;

    @Resource
    private FruugoCallV1Service fruugoCallV1Service;

    @Resource
    private FruugoFeedTaskService fruugoFeedTaskService;

    @Resource
    private FruugoTimePublishQueueService fruugoTimePublishQueueService;

    @Resource
    private FruugoTemplateService fruugoTemplateService;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        String accountNumber = JSON.parseObject(body).getString("accountNumber");
        String intervalTime = JSON.parseObject(body).getString("intervalTime");
        Integer batchCount = JSON.parseObject(body).getInteger("batchCount");
        log.debug("queue[{}]: {}", "FRUUGO_BATCH_SUBMIT_QUEUE", accountNumber);

        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, accountNumber);
        if (ObjectUtils.isEmpty(saleAccountByAccountNumber)) {
            log.error("店铺不存在或者非Fruugo新店铺！");
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        boolean lock = false;
        String key = FruugoRedisConstant.batchSubmitLimitKey + accountNumber;
        try {
            lock = PublishRedissonUtils.attemptsTryLock(key, TimeUnit.SECONDS, 3, -1, 0);
            if (!lock) {
                log.warn("{}批量提交获取锁失败", accountNumber);
                return;
            }

            int limit = 500;
            int offset = 0;
            while (true) {
                List<FruugoBatchPublish> fruugoBatchPublishList = this.getFruugoBatchPublishList(accountNumber, limit, offset);
                if (CollectionUtils.isEmpty(fruugoBatchPublishList)) {
                    break;
                }
                //按一百个分批一份
                List<List<FruugoBatchPublish>> batches = splitIntoBatches(fruugoBatchPublishList);
                for (List<FruugoBatchPublish> batch : batches) {
                    if (batch.size() >= batchCount) {
                        submitData(saleAccountByAccountNumber, batch);
                    } else {
                        //判断最后一条数据与当前时间间隔是否超过30分钟
                        if (batch.get(batch.size() - 1).getCreateTime().getTime() + 1000L * 60 * Integer.parseInt(intervalTime) < System.currentTimeMillis()) {
                            submitData(saleAccountByAccountNumber, batch);
                        }
                    }
                }
                offset += limit;
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("自动刊登消息消费异常：message:{}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        } finally {
            if (lock) {
                try {
                    PublishRedissonUtils.unlock(key);
                } catch (Exception e) {
                    log.warn("释放加锁失败,店铺:{}", accountNumber);
                }
            }
        }



    }

    private void submitData(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, List<FruugoBatchPublish> batch) {

        List<FeedTask> feedTasks = null;
        List<Integer> queueIds = null;
        List<Integer> templateIdList = null;
        try {
            List<String> templateIds = batch.stream().map(i -> i.getTemplateId().toString()).collect(Collectors.toList());
            List<FruugoTemplateInfoVO> fruugoTemplateInfoVoS = batch.stream().map(i -> {
                String publishInfo = i.getPublishInfo();
                return JSON.parseObject(publishInfo, FruugoTemplateInfoVO.class);
            }).collect(Collectors.toList());

            //查询出相关的处理报告
            FeedTaskExample example = new FeedTaskExample();
            example.createCriteria()
                    .andAccountNumberEqualTo(saleAccountAndBusinessResponse.getAccountNumber())
                    .andTaskTypeEqualTo(FruugoFeedTaskEnum.UPLOAD_LISTING.getCode())
                    .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                    .andAttribute5In(templateIds);
            feedTasks = fruugoFeedTaskService.getListByExample(example);

            CreateProductRequest createProductRequest = FruugoConvertDto.toCreateProductRequest(fruugoTemplateInfoVoS);
            XxlJobLogger.log("店铺:" + saleAccountAndBusinessResponse.getAccountNumber());
            ApiResult<String> apiResult = fruugoCallV1Service.createOrUpdateProduct(createProductRequest, saleAccountAndBusinessResponse);

            //过滤出定时刊登的队列ID
            queueIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(feedTasks)) {
                queueIds = feedTasks.stream().map(FeedTask::getAttribute3)
                        .filter(StringUtils::isNotBlank)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
            } else {
                XxlJobLogger.log("店铺:{},spu:{}未获取到需要执行的任务", saleAccountAndBusinessResponse.getAccountNumber(), String.join(",", templateIds));
            }

            //模板状态更新
            templateIdList = templateIds.stream().map(Integer::parseInt).collect(Collectors.toList());
            fruugoTemplateService.updatePublishStatus(templateIdList, apiResult.isSuccess(), apiResult.getResult());

            //修改刊登中间表状态
            List<Integer> batchPublishIds = batch.stream().map(FruugoBatchPublish::getId).collect(Collectors.toList());
            updateBatchPublishStatus(batchPublishIds);

            if (!apiResult.isSuccess()) {
                //处理报告失败
                fruugoFeedTaskService.batchFailTask(feedTasks, apiResult.getErrorMsg());
                if (CollectionUtils.isNotEmpty(queueIds)) {
                    fruugoTimePublishQueueService.updateQueueFailStatus(queueIds);
                }
            } else {
                //操作报告attribute4存平台关联id
                String[] s = new String[5];
                s[3] = apiResult.getResult();
                fruugoFeedTaskService.batchRunningTask(feedTasks, "", s);
            }
        } catch (Exception e) {
            fruugoFeedTaskService.batchFailTask(feedTasks, e.getMessage());
            if (CollectionUtils.isNotEmpty(queueIds)) {
                fruugoTimePublishQueueService.updateQueueFailStatus(queueIds);
            }
            XxlJobLogger.log("店铺" + saleAccountAndBusinessResponse.getAccountNumber() + "执行失败" + e);

        }
    }

    private void updateBatchPublishStatus(List<Integer> batchPublishIds) {
        FruugoBatchPublish fruugoBatchPublish = new FruugoBatchPublish();
        fruugoBatchPublish.setPublishStatus(1);
        fruugoBatchPublish.setExecuteTime(new Timestamp(System.currentTimeMillis()));
        FruugoBatchPublishExample fruugoBatchPublishExample = new FruugoBatchPublishExample();
        fruugoBatchPublishExample.createCriteria().andIdIn(batchPublishIds);
        fruugoBatchPublishService.updateByExampleSelective(fruugoBatchPublish, fruugoBatchPublishExample);
    }


    private List<FruugoBatchPublish> getFruugoBatchPublishList(String accountNumber, int limit, int offset) {
        FruugoBatchPublishExample fruugoBatchPublishExample = new FruugoBatchPublishExample();
        FruugoBatchPublishExample.Criteria criteria = fruugoBatchPublishExample.createCriteria();
        criteria.andAccountEqualTo(accountNumber);
        criteria.andPublishStatusEqualTo(0);
        fruugoBatchPublishExample.setLimit(limit);
        fruugoBatchPublishExample.setOffset(offset);
        fruugoBatchPublishExample.setOrderByClause("create_time asc");
        return fruugoBatchPublishService.selectByExample(fruugoBatchPublishExample);
    }


    private List<List<FruugoBatchPublish>> splitIntoBatches(List<FruugoBatchPublish> fruugoBatchPublishList) {
        // 分批次，首先按照每 100 条分组
        List<List<FruugoBatchPublish>> batches = new ArrayList<>();

        int size = fruugoBatchPublishList.size();
        // 计算完整批次的数量
        int fullBatchCount = size / 100;
        // 处理完整的 100 条一批的数据
        for (int i = 0; i < fullBatchCount; i++) {
            int start = i * 100;
            int end = (i + 1) * 100;
            batches.add(fruugoBatchPublishList.subList(start, end));
        }

        // 处理不足 100 条的数据，将其放在最后一个 List 中
        if (size % 100 != 0) {
            batches.add(fruugoBatchPublishList.subList(fullBatchCount * 100, size));
        }
        return batches;
    }
}
