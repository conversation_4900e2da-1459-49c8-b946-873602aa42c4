package com.estone.erp.publish.fruugo.compont.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.fruugo.call.FruugoCallV1Service;
import com.estone.erp.publish.fruugo.compont.publish.param.SpuPublishParam;
import com.estone.erp.publish.fruugo.enums.*;
import com.estone.erp.publish.fruugo.model.FruugoBatchPublish;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.FruugoTimePublishQueue;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.service.FruugoBatchPublishService;
import com.estone.erp.publish.fruugo.service.FruugoFeedTaskService;
import com.estone.erp.publish.fruugo.service.FruugoTemplateService;
import com.estone.erp.publish.fruugo.service.FruugoTimePublishQueueService;
import com.estone.erp.publish.fruugo.util.FruuugoTemplateDateUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletionException;

@Slf4j
public abstract class PublishExecutor<T> implements Executor<T> {

    @Resource
    private FruugoTemplateService fruugoTemplateService;

    @Resource
    private FruugoCallV1Service fruugoCallV1Service;
    @Resource
    private FruugoFeedTaskService fruugoFeedTaskService;
    @Resource
    private FruugoTimePublishQueueService fruugoTimePublishQueueService;

    @Resource
    private FruugoBatchPublishService fruugoBatchPublishService;

    public PublishExecutor() {
    }

    protected abstract FruugoTemplate getTemplateData(T param) throws BusinessException;

    @Override
    public void execute(T param) throws BusinessException {
        StopWatch started = StopWatch.createStarted();
        FruugoTemplate templateData = null;
        FeedTask feedTask = null;

        try {
            templateData = getTemplateData(param);
            log.info("生成模板数据耗时{}", started);
            if (templateData == null) {
                return;
            }
            //记录一个操作报告
            feedTask = this.savePublishOperationReport(templateData);
            FruugoTemplateInfoVO templateInfoVO = FruugoConvertDto.toFruugoTemplateInfoVO(templateData);
            //校验店铺是否为fruugo新店铺
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, templateInfoVO.getAccountNumber());
            if (ObjectUtils.isEmpty(saleAccount) || BooleanUtils.isFalse(saleAccount.getColBool2())) {
                throw new BusinessException("店铺不存在或者非Fruugo新店铺！");
            }
            if (StringUtils.isBlank(saleAccount.getColStr1())) {
                throw new BusinessException("店铺密码为空！");
            }

            //过滤侵权词
            FruuugoTemplateDateUtils.filterInfringementWord(templateInfoVO);


            Map<String, String> stringIntegerMap = FruuugoTemplateDateUtils.checkInfringmentWord(templateInfoVO);
            if (MapUtils.isNotEmpty(stringIntegerMap) && (stringIntegerMap.containsKey("品牌") || stringIntegerMap.containsKey("属性"))) {
                StringBuilder errorMsg = new StringBuilder("存在侵权词：");
                if (stringIntegerMap.containsKey("品牌")){
                    errorMsg.append("品牌："+ stringIntegerMap.get("品牌"));
                }
                if (stringIntegerMap.containsKey("属性")){
                    errorMsg.append("属性："+ stringIntegerMap.get("属性"));
                }
                throw new BusinessException(errorMsg.toString());
            }
            templateData = FruugoConvertDto.toFruugoTemplate(templateInfoVO);
            log.info("过滤侵权词耗时{}", started);

            //替换图片为腾讯云链接
            fruugoTemplateService.replaceImage(templateInfoVO);
            //更新模板刊登状态进行中
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
            templateData.setUpdateBy(userName);
            templateData.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            templateData.setStatus(FruugoTemplateStatusEnums.PUBLISHING.getCode());
            fruugoTemplateService.updateByPrimaryKeySelective(templateData, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
            log.info("替换图片为腾讯云链接耗时{}", started);

            //将数据保存到刊登中间表
            this.saveBatchPublish(templateInfoVO);


        } catch (Exception e) {
            log.error("Fruugo 刊登异常,class :{}, execute error,param:{}", this.getClass().getSimpleName(), JSON.toJSONString(param), e);
            fruugoTemplateService.updatePublishStatus(List.of(templateData.getId()), false, null);
            String errorMsg = null;
            if (e instanceof CompletionException) {
                errorMsg = e.getCause().getMessage();
            } else {
                errorMsg = e.getMessage();
            }
            fruugoFeedTaskService.failTask(feedTask, errorMsg);
            //如果是定时刊登，回写定时队列状态
            if (param instanceof SpuPublishParam) {
                SpuPublishParam spuParam = (SpuPublishParam) param;
                if (spuParam.getPublishType().equals(FruugoPublishModeEnum.TIME_PUBLISH.getCode())) {
                    updateQueueFailStatus(spuParam.getTimePublishQueueId());
                }
            }
            throw new RuntimeException(errorMsg);
        } finally {
            log.info("Fruugo刊登,class:{},param:{},总耗时:{}", this.getClass().getSimpleName(), JSON.toJSONString(param), started);
        }
    }


    private void saveBatchPublish(FruugoTemplateInfoVO template) {
        FruugoBatchPublish fruugoBatchPublish = new FruugoBatchPublish();
        fruugoBatchPublish.setAccount(template.getAccountNumber());
        fruugoBatchPublish.setTemplateId(template.getId());
        fruugoBatchPublish.setPublishStatus(0);
        fruugoBatchPublish.setCreateTime(new Timestamp(System.currentTimeMillis()));
        fruugoBatchPublish.setPublishInfo(JSON.toJSONString(template));
        fruugoBatchPublishService.insert(fruugoBatchPublish);
    }


    private void updateQueueFailStatus(Integer queueId) {
        FruugoTimePublishQueue queue = new FruugoTimePublishQueue();
        queue.setId(queueId);
        queue.setStatus(FruugoTimePublishEnums.END.getCode());
        queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        queue.setPublishStatus(FruugoTemplateStatusEnums.FAIL.getCode());
        fruugoTimePublishQueueService.updateByPrimaryKeySelective(queue);
    }


    private FeedTask savePublishOperationReport(FruugoTemplate fruugoTemplate) {
        //添加处理报告,attribute5存模板id
        String[] s = new String[5];
        s[4] = String.valueOf(fruugoTemplate.getId());
        //如果是定时刊登，需要将定时队列id写入attribute3
        if (ObjectUtils.isNotEmpty(fruugoTemplate.getQueueId())) {
            s[2] = String.valueOf(fruugoTemplate.getQueueId());
        }
        FeedTask feedTask = fruugoFeedTaskService.newTask(fruugoTemplate.getProductId(),
                fruugoTemplate.getAccountNumber(),
                FruugoFeedTaskEnum.UPLOAD_LISTING.getCode(),
                fruugoTemplate.getArticleNumber(), s);
        return feedTask;
    }
}