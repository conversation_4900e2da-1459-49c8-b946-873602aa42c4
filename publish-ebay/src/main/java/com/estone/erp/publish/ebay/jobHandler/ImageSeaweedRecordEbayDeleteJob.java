package com.estone.erp.publish.ebay.jobHandler;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.cos.TencentCos;
import com.estone.erp.publish.common.cos.TencentCosUtils;
import com.estone.erp.publish.system.imageSeaweedRecord.model.ImageSeaweedRecord;
import com.estone.erp.publish.system.imageSeaweedRecord.model.ImageSeaweedRecordExample;
import com.estone.erp.publish.system.imageSeaweedRecord.service.ImageSeaweedRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/6 9:57
 * @description 删除ebay图片
 */
@Component
@Deprecated
@Slf4j
public class ImageSeaweedRecordEbayDeleteJob {

    @Resource
    private ImageSeaweedRecordService imageSeaweedRecordService;

    @XxlJob("ImageSeaweedRecordEbayDeleteJob")
    public ReturnT<String> run(String param) throws Exception {
        log.warn("*****************删除ebay图片*****************");
        /*Date date = new Date();
        Date _3hourBefore = DateUtils.addHours(date, -3);
        Timestamp less3Hour = new Timestamp(_3hourBefore.getTime());

        ImageSeaweedRecordExample queryEx = new ImageSeaweedRecordExample();
        queryEx.createCriteria().andCreateTimeLessThan(less3Hour).andDeleteTimeIsNull();
        List<ImageSeaweedRecord> list = imageSeaweedRecordMapper.selectByExample(queryEx);
        if (CollectionUtils.isNotEmpty(list)){
            for (ImageSeaweedRecord record : list) {
                String newImageUrl = record.getNewImageUrl();
                // 删除图片地址
                HttpParams<String> httpParams = new HttpParams<>();
                httpParams.setUrl(newImageUrl);
                httpParams.setHttpMethod(HttpMethod.DELETE);
                boolean delOk = false;
                try {
                    ApiResult<String> apiResult = HttpUtils.exchange2ApiResult(httpParams, String.class);
                    delOk = apiResult.isSuccess();
                    if (!apiResult.isSuccess() && StringUtils.contains(apiResult.getErrorMsg(), "no entry is found in filer store")){
                        delOk = true;
                    }
                }catch (Exception e){
                    if (StringUtils.contains(e.getMessage(), "no entry is found in filer store")) {
                        delOk = true;
                    }else{
                        log.error("删除图片失败，图片地址：" + newImageUrl, e);
                    }
                }
                if (delOk){
                    record.setDeleteTime(new Timestamp(date.getTime()));
                    imageSeaweedRecordMapper.updateByPrimaryKeySelective(record);
                }
            }
            log.warn("*****************删除ebay图片*****************删除了{}条", list.size());
            XxlJobLogger.log("*****************删除ebay图片*****************删除了{}条", list.size());
        }*/

//        ImageSeaweedRecordExample example = new ImageSeaweedRecordExample();
//        example.createCriteria().andCreateTimeLessThan(less3Hour);
//        int i = imageSeaweedRecordMapper.deleteByExample(example);
        int hour =0;
        if (StringUtils.isNotBlank(param)){
            hour = Integer.parseInt(param);
        }
        handleHistoryData();
        handleTencosDeleteJobhandler(hour);
        return ReturnT.SUCCESS;
    }

    private void handleTencosDeleteJobhandler(int hour) {
        Date date = new Date();
        if (hour <= 0){
            hour = 3;
            XxlJobLogger.log("删除hour h之前的数据:" + hour);
        }
        Date hourBefore = DateUtils.addHours(date, -hour);
        Timestamp lessHour = new Timestamp(hourBefore.getTime());

        int limit = 500;
        long startId = 0;
        int total = 0;
        while (true) {
            try {
                ImageSeaweedRecordExample imageSeaweedRecordExample = new ImageSeaweedRecordExample();
                ImageSeaweedRecordExample.Criteria criteria = imageSeaweedRecordExample.createCriteria();
                criteria
                        .andCreateTimeGreaterThanOrEqualTo(new Timestamp(DateUtils.addHours(date, -36).getTime()))
                        .andCreateTimeLessThan(lessHour)
                        .andDeleteTimeIsNull()
                        .andIdGreaterThan(startId);
                imageSeaweedRecordExample.setOrderByClause("id asc");
                imageSeaweedRecordExample.setLimit(limit);
                List<ImageSeaweedRecord> list = imageSeaweedRecordService.selectByExample(imageSeaweedRecordExample);
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                total += list.size();
                ImageSeaweedRecord imageSeaweedRecord = list.get(list.size() - 1);
                if (imageSeaweedRecord != null) {
                    startId = imageSeaweedRecord.getId();
                }

                List<String> imageList = list.stream().map(o -> o.getNewImageUrl().replaceAll(TencentCos.Ebay_Image.getPrefix(), "")).collect(Collectors.toList());
                //TencentCosUtils.deleteObj(TencentCos.Ebay_Image,imageList);
                TencentCosUtils.batcheleteObj(TencentCos.Ebay_Image, imageList);
                Timestamp time = new Timestamp(date.getTime());
                list.stream().forEach(o -> {
                    o.setDeleteTime(time);
                });
                imageSeaweedRecordService.batchUpdateDeleteDateById(list);
            } catch (Exception e) {
                XxlJobLogger.log("删除腾讯云图片记录执行出错：" + e.getMessage());
            }
            XxlJobLogger.log("累计执行删除：" + total);
        }
    }

    /**
     * 处理失败模板 两个月前失败数据删除
     */
    private void handleHistoryData() {
        int offset = 0;
        int limit = 1000;
        int page = 1;
        long startId = 0;

        int delTotal = 0;
        // 一直处理第一页数据 删除后后面数据会成为第一页
        // 失败旧退出 并发送钉钉消息
        while (true) {
            ImageSeaweedRecordExample example = new ImageSeaweedRecordExample();
            ImageSeaweedRecordExample.Criteria criteria = example.createCriteria();
            Date beginDate =  DateUtils.addDays(new Date(), -4);
            criteria.andCreateTimeLessThanOrEqualTo(new Timestamp(beginDate.getTime()))
                    .andIdGreaterThan(startId);
            example.setLimit(limit);
            example.setOffset(offset);
            example.setOrderByClause("id asc");
            List<ImageSeaweedRecord> imageSeaweedRecordList = imageSeaweedRecordService.selectByExample(example);

            XxlJobLogger.log("-------第{}页--------", page);
            if(CollectionUtils.isEmpty(imageSeaweedRecordList)) {
                XxlJobLogger.log("-------第{}页小于4天前的历史数据删除完毕--------", page);
                break;
            }
            ImageSeaweedRecord imageSeaweedRecord = imageSeaweedRecordList.get(imageSeaweedRecordList.size() - 1);
            if (imageSeaweedRecord != null) {
                startId = imageSeaweedRecord.getId();
            }

            List<Long> deleteIds = new ArrayList<>();
            try {
                deleteIds = imageSeaweedRecordList.stream().map(ImageSeaweedRecord::getId).collect(Collectors.toList());
                imageSeaweedRecordService.deleteByPrimaryKey(deleteIds);

                delTotal += deleteIds.size();
            } catch (Exception e) {
                XxlJobLogger.log("-------第{}页异常-------{}", e.getMessage());
                break;
            }
            page ++;
        }
        XxlJobLogger.log("共删除小于4天前历史数据条数：" + delTotal);
    }
}
