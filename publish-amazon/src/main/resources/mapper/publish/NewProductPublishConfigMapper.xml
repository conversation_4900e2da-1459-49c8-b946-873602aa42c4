<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.NewProductPublishConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.NewProductPublishConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="job_number" property="jobNumber" jdbcType="CHAR" />
    <result column="sale_number" property="saleNumber" jdbcType="VARCHAR" />
    <result column="number" property="number" jdbcType="INTEGER" />
    <result column="allocated_quantity" property="allocatedQuantity" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="CHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, job_number, sale_number, `number`, allocated_quantity, create_by, create_time, update_by, 
    update_time
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.amazon.model.NewProductPublishConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from new_product_publish_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from new_product_publish_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from new_product_publish_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.NewProductPublishConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into new_product_publish_config (job_number, sale_number, `number`, allocated_quantity, 
      create_by, create_time, update_by, 
      update_time)
    values (#{jobNumber,jdbcType=CHAR}, #{saleNumber,jdbcType=VARCHAR}, #{number,jdbcType=INTEGER}, #{allocatedQuantity,jdbcType=INTEGER}, 
      #{createBy,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=CHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.NewProductPublishConfigExample" resultType="java.lang.Integer" >
    select count(*) from new_product_publish_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update new_product_publish_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.jobNumber != null" >
        job_number = #{record.jobNumber,jdbcType=CHAR},
      </if>
      <if test="record.saleNumber != null" >
        sale_number = #{record.saleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null" >
        `number` = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.allocatedQuantity != null" >
        allocated_quantity = #{record.allocatedQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=CHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=CHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.amazon.model.NewProductPublishConfig" >
    update new_product_publish_config
    <set >
      <if test="jobNumber != null" >
        job_number = #{jobNumber,jdbcType=CHAR},
      </if>
      <if test="saleNumber != null" >
        sale_number = #{saleNumber,jdbcType=VARCHAR},
      </if>
      <if test="number != null" >
        `number` = #{number,jdbcType=INTEGER},
      </if>
      <if test="allocatedQuantity != null" >
        allocated_quantity = #{allocatedQuantity,jdbcType=INTEGER},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=CHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <!-- 批量插入 -->
  <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
    insert into new_product_publish_config (job_number, sale_number, number, allocated_quantity,
    create_by, create_time, update_by,
    update_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.jobNumber,jdbcType=VARCHAR}, #{item.saleNumber,jdbcType=VARCHAR}, #{item.number,jdbcType=INTEGER}, #{item.allocatedQuantity,jdbcType=INTEGER},
      #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <delete id="deleteAll" >
    delete from new_product_publish_config
  </delete>
</mapper>
