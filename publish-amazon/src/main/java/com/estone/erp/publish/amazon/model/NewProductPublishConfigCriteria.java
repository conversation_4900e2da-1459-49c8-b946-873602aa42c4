package com.estone.erp.publish.amazon.model;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-29 17:54:40
 */
public class NewProductPublishConfigCriteria extends NewProductPublishConfig {
    private static final long serialVersionUID = 1L;

    private List<String> saleNumbers;

    private List<String> saleLeaders;

    private List<String> saleSupervisors;

    /**
     * 分配数量区间
     */
    private Integer fromAllocatedQuantity;
    private Integer toAllocatedQuantity;

    public NewProductPublishConfigExample getExample() {
        NewProductPublishConfigExample example = new NewProductPublishConfigExample();
        NewProductPublishConfigExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(this.getSaleNumbers())) {
            criteria.andSaleNumberIn(this.getSaleNumbers());
        }
        if (this.getJobNumber() != null) {
            criteria.andJobNumberEqualTo(this.getJobNumber());
        }
        if (this.getNumber() != null) {
            criteria.andNumberEqualTo(this.getNumber());
        }
        if (this.getAllocatedQuantity() != null) {
            criteria.andAllocatedQuantityEqualTo(this.getAllocatedQuantity());
        }
        if (this.getCreateBy() != null) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getUpdateBy() != null) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateTime() != null) {
            criteria.andUpdateTimeEqualTo(this.getUpdateTime());
        }
        if (this.getFromAllocatedQuantity() != null) {
            criteria.andAllocatedQuantityGreaterThanOrEqualTo(this.getFromAllocatedQuantity());
        }
        if (this.getToAllocatedQuantity() != null) {
            criteria.andAllocatedQuantityLessThanOrEqualTo(this.getToAllocatedQuantity());
        }

        return example;
    }

    public Integer getFromAllocatedQuantity() {
        return fromAllocatedQuantity;
    }

    public void setFromAllocatedQuantity(Integer fromAllocatedQuantity) {
        this.fromAllocatedQuantity = fromAllocatedQuantity;
    }

    public Integer getToAllocatedQuantity() {
        return toAllocatedQuantity;
    }

    public void setToAllocatedQuantity(Integer toAllocatedQuantity) {
        this.toAllocatedQuantity = toAllocatedQuantity;
    }

    public List<String> getSaleNumbers() {
        return saleNumbers;
    }

    public void setSaleNumbers(List<String> saleNumbers) {
        this.saleNumbers = saleNumbers;
    }

    public List<String> getSaleLeaders() {
        return saleLeaders;
    }

    public void setSaleLeaders(List<String> saleLeaders) {
        this.saleLeaders = saleLeaders;
    }

    public List<String> getSaleSupervisors() {
        return saleSupervisors;
    }

    public void setSaleSupervisors(List<String> saleSupervisors) {
        this.saleSupervisors = saleSupervisors;
    }
}