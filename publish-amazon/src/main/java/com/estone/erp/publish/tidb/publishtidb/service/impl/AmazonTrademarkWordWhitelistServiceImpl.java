package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.AmazonTrademarkWordWhitelistVO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonTrademarkWordWhitelistMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonWordDeleteLogService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.reflections.Reflections.log;

/**
 * <p>
 * Amazon商标词白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Service
public class AmazonTrademarkWordWhitelistServiceImpl extends ServiceImpl<AmazonTrademarkWordWhitelistMapper, AmazonTrademarkWordWhitelist> implements AmazonTrademarkWordWhitelistService {

    @Resource
    private AmazonWordDeleteLogService amazonWordDeleteLogService;
    private Cache<String, List<String>> trademarkCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MILLISECONDS).build();

    private static final String ALL_WORDS_CACHE_KEY = "ALL_TRADEMARK_WORDS";


    /**
     * 构建查询条件
     *
     * @param dto 查询条件DTO
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<AmazonTrademarkWordWhitelist> buildQueryWrapper(TrademarkWordWhitelistQueryDTO dto) {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = new LambdaQueryWrapper<>();

        // 站点查询
        wrapper.eq(StringUtils.isNotBlank(dto.getSite()), AmazonTrademarkWordWhitelist::getSite, dto.getSite());

        // 侵权词汇模糊查询
        wrapper.like(StringUtils.isNotBlank(dto.getInfringementWord()),
            AmazonTrademarkWordWhitelist::getInfringementWord, dto.getInfringementWord());


        // 商标词标识
        if(CollectionUtils.isNotEmpty(dto.getTrademarkIdentificationList())){
            wrapper.and(wr ->{
                for (String trademark : dto.getTrademarkIdentificationList()) {
                    wr.or().like(AmazonTrademarkWordWhitelist::getTrademarkIdentification, trademark);
                }
                return wr;
            });
        }

        // 禁售平台查询
        if (CollectionUtils.isNotEmpty(dto.getForbidChannelList())) {
            List<String> forbidChannels = dto.getForbidChannelList();
            wrapper.and(wr -> {
                for (String channel : forbidChannels) {
                    wr.or().like(AmazonTrademarkWordWhitelist::getForbidChannel, channel);
                }
                return wr;
            });
        }

        // 禁售站点查询
        if (CollectionUtils.isNotEmpty(dto.getProhibitionSiteList())) {
            List<String> prohibitionSites = dto.getProhibitionSiteList();
            List<String> forbidChannels = dto.getForbidChannelList();

            wrapper.and(wr -> {
                for (String site : prohibitionSites) {
                    if (CollectionUtils.isNotEmpty(forbidChannels)) {
                        for (String channel : forbidChannels) {
                            String keyword = channel + "_" + site;
                            wr.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, keyword);
                        }
                    } else {
                        wr.or().like(AmazonTrademarkWordWhitelist::getProhibitionSite, site);
                    }
                }
                return wr;
            });
        }

        // 添加人查询
        wrapper.in(CollectionUtils.isNotEmpty(dto.getCreateByList()), AmazonTrademarkWordWhitelist::getCreateBy, dto.getCreateByList());

        // 添加时间范围查询
        wrapper.ge(StringUtils.isNotBlank(dto.getCreatedTimeStart() ), AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeStart());
        wrapper.le(StringUtils.isNotBlank(dto.getCreatedTimeEnd()), AmazonTrademarkWordWhitelist::getCreatedTime, dto.getCreatedTimeEnd());

        // 更新时间范围查询
        wrapper.ge(StringUtils.isNotBlank(dto.getUpdatedTimeStart()) , AmazonTrademarkWordWhitelist::getUpdatedTime, dto.getUpdatedTimeStart());
        wrapper.le(StringUtils.isNotBlank(dto.getUpdatedTimeEnd()), AmazonTrademarkWordWhitelist::getUpdatedTime, dto.getUpdatedTimeEnd());

        wrapper.orderByDesc(AmazonTrademarkWordWhitelist::getCreatedTime);
        return wrapper;
}

    @Override
    public CQueryResult<AmazonTrademarkWordWhitelistVO> queryPage(CQuery<TrademarkWordWhitelistQueryDTO> query) {
        ApiResult<Boolean> superAdminOrBigSupervisor = NewUsermgtUtils.isSuperAdminOrBigSupervisor(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrBigSupervisor.isSuccess()) {
            throw new BusinessException(superAdminOrBigSupervisor.getErrorMsg());
        }

        // 获取查询条件
        TrademarkWordWhitelistQueryDTO dto = query.getSearch();

        // 构建查询条件
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = buildQueryWrapper(dto);


        // 分页查询
        IPage<AmazonTrademarkWordWhitelist> resultPage = baseMapper.selectPage(new Page<>(query.getPage(), query.getLimit()), wrapper);

        // 转换为响应对象
        List<AmazonTrademarkWordWhitelistVO> responseList = resultPage.getRecords().stream()
                .map(AmazonTrademarkWordWhitelistVO::fromEntity)
                .collect(Collectors.toList());

        // 构建CQueryResult响应
        CQueryResult<AmazonTrademarkWordWhitelistVO> result = new CQueryResult<>();
        result.setRows(responseList);
        result.setSuccess(true);
        result.setTotal(resultPage.getTotal());
        result.setTotalPages(Long.valueOf(resultPage.getPages()).intValue());

        return result;

    }

    @Override
    public void addWhitelist(TrademarkWordWhitelistSaveDTO dto) {
        // 参数校验：侵权词汇不能为空
        if (StringUtils.isBlank(dto.getInfringementWord())) {
            return;
        }

        // 获取当前操作用户
        String currentUser = getCurrentUser();

        // 创建新的白名单记录
        createNewWhitelists(dto, currentUser);
    }



    /**
     * 获取当前操作用户
     *
     * @return 当前用户名
     */
    private String getCurrentUser() {
        return StringUtils.isNotBlank(WebUtils.getUserName())
            ? WebUtils.getUserName()
            : DataContextHolder.getUsername();
    }

    /**
     * 删除已存在的白名单记录并记录删除日志
     *
     * @param dto 保存DTO
     * @param currentUser 当前用户
     */
    private void deleteExistingWhitelists(TrademarkWordWhitelistSaveDTO dto, String currentUser) {
        // 构建查询条件
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonTrademarkWordWhitelist::getInfringementWord, dto.getInfringementWord());

        // 如果指定了站点列表，则只删除指定站点的记录
        if (CollectionUtils.isNotEmpty(dto.getSiteList())) {
            wrapper.in(AmazonTrademarkWordWhitelist::getSite, dto.getSiteList());
        }

        // 查询并删除已存在的记录
        List<AmazonTrademarkWordWhitelist> existingWhitelists = list(wrapper);
        for (AmazonTrademarkWordWhitelist whitelist : existingWhitelists) {
            // 删除白名单记录
            removeById(whitelist.getId());
            // 记录删除日志
            recordDeleteLog(whitelist, currentUser);
        }
    }

    /**
     * 记录删除日志
     *
     * @param whitelist 被删除的白名单记录
     * @param currentUser 当前用户
     */
    private void recordDeleteLog(AmazonTrademarkWordWhitelist whitelist, String currentUser) {
        AmazonWordDeleteLog deleteLog = new AmazonWordDeleteLog();
        deleteLog.setSite(whitelist.getSite());
        deleteLog.setInfringementWord(whitelist.getInfringementWord());
        deleteLog.setOperatedTime(LocalDateTime.now());
        deleteLog.setOperatedBy(currentUser);
        amazonWordDeleteLogService.save(deleteLog);
    }

    /**
     * 创建新的白名单记录
     *
     * @param dto 保存DTO
     * @param currentUser 当前用户
     */
    private void createNewWhitelists(TrademarkWordWhitelistSaveDTO dto, String currentUser) {
        // 查询已存在的白名单记录
        List<AmazonTrademarkWordWhitelist> existingWhitelists = queryExistingWhitelists(dto.getInfringementWord());

        // 检查是否存在全站点记录
        boolean hasAllSiteRecord = existingWhitelists.stream().anyMatch(whitelist -> null == whitelist.getSite());

        // 获取已存在的站点列表
        List<String> existingSites = existingWhitelists.stream()
                .map(AmazonTrademarkWordWhitelist::getSite)
                .distinct()
                .collect(Collectors.toList());

        // 处理站点列表，如果需要跳过处理则直接返回
        if (shouldSkipProcessing(dto, existingSites, hasAllSiteRecord)) {
            return;
        }

        // 获取目标站点列表
        List<String> targetSites = getTargetSites(dto, existingSites, hasAllSiteRecord);

        // 创建白名单记录
        createWhitelistRecords(dto, targetSites, existingWhitelists, currentUser);
    }

    /**
     * 查询已存在的白名单记录
     *
     * @param infringementWord 侵权词汇
     * @return 已存在的白名单记录列表
     */
    private List<AmazonTrademarkWordWhitelist> queryExistingWhitelists(String infringementWord) {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonTrademarkWordWhitelist::getInfringementWord, infringementWord);
        return list(wrapper);
    }

    /**
     * 判断是否应该跳过处理
     *
     * @param dto              保存DTO
     * @param existingSites    已存在的站点列表
     * @param hasAllSiteRecord 是否存在全站点记录
     * @return 是否跳过处理
     */
    private boolean shouldSkipProcessing(TrademarkWordWhitelistSaveDTO dto, List<String> existingSites, boolean hasAllSiteRecord) {
        Boolean isImportData = dto.getBelongImportData();

        if (BooleanUtils.isTrue(isImportData)) {
            // 导入数据模式下，如果存在全站点记录则跳过
            if (hasAllSiteRecord) {
                return true;
            }

            // 导入数据模式下，如果过滤后没有有效站点则跳过
            if (CollectionUtils.isNotEmpty(dto.getSiteList())) {
                List<String> validSites = dto.getSiteList().stream()
                        .filter(site -> !existingSites.contains(site) && AmazonConstant.amazonSiteList.contains(site))
                        .collect(Collectors.toList());
                return CollectionUtils.isEmpty(validSites);
            }
        }

        return false;
    }

    /**
     * 获取目标站点列表
     *
     * @param dto 保存DTO
     * @param existingSites 已存在的站点列表
     * @param hasAllSiteRecord 是否存在全站点记录
     * @return 目标站点列表
     */
    private List<String> getTargetSites(TrademarkWordWhitelistSaveDTO dto, List<String> existingSites, boolean hasAllSiteRecord) {
        Boolean isImportData = dto.getBelongImportData();
        List<String> inputSites = dto.getSiteList();

        if (!BooleanUtils.isTrue(isImportData)) {
            // 非导入数据：进行严格验证
            return validateAndReturnSites(inputSites, existingSites, hasAllSiteRecord);
        } else {
            // 导入数据：过滤已存在的数据
            if (CollectionUtils.isNotEmpty(inputSites)) {
                return inputSites.stream()
                        .filter(site -> !existingSites.contains(site) && AmazonConstant.amazonSiteList.contains(site))
                        .collect(Collectors.toList());
            }
            return inputSites;
        }
    }

    /**
     * 验证站点并返回目标站点列表（非导入数据模式）
     *
     * @param inputSites       输入的站点列表
     * @param existingSites    已存在的站点列表
     * @param hasAllSiteRecord 是否存在全站点记录
     * @return 验证通过的站点列表
     */
    private List<String> validateAndReturnSites(List<String> inputSites, List<String> existingSites, boolean hasAllSiteRecord) {
        if (hasAllSiteRecord) {
            throw new RuntimeException("全站点已存在数据，请修改后添加");
        }

        if (CollectionUtils.isNotEmpty(inputSites)) {
            // 验证是否包含非Amazon站点
            validateAmazonSites(inputSites);
            // 验证是否包含已存在的站点
            validateExistingSites(inputSites, existingSites);
        }

        return inputSites;
    }



    /**
     * 验证输入站点是否都是Amazon站点
     *
     * @param inputSites 输入的站点列表
     */
    private void validateAmazonSites(List<String> inputSites) {
        List<String> invalidSites = inputSites.stream()
                .filter(site -> !AmazonConstant.amazonSiteList.contains(site))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(invalidSites)) {
            String invalidSiteStr = String.join(",", invalidSites);
            throw new RuntimeException(String.format("%s站点为非Amazon站点，请修改后添加", invalidSiteStr));
        }
    }

    /**
     * 验证输入站点是否与已存在站点冲突
     *
     * @param inputSites    输入的站点列表
     * @param existingSites 已存在的站点列表
     */
    private void validateExistingSites(List<String> inputSites, List<String> existingSites) {
        List<String> conflictSites = inputSites.stream()
                .filter(existingSites::contains)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(conflictSites)) {
            String conflictSiteStr = String.join(",", conflictSites);
            throw new RuntimeException(String.format("%s站点已存在站点数据，请修改后添加", conflictSiteStr));
        }
    }

    /**
     * 创建白名单记录
     *
     * @param dto                保存DTO
     * @param targetSites        目标站点列表
     * @param existingWhitelists 已存在的白名单记录
     * @param currentUser        当前用户
     */
    private void createWhitelistRecords(TrademarkWordWhitelistSaveDTO dto, List<String> targetSites,
                                        List<AmazonTrademarkWordWhitelist> existingWhitelists, String currentUser) {
        if (CollectionUtils.isEmpty(targetSites)) {
            // 删除已存在的记录并创建全站点记录
            deleteExistingRecords(existingWhitelists, currentUser);
            createWhitelistRecord(dto, null, currentUser);
        } else {
            // 为每个指定站点创建记录
            for (String site : targetSites) {
                createWhitelistRecord(dto, site, currentUser);
            }
        }
    }

    /**
     * 删除已存在的记录
     *
     * @param existingWhitelists 已存在的白名单记录
     * @param currentUser        当前用户
     */
    private void deleteExistingRecords(List<AmazonTrademarkWordWhitelist> existingWhitelists, String currentUser) {
        for (AmazonTrademarkWordWhitelist whitelist : existingWhitelists) {
            removeById(whitelist.getId());
            recordDeleteLog(whitelist, currentUser);
        }
    }

    /**
     * 创建单个白名单记录
     *
     * @param dto 保存DTO
     * @param site 站点（可为空）
     * @param currentUser 当前用户
     */
    private void createWhitelistRecord(TrademarkWordWhitelistSaveDTO dto, String site, String currentUser) {
        AmazonTrademarkWordWhitelist whitelist = new AmazonTrademarkWordWhitelist();
        whitelist.setSite(site);
        whitelist.setInfringementWord(dto.getInfringementWord());
        whitelist.setTrademarkIdentification(dto.getTrademarkIdentification());
        whitelist.setProhibitionSite(dto.getProhibitionSite());
        whitelist.setForbidChannel(dto.getForbidChannel());
        whitelist.setCreateBy(currentUser);
        whitelist.setCreatedTime(LocalDateTime.now());
        save(whitelist);
    }

    /**
     * 根据查询条件获取TiDB分页元数据列表
     *
     * @param wrapper 查询条件
     * @return TiDB分页元数据列表
     */
    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper) {
        List<Map<Object, Object>> mapList = baseMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(mapList);
    }

    @Override
    public List<String> getTrademarkWhiteListWords(String site) {
        try {
            return trademarkCache.get(site, () -> {
                List<String> list = getTrademarkWhiteListWordsFromDB(site);
                CollectionUtils.isEmpty(list);
                return list;
            });
        } catch (ExecutionException e) {
            log.error("从缓存加载站点 [{}] 的商标白名单失败", site, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getAllTrademarkWhiteListWords() {
        try {
            return trademarkCache.get(ALL_WORDS_CACHE_KEY, () -> {
                return getAllTrademarkWhiteListWordsFromDB();
            });
        } catch (ExecutionException e) {
            log.error("从缓存加载所有商标白名单失败", e);
            return Collections.emptyList();
        }
    }

    private List<String> getTrademarkWhiteListWordsFromDB(String site) {
        List<AmazonTrademarkWordWhitelist> list = baseMapper.selectList(new LambdaQueryWrapper<AmazonTrademarkWordWhitelist>()
                .eq(AmazonTrademarkWordWhitelist::getSite, site).or().isNull(AmazonTrademarkWordWhitelist::getSite));
        return list.stream().map(AmazonTrademarkWordWhitelist::getInfringementWord).collect(Collectors.toList());
    }

    private List<String> getAllTrademarkWhiteListWordsFromDB() {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AmazonTrademarkWordWhitelist::getInfringementWord);
        List<AmazonTrademarkWordWhitelist> list = baseMapper.selectList(queryWrapper);
        return list.stream().map(AmazonTrademarkWordWhitelist::getInfringementWord).collect(Collectors.toList());
    }

}
