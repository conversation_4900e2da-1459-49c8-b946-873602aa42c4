package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.componet.checkserver.AmazonCheckingWordService;
import com.estone.erp.publish.amazon.componet.checkserver.CheckingWordContext;
import com.estone.erp.publish.amazon.constant.AmazonJobConstant;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.model.dto.AmazonDelInfringementWordDO;
import com.estone.erp.publish.amazon.model.dto.SearchVo;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.TortUtils;
import com.estone.erp.publish.system.erpCommon.module.TrieResultVo;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonAttributeReplaceLogService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description 侵权词辅助类
 */
@Slf4j
@Component
public class AmazonInfringementWordHelper {
    @Autowired
    private InfringementWordService infringementWordService;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private AmazonCheckingWordService templateCheckingWordService;
    @Resource
    private AmazonCheckingWordService listingCheckingWordService;
    @Resource
    private AmazonTemplateForbiddenSaleChannelHelper amazonTemplateForbiddenSaleChannelHelper;
    @Resource
    private AmazonAttributeReplaceLogService amazonAttributeReplaceLogService;
    @Resource
    private AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService;

    /**
     * 备案词替换，配置超出的词汇未作判定
     */
    private static final List<String> specialStrList = new ArrayList<>(Arrays.asList("㊐", "㊊", "㊎", " ㊍", "㊌", "㊋", "㊏", "㊑", "㊒", "㊓", "㊔", "㊕",
            "㊖", "㊘", "㊜", "㊝", "㊞", "㊟", "㊠", "㊡", "㊢", "㊩", "㊪", "㊫", "㊬", "㊭", "㊮", "㊯", "㊰", "㊚", "㊛", "㊣", "㊤", "㊥", "㊦", "㊧", "㊨"));

    /**
     * 获取订单店铺信息的全部品牌： 备案品牌 以及 授权品牌(逗号分隔)，并转换为 Map用于排除校验
     *
     * @param accountNumber
     * @return
     */
    public Map<String, String> getOrderAccountBrandMap(String accountNumber) {
        List<String> exclusdBrandList = new ArrayList<>();
        exclusdBrandList = AmazonJobConstant.ACCOUNT_AND_ORDER_BRAND_SET_MAP.getIfPresent(accountNumber);
        if (CollectionUtils.isEmpty(exclusdBrandList) || exclusdBrandList.size() == 0) {
            exclusdBrandList = new ArrayList<>();
            AmazonAccountRelationExample example = new AmazonAccountRelationExample();
            example.createCriteria().andAccountNumberEqualTo(accountNumber);
            String filedColumns = "account_number, record_brand, auth_brand";
            example.setFiledColumns(filedColumns);
            List<AmazonAccountRelation> relations = amazonAccountRelationService.selectFiledColumnsByExample(example);
            if (CollectionUtils.isNotEmpty(relations)) {
                AmazonAccountRelation accountRelation = relations.get(0);
                String recordBrand = accountRelation.getRecordBrand();
                if (StringUtils.isNotBlank(recordBrand)) {
                    exclusdBrandList.add(recordBrand);
                }
                String authBrand = accountRelation.getAuthBrand();
                if (StringUtils.isNotBlank(authBrand)) {
                    exclusdBrandList.addAll(new ArrayList<>(Arrays.asList(StringUtils.split(authBrand, ","))));
                }
                exclusdBrandList = exclusdBrandList.stream().distinct().collect(Collectors.toList());
                AmazonJobConstant.ACCOUNT_AND_ORDER_BRAND_SET_MAP.put(accountNumber, exclusdBrandList);
            } else {
                log.warn(String.format("店铺配置未查询到当前账号信息%s，故无备案品牌 和授权品牌", accountNumber));
            }
        }
        if (CollectionUtils.isNotEmpty(exclusdBrandList) && exclusdBrandList.size() > 0) {
            Map<String, String> map = new HashMap<>(exclusdBrandList.size());
            AtomicInteger i = new AtomicInteger();
            exclusdBrandList.forEach(brand -> {
                map.put(brand, specialStrList.get(i.get()));
                i.getAndIncrement();
            });
            return map;
        }
        return null;
    }

    /**
     * 替换或者还原，订单账号配置的品牌词
     *
     * @param text
     * @param orderAccountBrandMap
     * @param isReplace            true 表示替换为 specialStrList 中的特殊字符 ，false 表示还原成备案品牌
     * @return
     */
    public String orderAccountBrandReplace(String text, Map<String, String> orderAccountBrandMap, boolean isReplace) {
        if (StringUtils.isBlank(text) || MapUtils.isEmpty(orderAccountBrandMap)) {
            return text;
        }
        for (String orderAccountBrand : orderAccountBrandMap.keySet()) {
            String regex = isReplace ? orderAccountBrand : orderAccountBrandMap.get(orderAccountBrand);
            String replacement = isReplace ? orderAccountBrandMap.get(orderAccountBrand) : orderAccountBrand;
            text = StrUtil.replaceAllIgnoreCase(text, regex, replacement);
        }
        return text;
    }

    /**
     * 查询侵权词/违禁词、商标词 详细信息
     *
     * @param context
     * @return
     */
    public ApiResult<List<WordValidateResult>> searchTemplateInfringementWordInfos(CheckingWordContext context) {
        String accountNumber = context.getAccountNumber();
        if (StringUtils.isNotBlank(accountNumber)) {
            boolean flagAmzSpecialGoodsAccountNumber = AmazonTemplateForbiddenSaleChannelHelper.checkIsAmzSpecialGoodsAccountNumber(accountNumber);
            if (flagAmzSpecialGoodsAccountNumber && StringUtils.isNotBlank(context.getCheckDatatype()) && context.getCheckDatatype().equalsIgnoreCase("templateData")) {
                //ES-11404 【Amazon】模版刊登，参数配置了亚马逊特供产品可刊登店铺的账号不通过系统校验自动过滤删除侵权词/商标词（先做先发）
                return ApiResult.newSuccess();
            }
        }

        // 替换订单账号配置的品牌词
        if (context.getOrderAccountBrandReplace() && StringUtils.isNotBlank(accountNumber)) {
            Map<String, String> orderAccountBrandMap = getOrderAccountBrandMap(accountNumber);
            String replaceText = orderAccountBrandReplace(context.getText(), orderAccountBrandMap, true);
            context.setCheckText(replaceText);
        }

        ApiResult<TrieResultVo> trieResultVoApiResult = templateCheckingWordService.checkingWord(context);
        if (!trieResultVoApiResult.isSuccess()) {
            throw new RuntimeException(trieResultVoApiResult.getErrorMsg());
        }

        // 排除不需要过滤的特殊词汇
        TrieResultVo trieResult = trieResultVoApiResult.getResult();
        if (trieResult != null) {
            TortUtils.removeNoFiltering(trieResult);
        }
        List<WordValidateResult> wordValidateResults = getInfringementWordInfos(context.getSite(), trieResultVoApiResult.getResult(), true);
        return ApiResult.newSuccess(wordValidateResults);
    }

    /**
     * 对目标文本删除侵权词
     *
     * @param delInfringementWordDO 待删除资源
     */
    public ApiResult<String> delTextInfringementWords(AmazonDelInfringementWordDO delInfringementWordDO) {
        try {
            String accountNumber = delInfringementWordDO.getAccountNumber();
            String site = delInfringementWordDO.getSite();
            Map<String, String> orderAccountBrandMap = getOrderAccountBrandMap(accountNumber);
            List<String> texts = delInfringementWordDO.getSourceTexts().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            String checkText = orderAccountBrandReplace(StringUtils.join(texts, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT), orderAccountBrandMap, true);
            CheckingWordContext checkingWordContext = new CheckingWordContext(checkText, site, delInfringementWordDO.getSonSku(), delInfringementWordDO.getWenAnType());
            ApiResult<List<WordValidateResult>> listApiResult = searchTemplateInfringementWordInfos(checkingWordContext);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError(listApiResult.getErrorMsg());
            }
            List<WordValidateResult> wordValidateResults = listApiResult.getResult();
            if (CollectionUtils.isEmpty(wordValidateResults)) {
                return ApiResult.newSuccess();
            }
            List<String> resultTexts = new ArrayList<>();
            for (String sourceText : delInfringementWordDO.getSourceTexts()) {
                String resultText = StringUtils.capitalize(delInfringementWord(sourceText, orderAccountBrandMap, wordValidateResults));
                resultTexts.add(resultText);
            }
            delInfringementWordDO.setSourceTexts(resultTexts);
        } catch (Exception e) {
            return ApiResult.newError("对目标文本删除侵权词失败,原因如下：" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    /**
     * 替换属性的侵权词和商标词
     *
     * @param amazonTemplateBO
     * @return
     */
    public ApiResult<String> replaceTemplateAttrInfringementWord(AmazonTemplateBO amazonTemplateBO) {
        if (null == amazonTemplateBO || !amazonTemplateBO.getSaleVariant() || CollectionUtils.isEmpty(amazonTemplateBO.getAmazonSkus())) {
            return ApiResult.newSuccess();
        }
        try {
            String accountNumber = amazonTemplateBO.getSellerId();
            if (StringUtils.isNotBlank(accountNumber)) {
                boolean flagAmzSpecialGoodsAccountNumber = amazonTemplateForbiddenSaleChannelHelper.checkIsAmzSpecialGoodsAccountNumber(accountNumber);
                if (flagAmzSpecialGoodsAccountNumber) {
                    //ES-11404 【Amazon】模版刊登，参数配置了亚马逊特供产品可刊登店铺的账号不通过系统校验自动过滤删除侵权词/商标词（先做先发）
                    return ApiResult.newSuccess();
                }
            }
            List<AmazonSku> amazonSkuList = handleTemplateAttrReplace(amazonTemplateBO.getAmazonSkus(), amazonTemplateBO.getId(), amazonTemplateBO.getCountry(), amazonTemplateBO.getInterfaceType());
            amazonTemplateBO.setVariations(JSON.toJSONString(amazonSkuList));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 替换属性的侵权词和商标词
     *
     * @param amazonTemplateWithBLOBs
     * @return
     */
    public ApiResult<String> replaceTemplateAttrInfringementWord(AmazonTemplateWithBLOBs amazonTemplateWithBLOBs) {
        if (null == amazonTemplateWithBLOBs || !Boolean.TRUE.equals(amazonTemplateWithBLOBs.getSaleVariant()) || StringUtils.isBlank(amazonTemplateWithBLOBs.getVariations())) {
            return ApiResult.newSuccess();
        }
        try {
            String accountNumber = amazonTemplateWithBLOBs.getSellerId();
            if (StringUtils.isNotBlank(accountNumber)) {
                boolean flagAmzSpecialGoodsAccountNumber = amazonTemplateForbiddenSaleChannelHelper.checkIsAmzSpecialGoodsAccountNumber(accountNumber);
                if (flagAmzSpecialGoodsAccountNumber) {
                    //ES-11404 【Amazon】模版刊登，参数配置了亚马逊特供产品可刊登店铺的账号不通过系统校验自动过滤删除侵权词/商标词（先做先发）
                    return ApiResult.newSuccess();
                }
            }
            List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplateWithBLOBs.getVariations(), AmazonSku.class);
            amazonSkuList = handleTemplateAttrReplace(amazonSkuList, amazonTemplateWithBLOBs.getId(), amazonTemplateWithBLOBs.getCountry(), amazonTemplateWithBLOBs.getInterfaceType());
            amazonTemplateWithBLOBs.setVariations(JSON.toJSONString(amazonSkuList));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    private List<AmazonSku> handleTemplateAttrReplace(List<AmazonSku> amazonSkus, Integer templateId, String site, Integer interfaceType) throws Exception {
        List<String> attrList = AmazonTemplateUtils.getAttrValueList(amazonSkus, interfaceType);
        if (CollectionUtils.isEmpty(attrList)) {
            return amazonSkus;
        }

        // 预处理：构建已存在值的集合和检查侵权词
        Set<String> existingValues = attrList.stream()
                .map(value -> value.trim().toUpperCase())
                .collect(Collectors.toSet());

        String text = StringUtils.join(attrList, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT);
        CheckingWordContext checkingWordContext = new CheckingWordContext(text, site);
        ApiResult<List<WordValidateResult>> trieResultVoApiResult = searchTemplateInfringementWordInfos(checkingWordContext);
        if (!trieResultVoApiResult.isSuccess()) {
            throw new BusinessException("属性校验侵权词商标词失败，" + trieResultVoApiResult.getErrorMsg());
        }

        List<WordValidateResult> infringementWordInfos = trieResultVoApiResult.getResult();
        boolean hasInfringementWords = !CollectionUtils.isEmpty(infringementWordInfos);

        // 如果没有侵权词，直接返回
        if (!hasInfringementWords) {
            return amazonSkus;
        }

        // 创建日志收集器
        AttributeReplaceLogCollector logCollector = new AttributeReplaceLogCollector(site);

        // 根据接口类型选择不同的处理逻辑
        AtomicInteger indexCounter = new AtomicInteger(0);
        if (TemplateInterfaceTypeEnums.JSON.isTrue(interfaceType)) {
            handleNameValueAttributes(amazonSkus, infringementWordInfos, existingValues, logCollector);
            handleJsonInterfaceAttributes(amazonSkus, infringementWordInfos, indexCounter, logCollector);
        } else {
            handleNameValueAttributes(amazonSkus, infringementWordInfos, existingValues, logCollector);
        }


        return amazonSkus;
    }

    /**
     * 处理JSON接口类型的属性替换
     */
    private void handleJsonInterfaceAttributes(List<AmazonSku> amazonSkus,
                                               List<WordValidateResult> infringementWordInfos,
                                               AtomicInteger indexCounter,
                                               AttributeReplaceLogCollector logCollector) {
        for (AmazonSku amazonSku : amazonSkus) {
            Map<String, Object> variantAttribute = amazonSku.getVariantAttribute();
            if (MapUtils.isEmpty(variantAttribute)) {
                continue;
            }

            String articleNumber = amazonSku.getSku();

            // 处理每个属性键值对
            for (Map.Entry<String, Object> entry : variantAttribute.entrySet()) {
                JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(entry.getValue()));
                processJsonAttributeArray(jsonArray, infringementWordInfos, indexCounter, articleNumber, logCollector);
                variantAttribute.put(entry.getKey(), jsonArray);
            }
        }
    }

    /**
     * 处理JSON属性数组
     */
    private void processJsonAttributeArray(JSONArray jsonArray,
                                           List<WordValidateResult> infringementWordInfos,
                                           AtomicInteger indexCounter,
                                           String articleNumber,
                                           AttributeReplaceLogCollector logCollector) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject attrObj = jsonArray.getJSONObject(i);
            String originalValue = attrObj.getString("value");

            if (StringUtils.isNotBlank(originalValue)) {
                String processedValue = processJsonAttributeValue(originalValue, infringementWordInfos, indexCounter, articleNumber, logCollector);
                attrObj.put("value", processedValue);
            }
        }
    }

    /**
     * 处理JSON属性值的侵权词替换
     */
    private String processJsonAttributeValue(String value,
                                             List<WordValidateResult> infringementWordInfos,
                                             AtomicInteger indexCounter,
                                             String articleNumber,
                                             AttributeReplaceLogCollector logCollector) {
        String cleanedValue = infringementWordService.delInfringementWord(value, infringementWordInfos);
        String processedValue = value;

        if (StringUtils.isBlank(cleanedValue)) {
            // 完全被删除，使用索引字符串替换
            processedValue = StrUtil.getLetterListIndexStr(indexCounter.getAndIncrement());
            // 记录日志：原值被完全删除
            logCollector.addLog(articleNumber, value, processedValue);
        } else if (!value.equalsIgnoreCase(cleanedValue)) {
            // 部分被删除，添加索引后缀
            processedValue = cleanedValue + " " + StrUtil.getLetterListIndexStr(indexCounter.getAndIncrement());
            // 记录日志：原值被部分替换
            logCollector.addLog(articleNumber, value, processedValue);
        }

        return processedValue;
    }

    /**
     * 处理普通接口类型的属性替换
     */
    private void handleNameValueAttributes(List<AmazonSku> amazonSkus,
                                           List<WordValidateResult> infringementWordInfos,
                                           Set<String> existingValues,
                                           AttributeReplaceLogCollector logCollector) throws Exception {
        for (AmazonSku amazonSku : amazonSkus) {
            String articleNumber = amazonSku.getSku();
            if (CollectionUtils.isEmpty(amazonSku.getNameValues())) {
                continue;
            }
            for (NameValue nameValue : amazonSku.getNameValues()) {
                String originalValue = nameValue.getValue();
                String processedValue = processNameValue(originalValue, infringementWordInfos, existingValues, articleNumber, logCollector);
                nameValue.setValue(processedValue);
            }
        }
    }

    /**
     * 处理普通属性值
     */
    private String processNameValue(String originalValue,
                                    List<WordValidateResult> infringementWordInfos,
                                    Set<String> existingValues,
                                    String articleNumber,
                                    AttributeReplaceLogCollector logCollector) throws Exception {
        String cleanedValue = infringementWordService.delInfringementWord(originalValue, infringementWordInfos);

        // 检查是否需要替换：有侵权词、大小写不匹配或者是纯数字
        boolean needsReplacement = originalValue.length() != cleanedValue.length() ||
                !originalValue.equalsIgnoreCase(cleanedValue) ||
                StrUtil.checkIsNum(originalValue);

        if (needsReplacement) {
            String replaceValue = generateReplacementValue(infringementWordInfos, existingValues);
            // 记录日志：原值被替换为新值
            logCollector.addLog(articleNumber, originalValue, replaceValue);
            return replaceValue;
        }

        return originalValue;
    }

    /**
     * 生成替换值
     */
    private String generateReplacementValue(List<WordValidateResult> infringementWordInfos,
                                            Set<String> existingValues) throws Exception {
        String replaceValue = StrUtil.replacLettre(existingValues);
        if (StringUtils.isNotBlank(replaceValue)) {
            existingValues.add(replaceValue.toUpperCase());
            return replaceValue;
        } else {
            // 超出可替换数量，抛出异常
            List<String> words = infringementWordInfos.stream()
                    .map(WordValidateResult::getOriginWord)
                    .collect(Collectors.toList());
            String wordMsg = StringUtils.join(words, ",");
            throw new BusinessException(String.format("属性存在商标词或侵权词  %s  替换失败，超出可替换数量，请重新填写", wordMsg));
        }
    }

    /**
     * 批量保存属性替换日志
     */
    private void saveAttributeReplaceLogs(AttributeReplaceLogCollector logCollector) {
        if (!logCollector.hasLogs()) {
            return;
        }

        try {
            // 批量保存日志记录
            amazonAttributeReplaceLogService.saveBatch(logCollector.getLogs(), 300);
        } catch (Exception e) {
            log.error("保存属性替换日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 调用侵权词服务，检索侵权词、商标词
     * 商标词标识、侵权词标识
     *
     * @param searchVo
     * @return
     */
    public ApiResult<TrieResultVo> searchListingAllInfringementWord(SearchVo searchVo) {
        try {
            if (StringUtils.isBlank(searchVo.getText())) {
                return ApiResult.newSuccess();
            }
            CheckingWordContext context = new CheckingWordContext(searchVo.getText(), searchVo.getSite());
            context.setSonSku(searchVo.getSonSku());
            ApiResult<TrieResultVo> apiResult = listingCheckingWordService.checkingWord(context);
            if (!apiResult.isSuccess()) {
                throw new RuntimeException("调用[checking-service] 检索侵权词异常" + apiResult.getErrorMsg());
            }
            TrieResultVo trieResult = apiResult.getResult();
            // 排除不需要过滤的特殊词汇
            TortUtils.removeNoFiltering(trieResult);
            return apiResult;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 文本过滤侵权词
     *
     * @param tagSource
     * @param orderAccountBrandMap
     * @param infringementWordInfoList
     * @return
     */
    public String delInfringementWord(String tagSource, Map<String, String> orderAccountBrandMap, List<WordValidateResult> infringementWordInfoList) {
        if (StringUtils.isBlank(tagSource) || CollectionUtils.isEmpty(infringementWordInfoList)) {
            return tagSource;
        }
        tagSource = orderAccountBrandReplace(tagSource, orderAccountBrandMap, true);
        tagSource = infringementWordService.delInfringementWord(tagSource, infringementWordInfoList);
        tagSource = orderAccountBrandReplace(tagSource, orderAccountBrandMap, false);
        return tagSource;
    }

    /**
     * searchTerms 文本过滤侵权词
     *
     * @param template
     * @param orderAccountBrandMap
     * @param infringementWordInfoList
     * @return
     */
    public void searchTermsDelInfringementWord(AmazonTemplateWithBLOBs template, Map<String, String> orderAccountBrandMap, List<WordValidateResult> infringementWordInfoList) {
        if (null == template || StringUtils.isBlank(template.getSearchTerms()) || CollectionUtils.isEmpty(infringementWordInfoList)) {
            return;
        }
        try {
            if (BooleanUtils.isTrue(template.getSaleVariant())) {
                Map<String, List<String>> searchDataMap = (Map<String, List<String>>) JSON.parse(template.getSearchTerms());
                for (List<String> values : searchDataMap.values()) {
                    for (int i = 0; i < values.size(); i++) {
                        String value = values.get(i);
                        value = delInfringementWord(value, orderAccountBrandMap, infringementWordInfoList);
                        values.set(i, value);
                    }
                }
                template.setSearchTerms(JSON.toJSONString(searchDataMap));
            } else {
                String searchTerms = jsonParseArrayDelInfringementWord(template.getSearchTerms(), orderAccountBrandMap, infringementWordInfoList);
                template.setSearchTerms(searchTerms);
            }
        } catch (Exception e) {
            log.error("searchTermsn 过滤侵权词出错" + e.getMessage(), e);
            throw new RuntimeException("searchTermsn 过滤侵权词出错" + e.getMessage());
        }
    }

    /**
     * josn list 去除侵权词
     *
     * @param tagSource
     * @param orderAccountBrandMap
     * @param infringementWordInfoList
     * @return
     */
    public String jsonParseArrayDelInfringementWord(String tagSource, Map<String, String> orderAccountBrandMap, List<WordValidateResult> infringementWordInfoList) {
        if (StringUtils.isBlank(tagSource)) {
            return tagSource;
        }
        try {
            List<String> list = JSON.parseArray(tagSource, String.class);
            for (int i = 0; i < list.size(); i++) {
                String value = list.get(i);
                value = delInfringementWord(value, orderAccountBrandMap, infringementWordInfoList);
                list.set(i, value);
            }
            return JSON.toJSONString(list);
        } catch (Exception e) {
            log.error("searchTermsn 过滤侵权词出错" + e.getMessage(), e);
            throw new RuntimeException("searchTermsn 过滤侵权词出错" + e.getMessage());
        }
    }

    /**
     * html 过滤侵权词
     *
     * @param html
     * @param orderAccountBrandMap
     * @param infringementWordInfoList
     * @return
     */
    public String htmlDelInfringementWord(String html, Map<String, String> orderAccountBrandMap, List<WordValidateResult> infringementWordInfoList) {
        if (StringUtils.isBlank(html) || CollectionUtils.isEmpty(infringementWordInfoList)) {
            return html;
        }

        Document doc = Jsoup.parse(html);
        htmlTranTextDelInfringementWord(doc.body(), orderAccountBrandMap, infringementWordInfoList);
        String updatedHtml = null;

        Elements docBody = doc.select("body");
        if (docBody != null) {
            updatedHtml = docBody.html(); // 提取 <body> 内容
        } else {
            updatedHtml = doc.html();
        }
        updatedHtml = updatedHtml.replaceAll("\\n", ""); // Jsoup默认会加 \n 手动去除
        return updatedHtml;
    }


    /**
     * html转文本后 删除侵权词
     *
     * @param node
     * @param orderAccountBrandMap
     * @param infringementWordInfoList
     */
    private void htmlTranTextDelInfringementWord(Node node, Map<String, String> orderAccountBrandMap, List<WordValidateResult> infringementWordInfoList) {
        if (node instanceof TextNode) {
            TextNode textNode = (TextNode) node;
            String text = textNode.getWholeText();

            text = text.replaceAll("\\u00A0", " "); // html 处理后会变成 不间断空格 需要替换回
            String newText = this.delInfringementWord(text, orderAccountBrandMap, infringementWordInfoList);
            textNode.text(newText);
        } else if (node instanceof Element) {
            Element element = (Element) node;
            for (Node child : element.childNodes()) {
                htmlTranTextDelInfringementWord(child, orderAccountBrandMap, infringementWordInfoList);
            }
        }
    }

    public List<WordValidateResult> getInfringementWordInfos(String site, TrieResultVo trieResult, boolean filterSystemBrandWordWhiteList) {
        List<WordValidateResult> infringementWordInfos = new ArrayList<>();
        if (trieResult == null) {
            return infringementWordInfos;
        }
        // 排除Amazon 白名单
        excludeBrandWordWhiteList(site, trieResult.getBrandWordSourceMap());
        excludeBrandWordWhiteList(site, trieResult.getInfringementWordSourceMap());
        if (MapUtils.isNotEmpty(trieResult.getBrandWordSourceMap())) {
            Map<String, WordValidateResult> brandWordSourceMap = trieResult.getBrandWordSourceMap();
            brandWordSourceMap.values().stream().filter(Objects::nonNull).forEach(infringementWordInfos::add);
        }

        if (MapUtils.isNotEmpty(trieResult.getInfringementWordSourceMap())) {
            Map<String, WordValidateResult> infringementWordSourceMap = trieResult.getInfringementWordSourceMap();
            infringementWordSourceMap.values().stream().filter(Objects::nonNull).forEach(infringementWordInfos::add);
        }
        return infringementWordInfos;
    }

    /**
     * 排除系统参数商标词白名单
     *
     * @param site
     * @param brandWordSourceMap
     */
    public void excludeBrandWordWhiteList(String site, Map<String, WordValidateResult> brandWordSourceMap) {
        List<String> brandWhiteListWords = amazonTrademarkWordWhitelistService.getTrademarkWhiteListWords(site);
        if (CollectionUtils.isEmpty(brandWhiteListWords) || MapUtils.isEmpty(brandWordSourceMap)) {
            return;
        }
        List<String> keySet = new ArrayList<>(brandWordSourceMap.keySet());
        for (String word : keySet) {
            for (String whiteListWord : brandWhiteListWords) {
                if (StringUtils.equalsIgnoreCase(word, whiteListWord)) {
                    brandWordSourceMap.remove(word);
                    break;
                }
            }
        }
    }

    /**
     * Amazon 商标词白名单
     *
     * @return
     */
    public List<String> getBrandWhiteListWords() {
        //String paramValue = CacheUtils.SystemParamGet("amazon_param.brand_word_white_list").getParamValue();
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "amazon_param", "brand_word_white_list", null);
        List<String> brandWhiteListWords = new ArrayList<>();
        if (StringUtils.isNotBlank(paramValue)) {
            String[] split = StringUtils.split(paramValue, ",");
            for (String word : split) {
                String trim = StringUtils.trim(word);
                if (StringUtils.isNotBlank(trim) && !brandWhiteListWords.contains(trim)) {
                    brandWhiteListWords.add(trim);
                }
            }
        }
        return brandWhiteListWords;
    }
}


