package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.amazon.model.NewProductPublishConfig;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.SaleStructureVO;
import lombok.Data;

import java.util.Optional;

@Data
public class NewProductPublishConfigVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 销售
     */
    @ExcelProperty("销售工号")
    private String saleNumber;

    /**
     * 分配数量
     */
    @ExcelProperty("分配数量")
    private Integer allocatedQuantity;

    /**
     * 销售组长
     */
    private String saleLeader;


    /**
     * 销售主管
     */
    private String saleSupervisor;

    public static NewProductPublishConfigVO convertToVO(NewProductPublishConfig newProductPublishConfig) {
        NewProductPublishConfigVO publishConfigVO = new NewProductPublishConfigVO();
        publishConfigVO.setId(newProductPublishConfig.getId());
        publishConfigVO.setSaleNumber(newProductPublishConfig.getSaleNumber());

        SaleStructureVO saleSuperiorNew = NewUsermgtUtils.getSaleSuperiorNew(newProductPublishConfig.getSaleNumber());
        String newSaleLeader = Optional.ofNullable(saleSuperiorNew.getSaleLeader())
                .map(s -> s.substring(s.lastIndexOf("-") + 1))
                .orElse("");

        String newSaleSupervisor = Optional.ofNullable(saleSuperiorNew.getSaleManager())
                .map(s -> s.substring(s.lastIndexOf("-") + 1))
                .orElse("");
        publishConfigVO.setSaleLeader(newSaleLeader);
        publishConfigVO.setSaleSupervisor(newSaleSupervisor);
        publishConfigVO.setAllocatedQuantity(newProductPublishConfig.getAllocatedQuantity());
        return publishConfigVO;
    }
}
