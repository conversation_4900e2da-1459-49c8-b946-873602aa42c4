package com.estone.erp.publish.amazon.componet.templatestatus;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingInfoService;
import io.swagger.client.model.listings.ItemSummaryByMarketplace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

/**
 * Amazon发布状态检查器
 * 负责检查模版发布状态、超时判定、Listing信息同步等功能
 */
@Slf4j
@Component
public class AmazonPublishStatusChecker {

    @Autowired
    private IAmazonListingInfoService amazonListingInfoService;

    /**
     * 检查模版是否超时
     * 
     * @param template 模版信息
     * @param timeoutHours 超时小时数
     * @return true-超时，false-未超时
     */
    public boolean isTemplateTimeout(AmazonTemplateBO template, int timeoutHours) {
        LocalDateTime lastUpdateTime = LocalDateTime.ofInstant(template.getLastUpdateDate().toInstant(), 
                                                               java.time.ZoneId.systemDefault());
        LocalDateTime now = LocalDateTime.now();
        long hours = ChronoUnit.HOURS.between(lastUpdateTime, now);
        
        boolean timeout = hours >= timeoutHours;
        if (timeout) {
            log.info("模版刊登超时（超过{}小时），模版ID：{}，更新时间：{}", 
                    timeoutHours, template.getId(), lastUpdateTime);
        }
        return timeout;
    }

    /**
     * 同步并获取Listing信息
     * 
     * @param template 模版信息
     * @return Listing信息列表，失败时返回null
     */
    public List<AmazonListingInfo> syncListingInfo(AmazonTemplateBO template) {
        // 获取模版的SellerSku映射
        Map<String, String> allSellerSkuSkuMapping = AmazonTemplateUtils.getAllSellerSkuSkuMapping(template);
        if (MapUtils.isEmpty(allSellerSkuSkuMapping)) {
            log.warn("模版没有SellerSku信息，模版ID：{}", template.getId());
            return null;
        }

        // 调用同步接口获取Listing信息
        ApiResult<List<AmazonListingInfo>> apiResult = amazonListingInfoService.syncListingInfo(
                template.getSellerId(),
                template.getCountry(),
                allSellerSkuSkuMapping,
                template.getSkuDataSource()
        );

        if (!apiResult.isSuccess() || CollectionUtils.isEmpty(apiResult.getResult())) {
            log.warn("同步Listing信息失败，模版ID：{}, 错误信息：{}", template.getId(), apiResult.getErrorMsg());
            return null;
        }

        return apiResult.getResult();
    }

    /**
     * 判断商品状态是否为可售
     * 
     * @param itemStatus 商品状态列表
     * @return true-可售，false-不可售
     */
    public boolean isBuyable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue());
    }

    /**
     * 判断商品状态是否为可发现
     * 
     * @param itemStatus 商品状态列表
     * @return true-可发现，false-不可发现
     */
    public boolean isDiscoverable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue());
    }
} 