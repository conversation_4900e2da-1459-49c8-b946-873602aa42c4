package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.tidb.publishtidb.componet.AmazonTrademarkWordWhitelistHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Amazon商标词白名单初始化定时任务
 *
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */
@Slf4j
@Component
public class AmazonTrademarkWordWhitelistInitJob extends AbstractJobHandler {

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private AmazonTrademarkWordWhitelistHelper amazonTrademarkWordWhitelistHelper;

    public AmazonTrademarkWordWhitelistInitJob() {
        super("AmazonTrademarkWordWhitelistInitJob");
    }

    @Override
    @XxlJob("AmazonTrademarkWordWhitelistInitJob")
    public ReturnT<String> run(String param) throws Exception {
        try {
            DataContextHolder.setUsername("admin");
            SystemParam systemParam = systemParamService.querySystemParamByCodeKey("amazon_param.brand_word_white_list");
            if (null == systemParam) {
                XxlJobLogger.log("Amazon商标词校验白名单未设置,结束执行该任务,请先到系统参数配置");
                return ReturnT.FAIL;
            }


            List<String> whitelists = CommonUtils.splitList(systemParam.getParamValue(), ",");

            Map<String, List<String>> whitelistMap = new HashMap<>();

            whitelists.forEach(whitelist -> {
                whitelistMap.put(whitelist, null);

            });

            amazonTrademarkWordWhitelistHelper.addWhitelists(whitelistMap, true);
            XxlJobLogger.log("Amazon商标词白名单初始化定时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("Amazon商标词白名单初始化定时任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

}
