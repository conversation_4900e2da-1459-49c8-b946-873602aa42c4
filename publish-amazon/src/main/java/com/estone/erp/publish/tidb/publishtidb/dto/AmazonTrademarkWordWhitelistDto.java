package com.estone.erp.publish.tidb.publishtidb.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * Amazon商标词白名单查询DTO
 * 用于接收前端查询参数，包含分页信息和查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
public class AmazonTrademarkWordWhitelistDto {

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇（模糊查询）
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private List<String> trademarkIdentificationList;

    /**
     * 禁售站点列表（用于查询条件）
     * 参考WalmartItemController.searchWalmartItem方法的prohibitionSiteList字段实现
     */
    private List<String> prohibitionSiteList;

    /**
     * 禁售平台列表（用于查询条件）
     * 参考WalmartItemController.searchWalmartItem方法的forbidChannelStr字段转换为forbidChannelList实现
     */
    private List<String> forbidChannelList;

    /**
     * 添加人
     */
    private List<String> createByList;

    /**
     * 添加时间范围 - 开始时间
     */
    private String createdTimeStart;

    /**
     * 添加时间范围 - 结束时间
     */
    private String createdTimeEnd;

    /**
     * 修改时间范围 - 开始时间
     */
    private String modifiedTimeStart;

    /**
     * 修改时间范围 - 结束时间
     */
    private String modifiedTimeEnd;

    /**
     * 当前页码（默认第1页）
     */
    private Integer pageNum;

    /**
     * 每页条数（默认20条）
     */
    private Integer pageSize;

    /**
     * 排序字段（默认按id排序）
     */
    private String sort;

    /**
     * 是否升序排列
     * true: 升序(ASC)
     * false: 降序(DESC)
     * 默认降序
     */
    private Boolean isAsc;

    /**
     * 获取页码，如果为null则返回默认值1
     */
    public Integer getPageNum() {
        return pageNum != null && pageNum > 0 ? pageNum : 1;
    }

    /**
     * 获取每页条数，如果为null则返回默认值20
     */
    public Integer getPageSize() {
        return pageSize != null && pageSize > 0 ? pageSize : 20;
    }


}
