package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Amazon商标词白名单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
public interface AmazonTrademarkWordWhitelistMapper extends BaseMapper<AmazonTrademarkWordWhitelist> {

    /**
     * 获取TiDB分页元数据
     *
     * @param wrapper 查询条件
     * @return 分页元数据
     */
    List<Map<Object, Object>> getTidbPageMetaMap(@Param(Constants.WRAPPER) LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper);
}
