package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.NewProductPublishConfig;
import com.estone.erp.publish.amazon.model.NewProductPublishConfigCriteria;
import com.estone.erp.publish.amazon.model.dto.NewProductPublishConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-29 17:54:40
 */
public interface NewProductPublishConfigService {

    CQueryResult<NewProductPublishConfigVO> search(CQuery<NewProductPublishConfigCriteria> cquery);

    void updateNewProductPublishConfig(List<NewProductPublishConfig> requestParam);

    /**
     * 获取所有必刊登新品主管人员分配配置
     * @return
     */
    List<NewProductPublishConfig> getAllNewProductPublishConfig();

    ApiResult<String> importSaleConfig(List<NewProductPublishConfigVO> employeeList);

    ApiResult<String> batchDelete(List<Integer> ids);
}