package com.estone.erp.publish.amazon.jobHandler;


import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.AmazonProductListingEsBulkProcessor;
import com.estone.erp.publish.amazon.model.dto.AmazonUpdateInfringementDO;
import com.estone.erp.publish.amazon.model.dto.SearchVo;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonListingWaitCheck;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonListingWaitCheckService;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.system.erpCommon.module.TrieResultVo;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingWaitCheck;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonListingWaitCheckService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 增量
 *
 * @Description 定时任务：检测Amazon在线listing每天变化的侵权词商标词
 **/
@Slf4j
@Component
@Deprecated
public class WaitCheckInfringBrandWordJobHandler extends AbstractJobHandler {
    private final static int pageSize = 10000;

    /**
     * 可以设置需要查询的字段，不设置则取下方默认字段
     */
    private final static String[] fields = {"accountNumber", "site", "sellerSku", "mainSku", "articleNumber", "itemName", "itemDescription", "brandName", "order_num_total", "order_last_30d_count","infringingBrandWord"};

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private EsAmazonListingWaitCheckService esAmazonListingWaitCheckService;
    @Resource
    private AmazonListingWaitCheckService amazonListingWaitCheckService;
    @Resource
    private AmazonInfringementWordHelper  amazonInfringementWordHelper;
    @Autowired
    private AmazonProductListingEsBulkProcessor amazonProductListingEsBulkProcessor;


    public WaitCheckInfringBrandWordJobHandler() {
        super("WaitCheckInfringBrandWordJobHandler");
    }

    @Override
    @XxlJob("WaitCheckInfringBrandWordJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        Date time = DateUtils.addDays(new Date(),-1);
        String startUpdateDate = DateUtils.format(time,DateUtils.STANDARD_DATE_PATTERN);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(param)){
            XxlJobLogger.log("查询前一天: "  + startUpdateDate);
            if (!ObjectUtils.isEmpty(startUpdateDate)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("flagDate").from(startUpdateDate));
                XxlJobLogger.log(String.format("查询全量时间跑之后的时间范围：从%s 开始",startUpdateDate));
            }
        }else {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("flagDate").from("2022-10-27 09:30:00"));
            Date time1 = DateUtils.addHours(new Date(),-1);
            String endUpdateDate = DateUtils.format(time1,DateUtils.STANDARD_DATE_PATTERN);
            boolQueryBuilder.must(QueryBuilders.rangeQuery("flagDate").to(endUpdateDate));
            XxlJobLogger.log(String.format("查询全量校验之后的时间范围：从 2022-10-27 09:30:00 到 %s",endUpdateDate));
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(boolQueryBuilder)
                .withFields("id")
                .withPageable(PageRequest.of(0, 1));
        NativeSearchQuery searchQuery = queryBuilder.build();
        Page<EsAmazonListingWaitCheck> checkPage = esAmazonListingWaitCheckService.search(searchQuery);
        if (null == checkPage || checkPage.getTotalElements() <= 0) {
            XxlJobLogger.log(String.format("无待检测产品"));
            return ReturnT.FAIL;
        }
        long total = checkPage.getTotalElements();
        XxlJobLogger.log("查询检测条数：" + total);
        List<String> idList = new ArrayList<>();
        long totalPages = (long) Math.ceil((double) total / pageSize);
        for (int i = 0; i <= totalPages; i++) {
            NativeSearchQueryBuilder query = new NativeSearchQueryBuilder();
            query.withFields("id")
                    .withQuery(boolQueryBuilder)
                    .withSort(SortBuilders.fieldSort("flagDate").order(SortOrder.ASC))
                    .withPageable(PageRequest.of(i, pageSize));
            NativeSearchQuery search = query.build();
            Page<EsAmazonListingWaitCheck> result = esAmazonListingWaitCheckService.search(search);
            if (CollectionUtils.isNotEmpty(result.getContent())) {
                List<String> ids = result.getContent().stream().map(t -> t.getId()).collect(Collectors.toList());
                idList.addAll(ids);
            }
        }

        XxlJobLogger.log("***************** 发送校验开始,数量条数 ：" + idList.size());
        List<List<String>> allId = Lists.partition(idList,1000);
        for (List<String> ids : allId) {
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setIdList(ids);
            esAmazonProductListingRequest.setFields(fields);
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
                log.warn("此次在线Listing为空，不校验商标词");
                continue;
            }
            Map<String,List<EsAmazonProductListing>> accountNumberListMap = esAmazonProductListingList.stream().collect(Collectors.groupingBy(k -> k.getAccountNumber()));
            for (String accountNumber : accountNumberListMap.keySet()){
                Map<String, String> orderAccountBrandMap = amazonInfringementWordHelper.getOrderAccountBrandMap(accountNumber);
                for (EsAmazonProductListing esAmazonProductListing : accountNumberListMap.get(accountNumber)) {
                    AmazonExecutors.checkListingInfringementword(() -> {
                        try {
                            String sonsku = esAmazonProductListing.getArticleNumber().toUpperCase().trim();
                            List<String> txtList = Arrays.asList(esAmazonProductListing.getItemName(), esAmazonProductListing.getItemDescription(), esAmazonProductListing.getBrandName());
                            String text = StringUtils.join(txtList, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT).replaceAll("<br>",AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT).replaceAll("<br/>",AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT);
                            text = amazonInfringementWordHelper.orderAccountBrandReplace(text,orderAccountBrandMap,true);
                            List<WordValidateResult> infringementWordInfos = null;
                            if (StringUtils.isNotBlank(text)) {
                                SearchVo searchVo = new SearchVo(text, esAmazonProductListing.getSite(), null,sonsku);
                                ApiResult<TrieResultVo> trieResultVoApiResult = amazonInfringementWordHelper.searchListingAllInfringementWord(searchVo);
                                infringementWordInfos = amazonInfringementWordHelper.getInfringementWordInfos(esAmazonProductListing.getSite(), trieResultVoApiResult.getResult(), false);
                            }

                            if (CollectionUtils.isNotEmpty(infringementWordInfos)  || StringUtils.isNotBlank(esAmazonProductListing.getInfringingBrandWord())){
                                AmazonUpdateInfringementDO updateInfringementDO = new AmazonUpdateInfringementDO(esAmazonProductListing.getId(), new Date());
                                String infringBrandWord = null;
                                List<String> trademarkIdentification = null;
                                if (CollectionUtils.isNotEmpty(infringementWordInfos) ) {
                                    List<String> words = infringementWordInfos.stream()
                                            .map(WordValidateResult::getOriginWord)
                                            .distinct()
                                            .collect(Collectors.toList());

                                    infringBrandWord = StringUtils.join(words, ",");
                                    trademarkIdentification = infringementWordInfos.stream()
                                            .filter(o -> o != null && CollectionUtils.isNotEmpty(o.getTrademarkIdentification()))
                                            .map(WordValidateResult::getTrademarkIdentification)
                                            .collect(HashSet::new, HashSet::addAll, HashSet::addAll)
                                            .stream()
                                            .map(String::valueOf)
                                            .collect(Collectors.toList());

                                    updateInfringementDO.setInfringingBrandWord(infringBrandWord);
                                    updateInfringementDO.setTrademarkIdentification(trademarkIdentification);
                                }
                                updateInfringementDO.setInfringementWordInfos(infringementWordInfos);
                                amazonProductListingEsBulkProcessor.syncUpdateInfringementWord(updateInfringementDO);
                            }
                        } catch (Exception e) {
                            log.error(esAmazonProductListing.getAccountNumber() + "执行校验商标词数据异常:" + esAmazonProductListing.getId() + e.getMessage());
                            //  失败存入标识数据等待重试
                            AmazonListingWaitCheck esAmazonListingWaitCheck = new AmazonListingWaitCheck();
                            esAmazonListingWaitCheck.setRelationId(esAmazonProductListing.getId());
                            esAmazonListingWaitCheck.setTryFlagArticleNumber(esAmazonProductListing.getArticleNumber());
                            esAmazonListingWaitCheck.setFlagDate(new Timestamp(System.currentTimeMillis()));
                            amazonListingWaitCheckService.insert(esAmazonListingWaitCheck);
                        }
                    });
                }
            }
        }
        XxlJobLogger.log("***************** 发送校验结束 *****************");
        return ReturnT.SUCCESS;
    }
}
