package com.estone.erp.publish.amazon.jobHandler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.tidb.publishtidb.componet.AmazonTrademarkWordWhitelistHelper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 亚马逊同步侵权词白名单
 *
 * @Auther xhx
 */
@Component
public class AmazonSyncTrademarkWordWhitelistJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService;

    @Autowired
    private AmazonTrademarkWordWhitelistHelper amazonTrademarkWordWhitelistHelper;

    public AmazonSyncTrademarkWordWhitelistJobHandler() {
        super(AmazonSyncTrademarkWordWhitelistJobHandler.class.getName());
    }

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("AmazonSyncTrademarkWordWhitelistJobHandler")
    public ReturnT<String> run(String param) throws Exception {

        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<TidbPageMeta<Long>> pageMetaList = amazonTrademarkWordWhitelistService.getPageMetaListByWrapper(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(pageMetaList)) {
            return ReturnT.SUCCESS;
        }
        for (TidbPageMeta<Long> longTidbPageMeta : pageMetaList) {
            try {
                updatePage(longTidbPageMeta);
            } catch (Exception e) {
                XxlJobLogger.log("同步侵权词白名单异常: {}", e.getMessage());
            }
        }

        long totalCount = pageMetaList.stream()
                .mapToLong(TidbPageMeta::getPageSize)
                .sum();

        XxlJobLogger.log("同步侵权词白名单完成,total:{}", totalCount);
        return ReturnT.SUCCESS;
    }

    private void updatePage(TidbPageMeta<Long> longTidbPageMeta) {
        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ge(AmazonTrademarkWordWhitelist::getId, longTidbPageMeta.getStartKey())
                .le(AmazonTrademarkWordWhitelist::getId, longTidbPageMeta.getEndKey());
        List<AmazonTrademarkWordWhitelist> list = amazonTrademarkWordWhitelistService.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<String> words = list.stream().map(AmazonTrademarkWordWhitelist::getInfringementWord).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(words)) {
            return;
        }

        Map<String, Triple<String, String, String>> infringementWordTripleMap = amazonTrademarkWordWhitelistHelper.getInfringementWordTripleMap(words);

        for (AmazonTrademarkWordWhitelist amazonTrademarkWordWhitelist : list) {

            String infringementWordUpper = StringUtils.upperCase(amazonTrademarkWordWhitelist.getInfringementWord());
            Triple<String, String, String> triple = infringementWordTripleMap.get(infringementWordUpper);
            if (triple == null) {
                continue;
            }
            amazonTrademarkWordWhitelist.setTrademarkIdentification(triple.getLeft());
            amazonTrademarkWordWhitelist.setProhibitionSite(triple.getMiddle());
            amazonTrademarkWordWhitelist.setForbidChannel(triple.getRight());
            amazonTrademarkWordWhitelist.setUpdatedTime(LocalDateTime.now());
        }
        amazonTrademarkWordWhitelistService.updateBatchById(list);
    }


}
