package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.AmazonListingInfringementHelper;
import com.estone.erp.publish.amazon.model.AmazonShippingCostModel;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.platform.enums.AmazonCommonlyUsedSiteEnum;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.google.common.collect.Maps;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/8/18
 */
@Slf4j
public class AmazonListingUtils {

    public static final String NOT_ARTICLENUMER  = "匹配不到货号";

    private static final String COMPOSE_SKU_PREFIX = "ZH";

    private static String mysqlFields = "accountNumber, site, sellerSku,parentAsin, articleNumber,mainSku, itemName, itemDescription, brandName";


    private static AmazonProductListingService amazonProductListingService = SpringUtils.getBean(AmazonProductListingService.class);
    private static final AmazonInfringementWordHelper amazonInfringementWordHelper = SpringUtils.getBean(AmazonInfringementWordHelper.class);
    private static final AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService = SpringUtils.getBean(AmazonTrademarkWordWhitelistService.class);

    /**
     * 获取产品系统信息
     *
     * @param amazonProductListing
     */
    public static void handleAmazonProductInfo(AmazonProductListing amazonProductListing, boolean isSelectMerageSku,String articleNumber) {
        if (StringUtils.isBlank(articleNumber)) {
            articleNumber = amazonProductListing.getArticleNumber();
        }
        try {
            if (isSelectMerageSku){
                //  更换为合并sku
                String mergeSku = ProductUtils.getMergeSku(articleNumber);
                log.info( "isSelectMergeSku before->after:{} -> {}", articleNumber, mergeSku);
                articleNumber = mergeSku;
            }
            // 匹配组合套装，匹配上了就按组合产品的信息填充
            if (matchComposeProduct(articleNumber, amazonProductListing)) {
                return;
            }

            ProductInfoVO productInfoVO = ProductUtils.getSkuInfo(articleNumber);
            // 判断单品状态是否是废弃sku
            if (null != productInfoVO && StringUtils.isNotEmpty(productInfoVO.getSkuStatus())
                    && productInfoVO.getSkuStatus().equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode())){
                articleNumber = ProductUtils.getMergeSku(articleNumber);
                productInfoVO = ProductUtils.getSkuInfo(articleNumber);
            }
            if (null != productInfoVO && StringUtils.isNotEmpty(productInfoVO.getSonSku())) {
                // 主SKU
                amazonProductListing.setMainSku(StringUtils.upperCase(productInfoVO.getMainSku()));
                // 子SKU
                amazonProductListing.setArticleNumber(StringUtils.upperCase(productInfoVO.getSonSku()));
                // 类别id
                amazonProductListing.setCategoryId(productInfoVO.getCategoryId());
                // 类别中文名
                amazonProductListing.setCategoryCnName(productInfoVO.getCategoryCnName());
                // 禁售平台(逗号拼接)
                amazonProductListing.setForbidChannel(productInfoVO.getForbidChannel());
                // 单品状态
                amazonProductListing.setSkuStatus(productInfoVO.getSkuStatus());
                // 产品标签code
                amazonProductListing.setTagCodes(productInfoVO.getTagCodes());
                // 产品标签
                amazonProductListing.setTagNames(productInfoVO.getTagNames());
                // 特殊标签
                amazonProductListing.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
                // 特殊标签
                amazonProductListing.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());
                //禁售类型
                amazonProductListing.setInfringementTypename(productInfoVO.getInfringementTypeName());
                //禁售原因
                amazonProductListing.setInfringementObj(productInfoVO.getInfringementObj());
                //禁售站点
                List<String> siteList = productInfoVO.getProhibitionSite(SaleChannel.CHANNEL_AMAZON, ()->AmazonCommonlyUsedSiteEnum.getSiteList(null));
                if(CollectionUtils.isNotEmpty(siteList)) {
                    amazonProductListing.setNormalSale("," + String.join(",", siteList) + ",");
                }
                // 按照优先级取风险等级
                Integer highRiskLevelId = ProductUtils.getHighRiskLevelId(productInfoVO.getRiskLevelIdList());
                if (null != highRiskLevelId) {
                    amazonProductListing.setRiskLevelId(highRiskLevelId);
                }
                // 是否促销
                amazonProductListing.setPromotion(productInfoVO.getPromotion());
                // 是否新品
                amazonProductListing.setNewState(productInfoVO.getNewState());
                Integer skuDataSource = null != amazonProductListing.getSkuDataSource()? amazonProductListing.getSkuDataSource(): SkuDataSourceEnum.PRODUCT_SYSTEM.getCode();
                if (articleNumber.startsWith("GT") && !SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(skuDataSource)) {
                    skuDataSource = SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode();
                } else if (articleNumber.startsWith("TY")) {
                    skuDataSource = SkuDataSourceEnum.TAN_YA_SYSTEM.getCode();
                }
                amazonProductListing.setSkuDataSource(skuDataSource);
            }

        } catch (Exception e) {
            log.error(String.format("同步listing数据获取产品SKu信息: %s, ", articleNumber), e);
        }
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param articleNumber        货号
     * @param amazonProductListing listing
     */
    public static boolean matchComposeProduct(String articleNumber, AmazonProductListing amazonProductListing) {
        //log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",amazonProductListing.getAccountNumber(), articleNumber);
        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(amazonProductListing.getSkuDataSource())
                || StringUtils.startsWith(articleNumber, COMPOSE_SKU_PREFIX)) {
            // 组合产品
            ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
            if (composeProduct != null && StringUtils.isNotBlank(composeProduct.getComposeSku())) {
                setProductInfoByCompose(amazonProductListing, composeProduct);
                return true;
            }
        }

        // 非ZH开头的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {
            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return false;
            }
            amazonProductListing.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            amazonProductListing.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            amazonProductListing.setComposeStatus(stepEnum.getCode());
            amazonProductListing.setArticleNumber(articleNumber);
            //禁售类型
            amazonProductListing.setInfringementTypename(StrUtil.strAddDelimiter(StringUtils.join(suiteSku.getInfringementTypeNames(),"|"),"|"));
            //禁售原因
            amazonProductListing.setInfringementObj(StrUtil.strAddDelimiter(StringUtils.join(suiteSku.getInfringementObjs(),"|"),"|"));
            //禁售站点
            String sitesStr = StringUtils.join(suiteSku.getProhibitionSites(), ",");
            String normalSale = StrUtil.strAddComma(sitesStr);
            amazonProductListing.setNormalSale(normalSale);
            //禁售平台(逗号拼接)
            // 按照优先级取风险等级
            Integer highRiskLevelId = ProductUtils.getHighRiskLevelId(suiteSku.getRiskLevelIdList());
            if (null != highRiskLevelId) {
                amazonProductListing.setRiskLevelId(highRiskLevelId);
            }
            amazonProductListing.setForbidChannel(StrUtil.strAddComma(StringUtils.join(suiteSku.getForbidChannels(), ",")));
            return true;
        }
        ComposeSku composeProduct = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProduct != null && StringUtils.isBlank(composeProduct.getComposeSku())) {
            setProductInfoByCompose(amazonProductListing, composeProduct);
            return true;
        }
        return false;
    }

    public static void setProductInfoByCompose(AmazonProductListing amazonProductListing, ComposeSku composeProduct) {
        amazonProductListing.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        String articleNumber = StringUtils.upperCase(composeProduct.getComposeSku());
        // 组合状态
        amazonProductListing.setComposeStatus(composeProduct.getCheckStep());
        // 单品状态
        amazonProductListing.setSkuStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 主SKU
        amazonProductListing.setMainSku(articleNumber);
        // 子SKU
        amazonProductListing.setArticleNumber(articleNumber);
        // 类别id
        String categoryId = composeProduct.getCategoryId() != null ? StrUtil.strAddComma(composeProduct.getCategoryId().toString()) : null;
        amazonProductListing.setCategoryId(categoryId);
        // 类别中文名
        String categoryName = StringUtils.isNotBlank(composeProduct.getCategoryName()) ? StrUtil.strAddComma(composeProduct.getCategoryName().replaceAll(">", ",")) : null;
        amazonProductListing.setCategoryCnName(categoryName);
        //禁售类型
        amazonProductListing.setInfringementTypename(StrUtil.strAddDelimiter(StringUtils.join(composeProduct.getInfringementTypeNames(),"|"),"|"));
        //禁售原因
        amazonProductListing.setInfringementObj(StrUtil.strAddDelimiter(StringUtils.join(composeProduct.getInfringementObjs(),"|"),"|"));
        //禁售站点
        String sitesStr = StringUtils.join(composeProduct.getProhibitionSites(), ",");
        String normalSale = StrUtil.strAddComma(sitesStr);
        amazonProductListing.setNormalSale(normalSale);
        // 按照优先级取风险等级
        Integer highRiskLevelId = ProductUtils.getHighRiskLevelId(composeProduct.getRiskLevelIdList());
        if (null != highRiskLevelId) {
            amazonProductListing.setRiskLevelId(highRiskLevelId);
        }


        //禁售平台(逗号拼接)
        amazonProductListing.setForbidChannel(StrUtil.strAddComma(StringUtils.join(composeProduct.getForbidChannels(), ",")));
        String tagCode = StrUtil.strAddComma(composeProduct.getTagCode());
        String tagName = StrUtil.strAddComma(composeProduct.getTag());
        // 产品标签code
        amazonProductListing.setTagCodes(tagCode);
        // 产品标签
        amazonProductListing.setTagNames(tagName);
        // 特殊标签
        amazonProductListing.setSpecialGoodsCode(null);
        // 特殊标签
        amazonProductListing.setSpecialGoodsName(null);
    }

    /**
     * 默认 价格  在线列表运费数据>店铺配置运费模板对应的运费>店铺配置默认运费模板运费>NULL
     * @param merchantShippingGroup
     * @param amazonShippingCostModels
     * @return
     */
    public static Double getDefaultShippingCost(String merchantShippingGroup, List<AmazonShippingCostModel> amazonShippingCostModels) {
       if(CollectionUtils.isEmpty(amazonShippingCostModels)) {
           return null;
       }
        List<AmazonShippingCostModel> checkShippingCostModels = null;
       if(StringUtils.isNotBlank(merchantShippingGroup)) {
           checkShippingCostModels = amazonShippingCostModels.stream()
                   .filter(o->merchantShippingGroup.equalsIgnoreCase(o.getShippingGroup())).collect(Collectors.toList());
           if(CollectionUtils.isNotEmpty(checkShippingCostModels)) {
               return checkShippingCostModels.get(0).getShippingCost();
           }
       }

        checkShippingCostModels = amazonShippingCostModels.stream().filter(o->o.getIsDefault()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(checkShippingCostModels)) {
            return checkShippingCostModels.get(0).getShippingCost();
        }

       return null;
    }

    /**
     * 排除订单FBA库存管理存在的asin
     * @param esAmazonProductListings
     */
    public static List<EsAmazonProductListing> filterFBAExistAsin(List<EsAmazonProductListing> esAmazonProductListings) {
        esAmazonProductListings = esAmazonProductListings.stream().filter(o -> StringUtils.isNotBlank(o.getSonAsin())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esAmazonProductListings)) {
            return esAmazonProductListings;
        }
        List<String> sonAsinList = esAmazonProductListings.stream().map(EsAmazonProductListing::getSonAsin).collect(Collectors.toList());

        // 获取FBA asin
        List<String> existSonAsinList = OrderUtils.getFBAExistAsins(sonAsinList);
        if (CollectionUtils.isNotEmpty(existSonAsinList)) {
            esAmazonProductListings = esAmazonProductListings
                    .stream().filter(o -> !existSonAsinList.contains(o.getSonAsin())).collect(Collectors.toList());
        }

        return esAmazonProductListings;
    }

    /**
     * 排除存在可售数量或在途数量的FBA asin
     *
     * @param esAmazonProductListings
     */
    public static List<EsAmazonProductListing> filterExistInventoryFBAAsin(List<EsAmazonProductListing> esAmazonProductListings) {
        esAmazonProductListings = esAmazonProductListings.stream().filter(o -> StringUtils.isNotBlank(o.getSonAsin())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esAmazonProductListings)) {
            return esAmazonProductListings;
        }
        List<String> sonAsinList = esAmazonProductListings.stream().map(EsAmazonProductListing::getSonAsin).collect(Collectors.toList());

        // 获取存在可售数量或在途数量的FBA asin
        List<String> existSonAsinList = OrderUtils.checkSellableAndOnWayQuantity(sonAsinList);
        if (CollectionUtils.isNotEmpty(existSonAsinList)) {
            esAmazonProductListings = esAmazonProductListings
                    .stream().filter(o -> !existSonAsinList.contains(o.getSonAsin())).collect(Collectors.toList());
        }

        return esAmazonProductListings;
    }

    /**
     * 根据模板刷listing刊登角色
     * @param amazonTemplateBOS
     */
    public static void brushListingPublishRole(List<AmazonTemplateBO> amazonTemplateBOS) {
        for (AmazonTemplateBO amazonTemplateBO : amazonTemplateBOS) {
            try {
                List<AmazonProductListing> listingList = new ArrayList<>();

                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(amazonTemplateBO.getSellerId());
                amazonProductListing.setPublishRole(amazonTemplateBO.getPublishRole());
                amazonProductListing.setSellerSku(amazonTemplateBO.getSellerSku());
                listingList.add(amazonProductListing);

                Boolean saleVariant = amazonTemplateBO.getSaleVariant();
                if (null == saleVariant) {
                    XxlJobLogger.log(String.format("模板%s无法判断是单体还是变体", amazonTemplateBO.getId()));
                    continue;
                }

                // 如果是变体
                if(saleVariant) {
                    String variations = amazonTemplateBO.getVariations();
                    if (StringUtils.isBlank(variations)) {
                        XxlJobLogger.log(String.format("模板%s的多属性值为空", amazonTemplateBO.getId()));
                        continue;
                    }

                    List<AmazonSku> amazonSkuList = JSON.parseArray(variations, AmazonSku.class);
                    for (AmazonSku amazonSku : amazonSkuList) {
                        AmazonProductListing productListing = new AmazonProductListing();
                        productListing.setAccountNumber(amazonTemplateBO.getSellerId());
                        productListing.setPublishRole(amazonTemplateBO.getPublishRole());
                        productListing.setSellerSku(amazonSku.getSellerSKU());
                        listingList.add(productListing);
                    }
                }

                amazonProductListingService.batchUpdatePublishRoleByTemplate(listingList, amazonTemplateBO.getCountry());
            } catch (Exception e) {
                log.error(String.format("模板id为%s在执行刷数据时报错：%s", amazonTemplateBO.getId(), e.getMessage()));
                XxlJobLogger.log(String.format("模板id为%s在执行刷数据时报错：%s", amazonTemplateBO.getId(), e.getMessage()));
            }
        }
    }

    /**
     * 判断产品单品状态是否为清仓，甩卖，且库存+在途-待发＞0，且SKU在对应店铺不存在禁售站点
     * @param skuStatus
     * @param skuStock
     * @param normalSale
     * @param site
     * @return
     */
    public static Boolean checkClearanceReductionListing(String skuStatus, Integer skuStock, String normalSale, String site) {
        return (SingleItemEnum.CLEARANCE.getEnName().equals(skuStatus)
                || SingleItemEnum.REDUCTION.getEnName().equals(skuStatus))
                && null != skuStock
                && skuStock > 0
                && StringUtils.isNotBlank(site)
                && (StringUtils.isBlank(normalSale)
                || !normalSale.contains(site));
    }


    /**
     *  查询数据库 标题、品牌、描述校验
     * @param esAmazonProductListing
     * @return
     */
    public static AmazonProductListing getAmazonProductListing(EsAmazonProductListing esAmazonProductListing){
        AmazonProductListingExample example  = new AmazonProductListingExample();
        example.createCriteria().andAccountNumberEqualTo(esAmazonProductListing.getAccountNumber()).andSellerSkuEqualTo(esAmazonProductListing.getSellerSku());
        example.setColumns(mysqlFields);
        String site = esAmazonProductListing.getSite();
        List<AmazonProductListing> amazonProductListingList = amazonProductListingService.selectCustomColumnByExample(example,site);
        if (CollectionUtils.isNotEmpty(amazonProductListingList)){
            return amazonProductListingList.get(0);
        }
        return null;
    }

    /**
     * 链接修改标题描述删除侵权词
     *
     * @param updateData
     * @param amazonVariantTitleList
     * @param operationLogs
     */
    public static void updateTitleDelInfringementWord(List<EsAmazonProductListing> updateData, List<AmazonVariantBO> amazonVariantTitleList, List<AmazonPublishOperationLog> operationLogs, Boolean descIsHtml) {
        List<String> brandWhiteListWords = amazonTrademarkWordWhitelistService.getAllTrademarkWhiteListWords();
        Map<String, List<EsAmazonProductListing>> accountListing = updateData.stream().collect(Collectors.groupingBy(EsAmazonProductListing::getAccountNumber));
        accountListing.forEach((account, items)-> {
            Map<String,String> orderAccountBrandMap = amazonInfringementWordHelper.getOrderAccountBrandMap(account);
            for (EsAmazonProductListing item : items) {
                String title = item.getItemName();
                String description = item.getItemDescription();
                if (StringUtils.isEmpty(description) && StringUtils.isEmpty(title)) {
                    // 重提交的描述和标题取产品系统，第一次提交修改的取mysql
                    AmazonProductListing amazonProductListing = getAmazonProductListing(item);
                    Optional<AmazonProductListing> optional = Optional.ofNullable(amazonProductListing);
                    if (optional.isPresent()) {
                        title = amazonProductListing.getItemName();
                        description = amazonProductListing.getItemDescription();
                    }
                }

                List<WordValidateResult> infringementWordInfos = item.getInfringementWordInfos();
                // 找出对应的包含指定标识的商标词
                List<WordValidateResult> dataWords = infringementWordInfos.stream()
                        // 不在白名单中的
                        .filter(infringementWordInfo -> brandWhiteListWords.stream().noneMatch(whiteWord-> StringUtils.equalsIgnoreCase(infringementWordInfo.getOriginWord(), whiteWord)))
                        .filter(infringementWordInfo -> {
                            Set<String> trademarkIdentification = infringementWordInfo.getTrademarkIdentification();
                            if (CollectionUtils.isEmpty(trademarkIdentification)) {
                                return false;
                            }
                            return trademarkIdentification.stream().anyMatch(AmazonListingInfringementHelper.BAD_TRADEMARK::contains) ;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(dataWords)) {
                    continue;
                }
                AmazonVariantBO amazonVariantBO = new AmazonVariantBO();

                if (StringUtils.isNotBlank(title)) {
                    String newTitle = amazonInfringementWordHelper.delInfringementWord(title, orderAccountBrandMap, dataWords);
                    amazonVariantBO.setItemName(newTitle);
                }

                if (StringUtils.isNotBlank(description)) {
                    if(BooleanUtils.isTrue(descIsHtml)) {
                        String newDescription = amazonInfringementWordHelper.htmlDelInfringementWord(description, orderAccountBrandMap, dataWords);
                        amazonVariantBO.setItemDescription(newDescription);
                    } else {
                        String newDescription = amazonInfringementWordHelper.delInfringementWord(description, orderAccountBrandMap, dataWords);
                        amazonVariantBO.setItemDescription(newDescription);
                    }
                }
                amazonVariantBO.setRelationType(ProcessingReportTriggleType.Listing_UPDATE_TITLE_DESC_TASK.name());
                amazonVariantBO.setAccountNumber(item.getAccountNumber());
                amazonVariantBO.setSellerSku(item.getSellerSku());

                AmazonPublishOperationLog operationLog = createOperationLog(amazonVariantBO, title, description, dataWords, item);
                operationLogs.add(operationLog);
                amazonVariantTitleList.add(amazonVariantBO);
            }
        });
    }

    private static AmazonPublishOperationLog createOperationLog(AmazonVariantBO amazonVariantBO, String title, String description, List<WordValidateResult> dataWords, EsAmazonProductListing item) {
        AmazonPublishOperationLog operationLog = new AmazonPublishOperationLog();
        operationLog.setModId(item.getSonAsin());
        operationLog.setOpType(ProcessingReportTriggleType.Listing_UPDATE_TITLE_DESC_TASK.name());
        operationLog.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
        operationLog.setUser("admin");
        operationLog.setObject(amazonVariantBO.getAccountNumber());
        operationLog.setObject1(amazonVariantBO.getSellerSku());
        List<String> bardWords = dataWords.stream().map(WordValidateResult::getOriginWord).collect(Collectors.toList());
        List<String> trademark = dataWords.stream().map(WordValidateResult::getTrademarkIdentification).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<String, Object> metaObj = Maps.newHashMap();
        metaObj.put("bardWords", bardWords);
        metaObj.put("trademark", trademark);
        metaObj.put("site", item.getSite());

//        Map<String, Object> beforeObj = Maps.newHashMap();
//        beforeObj.put("title",title);
//        beforeObj.put("description",description);

        Map<String, Object> afterObj = Maps.newHashMap();
        afterObj.put("title",amazonVariantBO.getItemName());
        afterObj.put("description",amazonVariantBO.getItemDescription());

        operationLog.setMetaObj(JSON.toJSONString(metaObj));
//        operationLog.setBeforeObj(beforeObj);
        operationLog.setAfterObj(JSON.toJSONString(afterObj));
        operationLog.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        return operationLog;
    }
}