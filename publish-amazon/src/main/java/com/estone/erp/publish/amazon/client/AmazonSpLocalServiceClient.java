package com.estone.erp.publish.amazon.client;

import com.estone.erp.common.model.api.ApiResult;
import io.swagger.client.model.*;
import io.swagger.client.model.customer.*;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import io.swagger.client.model.listings.Item;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import io.swagger.client.request.*;
import io.swagger.client.response.AmazonReportsDownloadProgress;
import model.ProductTypeDefinition;
import model.ProductTypeList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import request.RequestDefinitionsApiParam;

/**
 * amazon-sp-local服务
 *
 * @Auther yucm
 * @Date 2021/8/11
 */
@FeignClient(name = "amazon-sp-local-service")
//@FeignClient(url = "*************:8083", name = "amazon-sp-local-service-yjy")
public interface AmazonSpLocalServiceClient {

    /**
     * 亚马逊listing信息获取
     *
     * @param requestListingsItemsApiParam
     * @return
     */
    @PostMapping("/listingsItems/getListingsItem")
    ApiResult<Item> getListingsItem(@RequestBody RequestListingsItemsApiParam requestListingsItemsApiParam);


    /**
     * 亚马逊listing信息获取
     *
     * @param requestListingsItemsApiParam
     * @return
     */
    @PostMapping("/listingsItems/searchListingsItems")
    ApiResult<io.swagger.client.model.listings.ItemSearchResults> searchListingsItems(@RequestBody RequestListingsItemsApiParam requestListingsItemsApiParam);


    /**
     * 亚马逊listing平台下架
     *
     * @param requestListingsItemsApiParam
     * @return
     */
    @PostMapping("/listingsItems/deleteListingsItem")
    ApiResult<ListingsItemSubmissionResponse> deleteListingsItem(@RequestBody RequestListingsItemsApiParam requestListingsItemsApiParam);

    /**
     * 请求下载报告 (支持等待结果 和异步接受MQ消息处理报告数据)
     *
     * @param requestReportsApiParam
     * @return
     */
    @PostMapping("/reports/getReportFileResponse")
    ApiResult<AmazonReportsDownloadProgress> getReportFileResponse(@RequestBody RequestReportsApiParam requestReportsApiParam);

    /**
     * 获取lingting详细信息
     *
     * @param requestCatalogApiParam
     * @return
     */
    @PostMapping("/catalogItems/getCatalogItem")
    ApiResult<io.swagger.client.model.catalogItems.Item> getCatalogItem(@RequestBody RequestCatalogApiParam requestCatalogApiParam);

    /**
     * 查询lingting详细信息
     *
     * @param requestCatalogApiParam
     * @return
     */
    @PostMapping("/catalogItems/searchCatalogItems")
    ApiResult<ItemSearchResults> searchCatalogItems(@RequestBody RequestCatalogApiParam requestCatalogApiParam);

    /**
     * 添加上传数据
     *
     * @param requestFeedsApiParam
     * @return
     */
    @PostMapping("/feeds/addFeedsTask/{feedType}")
    ApiResult<String> addFeedsTask(@RequestBody RequestFeedsApiParam requestFeedsApiParam, @PathVariable("feedType") String feedType);

    /**
     * 获取listing运费
     * @param requestProductPriceApiParam
     * @return
     */
    @PostMapping("/productPrice/getPricing")
    ApiResult<GetFeaturedOfferExpectedPriceBatchResponse> getFeaturedOfferExpectedPriceBatch(@RequestBody RequestProductPriceApiParam requestProductPriceApiParam);

    /**
     * 获取listing运费
     * @param requestProductPriceApiParam
     * @return
     */
    @PostMapping("/productPrice/getCompetitivePricing")
    ApiResult<CompetitiveSummaryBatchResponse> getCompetitivePricing(@RequestBody RequestProductPriceApiParam requestProductPriceApiParam);

    /**
     * 获取ASIN级别的客户评价主题
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getItemReviewTopics")
    ApiResult<ItemReviewTopicsResponse> getItemReviewTopics(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取ASIN级别的客户评价趋势
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getItemReviewTrends")
    ApiResult<ItemReviewTrendsResponse> getItemReviewTrends(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取浏览节点级别的客户评价主题
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getBrowseNodeReviewTopics")
    ApiResult<BrowseNodeReviewTopicsResponse> getBrowseNodeReviewTopics(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取浏览节点级别的客户评价趋势
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getBrowseNodeReviewTrends")
    ApiResult<BrowseNodeReviewTrendsResponse> getBrowseNodeReviewTrends(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取浏览节点级别的退货主题
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getBrowseNodeReturnTopics")
    ApiResult<BrowseNodeReturnTopicsResponse> getBrowseNodeReturnTopics(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取浏览节点级别的退货趋势
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getBrowseNodeReturnTrends")
    ApiResult<BrowseNodeReturnTrendsResponse> getBrowseNodeReturnTrends(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 获取商品的浏览节点信息
     * @param requestCustomerFeedbackApiParam
     * @return
     */
    @PostMapping("/customerFeedback/getItemBrowseNode")
    ApiResult<BrowseNodeResponse> getItemBrowseNode(@RequestBody RequestCustomerFeedbackApiParam requestCustomerFeedbackApiParam);

    /**
     * 全量更新
     */
    @RequestMapping(value = "/listingsItems/putListingsItem", method = {RequestMethod.POST})
    ApiResult<ListingsItemSubmissionResponse> putListingsItem(@RequestBody RequestListingsItemsApiParam requestListingsItemsApiParam);

    /**
     * 部分更新
     */
    @RequestMapping(value = "/listingsItems/patchListingsItem", method = {RequestMethod.POST})
    ApiResult<ListingsItemSubmissionResponse> patchListingsItem(@RequestBody RequestListingsItemsApiParam requestListingsItemsApiParam);

    /**
     * 获取产品类型定义
     */
    @RequestMapping(value = "/productTypeDefintions/getDefinitionsProductType", method = {RequestMethod.POST})
    ApiResult<ProductTypeDefinition> getDefinitionsProductType(@RequestBody RequestDefinitionsApiParam requestDefinitionsApiParam);


    /**
     * 获取预测产品类型定义
     */
    @RequestMapping(value = "/productTypeDefintions/searchDefinitionsProductTypes", method = {RequestMethod.POST})
    ApiResult<ProductTypeList> searchDefinitionsProductTypes(@RequestBody RequestDefinitionsApiParam requestDefinitionsApiParam);


    /**
     * 添加上传数据
     *
     * @param requestFeedsApiParam  传入值： systemFeedType，docId，jsonData，amazonSpAccount
     * @return
     */
    @PostMapping("/feeds/jsonAddFeedsTask")
    ApiResult<String> jsonAddFeedsTask(@RequestBody RequestFeedsApiParam requestFeedsApiParam);
}
