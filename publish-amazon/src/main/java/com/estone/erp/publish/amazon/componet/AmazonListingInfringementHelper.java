
package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.model.dto.AmazonUpdateInfringementDO;
import com.estone.erp.publish.amazon.model.dto.SearchVo;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.erpCommon.module.TrieResultVo;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-06-19 18:01
 */
@Component
public class AmazonListingInfringementHelper {
    /**
     * 可以设置需要查询的字段，不设置则取下方默认字段
     */
    public static String[] fields = {"id","accountNumber", "site", "sellerSku","parentAsin","sonAsin", "mainSku", "articleNumber", "infringingBrandWord","syncDate", "updateInfringementTime","infringementWordInfos"};
    public final static List<String> BAD_TRADEMARK = Lists.newArrayList("律所代理商标(A级)", "律所代理商标(A变体)", "亚马逊扫号商标");

    @Resource
    private AmazonInfringementWordHelper amazonInfringementWordHelper;
    @Autowired
    private AmazonProductListingEsBulkProcessor amazonProductListingEsBulkProcessor;

    public void updateListingHandler(List<String> esIds,AmazonProductListing amazonProductListing,boolean existInfringementWord) {
        String sonsku = amazonProductListing.getArticleNumber().trim();
        List<String> txtList = Arrays.asList(amazonProductListing.getItemName(), amazonProductListing.getItemDescription(), amazonProductListing.getBrandName(),
                StringUtils.defaultString(amazonProductListing.getSearchTerms(), ""), bulletPointFormat(amazonProductListing.getBulletPoint()),
                StringUtils.defaultString(amazonProductListing.getColorName(), ""), StringUtils.defaultString(amazonProductListing.getSizeName(), ""));
        // 特殊字符拼接  ㊍㊍
        String text = StringUtils.join(txtList, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT)
                .replaceAll("<br>",AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT)
                .replaceAll("<br/>",AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT);
        List<WordValidateResult> infringementWordInfos = null;
        if (StringUtils.isNotBlank(text)) {
            SearchVo searchVo = new SearchVo(text, amazonProductListing.getSite(), null, sonsku);
            ApiResult<TrieResultVo> trieResultVoApiResult = amazonInfringementWordHelper.searchListingAllInfringementWord(searchVo);
            infringementWordInfos = amazonInfringementWordHelper.getInfringementWordInfos(amazonProductListing.getSite(), trieResultVoApiResult.getResult(), false);
        }
        if (CollectionUtils.isNotEmpty(infringementWordInfos) || BooleanUtils.isTrue(existInfringementWord)){
            String _id = amazonProductListing.getAccountNumber() + "_"+ amazonProductListing.getSellerSku();
            AmazonUpdateInfringementDO updateInfringementDO = new AmazonUpdateInfringementDO(_id, new Date());
            String infringBrandWord = null;
            List<String> trademarkIdentification = null;
            if (CollectionUtils.isNotEmpty(infringementWordInfos)) {

                HashSet<String> wordSet = infringementWordInfos.stream()
                        .map(WordValidateResult::getOriginWord)
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                infringBrandWord = StringUtils.join(wordSet, ",");
                updateInfringementDO.setInfringingBrandWord(infringBrandWord);

                trademarkIdentification = infringementWordInfos.stream()
                        .filter(o -> o != null && CollectionUtils.isNotEmpty(o.getTrademarkIdentification()))
                        .map(WordValidateResult::getTrademarkIdentification)
                        .collect(HashSet::new, HashSet::addAll, HashSet::addAll)
                        .stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList());
                updateInfringementDO.setTrademarkIdentification(trademarkIdentification);
            }
            updateInfringementDO.setInfringementWordInfos(infringementWordInfos);
            for (String esId : esIds) {
                updateInfringementDO.setId(esId);
                amazonProductListingEsBulkProcessor.syncUpdateInfringementWord(updateInfringementDO);
            }
        }
    }

    private String bulletPointFormat(String bulletPoint) {
        if (StringUtils.isNotBlank(bulletPoint)) {
            List<String> strings = JSON.parseArray(bulletPoint, String.class);
            return StringUtils.join(strings," ");
        }
        return StringUtils.defaultString(bulletPoint, "");
    }
}
