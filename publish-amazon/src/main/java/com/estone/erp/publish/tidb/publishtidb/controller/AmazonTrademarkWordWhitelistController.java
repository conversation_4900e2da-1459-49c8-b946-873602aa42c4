package com.estone.erp.publish.tidb.publishtidb.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.tidb.publishtidb.componet.AmazonTrademarkWordWhitelistHelper;
import com.estone.erp.publish.tidb.publishtidb.domain.AmazonTrademarkWordWhitelistVO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonWordDeleteLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Amazon商标词白名单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Slf4j
@RestController
@RequestMapping("/amazonTrademarkWordWhitelist")
public class AmazonTrademarkWordWhitelistController {

    @Resource
    private AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService;

    @Resource
    private AmazonWordDeleteLogService amazonWordDeleteLogService;

    @Resource
    private AmazonTrademarkWordWhitelistHelper amazonTrademarkWordWhitelistHelper;
    /**
     * 查询Amazon商标词白名单列表
     *
     * @param query 查询参数
     * @return 分页查询结果
     */
    @PostMapping("/queryPage")
    public CQueryResult<AmazonTrademarkWordWhitelistVO> queryPage(@RequestBody(required = true) CQuery<TrademarkWordWhitelistQueryDTO> query) {
        try {
            return amazonTrademarkWordWhitelistService.queryPage(query);
        } catch (Exception e) {
            log.error("查询Amazon商标词白名单列表异常", e);
            return CQueryResult.failResult(e.getMessage());
        }
    }


    /**
     * 批量删除商标词白名单
     */
    @PostMapping(value = "/batchDelete")
    public ApiResult<?> batchDelete(@RequestBody List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return ApiResult.newError("参数必填！");
        }

        LambdaQueryWrapper<AmazonTrademarkWordWhitelist> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AmazonTrademarkWordWhitelist::getId, ids);

        List<AmazonTrademarkWordWhitelist> list = amazonTrademarkWordWhitelistService.list(wrapper);

        String currentUser = WebUtils.getUserName();
        for (AmazonTrademarkWordWhitelist whitelist : list) {
            amazonTrademarkWordWhitelistService.removeById(whitelist.getId());

            AmazonWordDeleteLog deleteLog  = new AmazonWordDeleteLog();
            deleteLog.setSite(whitelist.getSite());
            deleteLog.setInfringementWord(whitelist.getInfringementWord());
            deleteLog.setOperatedTime(LocalDateTime.now());
            deleteLog.setOperatedBy(currentUser);
            amazonWordDeleteLogService.save(deleteLog);

        }
        return ApiResult.newSuccess("请求成功！");
    }


    /**
     * 添加商标词白名单
     */
    @PostMapping(value = "/addWhitelist")
    public ApiResult<?> addWhitelist(@RequestBody TrademarkWordWhitelistSaveDTO dto) {
        Map<String, List<String>> whitelistMap = new HashMap<>();
        whitelistMap.put(dto.getInfringementWord(),dto.getSiteList());
        amazonTrademarkWordWhitelistHelper.addWhitelists(whitelistMap, false);
        return ApiResult.newSuccess();
    }

    /**
     * 导入商标词白名单
     * @param file
     * @return
     */
    @PostMapping("/import")
    public ApiResult<String> importData(@RequestParam(value = "file", required = true) MultipartFile file) {
        return amazonTrademarkWordWhitelistHelper.importData(file);
    }

}
