package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.Data;

import java.util.List;

@Data
public class TrademarkWordWhitelistSaveDTO {
    /**
     * 站点
     */
    private List<String> siteList;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点
     */
    private String prohibitionSite;

    /**
     * 禁售平台
     */
    private String forbidChannel;

    /**
     * 是否属于导入数据
     * true则过滤存在数据，false则正常该抛出就抛出
     */
    private Boolean belongImportData;

}
