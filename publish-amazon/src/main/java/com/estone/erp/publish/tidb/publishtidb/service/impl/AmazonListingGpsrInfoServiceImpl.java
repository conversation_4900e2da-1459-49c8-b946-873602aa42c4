package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.DynamicLimiter;
import com.estone.erp.common.util.PublishRedissonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.AmazonGpsrPdfHelper;
import com.estone.erp.publish.amazon.componet.AmazonProductListingEsBulkProcessor;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.dto.AmazonGPSRTemplateModel;
import com.estone.erp.publish.amazon.model.request.GenerateGPSRImageRequest;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonUpdateGPSRRequest;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonListingGpsrInfoMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingGpsrInfo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingGpsrInfoService;
import io.swagger.client.enums.SpFeedType;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon listing GPSR 信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
public class AmazonListingGpsrInfoServiceImpl extends ServiceImpl<AmazonListingGpsrInfoMapper, AmazonListingGpsrInfo> implements IAmazonListingGpsrInfoService {
    private final PublishLogger log = PublishLoggerFactory.getLogger(AmazonListingGpsrInfoServiceImpl.class);
    @Autowired
    private AmazonGpsrPdfHelper amazonGpsrPdfHelper;
    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Resource
    private AmazonProductListingEsBulkProcessor amazonProductListingEsBulkProcessor;
    @Autowired
    private AmazonPublishOperationLogService amazonOperateLogService;

    @Override
    public ApiResult<String> uploadCustomPdf(MultipartFile file, String accountNumber, String sellerSku) {
        try {
            String gpsrFilePathUrl = amazonGpsrPdfHelper.uploadGPSRFile2Seaweed(file, sellerSku);
            return ApiResult.newSuccess(gpsrFilePathUrl);
        } catch (Exception e) {
            log.error("自定义上传GPSR PDF 异常:{}", e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    public ApiResult<String> updateListingGPSRInfo(AmazonUpdateGPSRRequest request) {
        String pdfLink = request.getPdfLink();
        if (StringUtils.isNotBlank(pdfLink)) {
            // pdfLink不为空,代表使用自定义pdf文件上传到平台
            return updateCustomPdf2Listing(request);
        }
        // 系统生成gpsr文件并上传至平台
        return updateListingGPSRInfo2Listing(request);
    }

    @Override
    public List<AmazonListingGpsrInfo> getAmazonListingGpsrInfoBySellerSkus(String accountNumber, List<String> sellerSkus) {
        LambdaQueryWrapper<AmazonListingGpsrInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonListingGpsrInfo::getAccountNumber, accountNumber);
        queryWrapper.in(AmazonListingGpsrInfo::getSellerSku, sellerSkus);
        queryWrapper.select(AmazonListingGpsrInfo::getId, AmazonListingGpsrInfo::getSellerSku, AmazonListingGpsrInfo::getAccountNumber);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<AmazonListingGpsrInfo> getAmazonListingGpsrInfoByAsins(String merchantId, List<String> asins) {
        LambdaQueryWrapper<AmazonListingGpsrInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(merchantId), AmazonListingGpsrInfo::getMerchantId, merchantId);
        queryWrapper.in(AmazonListingGpsrInfo::getAsin, asins);
        queryWrapper.select(AmazonListingGpsrInfo::getId, AmazonListingGpsrInfo::getAsin, AmazonListingGpsrInfo::getSellerSku, AmazonListingGpsrInfo::getAccountNumber);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public ApiResult<String> generateGpsrInfo(EsAmazonProductListing request) {
        try {
            String accountNumber = request.getAccountNumber();
            String sellerSku = request.getSellerSku();
            String asin = Optional.ofNullable(request.getSonAsin()).orElseGet(request::getParentAsin);
            String user = Optional.ofNullable(WebUtils.getUserName()).orElseGet(() -> "admin");

            AmazonListingGpsrInfo amazonListingGpsrInfo = getAmazonListingGpsrInfo(accountNumber, sellerSku);
            if (amazonListingGpsrInfo == null) {
                // 先校验店铺配置套账GPSR信息
                AmazonAccountRelation manufacturerInfo = amazonAccountRelationService.getAccountManufacturerInfo(accountNumber);
                if (manufacturerInfo == null
                        || StringUtils.isBlank(manufacturerInfo.getManufacturerEn())
                        || StringUtils.isBlank(manufacturerInfo.getMerchantId())) {
                    return ApiResult.newSuccess("店铺GPSR制造商未配置");
                }
                // 根据套装检查是否已经生成过GPSR信息了
                checkGPSRManufacturerInfo(manufacturerInfo, sellerSku, asin);
                // 生成预览
                AmazonGPSRTemplateModel templateModel = amazonGpsrPdfHelper.previewPdfTemplate(accountNumber, sellerSku);
                AmazonListingGpsrInfo listingGpsrInfo = createdListingGPSRInfo(accountNumber, user, templateModel);
                baseMapper.insert(listingGpsrInfo);
                // 上传文件
                String gpsrFilePathUrl = amazonGpsrPdfHelper.generateAndUploadOss(templateModel, listingGpsrInfo);
                listingGpsrInfo.setGpsrImgLink(gpsrFilePathUrl);
                baseMapper.updateById(listingGpsrInfo);
                callUpdateListingGPSRInfo(listingGpsrInfo);
                return ApiResult.newSuccess(listingGpsrInfo.getGpsrImgLink());
            }

            if (StringUtils.isBlank(amazonListingGpsrInfo.getGpsrImgLink())) {
                AmazonGPSRTemplateModel templateModel = amazonGpsrPdfHelper.previewPdfTemplate(accountNumber, sellerSku);
                String gpsrFilePathUrl = amazonGpsrPdfHelper.generateAndUploadOss(templateModel, amazonListingGpsrInfo);
                amazonListingGpsrInfo.setGpsrImgLink(gpsrFilePathUrl);
                amazonListingGpsrInfo.setAsin(asin);
                amazonListingGpsrInfo.setType(2);
                amazonListingGpsrInfo.setUpdateBy(user);
                amazonListingGpsrInfo.setUpload(false);
                amazonListingGpsrInfo.setUpdatedTime(LocalDateTime.now());
                baseMapper.updateById(amazonListingGpsrInfo);
                callUpdateListingGPSRInfo(amazonListingGpsrInfo);
                return ApiResult.newSuccess(amazonListingGpsrInfo.getGpsrImgLink());
            }
            if (Boolean.FALSE.equals(amazonListingGpsrInfo.getUpload())) {
                callUpdateListingGPSRInfo(amazonListingGpsrInfo);
                return ApiResult.newSuccess(amazonListingGpsrInfo.getGpsrImgLink());
            }

            return ApiResult.newSuccess(amazonListingGpsrInfo.getGpsrImgLink());
        } catch (BusinessException e) {
            return ApiResult.newSuccess(e.getMessage());
        } catch (Exception e) {
            log.error("系统生成 GPSR PDF,上传至OSS异常:{}", e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    public void uploadCall2Amazon(AmazonPublishOperationLog operationLog) {
        String modId = operationLog.getModId();
        operationLog.setState(1);
        amazonOperateLogService.updateByPrimaryKeySelective(operationLog);
        try {
            Integer total = executeUploadCall2Amazon(modId);
            if (total > 80000) {
                log.errorForPolicy("套账:{},上传GPSR,到平台成功,共上传:{}条", "upload_gpsr", new DefaultAlertPolicy(), modId, total);
            }
        } catch (Exception e) {
            log.error("执行套账上传GPSR:{},上传到平台异常:{}", modId, e.getMessage(), e);
        }
        operationLog.setState(2);
        amazonOperateLogService.updateByPrimaryKeySelective(operationLog);
    }

    @Override
    public ApiResult<Map<String, String>> generateImage(GenerateGPSRImageRequest request) {
        String accountNumber = request.getAccountNumber();
        AmazonAccountRelation accountRelation = amazonAccountRelationService.selectByAccount(accountNumber);
        if (accountRelation == null) {
            return ApiResult.newError(accountNumber + ",未查询到店铺配置");
        }

        String categoryId = amazonGpsrPdfHelper.getCategoryId(request.getArticleNumber(), request.getSkuDataSource());
        List<AmazonGPSRTemplateModel> modelList = request.getSellerSkuList().stream().map(sellerSku -> {
            return amazonGpsrPdfHelper.previewImageTemplate(accountRelation, sellerSku, categoryId, accountRelation.getAccountCountry());
        }).collect(Collectors.toList());
        Map<String, String> resultMap = executeGenerateImage(modelList);

        return ApiResult.newSuccess(resultMap);
    }

    private Map<String, String> executeGenerateImage(List<AmazonGPSRTemplateModel> modelList) {
        Map<String, String> resultMap = new HashMap<>();
        for (AmazonGPSRTemplateModel model : modelList) {
            try {
                File tempFile = amazonGpsrPdfHelper.generateImage2TempFile(model);
                String gpsrImages = amazonGpsrPdfHelper.uploadGPSRFile2Seaweed(tempFile, model.getSellerSku());
                resultMap.put(model.getSellerSku(), gpsrImages);
            } catch (Exception e) {
                log.error("sellSku:{},生成图片异常:{}", model.getSellerSku(), e.getMessage(), e);
            }
        }
        return resultMap;
    }

    @Override
    public void resetFBAGPSRUploadFlag(List<AmazonListingGpsrInfo> existingGpsrInfos) {
        if (CollectionUtils.isEmpty(existingGpsrInfos)) {
            return;
        }
        existingGpsrInfos.forEach(amazonListingGpsrInfo -> {
            updateRecordExtra(amazonListingGpsrInfo, Maps.of("sourceType", "FBA"));
            amazonListingGpsrInfo.setUpload(false);
            amazonListingGpsrInfo.setUpdatedTime(LocalDateTime.now());
            baseMapper.updateById(amazonListingGpsrInfo);
        });
    }

    @Override
    public ApiResult<String> generateImage(AmazonTemplateBO templateInfo) {
        String accountNumber = templateInfo.getSellerId();
        String sellerSKU = templateInfo.getSellerSKU();
        AmazonAccountRelation accountRelation = amazonAccountRelationService.getAccountManufacturerInfo(accountNumber);
        if (accountRelation == null) {
            return ApiResult.newError(accountNumber + ",未查询到店铺配置");
        }

        String categoryId = amazonGpsrPdfHelper.getCategoryId(templateInfo.getParentSku(), templateInfo.getSkuDataSource());
        List<String> allSellerSkuSku = AmazonTemplateUtils.getAllSellerSkuSku(templateInfo);
        List<AmazonGPSRTemplateModel> modelList = allSellerSkuSku.stream().map(sellerSku -> {
            return amazonGpsrPdfHelper.previewImageTemplate(accountRelation, sellerSku, categoryId, accountRelation.getAccountCountry());
        }).collect(Collectors.toList());

        Map<String, String> resultMap = executeGenerateImage(modelList);

        String imageUrl = MapUtils.getString(resultMap, sellerSKU, "");
        if (!StringUtils.isEmpty(imageUrl)) {
            templateInfo.setGpsrImage(imageUrl);
        }

        List<AmazonSku> amazonSkus = templateInfo.getAmazonSkus();
        if (CollectionUtils.isNotEmpty(amazonSkus)) {
            amazonSkus.forEach(amazonSku -> {
                String sonSellerSku = amazonSku.getSellerSKU();
                String gpsrImage = MapUtils.getString(resultMap, sonSellerSku, "");
                if (!StringUtils.isEmpty(gpsrImage)) {
                    amazonSku.setGpsrImage(gpsrImage);
                }
            });
        }
        return ApiResult.newSuccess("");
    }

    private Integer executeUploadCall2Amazon(String merchantId) {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 3, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000));
        int total = 0;
        LocalDateTime startTime = null;
        LambdaQueryWrapper<AmazonListingGpsrInfo> countQueryWrapper = new LambdaQueryWrapper<>();
        countQueryWrapper.eq(AmazonListingGpsrInfo::getMerchantId, merchantId);
        countQueryWrapper.eq(AmazonListingGpsrInfo::getUpload, false);
        Integer count = baseMapper.selectCount(countQueryWrapper);
        while (true) {
            LambdaQueryWrapper<AmazonListingGpsrInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AmazonListingGpsrInfo::getMerchantId, merchantId);
            queryWrapper.eq(AmazonListingGpsrInfo::getUpload, false);
            queryWrapper.isNotNull(AmazonListingGpsrInfo::getGpsrImgLink);
            queryWrapper.ge(startTime != null, AmazonListingGpsrInfo::getCreatedTime, startTime);
            queryWrapper.orderByAsc(AmazonListingGpsrInfo::getCreatedTime);
            queryWrapper.last("limit 300");
            List<AmazonListingGpsrInfo> listingGpsrInfos = baseMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(listingGpsrInfos)) {
                break;
            }
            CompletableFuture.allOf(listingGpsrInfos.stream().map(item -> CompletableFuture.runAsync(() -> {
                try {
                    callUpdateListingGPSRInfo(item);
                } catch (Exception e) {
                    log.error("id:{},上传套账GPSR:{},到平台异常:{}", item.getId(), item.getSellerSku(), e.getMessage(), e);
                }
            }, threadPoolExecutor)).toArray(CompletableFuture[]::new)).join();
            startTime = listingGpsrInfos.stream().max(Comparator.comparing(AmazonListingGpsrInfo::getCreatedTime)).get().getCreatedTime();
            total += listingGpsrInfos.size();
            if (total % 3000 == 0) {
                log.info("套账:{},执行上传GPSR,到平台, 已处理:{}/{}, startTime:{}", merchantId, total, count, startTime);
            }

            if (total >= count) {
                return total;
            }
        }
        threadPoolExecutor.shutdown();
        return total;
    }

    /**
     * 系统生成 GPSR PDF 并上传至平台
     *
     * @param request
     * @return
     */
    private ApiResult<String> updateListingGPSRInfo2Listing(AmazonUpdateGPSRRequest request) {
        try {
            String accountNumber = request.getAccountNumber();
            String sellerSku = request.getSellerSku();
            String user = Optional.ofNullable(WebUtils.getUserName()).orElseGet(() -> "admin");
            AmazonListingGpsrInfo amazonListingGpsrInfo = getAmazonListingGpsrInfo(accountNumber, sellerSku);
            if (amazonListingGpsrInfo == null) {
                // 先校验店铺配置套账GPSR信息
                AmazonAccountRelation manufacturerInfo = amazonAccountRelationService.getAccountManufacturerInfo(accountNumber);
                if (manufacturerInfo == null
                        || StringUtils.isBlank(manufacturerInfo.getManufacturerEn())
                        || StringUtils.isBlank(manufacturerInfo.getMerchantId())) {
                    return ApiResult.newSuccess("店铺GPSR制造商未配置");
                }
                // 根据套装检查是否已经生成过GPSR信息了
                checkGPSRManufacturerInfo(manufacturerInfo, sellerSku, null);
                // 生成预览
                AmazonGPSRTemplateModel templateModel = amazonGpsrPdfHelper.previewPdfTemplate(accountNumber, sellerSku);
                AmazonListingGpsrInfo listingGpsrInfo = createdListingGPSRInfo(accountNumber, user, templateModel);
                // 上传文件
                String gpsrFilePathUrl = amazonGpsrPdfHelper.generateAndUploadOss(templateModel, listingGpsrInfo);
                listingGpsrInfo.setGpsrImgLink(gpsrFilePathUrl);
                baseMapper.insert(listingGpsrInfo);
                // 上传到平台
                return callUpdateListingGPSRInfo(listingGpsrInfo);
            }
            AmazonGPSRTemplateModel templateModel = amazonGpsrPdfHelper.previewPdfTemplate(accountNumber, sellerSku);
            String gpsrFilePathUrl = amazonGpsrPdfHelper.generateAndUploadOss(templateModel, amazonListingGpsrInfo);
            LambdaUpdateWrapper<AmazonListingGpsrInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(AmazonListingGpsrInfo::getType, 2);
            updateWrapper.set(AmazonListingGpsrInfo::getGpsrImgLink, gpsrFilePathUrl);
            updateWrapper.set(AmazonListingGpsrInfo::getUpdatedTime, LocalDateTime.now());
            updateWrapper.set(AmazonListingGpsrInfo::getUpdateBy, user);
            updateWrapper.eq(AmazonListingGpsrInfo::getId, amazonListingGpsrInfo.getId());
            baseMapper.update(null, updateWrapper);
            // 上传到平台
            return callUpdateListingGPSRInfo(amazonListingGpsrInfo);
        } catch (BusinessException e) {
            return ApiResult.newError(e.getMessage());
        } catch (Exception e) {
            log.error("系统生成 GPSR PDF,上传至平台异常:{}", e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 调用平台接口修改gpsr
     *
     * @param amazonListingGpsrInfo
     * @return
     */
    private ApiResult<String> callUpdateListingGPSRInfo(AmazonListingGpsrInfo amazonListingGpsrInfo) {
        DynamicLimiter.getInstance(AmazonConstant.UPDATE_GPSR_API_LIMITER + amazonListingGpsrInfo.getMerchantId(), 3d).acquire();
        // 处理报告
        ApiResult<ListingsItemSubmissionResponse> responseApiResult = AmazonSpLocalServiceUtils.updateListingGPSRImageAndOriginOfCountry(amazonListingGpsrInfo);
        if (responseApiResult.isSuccess()) {
            ListingsItemSubmissionResponse response = responseApiResult.getResult();
            if (!ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.equals(response.getStatus())) {
                // 失败
                amazonListingGpsrInfo.setUpload(false);
                updateRecordExtra(amazonListingGpsrInfo, Maps.of("reportMsg", StringUtils.defaultIfBlank(JSON.toJSONString(response), "")));
            } else {
                amazonListingGpsrInfo.setUpload(true);
                updateRecordExtra(amazonListingGpsrInfo, Maps.of("reportMsg", ""));
                // 更新本地数据
                amazonProductListingEsBulkProcessor.updateProductListingGpsr(amazonListingGpsrInfo);
            }
        } else {
            amazonListingGpsrInfo.setUpload(false);
            updateRecordExtra(amazonListingGpsrInfo, Maps.of("reportMsg", StringUtils.defaultIfBlank(JSON.toJSONString(responseApiResult), "")));
        }

        // 更新数据
        LambdaUpdateWrapper<AmazonListingGpsrInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AmazonListingGpsrInfo::getUpload, amazonListingGpsrInfo.getUpload());
        updateWrapper.set(AmazonListingGpsrInfo::getUpdatedTime, LocalDateTime.now());
        updateWrapper.set(AmazonListingGpsrInfo::getExtra, amazonListingGpsrInfo.getExtra());
        updateWrapper.eq(AmazonListingGpsrInfo::getId, amazonListingGpsrInfo.getId());
        baseMapper.update(null, updateWrapper);
        return ApiResult.newSuccess("修改成功");
    }

    private AmazonProcessReport createUpdateGPSRReport(String accountNumber, String sellerSku, String user) {
        AmazonProcessReport report = new AmazonProcessReport();
        report.setFeedType(SpFeedType.POST_PRODUCT_OVERRIDES_DATA.getValue());
        report.setAccountNumber(accountNumber);
        report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
        report.setDataValue(sellerSku);
        report.setRelationType(ProcessingReportTriggleType.LISTING_UPDATE_GPSR.name());
        report.setStatusCode(ProcessingReportStatusCode.Complete.name());
        report.setCreationDate(new Date());
        report.setCreatedBy(user);
        return report;
    }

    private void checkGPSRManufacturerInfo(AmazonAccountRelation manufacturerInfo, String sellerSku, String asin) {
        // 相同套装同一个Asin只需要生成一次GPSR信息
        checkMerchantAsin(manufacturerInfo.getMerchantId(), asin);
        // 不同套账不允许有相同的sellerSku
        checkMerchantSellerSku(manufacturerInfo.getMerchantId(), sellerSku);
    }

    private void checkMerchantAsin(String merchantId, String asin) {
        if (StringUtils.isBlank(asin)) {
            return;
        }
        boolean lock = false;
        String key = RedisConstant.AMAZON_UPDATE_GPSR_LOCK_PREFIX + merchantId + "_" + asin;
        try {
            lock = PublishRedissonUtils.attemptsTryLock(key, TimeUnit.SECONDS, 3, 10, 0);
            if (lock) {
                LambdaQueryWrapper<AmazonListingGpsrInfo> queryWrapper = new LambdaQueryWrapper<AmazonListingGpsrInfo>()
                        .eq(AmazonListingGpsrInfo::getAsin, asin)
                        .eq(AmazonListingGpsrInfo::getMerchantId, merchantId)
                        .eq(AmazonListingGpsrInfo::getType, 2)
                        .isNotNull(AmazonListingGpsrInfo::getGpsrImgLink)
                        .select(AmazonListingGpsrInfo::getMerchantId, AmazonListingGpsrInfo::getAccountNumber, AmazonListingGpsrInfo::getSellerSku, AmazonListingGpsrInfo::getType)
                        .last("limit 2");
                List<AmazonListingGpsrInfo> amazonListingGpsrInfos = baseMapper.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(amazonListingGpsrInfos)) {
                    AmazonListingGpsrInfo amazonListingGpsrInfo = amazonListingGpsrInfos.get(0);
                    throw new BusinessException(String.format("asin:%s在套账:%s已存在记录", asin, amazonListingGpsrInfo.getMerchantId()));

                }
            } else {
                throw new BusinessException("加锁失败");
            }
        } catch (Exception e) {
            throw new BusinessException(" 执行GPSR生成图片,前缀:" + merchantId + ",异常信息:" + e.getMessage());
        } finally {
            if (lock) {
                try {
                    PublishRedissonUtils.unlock(key);
                } catch (Exception e) {
                    log.warn("释放执行GPSR生成图片加锁失败,前缀:{}", merchantId);
                }
            }
        }
    }

    private void checkMerchantSellerSku(String merchantId, String sellerSku) {
        LambdaQueryWrapper<AmazonListingGpsrInfo> queryWrapper = new LambdaQueryWrapper<AmazonListingGpsrInfo>()
                .eq(AmazonListingGpsrInfo::getSellerSku, sellerSku)
                .select(AmazonListingGpsrInfo::getMerchantId, AmazonListingGpsrInfo::getAccountNumber)
                .groupBy(AmazonListingGpsrInfo::getMerchantId)
                .last("limit 2");
        List<AmazonListingGpsrInfo> amazonListingGpsrInfos = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(amazonListingGpsrInfos)) {
            Optional<AmazonListingGpsrInfo> listingGpsrInfo = amazonListingGpsrInfos.stream().filter(info -> !info.getMerchantId().equals(merchantId)).findFirst();
            if (listingGpsrInfo.isPresent()) {
                AmazonListingGpsrInfo otherMerchantInfo = listingGpsrInfo.get();
                throw new BusinessException(String.format("sellerSku:%s在%s店铺存在重复", sellerSku, otherMerchantInfo.getAccountNumber()));
            }
        }
    }


    /**
     * 使用自定义 GPSR pdf 文件上传至平台
     *
     * @param request
     * @return
     */
    private ApiResult<String> updateCustomPdf2Listing(AmazonUpdateGPSRRequest request) {
        AmazonAccountRelation accountRelation = amazonAccountRelationService.selectByAccount(request.getAccountNumber());
        AmazonListingGpsrInfo amazonListingGpsrInfo = new AmazonListingGpsrInfo();
        amazonListingGpsrInfo.setAccountNumber(request.getAccountNumber());
        amazonListingGpsrInfo.setSellerSku(request.getSellerSku());
        amazonListingGpsrInfo.setGpsrPdfLink(request.getPdfLink());
        amazonListingGpsrInfo.setUpload(true);
        amazonListingGpsrInfo.setSite(accountRelation.getAccountCountry());
        amazonListingGpsrInfo.setUpdateBy(Optional.ofNullable(WebUtils.getUserName()).orElseGet(() -> "admin"));
        // 处理报告
        AmazonProcessReport processReport = createUpdateGPSRReport(amazonListingGpsrInfo.getAccountNumber(), amazonListingGpsrInfo.getSellerSku(), amazonListingGpsrInfo.getUpdateBy());
        ApiResult<ListingsItemSubmissionResponse> responseApiResult = AmazonSpLocalServiceUtils.updateListingGPSRAndOriginOfCountry(amazonListingGpsrInfo);
        if (responseApiResult.isSuccess()) {
            ListingsItemSubmissionResponse response = responseApiResult.getResult();
            processReport.setStatus(ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.equals(response.getStatus()));
            if (!processReport.getStatus()) {
                // 失败
                processReport.setResultMsg(JSON.toJSONString(response));
            }
        } else {
            processReport.setStatus(false);
            processReport.setResultMsg(JSON.toJSONString(responseApiResult));
        }
        processReport.setFinishDate(new Date());
        amazonProcessReportService.insert(processReport);
        return ApiResult.newSuccess("上传成功");
    }


    private AmazonListingGpsrInfo createdListingGPSRInfo(String accountNumber, String user, AmazonGPSRTemplateModel templateModel) {
        LocalDateTime now = LocalDateTime.now();
        AmazonListingGpsrInfo info = new AmazonListingGpsrInfo();
        info.setMerchantId(templateModel.getMerchantId());
        info.setAccountNumber(accountNumber);
        info.setSite(templateModel.getSite());
        info.setAsin(templateModel.getAsin());
        info.setType(2);
        info.setSellerSku(templateModel.getSellerSku());
        info.setManufacturerEn(templateModel.getManufacturerEn());
        info.setManufacturerEmail(templateModel.getManufacturerEmail());
        info.setManufacturerAddress(templateModel.getManufacturerAddress());
        info.setManufacturerTel(templateModel.getManufacturerTel());
        info.setWarning(JSON.toJSONString(templateModel.getLangWarnings()));
        info.setUpload(false);
        info.setCreateBy(user);
        info.setUpdateBy(user);
        info.setCreatedTime(now);
        info.setUpdatedTime(now);
        return info;
    }

    private AmazonListingGpsrInfo getAmazonListingGpsrInfo(String accountNumber, String sellerSku) {
        return getOne(new LambdaQueryWrapper<AmazonListingGpsrInfo>()
                .eq(AmazonListingGpsrInfo::getAccountNumber, accountNumber)
                .eq(AmazonListingGpsrInfo::getSellerSku, sellerSku));
    }

    private void updateRecordExtra(AmazonListingGpsrInfo amazonListingGpsrInfo, Map<String, Object> extra) {
        String extraJson = amazonListingGpsrInfo.getExtra();
        if (StringUtils.isBlank(extraJson)) {
            String extraData = JSON.toJSONString(extra);
            amazonListingGpsrInfo.setExtra(extraData);
            return;
        }
        Map<String, Object> extraMap = JSON.parseObject(extraJson, Map.class);
        Object sourceType = extra.get("sourceType");
        if (sourceType != null) {
            String type = sourceType.toString();
            if ("FBA".equals(type)) {
                extraMap.put("isFBA", true);
            }
        }
        extraMap.putAll(extra);

        String extraData = JSON.toJSONString(extraMap);
        amazonListingGpsrInfo.setExtra(extraData);
    }
}
