package com.estone.erp.publish.amazon.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class NewProductPublishConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column new_product_publish_config.id
     */
    private Integer id;


    /**
     * 销售 database column new_product_publish_config.sale_number
     */
    private String saleNumber;
    /**
     * 主管工号 database column new_product_publish_config.job_number
     */
    private String jobNumber;

    /**
     * 人数 database column new_product_publish_config.number
     */
    private Integer number;

    /**
     * 分配数量(个/人) database column new_product_publish_config.allocated_quantity
     */
    private Integer allocatedQuantity;

    /**
     * 创建人 database column new_product_publish_config.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column new_product_publish_config.create_time
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;

    /**
     * 修改人 database column new_product_publish_config.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column new_product_publish_config.update_time
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;
}