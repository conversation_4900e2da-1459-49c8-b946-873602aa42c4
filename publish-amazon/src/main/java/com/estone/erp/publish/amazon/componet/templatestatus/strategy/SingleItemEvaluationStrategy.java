package com.estone.erp.publish.amazon.componet.templatestatus.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import io.swagger.client.model.listings.ItemSummaryByMarketplace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单体商品状态评估策略
 * 处理非变体商品的发布状态评估逻辑
 */
@Slf4j
@Component
public class SingleItemEvaluationStrategy implements PublishStatusEvaluationStrategy {

    @Override
    public boolean supports(AmazonTemplateBO template) {
        return !AmazonTemplateUtils.isSaleVariant(template);
    }

    @Override
    public EvaluationResult evaluatePublishStatus(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        if (CollectionUtils.isEmpty(listingInfoList)) {
            log.warn("Listing信息为空，模版ID：{}", template.getId());
            return EvaluationResult.FAIL;
        }

        String parentSellerSku = template.getSellerSKU();
        Map<String, AmazonListingInfo> listingInfoMap = listingInfoList.stream()
                .collect(Collectors.toMap(AmazonListingInfo::getSellerSku, Function.identity(), (k1, k2) -> k1));

        AmazonListingInfo amazonListingInfo = listingInfoMap.get(parentSellerSku);
        if (amazonListingInfo == null) {
            log.warn("未找到对应的Listing信息，模版ID：{}，SellerSku：{}", template.getId(), parentSellerSku);
            return EvaluationResult.FAIL;
        }

        List<String> itemStatus = getSellerSkuStatus(amazonListingInfo);

        // 检查是否可售
        if (isBuyable(itemStatus)) {
            return EvaluationResult.SUCCESS;
        }

        // 检查是否可发现且在线
        if (isDiscoverable(itemStatus)) {
            return EvaluationResult.NEED_SYNC;
        }

        if (StringUtils.isNotBlank(amazonListingInfo.getIssue())) {
            StringBuilder issueMsg = new StringBuilder();
            JSON.parseArray(amazonListingInfo.getSummaries()).forEach(summary -> {
                JSONObject summaryObject = (JSONObject) summary;
                String severity = summaryObject.getString("severity");
                if ("ERROR".equals(severity)) {
                    issueMsg.append("\n").append(summaryObject.toJSONString());
                }
            });
            if (issueMsg.length() > 0) {
                return EvaluationResult.FAIL;
            }

            return EvaluationResult.NEED_SYNC;
        }
        return EvaluationResult.NEED_SYNC;
    }

    @Override
    public String getFailureDetails(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        if (CollectionUtils.isEmpty(listingInfoList)) {
            return "Listing信息为空";
        }

        String parentSellerSku = template.getSellerSKU();
        AmazonListingInfo amazonListingInfo = listingInfoList.stream()
                .filter(info -> parentSellerSku.equals(info.getSellerSku()))
                .findFirst()
                .orElse(null);

        if (amazonListingInfo == null) {
            return "未找到对应的Listing信息";
        }

        if (Boolean.FALSE.equals(amazonListingInfo.getIsOnline()) && StringUtils.isNotBlank(amazonListingInfo.getIssue())) {
            return amazonListingInfo.getSellerSku() + ": " + amazonListingInfo.getIssue();
        }

        return "刊登失败，原因未知";
    }

    /**
     * 获取SellerSku的状态列表
     */
    private List<String> getSellerSkuStatus(AmazonListingInfo listingInfo) {
        String summaries = listingInfo.getSummaries();
        if (StringUtils.isBlank(summaries)) {
            return null;
        }

        JSONArray summariesArray = JSON.parseArray(summaries);
        JSONObject summariesObject = summariesArray.getJSONObject(0);
        if (summariesObject == null) {
            return null;
        }

        JSONArray status = summariesObject.getJSONArray("status");
        if (status == null) {
            return null;
        }

        return status.toJavaList(String.class);
    }

    /**
     * 判断商品状态是否为可售
     */
    private boolean isBuyable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue());
    }

    /**
     * 判断商品状态是否为可发现
     */
    private boolean isDiscoverable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue());
    }
} 