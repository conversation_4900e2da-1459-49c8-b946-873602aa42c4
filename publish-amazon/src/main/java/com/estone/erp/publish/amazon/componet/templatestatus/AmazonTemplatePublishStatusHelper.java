package com.estone.erp.publish.amazon.componet.templatestatus;

import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishHandler;
import com.estone.erp.publish.amazon.componet.templatestatus.strategy.PublishStatusEvaluationStrategy;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.google.common.collect.Lists;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * JSON 模版刊登状态处理类
 */
@Slf4j
@Component
public class AmazonTemplatePublishStatusHelper {

    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;

    @Autowired
    private AmazonPublishStatusChecker statusChecker;

    @Autowired
    private AmazonPublishStatusUpdater statusUpdater;

    @Autowired
    private AmazonPriceStockSyncProcessor priceStockSyncProcessor;

    @Autowired
    private List<PublishStatusEvaluationStrategy> evaluationStrategies;

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Autowired
    private SaleAccountService saleAccountService;

    @Autowired
    private AmazonPublishHandler amazonPublishHandler;


    /**
     * 执行指定分钟内的刊登中模版数据
     *
     * @return int 处理模版数量
     */
    public int handleAmazonTemplatePublishStatus(int beforeMinutes, int afterMinutes) {
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime afterDateTime = currentDateTime.minusMinutes(afterMinutes);

        AmazonTemplateExample example = new AmazonTemplateExample();
        AmazonTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(afterDateTime))
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.JSON.getCode());
        example.setOrderByClause("last_update_date");
        List<Integer> amazonTemplateIdList = amazonTemplateService.batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return 0;
        }
        Lists.partition(amazonTemplateIdList, 100).forEach(this::updatedTemplatePublishStatus);
        return amazonTemplateIdList.size();
    }

    /**
     * 更新模版刊登状态
     *
     * @param amazonTemplateIdList 模版ID列表
     */
    private void updatedTemplatePublishStatus(List<Integer> amazonTemplateIdList) {
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return;
        }

        log.info("开始处理JSON模版刊登状态，模版数量：{}", amazonTemplateIdList.size());

        // 批量查询模版信息
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria().andIdIn(amazonTemplateIdList);
        example.setColumns("id,country,step_template_status,seller_id,parent_SKU,seller_sku,product_type,sale_variant,variations,status,creation_date,last_update_date,sku_data_source,created_by");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());

        List<AmazonTemplateBO> templateList = amazonTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(templateList)) {
            log.warn("未找到模版信息，模版ID列表：{}", amazonTemplateIdList);
            return;
        }

        for (AmazonTemplateBO template : templateList) {
            // 检查模版是否超时（超过48小时）
            String publishTimeOutTime = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "TEMPLATE_TIME_OUT", 10);
            if (StringUtils.isBlank(publishTimeOutTime)) {
                publishTimeOutTime = "48";
            }
            int timeoutHours = Integer.parseInt(publishTimeOutTime);
            if (statusChecker.isTemplateTimeout(template, timeoutHours)) {
                List<AmazonProcessReport> reportList = getProcessReport(template.getId());
                statusUpdater.updateTimeoutFail(template, reportList, timeoutHours);
                return;
            }

            Boolean stepTemplateStatus = template.getStepTemplateStatus();
            if (Boolean.TRUE.equals(stepTemplateStatus)) {
                continue;
            }
            try {
                processTemplatePublishStatus(template);
            } catch (Exception e) {
                log.error("处理模版刊登状态异常，模版ID：{}", template.getId(), e);
            }
        }
    }

    /**
     * 处理单个模版的发布状态
     *
     * @param template 模版信息
     */
    private void processTemplatePublishStatus(AmazonTemplateBO template) {
        // 获取模版处理报告
        List<AmazonProcessReport> reportList = getProcessReport(template.getId());


        // 同步Listing信息
        List<AmazonListingInfo> listingInfoList = statusChecker.syncListingInfo(template);
        if (CollectionUtils.isEmpty(listingInfoList)) {
            return;
        }

        // 使用策略模式评估发布状态
        PublishStatusEvaluationStrategy strategy = getEvaluationStrategy(template);
        if (strategy == null) {
            log.warn("未找到合适的状态评估策略，模版ID：{}", template.getId());
            return;
        }

        PublishStatusEvaluationStrategy.EvaluationResult result = strategy.evaluatePublishStatus(template, listingInfoList);

        switch (result) {
            case SUCCESS:
                statusUpdater.updatePublishSuccess(template, reportList);
                // 同套账 asin 刊登
//                publishSameAsinTemplate(template, listingInfoList);
                break;
            case NEED_SYNC:
                priceStockSyncProcessor.createPriceStockSyncReport(template);
                statusUpdater.updatePriceStockSyncSuccess(template, reportList);
                break;
            case FAIL:
                String failureDetails = strategy.getFailureDetails(template, listingInfoList);
                statusUpdater.updatePublishFail(template, reportList, listingInfoList);
                break;
        }
    }

    /**
     * 获取适合的评估策略
     *
     * @param template 模版信息
     * @return 评估策略
     */
    private PublishStatusEvaluationStrategy getEvaluationStrategy(AmazonTemplateBO template) {
        return evaluationStrategies.stream()
                .filter(strategy -> strategy.supports(template))
                .findFirst()
                .orElse(null);
    }


    private void publishSameAsinTemplate(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        Map<String, String> sellerSkuAsinMap = listingInfoList.stream().collect(Collectors.toMap(AmazonListingInfo::getSellerSku, AmazonListingInfo::getAsin, (k1, k2) -> k1));
        if (MapUtils.isEmpty(sellerSkuAsinMap)) {
            return;
        }

        String sellerId = template.getSellerId();
        AmazonAccountRelation accountManufacturerInfo = amazonAccountRelationService.getAccountManufacturerInfo(sellerId);
        if (accountManufacturerInfo == null) {
            return;
        }
        String merchantId = accountManufacturerInfo.getMerchantId();
        if (StringUtils.isBlank(merchantId)) {
            return;
        }
        // 查询同套账 asin 刊登
        List<String> sameMerchantAccountNumber = saleAccountService.getSameMerchantAccountNumber(merchantId, SaleChannel.CHANNEL_AMAZON);
        if (CollectionUtils.isEmpty(sameMerchantAccountNumber)) {
            return;
        }
        sameMerchantAccountNumber.forEach(accountNumber -> {
            if (accountNumber.equals(sellerId)) {
                return;
            }
            log.info("同套账 asin 刊登，商家账号：{}，模版ID：{}", accountNumber, template.getId());
            amazonPublishHandler.sameAsinTemplatePublish(template, accountNumber, sellerSkuAsinMap);
        });

    }


    private List<AmazonProcessReport> getProcessReport(Integer templateId) {
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.setFiledColumns("id,status,status_code,relation_id,data_value,feed_type,account_number,relation_type");
        example.createCriteria()
                .andRelationIdEqualTo(templateId)
                .andStatusCodeEqualTo("Processing")
                .andFeedTypeEqualTo(SpFeedType.POST_PRODUCT_DATA.getValue())
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PUBLISH_TEMPLATE.name());

        return amazonProcessReportService.selectFiledColumnsByExample(example);
    }

    /**
     * 处理价格库存同步任务
     *
     * @param beforeMinutes 开始时间（距离当前时间的分钟数）
     * @param afterMinutes  结束时间（距离当前时间的分钟数）
     * @return int 处理的处理报告数量
     */
    public int handlePriceStockSync(int beforeMinutes, int afterMinutes) {
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime afterDateTime = currentDateTime.minusMinutes(afterMinutes);

        // 查询价格库存同步处理报告
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        AmazonProcessReportExample.Criteria criteria = example.createCriteria();
        criteria.andStatusCodeEqualTo("Processing")
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name())
                .andCreationDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andCreationDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(afterDateTime));
        example.setOrderByClause("creation_date");

        List<AmazonProcessReport> reportList = amazonProcessReportService.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(reportList)) {
            return 0;
        }

        log.info("开始处理价格库存同步任务，处理报告数量：{}", reportList.size());

        // 按模版ID分组处理
        Map<Integer, List<AmazonProcessReport>> templateReportMap = reportList.stream()
                .collect(Collectors.groupingBy(AmazonProcessReport::getRelationId));

        for (Map.Entry<Integer, List<AmazonProcessReport>> entry : templateReportMap.entrySet()) {
            Integer templateId = entry.getKey();
            List<AmazonProcessReport> reports = entry.getValue();

            try {
                // 获取模版信息
                AmazonTemplateBO template = getTemplateById(templateId);
                if (template == null) {
                    log.warn("未找到模版信息，模版ID：{}", templateId);
                    continue;
                }

                // 处理价格库存同步
                processPriceStockSync(template, reports);

            } catch (Exception e) {
                log.error("处理价格库存同步异常，模版ID：{}", templateId, e);
            }
        }

        return reportList.size();
    }

    /**
     * 处理全量价格库存同步（超时处理）
     *
     * @param timeoutHours 超时小时数
     * @return int 处理的处理报告数量
     */
    public int handleFullPriceStockSync(int timeoutHours) {
        LocalDateTime timeoutDateTime = LocalDateTime.now().minusHours(timeoutHours);

        // 查询超时的价格库存同步处理报告
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        AmazonProcessReportExample.Criteria criteria = example.createCriteria();
        criteria.andStatusCodeEqualTo("Processing")
                .andRelationTypeEqualTo(ProcessingReportTriggleType.PRICE_STOCK_SYNC.name())
                .andCreationDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(timeoutDateTime));

        List<AmazonProcessReport> reportList = amazonProcessReportService.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(reportList)) {
            return 0;
        }

        log.info("开始处理全量价格库存同步（超时处理），处理报告数量：{}", reportList.size());
        // 使用状态更新器批量处理超时报告
        statusUpdater.updateReportStatus(reportList, false, "价格库存同步超时失败（超过" + timeoutHours + "小时）");
        return reportList.size();
    }

    /**
     * 处理价格库存同步逻辑
     *
     * @param template 模版信息
     * @param reports  处理报告列表
     */
    private void processPriceStockSync(AmazonTemplateBO template, List<AmazonProcessReport> reports) {
        // 同步Listing信息
        List<AmazonListingInfo> listingInfoList = statusChecker.syncListingInfo(template);
        if (CollectionUtils.isEmpty(listingInfoList)) {
            statusUpdater.updateReportStatus(reports, false, "同步Listing信息失败");
            return;
        }

        // 使用策略模式评估发布状态
        PublishStatusEvaluationStrategy strategy = getEvaluationStrategy(template);
        if (strategy == null) {
            log.warn("未找到合适的状态评估策略，模版ID：{}", template.getId());
            statusUpdater.updateReportStatus(reports, false, "未找到合适的状态评估策略");
            return;
        }

        PublishStatusEvaluationStrategy.EvaluationResult result = strategy.evaluatePublishStatus(template, listingInfoList);

        switch (result) {
            case SUCCESS:
                statusUpdater.updatePublishSuccess(template, reports);
                // 同套账 asin 刊登
//                publishSameAsinTemplate(template, listingInfoList);
                break;
            case NEED_SYNC:
                // 价格库存同步中，状态保持不变
                break;
            case FAIL:
                String failureDetails = strategy.getFailureDetails(template, listingInfoList);
                statusUpdater.updateReportStatus(reports, false, "价格库存同步失败：" + failureDetails);
                break;
        }
    }

    /**
     * 根据模版ID获取模版信息
     *
     * @param templateId 模版ID
     * @return AmazonTemplateBO 模版信息
     */
    private AmazonTemplateBO getTemplateById(Integer templateId) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria().andIdEqualTo(templateId);
        example.setColumns("id,country,step_template_status,seller_id,publish_status,parent_SKU,seller_sku,product_type,sale_variant,variations,status,creation_date,last_update_date,sku_data_source");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        List<AmazonTemplateBO> templateList = amazonTemplateService.selectFiledColumnsByExample(example);
        return CollectionUtils.isNotEmpty(templateList) ? templateList.get(0) : null;
    }


}
