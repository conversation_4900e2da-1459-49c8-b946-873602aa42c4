package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.WordDeleteLogQueryDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonWordDeleteLog;

/**
 * <p>
 * amazon词汇删除日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
public interface AmazonWordDeleteLogService extends IService<AmazonWordDeleteLog> {

    CQueryResult<AmazonWordDeleteLog> queryPage(CQuery<WordDeleteLogQueryDTO> query);

}
