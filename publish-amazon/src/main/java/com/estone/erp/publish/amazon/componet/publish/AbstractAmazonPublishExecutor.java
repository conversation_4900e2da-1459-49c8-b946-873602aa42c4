package com.estone.erp.publish.amazon.componet.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.cardcode.CardCodeType;
import com.estone.erp.publish.amazon.cardcode.util.CardCodeUtils;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateBuilderHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateRepeatPublishHelper;
import com.estone.erp.publish.amazon.componet.feedresult.handler.TemplatePublishJsonFeedResultHandler;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingProductData;
import com.estone.erp.publish.amazon.componet.publish.domain.OSSImageData;
import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSampler;
import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSamplerFactory;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.PublishTypeEnum;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonPublishImagePathService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.model.listings.ListingsItemPutRequest;
import io.swagger.client.model.listings.ListingsItemSubmissionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-14 15:13
 */
@Slf4j
public abstract class AbstractAmazonPublishExecutor {
    @Autowired
    private AmazonAccountService amazonAccountService;
    @Autowired
    private JsonFeedSamplerFactory jsonFeedSamplerFactory;
    @Autowired
    protected AmazonTemplateService amazonTemplateService;
    @Autowired
    protected AmazonValidationHelper amazonValidationHelper;
    @Autowired
    private AmazonPublishRetryHandler amazonPublishRetryHandler;
    @Autowired
    private AmazonConstantMarketHelper amazonConstantMarketHelper;
    @Autowired
    protected AmazonProcessReportService amazonProcessReportService;
    @Autowired
    protected AmazonTemplateBuilderHelper amazonTemplateBuilderHelper;
    @Autowired
    private AmazonPublishImagePathService amazonPublishImagePathService;
    @Autowired
    protected AmazonTemplateRepeatPublishHelper amazonTemplateRepeatPublishHelper;



    /**
     * 具体刊登实现类型
     */
    protected abstract ApiResult<String> execute(AmazonPublishContext context);


    public ApiResult<String> publish(AmazonPublishContext context) {
        AmazonProcessReport processReport = getAmazonProcessReport(context);
        if (processReport == null || !ProcessingReportStatusCode.Init.name().equals(processReport.getStatusCode())) {
            return ApiResult.newError(String.format("处理报告:%s,不存在或已完成", context.getMessage().getReportId()));
        }
        context.setProcessReport(processReport);

        ApiResult<String> accountResult = initAccount(context);
        if (!accountResult.isSuccess()) {
            // 刊登结束
            publishFinish(context, accountResult);
            return accountResult;
        }

        // 处理报告状态更新 执行中
        updateProcessReport(context, ProcessingReportStatusCode.Processing);
        ApiResult<String> result;
        try {
            // 执行具体刊登
            result = execute(context);
        } catch (Exception e) {
            log.error("刊登异常：{}", JSON.toJSONString(context.getMessage()), e);
            result = ApiResult.newError(String.format("系统异常,刊登失败：%s", e.getMessage()));
        }
        // 刊登结束
        publishFinish(context, result);
        return ApiResult.newSuccess();
    }


    private ApiResult<String> initAccount(AmazonPublishContext context) {
        String accountNumber = context.getMessage().getAccountNumber();
        AmazonAccount amazonAccount = amazonAccountService.queryAmazonAccountByAccountNumber(accountNumber);
        if (amazonAccount == null) {
            return ApiResult.newError("刊登失败, 账户不存在:" + accountNumber);
        }
        context.setAccount(amazonAccount);
        return ApiResult.newSuccess();
    }

    /**
     * 执行模版刊登操作
     *
     * @param context
     * @return
     */
    public ApiResult<String> publishTemplate(AmazonPublishContext context) {
        AmazonTemplateBO template = context.getTemplate();
        try {
            // 设置店铺货币
            initCurrency(context);
            // 图片映射
            imageMappingHandle(context);
            Integer id = template.getId();

            // 随机采样10%提交至JSONFeed中
            String value = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "json_feed", "rate", 60);
            String rate = StringUtils.defaultIfBlank(value, "10");
            JsonFeedSampler jsonFeedSampler = jsonFeedSamplerFactory.getFeedJsonSampler(Integer.valueOf(rate));
            if (PublishTypeEnum.jsonFeedPublish(template.getPublishType()) || jsonFeedSampler.sample(id)) {
                // 异步批量刊登
                return asyncAddJsonFeed(context);
            } else {
                // 同步刊登
                return syncPutListing(context);
            }
        } catch (Exception e) {
            log.error("刊登异常,模版[{}],错误信息:{}", template.getId(), e.getMessage(), e);
            return ApiResult.newError("刊登异常," + e.getMessage());
        }
    }

    /**
     * 异步批量刊登 json feed
     */
    private ApiResult<String> asyncAddJsonFeed(AmazonPublishContext context) {
        AmazonTemplateBO template = context.getTemplate();
        template.setJsonFeedPublish(true);
        List<ListingProductData> productDataList = new ArrayList<>();
        if (AmazonTemplateUtils.isSaleVariant(template)) {
            // 变体刊登
            ListingProductData parentProductData = ListingProductData.ofParentProduct(context.getTemplate(), context.getAdditionalProperties());
            productDataList.add(parentProductData);
            List<ListingProductData> variantProducts = ListingProductData.ofSaleVariantProduct(context.getTemplate(), context.getAdditionalProperties());
            if (CollectionUtils.isEmpty(variantProducts)) {
                return ApiResult.newError(template.getId() + ",模版变体数据为空");
            }
            productDataList.addAll(variantProducts);
        } else {
            // 单体刊登
            ListingProductData listingProductData = ListingProductData.ofSingleProduct(context.getTemplate(), context.getAdditionalProperties());
            productDataList.add(listingProductData);
        }

        return AmazonSpLocalServiceUtils.publishTemplateJsonFeed(productDataList);
    }

    /**
     * 模版同步刊登
     */
    private ApiResult<String> syncPutListing(AmazonPublishContext context) {
        AmazonTemplateBO template = context.getTemplate();
        if (AmazonTemplateUtils.isSaleVariant(template)) {
            // 变体刊登
            return publishSaleVariantProduct(context);
        } else {
            // 单体刊登
            return publishSingleProduct(context);
        }
    }

    /**
     * 单体产品刊登
     *
     * @param context context
     * @return ApiResult
     */
    private ApiResult<String> publishSingleProduct(AmazonPublishContext context) {
        // 转换为ListingApi格式
        ListingProductData listingProductData = ListingProductData.ofSingleProduct(context.getTemplate(), context.getAdditionalProperties());
        return putListingsItem(listingProductData, ListingsItemPutRequest.RequirementsEnum.LISTING);
    }

    /**
     * 刊登变体产品
     *
     * @param context context
     * @return ApiResult
     */
    private ApiResult<String> publishSaleVariantProduct(AmazonPublishContext context) {
        // 先刊登父体
        ListingProductData parentProductData = ListingProductData.ofParentProduct(context.getTemplate(), context.getAdditionalProperties());

        ApiResult<String> putParentResult = putListingsItem(parentProductData, ListingsItemPutRequest.RequirementsEnum.LISTING_PRODUCT_ONLY);
        if (!putParentResult.isSuccess()) {
            return ApiResult.newError("刊登父体失败," + putParentResult.getErrorMsg());
        }
        // 再刊登变体
        List<ListingProductData> variantProducts = ListingProductData.ofSaleVariantProduct(context.getTemplate(), context.getAdditionalProperties());
        if (CollectionUtils.isEmpty(variantProducts)) {
            return ApiResult.newError("变体产品为空");
        }
        // 并行刊登变体
        List<CompletableFuture<ApiResult<String>>> futures = variantProducts.stream()
                .map(data -> CompletableFuture.supplyAsync(() ->
                                putListingsItem(data, ListingsItemPutRequest.RequirementsEnum.LISTING),
                        AmazonExecutors.PUBLISH_VARIANT_POOL))
                .collect(Collectors.toList());
        // 等待所有变体刊登完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 处理变体刊登结果
        List<ApiResult<String>> results = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        // 失败的变体
        List<String> failVariantSellerSkus = results.stream()
                .filter(result -> !result.isSuccess())
                .map(ApiResult::getErrorMsg)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(failVariantSellerSkus)) {
            return ApiResult.newSuccess("刊登成功");
        }
        // 部分变体刊登失败, 父体刊登成功
        return ApiResult.newError("部分变体刊登失败,失败原因:\n" + failVariantSellerSkus);
    }

    protected ApiResult<String> putListingsItem(ListingProductData listingProductData, ListingsItemPutRequest.RequirementsEnum requirements) {
        String sellerSku = listingProductData.getSellerSku();
        String accountNumber = listingProductData.getAccountNumber();
        // 执行刊登
        ListingsItemPutRequest listingsItemPutRequest = new ListingsItemPutRequest();
        listingsItemPutRequest.setProductType(listingProductData.getProductType());
        listingsItemPutRequest.setAttributes(listingProductData.getData());
        listingsItemPutRequest.setRequirements(requirements);

//        log.info("开始刊登,sellerSku:{},accountNumber:{},additionalProperties:{},listingsItemPutRequest:\n{}\n", sellerSku, accountNumber, StringUtils.join(listingProductData.getAdditionalProperties(), ","), JSON.toJSONString(listingsItemPutRequest));
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, accountNumber, true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);

        ApiResult<ListingsItemSubmissionResponse> apiResult = RetryUtil.doRetryApiResult(() -> {
            ApiResult<ListingsItemSubmissionResponse> responseApiResult = AmazonSpLocalServiceUtils.putListingsItem(amazonSpAccount, sellerSku, listingsItemPutRequest);
            if (!responseApiResult.isSuccess()) {
                String errorMsg = responseApiResult.getErrorMsg();
                // 限流重试
                if (errorMsg.contains("QuotaExceeded")) {
                    throw new RuntimeException("QuotaExceeded:" + responseApiResult.getErrorMsg());
                }
                // 代理重试
                if (errorMsg.contains("proxy-spapi")) {
                    throw new RuntimeException("Proxy-Spapi error:" + responseApiResult.getErrorMsg());
                }
                return responseApiResult;
            }
            return responseApiResult;
        }, 3, 1500);

        if (apiResult.isSuccess() && ListingsItemSubmissionResponse.StatusEnum.ACCEPTED.equals(apiResult.getResult().getStatus())) {
            ListingsItemSubmissionResponse response = apiResult.getResult();
            // 成功
            return ApiResult.newSuccess(sellerSku + ",刊登成功,结果信息:" + JSON.toJSONString(response));
        }
        // 失败
        String errorMsg = JSON.toJSONString(apiResult);
//        log.error("刊登失败,错误信息:\n{}", errorMsg);
        return ApiResult.newError(sellerSku + ",刊登失败,结果信息:\n" + errorMsg);
    }


    /**
     * 刊登成功结束回调 - 只处理同步请求的状态更新
     *
     * @param context context
     * @param publishResult  刊登结果
     */
    protected void publishFinish(AmazonPublishContext context, ApiResult<String> publishResult) {
        AmazonTemplateBO template = context.getTemplate();
        if (template == null) {
            // 更新处理报告
            updateProcessReport(context, publishResult);
            return;
        }
        if (PublishTypeEnum.jsonFeedPublish(template.getPublishType()) || Boolean.TRUE.equals(template.getJsonFeedPublish())) {
            // 异步请求
            updateAsyncProcessReport(context, publishResult);
            return;
        }
        // 更新处理报告
        updateProcessReport(context, publishResult);

        if (!publishResult.isSuccess()) {
            // 刊登失败
            template.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            template.setStepTemplateStatus(false);
            template.setLastUpdateDate(new Date());
            amazonTemplateService.update(template);

            // 特定刊登异常重试处理
            amazonPublishRetryHandler.retryPublishByFail(context, publishResult);
        }
    }

    /**
     * feed json 异步请求结果处理，
     * 回调结果 处理 {@link TemplatePublishJsonFeedResultHandler}
     */
    private void updateAsyncProcessReport(AmazonPublishContext context, ApiResult<String> publishResult) {
        AmazonProcessReport processReport = context.getProcessReport();
        if (publishResult.isSuccess()) {
            // 请求成功
            processReport.setTaskId(publishResult.getResult());
            amazonProcessReportService.update(processReport);
        } else {
            // 请求失败
            updateProcessReport(context, publishResult);
            // 刊登失败
            AmazonTemplateBO template = context.getTemplate();
            template.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            template.setStepTemplateStatus(false);
            template.setLastUpdateDate(new Date());
            amazonTemplateService.update(template);
        }
    }

    protected void updateProcessReport(AmazonPublishContext context, ApiResult<String> publishResult) {
        AmazonProcessReport processReport = context.getProcessReport();
        if (publishResult.isSuccess()) {
            // 请求成功
            processReport.setResultMsg(StringUtils.defaultIfBlank(publishResult.getErrorMsg(), "请求成功"));
        } else {
            // 请求失败
            processReport.setStatusCode(ProcessingReportStatusCode.Complete.name());
            processReport.setStatus(publishResult.isSuccess());
            processReport.setFinishDate(new Date());
            processReport.setResultMsg(StringUtils.defaultIfBlank(publishResult.getErrorMsg(), publishResult.getResult()));
        }
        amazonProcessReportService.update(processReport);
    }

    /**
     * 获取处理报告
     */
    private AmazonProcessReport getAmazonProcessReport(AmazonPublishContext context) {
        return amazonProcessReportService.selectByPrimaryKey(context.getMessage().getReportId());
    }

    /**
     * 更新处理报告状态
     */
    public void updateProcessReport(AmazonPublishContext context, ProcessingReportStatusCode statusCode) {
        AmazonProcessReport processReport = context.getProcessReport();
        processReport.setStatusCode(statusCode.name());
        amazonProcessReportService.update(processReport);
    }


    /**
     * 生成模板产品编码 EAN/UPC, 没有则生成ENA/UPC
     *
     * @param template 模版
     * @param account  店铺
     */
    public void generateTemplateProductCode(AmazonTemplateBO template, AmazonAccount account) {
        //UPC豁免UPC分类不生成EAN
        template.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
        if (Boolean.TRUE.equals(template.getUpcExempt())) {
            return;
        }
        List<AmazonSku> amazonSkus = template.getAmazonSkus();

        Predicate<AmazonTemplateBO> hasProductCode = data -> {
            if (!data.getSaleVariant() || org.apache.commons.collections.CollectionUtils.isEmpty(amazonSkus)) {
                return StringUtils.isNotEmpty(data.getStandardProdcutIdValue()) && StringUtils.isNotEmpty(data.getStandardProdcutIdType());
            }
            return amazonSkus.stream().allMatch(amazonSku -> StringUtils.isNotEmpty(amazonSku.getStandardProdcutIdValue()) && StringUtils.isNotEmpty(amazonSku.getStandardProdcutIdType()));
        };

        if (hasProductCode.test(template)) {
            return;
        }
        // 生成EAN/UPC
        String enaPrefixStr = null;
        if (StringUtils.isNotEmpty(account.getEanPrefix())) {
            enaPrefixStr = account.getEanPrefix();
        }
        int batchEnaSize = template.getSaleVariant() ? template.getAmazonSkus().size() : 1;
        List<String> eanList = CardCodeUtils.generateCardCodes(CardCodeType.EAN, batchEnaSize, enaPrefixStr, account.getAccountNumber(), "publish");
        if (CollectionUtils.isEmpty(eanList)) {
            log.error("店铺:{},刊登生成EAN/UPC失败,模版[{}]", account.getAccountNumber(), template.getId());
            return;
        }
        if (!template.getSaleVariant() || CollectionUtils.isEmpty(amazonSkus)) {
            template.setStandardProdcutIdType(CardCodeType.EAN.name());
            template.setStandardProdcutIdValue(eanList.get(0));
            return;
        }

        for (int i = 0; i < amazonSkus.size(); i++) {
            String enaCode = eanList.get(i);
            AmazonSku sku = amazonSkus.get(i);
            sku.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
            sku.setStandardProdcutIdType(CardCodeType.EAN.name());
            sku.setStandardProdcutIdValue(enaCode);
        }
        template.updateVariations(amazonSkus);

        AmazonTemplateBO updateTemplate = new AmazonTemplateBO();
        updateTemplate.setId(template.getId());
        if (!BooleanUtils.toBoolean(template.getSaleVariant())) {
            updateTemplate.setStandardProdcutIdType(template.getStandardProdcutIdType());
            updateTemplate.setStandardProdcutIdValue(template.getStandardProdcutIdValue());
        } else {
            updateTemplate.setVariations(template.getVariations());
        }
        //logger.info("店铺:{},刊登生成EAN/UPC成功,模版[{}],", account.getAccountNumber(), template.getId());
        amazonTemplateService.updateAmazonTemplateFilterNullByPrimaryKey(updateTemplate);
    }


    /**
     * 初始化账号对应的货币
     */
    private void initCurrency(AmazonPublishContext context) {
        AmazonAccount account = context.getAccount();
        AmazonMarketplace marketplace = amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId());
        String currencyCode = marketplace != null ? marketplace.getCurrency() : AmazonConstant.DEFAULT_COUNTRY_CURRENCY;
        context.getTemplate().setCurrency(currencyCode);
    }


    /**
     * 图片处理, 检查模版是否存在OSS图片，不存在则上传
     *
     * @param context
     */
    protected void imageMappingHandle(AmazonPublishContext context) {
        AmazonTemplateBO template = context.getTemplate();
        List<String> templateImages = AmazonTemplateUtils.getAllImages(List.of(template));
        if (CollectionUtils.isEmpty(templateImages)) {
            return;
        }

        Map<String, OSSImageData> imageMapping = template.getImageMapping();
        if (MapUtils.isEmpty(imageMapping)) {
            // 空 上传OSS图片
            String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(template.getSellerId());
            uploadImageToAliOSS(templateImages, imagePath, imageMapping, template);
            return;
        }

        // 处理临期图片 临期时间近6小时删除
        List<String> deleteImages = imageMapping.entrySet().stream()
                .filter(entry -> entry.getValue().getExpireTime().isBefore(LocalDateTime.now().minusHours(6)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deleteImages)) {
            // 删除过期图片
            imageMapping.entrySet().removeIf(entry -> deleteImages.contains(entry.getKey()));
        }

        // templateImages 中获取需要上传的图片
        List<String> needUploadImages = templateImages.stream()
                .filter(image -> !imageMapping.containsKey(image))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needUploadImages)) {
            // 无需上传
            return;
        }
        // 重新上传图片
        String imagePath = amazonPublishImagePathService.getOrCreateAmazonPublishImagePath(template.getSellerId());
        uploadImageToAliOSS(needUploadImages, imagePath, imageMapping, template);
    }

    private static void uploadImageToAliOSS(List<String> templateImages, String imagePath, Map<String, OSSImageData> imageMapping, AmazonTemplateBO template) {
        Map<String, String> map = AmazonUtils.copyImagesToAliOSS(templateImages, imagePath);
        // 更新图片映射，图片过期时间为5天
        LocalDateTime expireTime = LocalDateTime.now().plusDays(5);
        for (String key : map.keySet()) {
            imageMapping.put(key, new OSSImageData(map.get(key), expireTime));
        }
        template.setImageMapping(imageMapping);
    }


}
