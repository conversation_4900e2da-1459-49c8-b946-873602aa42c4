package com.estone.erp.publish.amazon.componet.publish.util;

import com.networknt.schema.JsonMetaSchema;
import com.networknt.schema.JsonSchemaFactory;
import com.networknt.schema.NonValidationKeyword;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025-03-27 14:18
 */
public class AmazonJsonSchemaFactory {

    private static volatile JsonSchemaFactory singleton;

    private AmazonJsonSchemaFactory() {
        singleton = AmazonJsonSchemaFactory.getInstance();
    }

    public static JsonSchemaFactory getInstance() {
        if (singleton == null) {
            synchronized (AmazonJsonSchemaFactory.class) {
                if (singleton == null) {
                    // "https://schemas.amazon.com/selling-partners/definitions/product-types/meta-schema/v1"
                    String defaultMetaSchemaIri = "https://schemas.amazon.com/selling-partners/definitions/product-types/meta-schema/v1";
                    JsonMetaSchema metaSchemas = JsonMetaSchema.builder(defaultMetaSchemaIri, JsonMetaSchema.getV201909())
                            .keywords(Arrays.asList(
                                    new NonValidationKeyword("$id"),
                                    new NonValidationKeyword("$schema"),
                                    new NonValidationKeyword("$lifecycle"),
                                    new NonValidationKeyword("$ref"),
                                    new NonValidationKeyword("editable"),
                                    new NonValidationKeyword("enumNames")
                            ))
                            .build();

                    singleton = JsonSchemaFactory.builder()
                            .defaultMetaSchemaIri(defaultMetaSchemaIri)
                            .metaSchema(metaSchemas)
                            .enableSchemaCache(true)
                            .build();
                }
            }
        }
        return singleton;
    }

}
