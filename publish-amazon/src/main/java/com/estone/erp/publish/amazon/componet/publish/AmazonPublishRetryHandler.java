package com.estone.erp.publish.amazon.componet.publish;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AmazonPublishRetryHandler {

    @Autowired
    private AmazonTemplateService amazonTemplateService;
    @Autowired
    private AmazonPublishHandler amazonPublishHandler;

    /**
     * 根据报错类型重新刊登模版
     *
     * @param context
     * @param publishResult
     */
    public void retryPublishByFail(AmazonPublishContext context, ApiResult<String> publishResult) {
        String errorMsg = publishResult.getErrorMsg();
        if (StringUtils.isBlank(errorMsg)) {
            return;
        }
        AmazonTemplateBO template = context.getTemplate();
        retryPublishByFailureDetails(template, errorMsg);

    }

    public void retryPublishByFailureDetails(AmazonTemplateBO template, String failureDetails) {

        // OSS 图片失效重刊登
        if (failureDetails.contains("Media Not Found on Server (404)")) {

            template.setOssImageData(null);
            amazonTemplateService.updateImageMapping(template);

            amazonPublishHandler.templatePublishNew(template);
        }


    }
}
