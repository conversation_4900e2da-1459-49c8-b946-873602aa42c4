package com.estone.erp.publish.amazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.ProductTypeTemplateBO;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonMarketplaceEnums;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.mapper.ProductTypeTemplateMapper;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.dto.TemplateCategoryMappingDO;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.amazon.service.ProductTypeTemplateService;
import com.estone.erp.publish.amazon.util.AmazonOperateLogUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalServiceUtils;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ProductCategoryInfo;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.model.catalogItems.Item;
import io.swagger.client.model.catalogItems.ItemBrowseClassification;
import io.swagger.client.model.catalogItems.ItemBrowseClassificationsByMarketplace;
import io.swagger.client.model.catalogItems.ItemSearchResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> product_type_template
 * 2023-08-16 17:22:49
 */
@Service("productTypeTemplateService")
@Slf4j
public class ProductTypeTemplateServiceImpl implements ProductTypeTemplateService {

    /**
     * 专有分类
     */
    private final static Cache<String, List<String>> PROPRIETARY_CLASSIFICATION = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES).build();

    /**
     * recommendClassificationIds 缓存
     */
    private final static Cache<String, List<String>> RE_CLASS_IDS = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.HOURS).build();

    @Resource
    private ProductTypeTemplateMapper productTypeTemplateMapper;
    @Resource
    private AmazonOperateLogService amazonOperateLogService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private CategoryMappingService categoryMappingService;
    @Resource
    private AmazonCategoryService amazonCategoryService;


    @Override
    public int countByExample(ProductTypeTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return productTypeTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ProductTypeTemplate> search(CQuery<ProductTypeTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ProductTypeTemplateCriteria query = cquery.getSearch();
        ProductTypeTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = productTypeTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        if (StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("update_time desc");
        }
        List<ProductTypeTemplate> productTypeTemplates = productTypeTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ProductTypeTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(productTypeTemplates);
        return result;
    }

    @Override
    public ProductTypeTemplate selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return productTypeTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProductTypeTemplate> selectByExample(ProductTypeTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return productTypeTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(ProductTypeTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return productTypeTemplateMapper.insert(record);
    }

   @Override
   public int updateByPrimaryKeySelective(ProductTypeTemplate record){
       Assert.notNull(record, "record is null!");
       return productTypeTemplateMapper.updateByPrimaryKeySelective(record);
   }

    @Override
    public int updateByExampleSelective(ProductTypeTemplate record, ProductTypeTemplateExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return productTypeTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return productTypeTemplateMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<ProductTypeTemplate> selectFiledColumnsByExample(ProductTypeTemplateExample example){
        Assert.notNull(example, "example is null!");
        return productTypeTemplateMapper.selectFiledColumnsByExample(example);
    }

    public String addProductTypeTemplate(ProductTypeTemplate productTypeTemplate){
        // 查询是否存在
        String msg = checkIsExistRepeartData(productTypeTemplate);
        if (StringUtils.isNotBlank(msg)){
            return msg;
        }
        Date time = new Date();
        productTypeTemplate.setEnable(true);
        productTypeTemplate.setCreateTime(time);
        productTypeTemplate.setUpdateTime(time);
        productTypeTemplate.setProductCategoryCodes(StrUtil.strAddComma(productTypeTemplate.getProductCategoryCodes()));
        insert(productTypeTemplate);
        return null;
    }

    private String checkIsExistRepeartData(ProductTypeTemplate productTypeTemplate){
        // 查询是否存在
        List<String> newCategoryIds =CommonUtils.splitList(productTypeTemplate.getProductCategoryCodes(), ",");
        ProductTypeTemplateExample productTypeTemplateExample = new ProductTypeTemplateExample();
        ProductTypeTemplateExample.Criteria criteria =productTypeTemplateExample.createCriteria();
        criteria.andSiteEqualTo(productTypeTemplate.getSite())
                .andTemplateNameEqualTo(productTypeTemplate.getTemplateName())
                .andProductTypeEqualTo(productTypeTemplate.getProductType());
        Optional<Integer> optional = Optional.ofNullable(productTypeTemplate.getId());
        if(optional.isPresent()){
            criteria.andIdNotEqualTo(productTypeTemplate.getId());
        }
        productTypeTemplateExample.setFiledColumns("id,product_category_codes");
        List<ProductTypeTemplate> productTypeTemplateList = selectFiledColumnsByExample(productTypeTemplateExample);
        if (CollectionUtils.isNotEmpty(productTypeTemplateList)){
            for (ProductTypeTemplate productTypeTemplate1: productTypeTemplateList){
                List<String> oldCategoryIds = CommonUtils.splitList(productTypeTemplate1.getProductCategoryCodes(), ",");
                List<String> delCategaryIds = oldCategoryIds.stream()
                        .filter(o -> !newCategoryIds.contains(o)).collect(Collectors.toList());
                List<String> addCategaryIds = newCategoryIds.stream()
                        .filter(o -> !oldCategoryIds.contains(o)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(delCategaryIds) && CollectionUtils.isEmpty(addCategaryIds)){
                    return "存在该数据，请勿重复添加";
                }
            }
        }
        return null;
    }

    @Override
    public String updateProductTypeTemplate(ProductTypeTemplateBO record) {
        Assert.notNull(record, "record is null!");
        ProductTypeTemplate updateProductTypeTemplate = new ProductTypeTemplate();
        BeanUtils.copyProperties(record,updateProductTypeTemplate);
        String msg = checkIsExistRepeartData(updateProductTypeTemplate);
        if (StringUtils.isNotBlank(msg)){
            return msg;
        }
        updateProductTypeTemplate.setUpdateTime(new Date());
        ProductTypeTemplate oldProductTypeTemplate = selectByPrimaryKey(record.getId());
        updateProductTypeTemplate.setProductCategoryCodes(StrUtil.strAddComma(record.getProductCategoryCodes()));
        productTypeTemplateMapper.updateByPrimaryKeySelective(updateProductTypeTemplate);
        // 店铺配置字段记录日志
        List<AmazonOperateLog> configLogs = AmazonOperateLogUtils.buildUpdateLog(oldProductTypeTemplate, record,
                AmazonOperateLogEnum.UPDATE_PRODUCT_TYPE_TEMPLATE, "id");

        // 如果分类全选，则日志不进行比较前后值，只记录“分类全选”
        if (BooleanUtils.isTrue(record.getSelectAllCategory())) {
            record.setProductCategoryCodes(AmazonOperateLogUtils.SELECT_ALL_CATEGORY);
        }
        // 处理特殊日志
        AmazonOperateLogUtils.tranSpecialLog(configLogs);

        // 批量保存日志
        amazonOperateLogService.batchInsert(configLogs);
        return null;
    }

    @Override
    public String batchUpdateStatus(List<Integer> idList,Boolean updateStatus,String userName){
        // 记录日志集合
        List<AmazonOperateLog> infoLogs = new ArrayList<>();
        String columns = "id,enable";
        ProductTypeTemplateExample productTypeTemplateExample = new ProductTypeTemplateExample();
        productTypeTemplateExample.setFiledColumns(columns);
        ProductTypeTemplateExample.Criteria query =productTypeTemplateExample.createCriteria();
        query.andIdIn(idList);
        query.andEnableEqualTo(!updateStatus);
        List<ProductTypeTemplate> productTypeTemplateList = selectFiledColumnsByExample(productTypeTemplateExample);
        if (CollectionUtils.isEmpty(productTypeTemplateList)){
           String msg = updateStatus? "请选择禁用的数据":"请选择启用的数据";
            return msg;
        }
        Date time = new Date();
        for (ProductTypeTemplate productTypeTemplate : productTypeTemplateList){
            // 加入日志
            ProductTypeTemplate updateProductTypeTemplate = new ProductTypeTemplate();
            updateProductTypeTemplate.setId(productTypeTemplate.getId());
            updateProductTypeTemplate.setEnable(updateStatus);
            List<AmazonOperateLog> configLogs = AmazonOperateLogUtils.buildUpdateLog(productTypeTemplate, updateProductTypeTemplate,
                    AmazonOperateLogEnum.UPDATE_PRODUCT_TYPE_TEMPLATE, "id");
            updateProductTypeTemplate.setUpdateBy(userName);
            updateProductTypeTemplate.setUpdateTime(time);
            updateByPrimaryKeySelective(updateProductTypeTemplate);
            infoLogs.addAll(configLogs);
        }
        amazonOperateLogService.batchInsert(infoLogs);
        return null;
    }

    /**
     * 处理导入的分类code和匹配产品系统二三级类目名
     * @param productCategoryInfoMap
     * @param bean
     * @param prodCodes
     * @return
     */
    private ProductTypeTemplate handleProdCategory(Map<String,ProductCategoryInfo> productCategoryInfoMap,ProductTypeTemplate bean,String prodCodes){
        if (MapUtils.isEmpty(productCategoryInfoMap) || StringUtils.isBlank(prodCodes)){
            return bean;
        }
        List<String> prodCodeList = Arrays.asList(StringUtils.split(prodCodes,","));
        Map<String,String> fullpathMap = new HashMap<>();
        StringBuffer allFullPath = new StringBuffer();
        StringBuffer code = new StringBuffer();
        for (String fullpathcode : prodCodeList){
            if (productCategoryInfoMap.containsKey(fullpathcode)){
                ProductCategoryInfo productCategoryInfo =productCategoryInfoMap.get(fullpathcode);
                String fullpath = productCategoryInfo.getFullpath().replace("系统类目三>", "");
                if (productCategoryInfo.getLevel()== 4) {
                    fullpath = StringUtils.substringBeforeLast(fullpath,">");
                }
                if (productCategoryInfo.getLevel()== 5) {
                    fullpath = StringUtils.substringBeforeLast(fullpath,">");
                    fullpath = StringUtils.substringBeforeLast(fullpath,">");
                }

                fullpathMap.put(fullpath,fullpath);
                code.append("," + productCategoryInfo.getCode().trim());
            }
        }
        for (String fullpathcode : fullpathMap.keySet()){
            allFullPath.append("@@@" + fullpathMap.get(fullpathcode).trim());
        }
        allFullPath.delete(0,3);
        bean.setProductCategoryCodes(StrUtil.strAddComma(String.valueOf(code)));
        bean.setProductCategoryNames(String.valueOf(allFullPath));
        return bean;
    }

    @Override
    public String importProductTypeTemplate(List<ProductTypeTemplate> productTypeTemplateList){
        if (CollectionUtils.isEmpty(productTypeTemplateList)){
            return "导入数据为空";
        }
        List<ProductCategoryInfo> productCategoryInfoList = ProductUtils.getProductSystemAllCategory();
        if (CollectionUtils.isEmpty(productCategoryInfoList)){
            return "未查询到产品系统的分类";
        }
        Map<String,ProductCategoryInfo> productCategoryInfoMap = productCategoryInfoList.stream()
                .collect(Collectors.toMap(o -> o.getFullpathcode(),o ->o));
        for (ProductTypeTemplate productTypeTemplate:productTypeTemplateList) {
            try {
                handleProdCategory(productCategoryInfoMap,productTypeTemplate,productTypeTemplate.getProductCategoryCodes());
                // 查询是否存在,存在跳过
                String msg = checkIsExistRepeartData(productTypeTemplate);
                if (StringUtils.isNotBlank(msg)) {
                    continue;
                }
                insert(productTypeTemplate);
            }catch (Exception e){
                log.error("导入数据出错：" + e.getMessage(),e);
            }
        }
        return null;
    }

    @Override
    public ApiResult<String> fixProductTypeNameConfigData() {
        SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, "product_type_template" ,"productTypeTemplateName");
        if (systemParam == null) {
            return ApiResult.newSuccess();
        }
        String paramValue = systemParam.getParamValue();
        Map<String,List<String>> configMap = JSON.parseObject(paramValue, new TypeReference<>(){});

        ProductTypeTemplateExample productTypeTemplateExample = new ProductTypeTemplateExample();
        productTypeTemplateExample.setFiledColumns("distinct site, template_name ");
        ProductTypeTemplateExample.Criteria query =productTypeTemplateExample.createCriteria();
        query.andEnableEqualTo(true);
        List<ProductTypeTemplate> productTypeTemplateList = productTypeTemplateMapper.selectFiledColumnsByExample(productTypeTemplateExample);
        if (CollectionUtils.isEmpty(productTypeTemplateList)) {
            return ApiResult.newSuccess();
        }

        for (ProductTypeTemplate productTypeTemplate : productTypeTemplateList) {
            List<String> templates = configMap.get(productTypeTemplate.getSite());
            if (CollectionUtils.isEmpty(templates)) {
                continue;
            }
            String templateName = productTypeTemplate.getTemplateName();
            if (!templates.contains(templateName)) {
                templates.add(templateName);
            }
        }

        systemParam.setParamValue(JSON.toJSONString(configMap));
        systemParamService.updateByPrimaryKeySelective(systemParam);
        String key = RedisConstant.PUBLISH_SYSTEM_NAME + SaleChannel.CHANNEL_AMAZON + ":product_type_template:productTypeTemplateName";
        PublishRedisClusterUtils.del(key);
        return ApiResult.newSuccess();
    }

    @Override
    public ApiResult<ProductTypeTemplate> getTemplateNameBySiteAndType(ProductTypeTemplate param) {
        if (StringUtils.isBlank(param.getSite()) || StringUtils.isBlank(param.getProductType())) {
            return ApiResult.newError("站点或分类类型不能为空");
        }

        ProductTypeTemplateExample productTypeTemplateExample = new ProductTypeTemplateExample();
        productTypeTemplateExample.setFiledColumns("id, site, template_name, product_type");
        ProductTypeTemplateExample.Criteria query =productTypeTemplateExample.createCriteria();
        query.andEnableEqualTo(true);
        query.andSiteEqualTo(param.getSite());
        query.andProductTypeEqualTo(param.getProductType());
        List<ProductTypeTemplate> productTypeTemplateList = productTypeTemplateMapper.selectFiledColumnsByExample(productTypeTemplateExample);
        if (CollectionUtils.isEmpty(productTypeTemplateList)) {
            return ApiResult.newError("无符合条件的数据");
        }

        ProductTypeTemplate productTypeTemplate = productTypeTemplateList.get(0);
        return ApiResult.newSuccess(productTypeTemplate);
    }

    @Override
    public void matchProductCategoryByKeywords(AmazonTemplateBO templateInfo) {
        String country = templateInfo.getCountry();
        if (StringUtils.isBlank(country)) {
            return;
        }

        String classificationsSearchTerms = templateInfo.getClassificationsSearchTerms();
        if (StringUtils.isBlank(classificationsSearchTerms)) {
            return;
        }

        AmazonMarketplaceEnums marketplaceEnums = AmazonMarketplaceEnums.fromSiteName(country);
        List<AmazonMarketplaceEnums> checkSites = List.of(AmazonMarketplaceEnums.AE, AmazonMarketplaceEnums.AU, AmazonMarketplaceEnums.MX);
        if (checkSites.stream().noneMatch(site -> site.equals(marketplaceEnums))) {
            return;
        }

        String key = marketplaceEnums.name() + ":" + classificationsSearchTerms;
        List<String> recommendClassificationIds;
        try {
            recommendClassificationIds = RE_CLASS_IDS.get(key, () -> {
                List<String> ids = getRecommendClassificationIds(templateInfo);
                if (CollectionUtils.isEmpty(ids)) {
                    return Collections.emptyList();
                }
                return ids;
            });
        } catch (Exception e) {
            return;
        }
        if (CollectionUtils.isEmpty(recommendClassificationIds)) {
            return;
        }

        if (AmazonMarketplaceEnums.AE == marketplaceEnums) {
            templateInfo.setCategoryId(recommendClassificationIds.get(0));
            resetPlatFormCategoryInfo(templateInfo);
            return;
        }
        List<String> proprietaryClassifications = PROPRIETARY_CLASSIFICATION.getIfPresent(country);
        if (proprietaryClassifications == null) {
            SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, "PROPRIETARY", "CLASSIFICATION");
            Map<String, List<String>> config = JSON.parseObject(systemParam.getParamValue(), new TypeReference<Map<String, List<String>>>() {
            });
            if (config.get(country) == null) {
                return;
            }
            proprietaryClassifications = config.get(country);
            PROPRIETARY_CLASSIFICATION.putAll(config);
        }

        boolean allMatchProprietary = recommendClassificationIds.stream().allMatch(proprietaryClassifications::contains);
        if (allMatchProprietary) {
            throw new BusinessException("通过搜索词获取推荐分类,无可用分类");
        }

        for (String recommend : recommendClassificationIds) {
            if (!proprietaryClassifications.contains(recommend)) {
                templateInfo.setCategoryId(recommend);
                resetPlatFormCategoryInfo(templateInfo);
                return;
            }
        }
    }

    private void resetPlatFormCategoryInfo(AmazonTemplateBO templateInfo) {
        AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId(templateInfo.getCategoryId(), templateInfo.getCountry());
        if (category != null) {
            templateInfo.setBrowsePathById(category.getBrowsePathById());
            String[] productTypeDefinitionsArray = category.getProductTypeDefinitions().split(",");
            templateInfo.setProductType(productTypeDefinitionsArray[0]);
            templateInfo.setCategoryTemplateName(category.getBrowsePathByName());
        }
    }

    private List<String> getRecommendClassificationIds(AmazonTemplateBO templateInfo) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, templateInfo.getSellerId(), true);
        AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
        ApiResult<ItemSearchResults> searchResultsApiResult = AmazonSpLocalServiceUtils.searchCatalogItemsByIdentifiersV2(List.of(templateInfo.getClassificationsSearchTerms()), amazonSpAccount);
        if (!searchResultsApiResult.isSuccess()) {
            return new ArrayList<>();
        }
        ItemSearchResults itemSearchResults = searchResultsApiResult.getResult();
        if (itemSearchResults == null || CollectionUtils.isEmpty(itemSearchResults.getItems())) {
            return new ArrayList<>();
        }
        List<String> recommendClassificationIds = new ArrayList<>();
        List<Item> items = itemSearchResults.getItems();
        for (Item itemData : items) {
            if (CollectionUtils.isEmpty(itemData.getClassifications())) {
                continue;
            }
            for (ItemBrowseClassificationsByMarketplace classifications : itemData.getClassifications()) {
                List<ItemBrowseClassification> classificationList = classifications.getClassifications();
                if (CollectionUtils.isEmpty(classificationList)) {
                    continue;
                }

                for (ItemBrowseClassification itemBrowseClassification : classificationList) {
                    recommendClassificationIds.add(itemBrowseClassification.getClassificationId());
                }
            }
        }
        return recommendClassificationIds.stream().distinct().limit(3).collect(Collectors.toList());
    }

    /**
     * 根据系统类目和站点查询模板名称
     *
     * @param categoryCodePath 系统类目
     * @param site             站点
     * @param interfaceType
     * @return 模板名称
     */
    @Override
    public TemplateCategoryMappingDO matchTemplateCategoryMapping(String categoryCodePath, String site, Integer interfaceType) {

        // 先查询类目映射获取分类类型
        CategoryMappingExample example = new CategoryMappingExample();
        example.createCriteria()
                .andSystemCategoryIdEqualTo(categoryCodePath)
                .andSiteEqualTo(site)
                .andApplyStateEqualTo(ApplyStatusEnum.YES.getCode());
        List<CategoryMapping> categoryMappings = categoryMappingService.selectByExample(example);
        if (CollectionUtils.isEmpty(categoryMappings)) {
            return null;
        }
        Collections.shuffle(categoryMappings);

        CategoryMapping categoryMapping = categoryMappings.get(0);
        TemplateCategoryMappingDO templateCategoryMappingDO = new TemplateCategoryMappingDO();
        templateCategoryMappingDO.setSystemCategoryPathCode(categoryCodePath);
        templateCategoryMappingDO.setSite(site);
        templateCategoryMappingDO.setPlatformCategoryId(categoryMapping.getPlatformCategoryId());
        String productType = categoryMapping.getProductTypeName();
        String jsonProductType = categoryMapping.getJsonProductType();
        if (StringUtils.isNotBlank(productType) && TemplateInterfaceTypeEnums.XSD.isTrue(interfaceType)) {
            templateCategoryMappingDO.setProductType(productType);
            templateCategoryMappingDO.setParentProductType(StringUtils.substringBefore(productType, "."));
        }else if (StringUtils.isNotBlank(jsonProductType) && TemplateInterfaceTypeEnums.JSON.isTrue(interfaceType)) {
            templateCategoryMappingDO.setProductType(jsonProductType);
        }

        // 分类类型+产品分类id获取一个模板名称
        ProductTypeTemplateExample typeTemplateExample = new ProductTypeTemplateExample();
        typeTemplateExample.createCriteria()
                .andEnableEqualTo(true)
                .andSiteEqualTo(site)
                .andProductTypeEqualTo(categoryMapping.getProductTypeName());
        List<ProductTypeTemplate> productTypeTemplates = productTypeTemplateMapper.selectByExample(typeTemplateExample);
        if (CollectionUtils.isNotEmpty(productTypeTemplates)) {
            ProductTypeTemplate productTypeTemplate = productTypeTemplates.get(0);
            templateCategoryMappingDO.setPlatformCategoryTemplateName(productTypeTemplate.getTemplateName());
        }

        // 获取平台全路径id
        AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithCategoryIdAndName(categoryMapping.getPlatformCategoryId(), categoryMapping.getPlatformCategoryName(), site);
        if (category != null) {
            templateCategoryMappingDO.setBrowsePathById(category.getBrowsePathById());
            if (TemplateInterfaceTypeEnums.JSON.isTrue(interfaceType)) {
                // 接口类型为json时，返回新分类类型
                if (StringUtils.isBlank(templateCategoryMappingDO.getProductType())) {
                    String[] productTypeDefinitionsArray = category.getProductTypeDefinitions().split(",");
                    templateCategoryMappingDO.setProductType(productTypeDefinitionsArray[0]);
                }
                templateCategoryMappingDO.setPlatformCategoryTemplateName(category.getBrowsePathByName());
            }
        }


        return templateCategoryMappingDO;
    }

}