package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * Amazon商标词白名单查询DTO
 * 用于接收前端查询参数，包含分页信息和查询条件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
public class TrademarkWordWhitelistQueryDto {


    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇（模糊查询）
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private List<String> trademarkIdentificationList;

    /**
     * 禁售站点列表（用于查询条件）
     */
    private List<String> prohibitionSiteList;

    /**
     * 禁售平台列表（用于查询条件）
     */
    private List<String> forbidChannelList;

    /**
     * 添加人
     */
    private List<String> createByList;

    /**
     * 添加时间范围 - 开始时间
     */
    private String createdTimeStart;

    /**
     * 添加时间范围 - 结束时间
     */
    private String createdTimeEnd;

    /**
     * 修改时间范围 - 开始时间
     */
    private String modifiedTimeStart;

    /**
     * 修改时间范围 - 结束时间
     */
    private String modifiedTimeEnd;

    /**
     * 修改时间范围 - 开始时间
     */
    private String updatedTimeStart;

    /**
     * 修改时间范围 - 结束时间
     */
    private String updatedTimeEnd;

}
