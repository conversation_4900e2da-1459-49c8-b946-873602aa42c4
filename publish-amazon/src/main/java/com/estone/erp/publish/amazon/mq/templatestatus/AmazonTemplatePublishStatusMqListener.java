package com.estone.erp.publish.amazon.mq.templatestatus;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.mq.model.TemplatePublishStatusMessage;
import com.estone.erp.publish.amazon.mq.templatestatus.strategy.PublishStatusEvaluationStrategy;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Amazon 模板刊登状态队列监听器
 * AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class AmazonTemplatePublishStatusMqListener implements ChannelAwareMessageListener {
    @Autowired
    private AmazonTemplateService amazonTemplateService;
    @Autowired
    private AmazonPublishStatusChecker statusChecker;

    @Autowired
    private AmazonPublishStatusUpdater statusUpdater;

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;

    @Autowired
    private List<PublishStatusEvaluationStrategy> evaluationStrategies;

    @Autowired
    private AmazonPriceStockSyncProcessor priceStockSyncProcessor;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            // 获取消息体
            if (StringUtils.isBlank(body)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 解析消息内容
            TemplatePublishStatusMessage statusMessage = JSON.parseObject(body, TemplatePublishStatusMessage.class);
            if (statusMessage == null) {
                log.warn("解析模板刊登状态消息失败，消息内容：{}", body);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 处理模板刊登状态
            processTemplatePublishStatus(statusMessage);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("AMAZON_TEMPLATE_PUBLISH_STATUS_QUEUE MQ Listener Error, message: {}", body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    /**
     * 处理模板刊登状态
     *
     * @param statusMessage 状态消息
     */
    @SneakyThrows
    private void processTemplatePublishStatus(TemplatePublishStatusMessage statusMessage) {
        List<Integer> templateIds = statusMessage.getTemplateIds();
        List<AmazonTemplateBO> amazonTemplateBOS = searchTemplate(templateIds);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            log.warn("未查询到对应的模版信息，模版ID：{}", templateIds);
            return;
        }

        List<AmazonListingInfo> listingInfoList = statusChecker.syncListingInfos(amazonTemplateBOS);
        if (CollectionUtils.isEmpty(listingInfoList)) {
            log.warn("未查询到对应的Listing信息，模版ID：{}", templateIds);
            return;
        }

        Map<String, AmazonListingInfo> amazonListingInfoMap = listingInfoList.stream()
                .collect(Collectors.toMap(AmazonListingInfo::getSellerSku, Function.identity(), (k1, k2) -> k1));

        for (AmazonTemplateBO amazonTemplateBO : amazonTemplateBOS) {
            List<String> allSellerSkuSku = AmazonTemplateUtils.getAllSellerSkuSku(amazonTemplateBO);
            List<AmazonListingInfo> amazonListingInfos = allSellerSkuSku.stream()
                    .map(amazonListingInfoMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            try {
                checkTemplateStatus(amazonTemplateBO, amazonListingInfos);
            } catch (Exception e) {
                log.error("处理模板刊登状态失败，模版ID：{}，错误信息：{}", amazonTemplateBO.getId(), e.getMessage(), e);
            }
        }
    }


    private void checkTemplateStatus(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        if (CollectionUtils.isEmpty(listingInfoList)) {
            log.warn("未查询到对应的Listing信息，模版ID：{}", template.getId());
            return;
        }

        // 使用策略模式评估发布状态
        PublishStatusEvaluationStrategy strategy = getEvaluationStrategy(template);
        if (strategy == null) {
            log.warn("未找到合适的状态评估策略，模版ID：{}", template.getId());
            return;
        }

        List<AmazonProcessReport> reportList = getProcessReport(template.getId());

        PublishStatusEvaluationStrategy.EvaluationResult result = strategy.evaluatePublishStatus(template, listingInfoList);

        switch (result) {
            case SUCCESS:
                statusUpdater.updatePublishSuccess(template, reportList);
                // 同套账 asin 刊登
//                publishSameAsinTemplate(template, listingInfoList);
                break;
            case NEED_SYNC:
                if (reportList.stream().noneMatch(report -> ProcessingReportTriggleType.PRICE_STOCK_SYNC.name().equals(report.getRelationType()))) {
                    priceStockSyncProcessor.createPriceStockSyncReport(template);
                }
                statusUpdater.updatePriceStockSyncSuccess(template, reportList);
                break;
            case FAIL:
                String failureDetails = strategy.getFailureDetails(template, listingInfoList);
                statusUpdater.updatePublishFail(template, reportList, failureDetails);
                break;
        }


    }

    private List<AmazonProcessReport> getProcessReport(Integer templateId) {
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.setFiledColumns("id,status,status_code,relation_id,data_value,feed_type,account_number,relation_type");
        example.createCriteria()
                .andRelationIdEqualTo(templateId)
                .andStatusCodeEqualTo("Processing");

        return amazonProcessReportService.selectFiledColumnsByExample(example);
    }


    /**
     * 获取适合的评估策略
     *
     * @param template 模版信息
     * @return 评估策略
     */
    private PublishStatusEvaluationStrategy getEvaluationStrategy(AmazonTemplateBO template) {
        return evaluationStrategies.stream()
                .filter(strategy -> strategy.supports(template))
                .findFirst()
                .orElse(null);
    }

    private List<AmazonTemplateBO> searchTemplate(List<Integer> templateIds) {
        // 批量查询模版信息
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
        example.setColumns("id,country,step_template_status,seller_id,parent_SKU,seller_sku,product_type,sale_variant,variations,status,creation_date,last_update_date,sku_data_source,created_by");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        return amazonTemplateService.selectFiledColumnsByExample(example);
    }


}
