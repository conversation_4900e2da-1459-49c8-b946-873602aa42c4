package com.estone.erp.publish.tidb.publishtidb.vo;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonTrademarkWordWhitelist;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * Amazon商标词白名单查询结果VO类
 * 包含禁售平台和禁售站点的字段转换处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Data
public class AmazonTrademarkWordWhitelistVO {

    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 站点
     */
    private String site;

    /**
     * 侵权词汇
     */
    private String infringementWord;

    /**
     * 商标词标识
     */
    private String trademarkIdentification;

    /**
     * 禁售站点（原始字符串）
     */
    private String prohibitionSite;

    /**
     * 禁售平台（原始字符串）
     */
    private String forbidChannel;


    /**
     * 添加人
     */
    private String createBy;

    /**
     * 添加时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 将实体对象转换为VO对象
     * 实现禁售平台和禁售站点的字符串到List转换
     *
     * @param entity Amazon商标词白名单实体对象
     * @return VO对象
     */
    public static AmazonTrademarkWordWhitelistVO fromEntity(AmazonTrademarkWordWhitelist entity) {
        if (entity == null) {
            return null;
        }

        AmazonTrademarkWordWhitelistVO vo = new AmazonTrademarkWordWhitelistVO();

        // 复制基本属性
        BeanUtils.copyProperties(entity, vo);

        return vo;
    }


}
