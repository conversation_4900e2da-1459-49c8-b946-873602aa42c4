package com.estone.erp.publish.amazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmazonOperateLogEnum;
import com.estone.erp.publish.amazon.enums.AmazonReqAttrAdapterTypeEnums;
import com.estone.erp.publish.amazon.mapper.ProductTypeTemplateJsonAttrMapper;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttrCriteria;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttrExample;
import com.estone.erp.publish.amazon.model.dto.AmazonRequiredAttributeDO;
import com.estone.erp.publish.amazon.model.dto.ProductTypeTemplateJsonAttrDO;
import com.estone.erp.publish.amazon.service.AmazonOperateLogService;
import com.estone.erp.publish.amazon.service.ProductTypeTemplateJsonAttrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025-03-11 15:27:36
 */
@Service("productTypeTemplateJsonAttrService")
@Slf4j
public class ProductTypeTemplateJsonAttrServiceImpl implements ProductTypeTemplateJsonAttrService {
    @Resource
    private ProductTypeTemplateJsonAttrMapper productTypeTemplateJsonAttrMapper;
    @Resource
    private AmazonOperateLogService amazonOperateLogService;

    @Override
    public int countByExample(ProductTypeTemplateJsonAttrExample example) {
        Assert.notNull(example, "example is null!");
        return productTypeTemplateJsonAttrMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ProductTypeTemplateJsonAttrDO> search(CQuery<ProductTypeTemplateJsonAttrCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ProductTypeTemplateJsonAttrCriteria query = cquery.getSearch();
        ProductTypeTemplateJsonAttrExample example = query.getExample();
        example.setOrderByClause("created_time desc");
        if (Objects.nonNull(query.getType()) && query.getType() == 2) {
            example.setGroupByClause("product_type,attribute_name, attribute_value");
        }

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = productTypeTemplateJsonAttrMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ProductTypeTemplateJsonAttr> productTypeTemplateJsonAttrs = productTypeTemplateJsonAttrMapper.selectByExample(example);
        List<ProductTypeTemplateJsonAttrDO> productTypeTemplateJsonAttrDOS = productTypeTemplateJsonAttrs.stream()
                .map(ProductTypeTemplateJsonAttrDO::new)
                .collect(Collectors.toList());
        // 组装结果
        CQueryResult<ProductTypeTemplateJsonAttrDO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(productTypeTemplateJsonAttrDOS);
        return result;
    }

    @Override
    public CQueryResult<ProductTypeTemplateJsonAttrDO> searchGroupBy(CQuery<ProductTypeTemplateJsonAttrCriteria> cquery, String groupByFields) {
        Assert.notNull(cquery, "cquery is null!");
        Assert.notNull(groupByFields, "groupByFields is null!");

        ProductTypeTemplateJsonAttrCriteria query = cquery.getSearch();
        ProductTypeTemplateJsonAttrExample example = query.getExample();
        example.setGroupByClause(groupByFields);

        if (example.getOrderByClause() == null) {
            example.setOrderByClause("created_time desc");
        }

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            // 注意：使用group by时，countByExample可能不准确，这里简化处理
            total = productTypeTemplateJsonAttrMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }

        List<ProductTypeTemplateJsonAttr> productTypeTemplateJsonAttrs = productTypeTemplateJsonAttrMapper.selectByExample(example);
        List<ProductTypeTemplateJsonAttrDO> productTypeTemplateJsonAttrDOS = productTypeTemplateJsonAttrs.stream()
                .map(ProductTypeTemplateJsonAttrDO::new)
                .collect(Collectors.toList());

        // 组装结果
        CQueryResult<ProductTypeTemplateJsonAttrDO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(productTypeTemplateJsonAttrDOS);
        return result;
    }

    @Override
    public ProductTypeTemplateJsonAttr selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return productTypeTemplateJsonAttrMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProductTypeTemplateJsonAttr> selectByExample(ProductTypeTemplateJsonAttrExample example) {
        Assert.notNull(example, "example is null!");
        return productTypeTemplateJsonAttrMapper.selectByExample(example);
    }

    @Override
    public int insert(ProductTypeTemplateJsonAttr record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return productTypeTemplateJsonAttrMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ProductTypeTemplateJsonAttr record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return productTypeTemplateJsonAttrMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ProductTypeTemplateJsonAttr record, ProductTypeTemplateJsonAttrExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return productTypeTemplateJsonAttrMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return productTypeTemplateJsonAttrMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ApiResult<String> saveOrUpdateProductTypeTemplateJsonAttr(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        Assert.notNull(amazonRequiredAttributeDO, "参数不能为空!");
        Assert.notNull(amazonRequiredAttributeDO.getAttributeName(), "必填属性名称不能为空!");
        Assert.notNull(amazonRequiredAttributeDO.getAttributeValue(), "必填属性值不能为空!");
        Assert.notNull(amazonRequiredAttributeDO.getType(), "类型不能为空!");
        Assert.notNull(amazonRequiredAttributeDO.getApplicableAttributeType(), "适用属性类型不能为空!");
        try {
            AmazonReqAttrAdapterTypeEnums adapterTypeEnum = amazonRequiredAttributeDO.matchAdapterType();
            // 站点匹配
            if (adapterTypeEnum == AmazonReqAttrAdapterTypeEnums.SITE_MATCH) {
                handleSiteMatchAttribute(amazonRequiredAttributeDO);
                return ApiResult.newSuccess("保存站点属性成功");
            }

            // 分类类型匹配
            if (adapterTypeEnum == AmazonReqAttrAdapterTypeEnums.CATEGORY_TYPE_MATCH) {
                handleCategoryTypeMatchAttribute(amazonRequiredAttributeDO);
                return ApiResult.newSuccess("保存分类属性成功");
            }

            // 站点+分类类型匹配
            if (adapterTypeEnum == AmazonReqAttrAdapterTypeEnums.SITE_CATEGORY_TYPE_MATCH) {
                handleSiteCategoryTypeMatchAttribute(amazonRequiredAttributeDO);
                return ApiResult.newSuccess("保存站点-分类属性成功");
            }
            // 通用属性
            if (adapterTypeEnum == AmazonReqAttrAdapterTypeEnums.GENERAL_ATTR) {
                handleGeneralAttribute(amazonRequiredAttributeDO);
                return ApiResult.newSuccess("保存通用属性成功");
            }
            return ApiResult.newError("type not support!");
        } catch (Exception e) {
            log.error("保存必填属性失败", e);
            return ApiResult.newError("保存属性失败: " + e.getMessage());
        }
    }

    private void handleGeneralAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        // 查询已存在的属性记录
        ProductTypeTemplateJsonAttr oldData = null;
        if (amazonRequiredAttributeDO.getId() != null) {
            oldData = updateAttributeHandler(amazonRequiredAttributeDO);
        } else {
            // 检查重复属性
            checkRepeatAttribute(amazonRequiredAttributeDO);

        }
        deleteRelatedAttrs(amazonRequiredAttributeDO.getType(), amazonRequiredAttributeDO.getAttributeName());
        ProductTypeTemplateJsonAttr baseRecord = createBaseRecord(amazonRequiredAttributeDO);
        if (oldData != null) {
            baseRecord.setCreateBy(oldData.getCreateBy());
            baseRecord.setCreatedTime(oldData.getCreatedTime());
        }

        baseRecord.setSite(null);
        baseRecord.setProductType(null);
        productTypeTemplateJsonAttrMapper.insert(baseRecord);
        if (oldData != null) {
            ProductTypeTemplateJsonAttrDO attrDO = new ProductTypeTemplateJsonAttrDO(oldData);
            oldData.setSite(CollectionUtils.isNotEmpty(attrDO.getSites()) ? String.join(",", attrDO.getSites()) : attrDO.getSite());
            oldData.setProductType(CollectionUtils.isNotEmpty(attrDO.getProductTypes()) ? String.join(",", attrDO.getProductTypes()) : attrDO.getProductType());
            // 记录操作日志
            oldData.setId(baseRecord.getId());
            baseRecord.setSite(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getSites()) ? String.join(",", amazonRequiredAttributeDO.getSites()) : baseRecord.getSite());
            baseRecord.setProductType(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes()) ? String.join(",", amazonRequiredAttributeDO.getProductTypes()) : baseRecord.getProductType());
            amazonOperateLogService.addProductTypeAttributeLog(oldData, baseRecord, AmazonOperateLogEnum.TEMPLATE_REQUIRED_ATTR_CONFIG);
        }
    }

    private void handleSiteCategoryTypeMatchAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        // 查询已存在的属性记录
        ProductTypeTemplateJsonAttr oldData = null;
        if (amazonRequiredAttributeDO.getId() != null) {
            oldData = updateAttributeHandler(amazonRequiredAttributeDO);
        } else {
            // 检查重复属性
            checkRepeatAttribute(amazonRequiredAttributeDO);
        }


        for (String productType : amazonRequiredAttributeDO.getProductTypes()) {
            for (String site : amazonRequiredAttributeDO.getSites()) {
                ProductTypeTemplateJsonAttr baseRecord = createBaseRecord(amazonRequiredAttributeDO);
                baseRecord.setSite(site);
                baseRecord.setProductType(productType);
                amazonRequiredAttributeDO.setSite(site);
                amazonRequiredAttributeDO.setProductType(productType);
                delExistedAttribute(amazonRequiredAttributeDO);

                if (oldData != null) {
                    baseRecord.setCreateBy(oldData.getCreateBy());
                    baseRecord.setCreatedTime(oldData.getCreatedTime());
                }
                productTypeTemplateJsonAttrMapper.insert(baseRecord);
                if (oldData != null) {
                    // 记录操作日志
                    oldData.setId(baseRecord.getId());
                    baseRecord.setSite(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getSites()) ? String.join(",", amazonRequiredAttributeDO.getSites()) : baseRecord.getSite());
                    baseRecord.setProductType(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes()) ? String.join(",", amazonRequiredAttributeDO.getProductTypes()) : baseRecord.getProductType());
                    amazonOperateLogService.addProductTypeAttributeLog(oldData, baseRecord, AmazonOperateLogEnum.TEMPLATE_REQUIRED_ATTR_CONFIG);
                }
            }
        }
    }

    private void handleCategoryTypeMatchAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        // 查询已存在的属性记录
        ProductTypeTemplateJsonAttr oldData = null;
        if (amazonRequiredAttributeDO.getId() != null) {
            oldData = updateAttributeHandler(amazonRequiredAttributeDO);
        } else {
            // 检查重复属性
            checkRepeatAttribute(amazonRequiredAttributeDO);
        }

        for (String productType : amazonRequiredAttributeDO.getProductTypes()) {
            ProductTypeTemplateJsonAttr baseRecord = createBaseRecord(amazonRequiredAttributeDO);
            baseRecord.setProductType(productType);
            if (oldData != null) {
                baseRecord.setCreateBy(oldData.getCreateBy());
                baseRecord.setCreatedTime(oldData.getCreatedTime());
            }
            baseRecord.setSite(null);
            amazonRequiredAttributeDO.setProductType(productType);
            delExistedAttribute(amazonRequiredAttributeDO);
            productTypeTemplateJsonAttrMapper.insert(baseRecord);
            if (oldData != null) {
                // 记录操作日志
                oldData.setId(baseRecord.getId());
                baseRecord.setSite(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getSites()) ? String.join(",", amazonRequiredAttributeDO.getSites()) : baseRecord.getSite());
                baseRecord.setProductType(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes()) ? String.join(",", amazonRequiredAttributeDO.getProductTypes()) : baseRecord.getProductType());
                amazonOperateLogService.addProductTypeAttributeLog(oldData, baseRecord, AmazonOperateLogEnum.TEMPLATE_REQUIRED_ATTR_CONFIG);
            }
        }
    }

    private ProductTypeTemplateJsonAttr updateAttributeHandler(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        ProductTypeTemplateJsonAttr oldData = productTypeTemplateJsonAttrMapper.selectByPrimaryKey(amazonRequiredAttributeDO.getId());
        if (oldData != null) {
            ProductTypeTemplateJsonAttrDO attrDO = new ProductTypeTemplateJsonAttrDO(oldData);
            oldData.setSite(CollectionUtils.isNotEmpty(attrDO.getSites()) ? String.join(",", attrDO.getSites()) : attrDO.getSite());
            oldData.setProductType(CollectionUtils.isNotEmpty(attrDO.getProductTypes()) ? String.join(",", attrDO.getProductTypes()) : attrDO.getProductType());

            // 同类型的数据可能存在多条需要删除
            AmazonRequiredAttributeDO existAttr = new AmazonRequiredAttributeDO();
            existAttr.setType(oldData.getType());
            existAttr.setProductType(oldData.getProductType());
            existAttr.setAttributeName(oldData.getAttributeName());
            existAttr.setSites(attrDO.getSites());
            delExistedAttribute(existAttr);
        }
        if (amazonRequiredAttributeDO.getType() == 1) {
            checkSingleRepeatAttribute(amazonRequiredAttributeDO);
        } else {
            // 检查是否存在相同类型的属性
            deleteRelatedAttrs(amazonRequiredAttributeDO.getType(), amazonRequiredAttributeDO.getAttributeName());
        }
        if (amazonRequiredAttributeDO.getId() != null) {
            productTypeTemplateJsonAttrMapper.deleteByPrimaryKey(List.of(amazonRequiredAttributeDO.getId()));
        }
        return oldData;
    }

    private void checkSingleRepeatAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        // 只有在完全相同的条件下才检查重复
        ProductTypeTemplateJsonAttrExample relatedExample = new ProductTypeTemplateJsonAttrExample();
        ProductTypeTemplateJsonAttrExample.Criteria criteria = relatedExample.createCriteria()
                .andTypeEqualTo(amazonRequiredAttributeDO.getType())
                .andAttributeNameEqualTo(amazonRequiredAttributeDO.getAttributeName())
                .andIdNotEqualTo(amazonRequiredAttributeDO.getId());

        // 添加站点条件
        if (CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getSites())) {
            criteria.andSiteEqualTo(amazonRequiredAttributeDO.getSites().get(0));
        } else {
            criteria.andSiteIsNull();
        }

        // 添加产品类型条件
        if (CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes())) {
            criteria.andProductTypeEqualTo(amazonRequiredAttributeDO.getProductTypes().get(0));
        } else {
            criteria.andProductTypeIsNull();
        }

        List<ProductTypeTemplateJsonAttr> relatedAttrs = productTypeTemplateJsonAttrMapper.selectByExample(relatedExample);
        if (CollectionUtils.isNotEmpty(relatedAttrs)) {
            throw new IllegalArgumentException("属性值已存在，请重新选择属性或编辑已存在的属性!");
        }
    }

    private void handleSiteMatchAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        // 查询已存在的属性记录
        ProductTypeTemplateJsonAttr oldData = null;
        if (amazonRequiredAttributeDO.getAttributeName() != null) {
            oldData = updateAttributeHandler(amazonRequiredAttributeDO);
        } else {
            // 检查重复属性
            if (amazonRequiredAttributeDO.getType() == 1) {
                checkRepeatAttribute(amazonRequiredAttributeDO);
            }
        }


        for (String site : amazonRequiredAttributeDO.getSites()) {
            ProductTypeTemplateJsonAttr baseRecord = createBaseRecord(amazonRequiredAttributeDO);
            baseRecord.setSite(site);
            if (oldData != null) {
                baseRecord.setCreateBy(oldData.getCreateBy());
                baseRecord.setCreatedTime(oldData.getCreatedTime());
            }

            baseRecord.setProductType(null);
            amazonRequiredAttributeDO.setSite(site);
            delExistedAttribute(amazonRequiredAttributeDO);
            productTypeTemplateJsonAttrMapper.insert(baseRecord);
            if (oldData != null) {
                // 记录操作日志
                oldData.setId(baseRecord.getId());
                // 记录操作日志
                baseRecord.setSite(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getSites()) ? String.join(",", amazonRequiredAttributeDO.getSites()) : baseRecord.getSite());
                baseRecord.setProductType(CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes()) ? String.join(",", amazonRequiredAttributeDO.getProductTypes()) : baseRecord.getProductType());
                amazonOperateLogService.addProductTypeAttributeLog(oldData, baseRecord, AmazonOperateLogEnum.TEMPLATE_REQUIRED_ATTR_CONFIG);
            }
        }
    }

    private ProductTypeTemplateJsonAttr delExistedAttribute(AmazonRequiredAttributeDO baseRecord) {
        ProductTypeTemplateJsonAttrExample example = new ProductTypeTemplateJsonAttrExample();
        ProductTypeTemplateJsonAttrExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(baseRecord.getType());
        criteria.andAttributeNameEqualTo(baseRecord.getAttributeName());
        if (baseRecord.getProductType() != null) {
            criteria.andProductTypeEqualTo(baseRecord.getProductType());
        }
        if (baseRecord.getSite() != null) {
            criteria.andSiteEqualTo(baseRecord.getSite());
        } else if (CollectionUtils.isNotEmpty(baseRecord.getSites())) {
            criteria.andSiteIn(baseRecord.getSites());
        }

        List<ProductTypeTemplateJsonAttr> productTypeTemplateJsonAttrs = productTypeTemplateJsonAttrMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(productTypeTemplateJsonAttrs)) {
            List<Integer> ids = productTypeTemplateJsonAttrs.stream().map(ProductTypeTemplateJsonAttr::getId).collect(Collectors.toList());
            productTypeTemplateJsonAttrMapper.deleteByPrimaryKey(ids);
        }
        return null;
    }

    private void deleteRelatedAttrs(Integer type, String attributeName) {
        ProductTypeTemplateJsonAttrExample relatedExample = new ProductTypeTemplateJsonAttrExample();
        relatedExample.createCriteria()
                .andTypeEqualTo(type)
                .andAttributeNameEqualTo(attributeName)
                .andSiteIsNull()  // 只删除通用属性，不删除站点特定的属性
                .andProductTypeIsNull();  // 只删除通用属性，不删除产品类型特定的属性

        List<ProductTypeTemplateJsonAttr> relatedAttrs = productTypeTemplateJsonAttrMapper.selectByExample(relatedExample);
        if (CollectionUtils.isNotEmpty(relatedAttrs)) {
            List<Integer> ids = relatedAttrs.stream()
                    .map(ProductTypeTemplateJsonAttr::getId)
                    .collect(Collectors.toList());
            productTypeTemplateJsonAttrMapper.deleteByPrimaryKey(ids);
        }
    }

    private void checkRepeatAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        ProductTypeTemplateJsonAttrExample relatedExample = new ProductTypeTemplateJsonAttrExample();
        // 只检查通用属性和非站点相关的属性是否重复
        relatedExample.or(relatedExample.createCriteria()
                .andSiteIsNull()
                .andProductTypeIsNull()
                .andTypeEqualTo(amazonRequiredAttributeDO.getType())
                .andAttributeNameEqualTo(amazonRequiredAttributeDO.getAttributeName())
        );

        // 检查只有产品类型的属性
        if (CollectionUtils.isNotEmpty(amazonRequiredAttributeDO.getProductTypes()) && CollectionUtils.isEmpty(amazonRequiredAttributeDO.getSites())) {
            relatedExample.or(relatedExample.createCriteria()
                    .andProductTypeIn(amazonRequiredAttributeDO.getProductTypes())
                    .andSiteIsNull()
                    .andTypeEqualTo(amazonRequiredAttributeDO.getType())
                    .andAttributeNameEqualTo(amazonRequiredAttributeDO.getAttributeName())
            );
        }

        // 不再检查站点相关的属性是否重复，因为我们允许不同站点有相同属性名称的不同值

        List<ProductTypeTemplateJsonAttr> relatedAttrs = productTypeTemplateJsonAttrMapper.selectByExample(relatedExample);
        if (CollectionUtils.isNotEmpty(relatedAttrs)) {
            throw new IllegalArgumentException("属性值已存在，请重新选择属性或编辑已存在的属性!");
        }
    }

    @Override
    public ApiResult<String> deleteByIds(List<Integer> ids) {
        ids.forEach(id -> {
            ProductTypeTemplateJsonAttr dbData = selectByPrimaryKey(id);
            if (dbData != null) {
                AmazonRequiredAttributeDO existAttr = new AmazonRequiredAttributeDO();
                existAttr.setType(dbData.getType());
                existAttr.setProductType(dbData.getProductType());
                existAttr.setAttributeName(dbData.getAttributeName());
                if (StringUtils.isNotBlank(dbData.getExtraData())) {
                    Map<String, List<String>> extraDataMap = JSON.parseObject(dbData.getExtraData(), new TypeReference<>() {
                    });
                    List<String> sites = extraDataMap.get("sites");
                    existAttr.setSites(sites);
                }
                delExistedAttribute(existAttr);
            }

        });
        return ApiResult.newSuccess();
    }

    @Override
    public void matchRequiredAttribute(AmazonTemplateBO templateInfo) {
        String productType = templateInfo.getProductType();
        String site = templateInfo.getCountry();
        if (StringUtils.isBlank(productType)) {
            return;
        }
        AmazonRequiredAttributeDO attributeQuery = new AmazonRequiredAttributeDO();
        attributeQuery.setProductType(productType);
        attributeQuery.setSite(site);
        attributeQuery.setType(1);
        List<ProductTypeTemplateJsonAttr> requiredAttribute = getRequiredAttribute(attributeQuery);
        if (CollectionUtils.isEmpty(requiredAttribute)) {
            return;
        }
        HashMap<Object, Object> optionalAttrMap = new HashMap<>();
        // 匹配运营配置属性
        for (ProductTypeTemplateJsonAttr attr : requiredAttribute) {
            JSONObject attributeValue = JSON.parseObject(attr.getAttributeValue());
            JSONArray jsonArray = attributeValue.getJSONArray(attr.getAttributeName());
            optionalAttrMap.put(attr.getAttributeName(), jsonArray);
        }
        templateInfo.setExtraData(JSON.toJSONString(optionalAttrMap));
    }



    @Override
    public List<ProductTypeTemplateJsonAttr> getTemplateRequiredAttribute(AmazonRequiredAttributeDO requiredAttributeDO) {
        String productType = requiredAttributeDO.getProductType();
        String site = requiredAttributeDO.getSite();
        ProductTypeTemplateJsonAttrExample example = new ProductTypeTemplateJsonAttrExample();
        example.setCustomList("id, site, product_type, attribute_name, attribute_value, `type`, applicable_attribute_type, code_adapter");
        // 站点分类类型适用属性
        example.or(example.createCriteria().andSiteEqualTo(site).andProductTypeEqualTo(productType));
        // 全站点当前分类类型适用属性
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteEqualTo(site).andProductTypeIsNull());
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteIsNull().andProductTypeEqualTo(productType));
        // 全站点所有分类类型适用属性
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteIsNull().andProductTypeIsNull());
        return productTypeTemplateJsonAttrMapper.selectByExampleCustomList(example);
    }

    @Override
    public List<String> getAdapterProperties() {
        ProductTypeTemplateJsonAttrExample example = new ProductTypeTemplateJsonAttrExample();
        example.setCustomList("distinct attribute_name");
        example.createCriteria()
                .andCodeAdapterEqualTo(false)
                .andTypeEqualTo(2);
        List<ProductTypeTemplateJsonAttr> productTypeTemplateJsonAttrs = productTypeTemplateJsonAttrMapper.selectByExampleCustomList(example);
        return productTypeTemplateJsonAttrs.stream().map(ProductTypeTemplateJsonAttr::getAttributeName).collect(Collectors.toList());
    }

    @Override
    public List<ProductTypeTemplateJsonAttr> getTechnicalRequiredAttribute(String productType, String site) {
        ProductTypeTemplateJsonAttrExample example = new ProductTypeTemplateJsonAttrExample();
        example.setCustomList("id, site, product_type, attribute_name, attribute_value, `type`, applicable_attribute_type, code_adapter");
        // 站点分类类型适用属性
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteEqualTo(site).andProductTypeEqualTo(productType));
        // 全站点当前分类类型适用属性
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteIsNull().andProductTypeEqualTo(productType));
        // 全站点所有分类类型适用属性
        example.or(example.createCriteria().andTypeEqualTo(2).andSiteIsNull().andProductTypeIsNull());
        return productTypeTemplateJsonAttrMapper.selectByExampleCustomList(example);
    }

    @Override
    public List<ProductTypeTemplateJsonAttr> getRequiredAttribute(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        Assert.notNull(amazonRequiredAttributeDO, "参数不能为空!");

        ProductTypeTemplateJsonAttrExample example = new ProductTypeTemplateJsonAttrExample();
        example.setCustomList("id, site, product_type, attribute_name, attribute_value, `type`, applicable_attribute_type, code_adapter");

        // 设置类型条件
        Integer type = amazonRequiredAttributeDO.getType();
        String site = amazonRequiredAttributeDO.getSite();
        String productType = amazonRequiredAttributeDO.getProductType();

        // 优先级1：匹配特定站点和产品类型的属性
        if (site != null && productType != null) {
            ProductTypeTemplateJsonAttrExample.Criteria criteria1 = example.createCriteria();
            criteria1.andSiteEqualTo(site);
            criteria1.andProductTypeEqualTo(productType);
            if (type != null) {
                criteria1.andTypeEqualTo(type);
            }
        }

        // 优先级2：匹配特定站点的属性（不限产品类型）
        if (site != null) {
            ProductTypeTemplateJsonAttrExample.Criteria criteria2 = example.or();
            criteria2.andSiteEqualTo(site);
            criteria2.andProductTypeIsNull();
            if (type != null) {
                criteria2.andTypeEqualTo(type);
            }
        }

        // 优先级3：匹配特定产品类型的属性（不限站点）
        if (productType != null) {
            ProductTypeTemplateJsonAttrExample.Criteria criteria3 = example.or();
            criteria3.andProductTypeEqualTo(productType);
            criteria3.andSiteIsNull();
            if (type != null) {
                criteria3.andTypeEqualTo(type);
            }
        }

        // 优先级4：通用属性（不限站点和产品类型）
        ProductTypeTemplateJsonAttrExample.Criteria criteria4 = example.or();
        criteria4.andSiteIsNull();
        criteria4.andProductTypeIsNull();
        if (type != null) {
            criteria4.andTypeEqualTo(type);
        } else {
            // 如果没有指定类型，默认使用技术部配置
            criteria4.andTypeEqualTo(2);
        }

        return productTypeTemplateJsonAttrMapper.selectByExampleCustomList(example);
    }


    // 创建基础记录对象
    private ProductTypeTemplateJsonAttr createBaseRecord(AmazonRequiredAttributeDO amazonRequiredAttributeDO) {
        ProductTypeTemplateJsonAttr record = new ProductTypeTemplateJsonAttr();
        record.setAttributeName(amazonRequiredAttributeDO.getAttributeName());
        record.setAttributeValue(amazonRequiredAttributeDO.getAttributeValue());
        record.setType(amazonRequiredAttributeDO.getType());
        record.setApplicableAttributeType(amazonRequiredAttributeDO.getApplicableAttributeType());
        record.setAdapterType(amazonRequiredAttributeDO.matchAdapterType().getCode());
        record.setCodeAdapter(amazonRequiredAttributeDO.getCodeAdapter());
        record.setCreateBy(WebUtils.getUserName());
        record.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        record.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        HashMap<Object, Object> extraData = new HashMap<>();
        extraData.put("productTypes", amazonRequiredAttributeDO.getProductTypes());
        extraData.put("sites", amazonRequiredAttributeDO.getSites());
        record.setExtraData(JSON.toJSONString(extraData));
        return record;
    }
}
