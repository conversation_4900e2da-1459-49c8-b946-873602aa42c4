package com.estone.erp.publish.amazon.mq.templatestatus.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.model.dto.PublishVariantStatus;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;
import io.swagger.client.model.listings.FulfillmentAvailability;
import io.swagger.client.model.listings.ItemOfferByMarketplace;
import io.swagger.client.model.listings.ItemSummaryByMarketplace;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 变体商品状态评估策略
 * 处理变体商品的发布状态评估逻辑
 */
@Slf4j
@Component
public class VariantItemEvaluationStrategy implements PublishStatusEvaluationStrategy {

    @Override
    public boolean supports(AmazonTemplateBO template) {
        return AmazonTemplateUtils.isSaleVariant(template);
    }

    @Override
    public EvaluationResult evaluatePublishStatus(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        if (CollectionUtils.isEmpty(listingInfoList)) {
            log.warn("Listing信息为空，模版ID：{}", template.getId());
            return EvaluationResult.FAIL;
        }

        Map<String, AmazonListingInfo> listingInfoMap = listingInfoList.stream()
                .filter(Objects::nonNull)
                .filter(amazonListingInfo -> StringUtils.isNotBlank(amazonListingInfo.getSellerSku()))
                .collect(Collectors.toMap(AmazonListingInfo::getSellerSku, Function.identity(), (k1, k2) -> k1));
        
        List<PublishVariantStatus> publishVariantStatusList = buildVariantStatusList(template, listingInfoMap);
        
        // 检查是否全部成功
        boolean allSuccess = publishVariantStatusList.stream()
                .allMatch(variantStatus -> {
                    Integer itemType = variantStatus.getItemType();
                    if (AmazonListingitemtypeEnum.Maleparent_Item.isTrue(itemType)) {
                        return ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue().equals(variantStatus.getStatus());
                    }
                    if (AmazonListingitemtypeEnum.Vriant_Item.isTrue(itemType)) {
                        return ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue().equals(variantStatus.getStatus());
                    }
                    return ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue().equals(variantStatus.getStatus());
                });
        if (allSuccess) {
            return EvaluationResult.SUCCESS;
        }

        // 检查是否有错误
        boolean anyError = publishVariantStatusList.stream()
                .anyMatch(variantStatus -> StringUtils.isNotBlank(variantStatus.getErrorMessage()));
        if (anyError) {
            return EvaluationResult.FAIL;
        }

        // 检查是否全部可发现
        boolean allDiscoverable = publishVariantStatusList.stream()
                .allMatch(variantStatus -> ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue().equals(variantStatus.getStatus()));
        if (allDiscoverable) {
            return EvaluationResult.NEED_SYNC;
        }

        return EvaluationResult.NEED_SYNC;
    }

    @Override
    public String getFailureDetails(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList) {
        if (CollectionUtils.isEmpty(listingInfoList)) {
            return "Listing信息为空";
        }

        StringBuilder issueMsg = new StringBuilder();
        for (AmazonListingInfo listingInfo : listingInfoList) {
            if (Boolean.FALSE.equals(listingInfo.getIsOnline()) && StringUtils.isNotBlank(listingInfo.getIssue())) {
                if (issueMsg.length() > 0) {
                    issueMsg.append("; ");
                }
                issueMsg.append("\n").append(listingInfo.getSellerSku()).append(": ").append(listingInfo.getIssue());
            }
        }
        return issueMsg.length() > 0 ? issueMsg.toString() : "变体商品刊登失败";
    }

    /**
     * 构建变体状态列表
     */
    private List<PublishVariantStatus> buildVariantStatusList(AmazonTemplateBO template, 
                                                              Map<String, AmazonListingInfo> listingInfoMap) {
        List<PublishVariantStatus> publishVariantStatusList = new ArrayList<>();
        String parentSellerSku = template.getSellerSKU();
        List<String> allSellerSkuSku = AmazonTemplateUtils.getAllSellerSkuSku(template);
        
        allSellerSkuSku.forEach(sellerSku -> {
            int itemType = StringUtils.equals(sellerSku, parentSellerSku) 
                    ? AmazonListingitemtypeEnum.Maleparent_Item.getStatusCode() 
                    : AmazonListingitemtypeEnum.Vriant_Item.getStatusCode();
            
            AmazonListingInfo amazonListingInfo = listingInfoMap.get(sellerSku);
            if (amazonListingInfo == null) {
                PublishVariantStatus publishVariantStatus = new PublishVariantStatus(sellerSku, itemType, null, "未找到Listing信息");
                publishVariantStatusList.add(publishVariantStatus);
                return;
            }
            
            List<String> itemStatus = getSellerSkuStatus(amazonListingInfo);

            if (isBuyable(itemStatus)) {
                String status = ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue();
                PublishVariantStatus publishVariantStatus = new PublishVariantStatus(sellerSku, itemType, status, "");
                publishVariantStatusList.add(publishVariantStatus);
                return;
            }

            if (isDiscoverable(itemStatus)) {
                String status = ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue();
                PublishVariantStatus publishVariantStatus = new PublishVariantStatus(sellerSku, itemType, status, "");
                String offers = amazonListingInfo.getOffers();
                String fulfillmentAvailability = amazonListingInfo.getFulfillmentAvailability();
                if (StringUtils.isNotBlank(offers) && StringUtils.isNotBlank(fulfillmentAvailability)) {
                    List<ItemOfferByMarketplace> itemOffers = JSON.parseArray(offers, ItemOfferByMarketplace.class);
                    for (ItemOfferByMarketplace itemOffer : itemOffers) {
                        String amount = itemOffer.getPrice().getAmount();
                        publishVariantStatus.setAmount(amount);
                    }
                    List<FulfillmentAvailability> fulfillmentAvailabilities = JSON.parseArray(fulfillmentAvailability, FulfillmentAvailability.class);
                    for (FulfillmentAvailability availability : fulfillmentAvailabilities) {
                        Integer quantity = availability.getQuantity();
                        publishVariantStatus.setQuantity(quantity);
                    }
                }
                if (Objects.nonNull(publishVariantStatus.getAmount()) && Objects.nonNull(publishVariantStatus.getQuantity())) {
                    publishVariantStatus.setStatus(ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue());
                }
                publishVariantStatusList.add(publishVariantStatus);
                return;
            }


            if (StringUtils.isNotBlank(amazonListingInfo.getIssue())) {
                PublishVariantStatus publishVariantStatus = new PublishVariantStatus(sellerSku, itemType, null, "");
                StringBuilder issueMsg = new StringBuilder();
                JSON.parseArray(amazonListingInfo.getIssue()).forEach(issue -> {
                    JSONObject summaryObject = (JSONObject) issue;
                    String severity = summaryObject.getString("severity");
                    if ("ERROR".equals(severity)) {
                        issueMsg.append("\n").append(summaryObject.toJSONString());
                    }
                });
                if (issueMsg.length() > 0) {
                    publishVariantStatus.setErrorMessage(issueMsg.toString());
                    publishVariantStatusList.add(publishVariantStatus);
                }
            }
        });
        
        return publishVariantStatusList;
    }

    /**
     * 获取SellerSku的状态列表
     */
    private List<String> getSellerSkuStatus(AmazonListingInfo listingInfo) {
        String summaries = listingInfo.getSummaries();
        if (StringUtils.isBlank(summaries)) {
            return null;
        }

        JSONArray summariesArray = JSON.parseArray(summaries);
        JSONObject summariesObject = summariesArray.getJSONObject(0);
        if (summariesObject == null) {
            return null;
        }

        JSONArray status = summariesObject.getJSONArray("status");
        if (status == null) {
            return null;
        }

        return status.toJavaList(String.class);
    }

    /**
     * 判断商品状态是否为可售
     */
    private boolean isBuyable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.BUYABLE.getValue());
    }

    /**
     * 判断商品状态是否为可发现
     */
    private boolean isDiscoverable(List<String> itemStatus) {
        if (CollectionUtils.isEmpty(itemStatus)) {
            return false;
        }
        return itemStatus.contains(ItemSummaryByMarketplace.StatusEnum.DISCOVERABLE.getValue());
    }
} 