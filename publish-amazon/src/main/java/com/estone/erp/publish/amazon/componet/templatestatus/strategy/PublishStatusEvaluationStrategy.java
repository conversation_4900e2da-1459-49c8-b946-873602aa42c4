package com.estone.erp.publish.amazon.componet.templatestatus.strategy;

import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonListingInfo;

import java.util.List;

/**
 * 发布状态评估策略接口
 * 定义单体和变体商品的状态评估规范
 */
public interface PublishStatusEvaluationStrategy {

    /**
     * 评估发布状态结果
     */
    enum EvaluationResult {
        SUCCESS,        // 刊登成功
        FAIL,          // 刊登失败
        NEED_SYNC      // 需要价格库存同步
    }

    /**
     * 判断当前策略是否支持指定的模版类型
     * 
     * @param template 模版信息
     * @return true-支持，false-不支持
     */
    boolean supports(AmazonTemplateBO template);

    /**
     * 评估发布状态
     * 
     * @param template 模版信息
     * @param listingInfoList Listing信息列表
     * @return 评估结果
     */
    EvaluationResult evaluatePublishStatus(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList);

    /**
     * 获取评估失败的详细信息
     * 
     * @param template 模版信息
     * @param listingInfoList Listing信息列表
     * @return 失败详细信息
     */
    String getFailureDetails(AmazonTemplateBO template, List<AmazonListingInfo> listingInfoList);
} 