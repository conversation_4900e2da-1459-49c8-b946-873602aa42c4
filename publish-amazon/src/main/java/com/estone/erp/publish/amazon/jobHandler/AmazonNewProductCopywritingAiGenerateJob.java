package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.system.ai.AiServiceClient;
import com.estone.erp.publish.system.ai.bean.ChatChoice;
import com.estone.erp.publish.system.ai.bean.ChatCompletionResponse;
import com.estone.erp.publish.system.ai.bean.ChatOpenaiRequest;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.response.OfficialAmazonResponse;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewBasisInfoVO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonNewProductCopywritingReview;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Semaphore;


/**
 * Amazon新产品AI文案标题生成定时任务
 *
 * <AUTHOR> Assistant
 * @date 2025-07-11
 */
@Slf4j
@Component
public class AmazonNewProductCopywritingAiGenerateJob extends AbstractJobHandler {

    @Autowired
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;

    @Autowired
    private AmazonNewProductCopywritingReviewService amazonNewProductCopywritingReviewService;

    @Autowired
    private AiServiceClient aiServiceClient;

    @Autowired
    private ProductClient productClient;

    @Resource
    private SystemParamService systemParamService;
    public AmazonNewProductCopywritingAiGenerateJob() {
        super("AmazonNewProductCopywritingAiGenerateJob");
    }

    /**
     * 内部参数类
     */
    @Data
    public static class InnerParam {
        /**
         * 指定产品ID
         */
        private List<Integer> ids;

        /**
         * spu
         */
        private List<String> spuList;

        /**
         * 开始日期 (格式: yyyy-MM-dd HH:mm:ss)
         */
        private String startDate;

        /**
         * 结束日期 (格式: yyyy-MM-dd HH:mm:ss)
         */
        private String endDate;

        /**
         * 线程数量，默认为10
         */
        private Integer threadNum = 10;
    }

    @Override
    @XxlJob("AmazonNewProductCopywritingAiGenerateJob")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("Amazon新产品AI文案标题生成任务开始执行，参数：{}", param);

        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);


            SystemParam systemParam = systemParamService.querySystemParamByCodeKey("AMAZON.COPYWRITING_GENERATION_INSTRUCTIONS");
            if (null == systemParam) {
                XxlJobLogger.log("文案生成指令未设置,结束执行该任务,请先到系统参数配置");
                return ReturnT.FAIL;
            }

            // 文案生成指令
            String instructions = systemParam.getParamValue();
            // 获取需要处理的产品列表
            List<AmazonMustPublishNewProduct> productList = getProductsToProcess(innerParam);

            if (CollectionUtils.isEmpty(productList)) {
                XxlJobLogger.log("未找到需要处理的产品数据");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到 {} 个产品需要生成AI标题", productList.size());

            // 使用信号量+线程池批量处理产品
            batchProcessProducts(productList, instructions, innerParam.getThreadNum());

            XxlJobLogger.log("任务执行完成");
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            log.error("Amazon新产品AI文案标题生成任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 批量处理产品 - 使用信号量+线程池控制并发
     * 参考WalmartTaskOffLinkListingRetireJobHandler的实现方式
     */
    private void batchProcessProducts(List<AmazonMustPublishNewProduct> productList,
                                      String instructions, Integer threadNum) throws InterruptedException {

        // 使用传入的线程数量，如果为空则使用默认值10
        int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;

        // 控制线程池数量，创建Semaphore信号量
        final Semaphore sp = new Semaphore(actualThreadNum);

        XxlJobLogger.log("开始批量处理产品，并发线程数：{}", actualThreadNum);

        // 按产品循环处理数据
        for (AmazonMustPublishNewProduct product : productList) {
            sp.acquire();
            AmazonExecutors.executeAiService(() -> {
                try {
                    // 处理单个产品的AI调用
                    processProduct(product, instructions);
                } catch (Exception e) {
                    log.error("处理产品失败，SPU: {}, 错误: {}", product.getSpu(), e.getMessage(), e);
                    XxlJobLogger.log("处理产品失败，SPU: {}, 错误: {}", product.getSpu(), e.getMessage());
                } finally {
                    sp.release();
                }
            });
        }
    }

    /**
     * 解析任务参数
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数解析失败，使用默认参数: {}", e.getMessage());
            }
        }
        return innerParam;
    }


    /**
     * 获取需要处理的产品列表
     */
    private List<AmazonMustPublishNewProduct> getProductsToProcess(InnerParam innerParam) {
        AmazonMustPublishNewProductExample example = new AmazonMustPublishNewProductExample();
        AmazonMustPublishNewProductExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(innerParam.getIds()) || CollectionUtils.isNotEmpty(innerParam.getSpuList())
                || StringUtils.isNotBlank(innerParam.getStartDate()) || StringUtils.isNotBlank(innerParam.getEndDate())) {
            if (CollectionUtils.isNotEmpty(innerParam.getIds())) {
                criteria.andIdIn(innerParam.getIds());
            }
            if (CollectionUtils.isNotEmpty(innerParam.getSpuList())) {
                criteria.andSpuIn(innerParam.getSpuList());
            }

            String startTime = innerParam.getStartDate();
            String endTime = innerParam.getEndDate();

            if (StringUtils.isNotBlank(startTime)) {
                criteria.andCreatedTimeGreaterThanOrEqualTo(Timestamp.valueOf(startTime));
            }

            if (StringUtils.isNotBlank(endTime)) {
                criteria.andCreatedTimeLessThanOrEqualTo(Timestamp.valueOf(endTime));
            }
        } else {
            LocalDate today = LocalDate.now();
            String startTime = today.atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String endTime = today.atTime(23, 59, 59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            criteria.andCreatedTimeBetween(
                    java.sql.Timestamp.valueOf(startTime),
                    java.sql.Timestamp.valueOf(endTime)
            );
        }

        return amazonMustPublishNewProductService.selectByExample(example);
    }

    /**
     * 处理单个产品
     */
    private void processProduct(AmazonMustPublishNewProduct product, String instructions) throws Exception {
        String spu = product.getSpu();

        // 检查是否已经生成过AI标题
        if (isAlreadyProcessed(spu)) {
            return;
        }

        // 获取Amazon文案数据
        OfficialAmazonResponse amazonData = getAmazonOfficialData(spu);
        ReviewBasisInfoVO vo = ReviewBasisInfoVO.transfer(amazonData);

        // 提取参考标题
        List<String> referenceTitles = extractReferenceTitles(vo);

        // 构建AI请求
        ChatOpenaiRequest aiRequest = buildAiRequest(referenceTitles, instructions);

        // 调用AI服务
        ApiResult<ChatCompletionResponse> aiResponse = aiServiceClient.sendProcessOrdinaryTencent(aiRequest);

        if (!aiResponse.isSuccess()) {
            throw new RuntimeException(String.format("AI服务调用失败: %s", aiResponse.getErrorMsg()));
        }

        // 解析AI响应
        List<String> generatedTitles = parseAiResponse(aiResponse.getResult());

        // 保存生成的标题
        saveGeneratedTitles(spu, generatedTitles);
    }

    /**
     * 检查产品是否已经处理过
     */
    private boolean isAlreadyProcessed(String spu) {
        LambdaQueryWrapper<AmazonNewProductCopywritingReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonNewProductCopywritingReview::getSpu, spu);
        wrapper.and(w -> w.isNotNull(AmazonNewProductCopywritingReview::getLongTitle6AiGenerated)
                .or().isNotNull(AmazonNewProductCopywritingReview::getLongTitle7AiGenerated)
                .or().isNotNull(AmazonNewProductCopywritingReview::getLongTitle8AiGenerated));

        return amazonNewProductCopywritingReviewService.count(wrapper) > 0;
    }

    /**
     * 获取Amazon文案数据
     */
    private OfficialAmazonResponse getAmazonOfficialData(String spu) throws Exception {
        ApiResult<OfficialAmazonResponse> response = productClient.getCheckAmazonOfficial(spu);

        if (!response.isSuccess()) {
            throw new RuntimeException(String.format("获取Amazon文案数据失败: %s", response.getErrorMsg()));
        }

        OfficialAmazonResponse result = response.getResult();
        if (result == null) {
            throw new RuntimeException(String.format("产品 %s 的Amazon文案数据为空", spu));
        }

        return result;
    }

    /**
     * 提取参考标题（长标题1-5）
     */
    private List<String> extractReferenceTitles(ReviewBasisInfoVO vo) {
        List<String> titles = new ArrayList<>();
        titles.add(Optional.ofNullable(vo.getLongTitle1()).orElse(""));
        titles.add(Optional.ofNullable(vo.getLongTitle2()).orElse(""));
        titles.add(Optional.ofNullable(vo.getLongTitle3()).orElse(""));
        titles.add(Optional.ofNullable(vo.getLongTitle4()).orElse(""));
        titles.add(Optional.ofNullable(vo.getLongTitle5()).orElse(""));
        return titles;
    }

    /**
     * 构建AI请求对象
     */
    private ChatOpenaiRequest buildAiRequest(List<String> referenceTitles, String instructions) {
        // 构建prompt
        StringBuilder promptBuilder = new StringBuilder();

        for (int i = 0; i < referenceTitles.size(); i++) {
            promptBuilder.append("标题").append(i + 1).append("：").append(referenceTitles.get(i)).append("\n");
        }

        promptBuilder.append(instructions);

        // 创建选项对象
        ChatOpenaiRequest.Options options = new ChatOpenaiRequest.Options();

        // 使用Builder模式构建请求对象
        return ChatOpenaiRequest.builder()
                .prompt(promptBuilder.toString())
                .model("deepseek-r1")
                .systemMessage("")
                .options(options)
                .build();
    }

    /**
     * 解析AI响应，提取生成的标题
     */
    private List<String> parseAiResponse(ChatCompletionResponse response) throws Exception {
        if (response == null) {
            throw new RuntimeException("AI响应为空");
        }

        // 检查choices是否存在
        if (CollectionUtils.isEmpty(response.getChoices())) {
            throw new RuntimeException("AI响应中没有choices数据");
        }

        // 获取第一个choice
        ChatChoice firstChoice = response.getChoices().get(0);
        if (firstChoice == null || firstChoice.getMessage() == null) {
            throw new RuntimeException("AI响应中没有message数据");
        }

        String content = firstChoice.getMessage().getContent();
        if (StringUtils.isBlank(content)) {
            throw new RuntimeException("AI响应内容为空");
        }

        // 处理内容格式：去除开头的第一个"\n"，然后按"\n"分割
        if (content.startsWith("\n\n")) {
            content = content.substring(2);
        }

        String[] titleArray = content.split("\n");
        List<String> titles = new ArrayList<>();

        for (String title : titleArray) {
            if (StringUtils.isNotBlank(title.trim())) {
                titles.add(title.trim());
            }
        }

        if (titles.size() < 3) {
            throw new RuntimeException(String.format("AI生成的标题数量不足，期望3个，实际%d个", titles.size()));
        }

        // 只取前3个标题
        return titles.subList(0, 3);
    }

    /**
     * 保存生成的标题到数据库
     */
    private void saveGeneratedTitles(String spu, List<String> generatedTitles) {
        // 查询是否已存在记录
        LambdaQueryWrapper<AmazonNewProductCopywritingReview> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonNewProductCopywritingReview::getSpu, spu);
        AmazonNewProductCopywritingReview existingReview = amazonNewProductCopywritingReviewService.getOne(wrapper);

        AmazonNewProductCopywritingReview review;
        if (existingReview != null) {
            // 更新现有记录
            review = existingReview;
        } else {
            // 创建新记录
            review = new AmazonNewProductCopywritingReview();
            review.setSpu(spu);
            review.setCreatedTime(LocalDateTime.now());
        }

        // 设置AI生成的标题
        if (generatedTitles.size() >= 1) {
            review.setLongTitle6AiGenerated(generatedTitles.get(0));
        }
        if (generatedTitles.size() >= 2) {
            review.setLongTitle7AiGenerated(generatedTitles.get(1));
        }
        if (generatedTitles.size() >= 3) {
            review.setLongTitle8AiGenerated(generatedTitles.get(2));
        }

        review.setUpdatedTime(LocalDateTime.now());

        // 保存或更新记录
        if (existingReview != null) {
            amazonNewProductCopywritingReviewService.updateById(review);
            XxlJobLogger.log("更新产品 {} 的AI生成标题", spu);
        } else {
            amazonNewProductCopywritingReviewService.save(review);
            XxlJobLogger.log("保存产品 {} 的AI生成标题", spu);
        }
    }
}
