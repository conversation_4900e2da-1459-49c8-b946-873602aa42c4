package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CopywritingMessage {
    /**
     * spu
     */
    private String spu;

    /**
     * 长标题6
     */
    private String longTitle6;
    /**
     * 长标题7
     */
    private String longTitle7;
    /**
     * 长标题8
     */
    private String longTitle8;

    /**
     * 必选关键词
     */
    private String mustKeyword;
}
