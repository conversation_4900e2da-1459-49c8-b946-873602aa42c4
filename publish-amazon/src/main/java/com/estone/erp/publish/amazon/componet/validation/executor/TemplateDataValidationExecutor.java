package com.estone.erp.publish.amazon.componet.validation.executor;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.InfringementErrorMsgBo;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.componet.AmazonInfringementWordHelper;
import com.estone.erp.publish.amazon.componet.checkserver.CheckingWordContext;
import com.estone.erp.publish.amazon.componet.publish.util.AmazonJsonSchemaFactory;
import com.estone.erp.publish.amazon.componet.publish.domain.ListingProductData;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationExecutor;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateValidationEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonCategorySpProductType;
import com.estone.erp.publish.amazon.model.AmazonCategoryWithBLOBs;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.estone.erp.publish.amazon.model.dto.TemplateInfringementWordDTO;
import com.estone.erp.publish.amazon.service.AmazonCategoryService;
import com.estone.erp.publish.amazon.service.AmazonCategorySpProductTypeService;
import com.estone.erp.publish.amazon.util.AmazonAutoPublishUtil;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.networknt.schema.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.estone.erp.publish.common.executors.AmazonExecutors.checkInfringementWordAsyncTask;

/**
 * <AUTHOR>
 * @date 2024-03-29 9:25
 */
@Slf4j
@Component
public class TemplateDataValidationExecutor implements AmazonValidationExecutor {

    @Autowired
    private AmazonInfringementWordHelper amazonInfringementWordHelper;
    @Autowired
    private AmazonCategoryService amazonCategoryService;
    @Autowired
    private AmazonCategorySpProductTypeService amazonCategorySpProductTypeService;



    /**
     * 校验模板信息 - 模板数据校验
     *
     * @param context
     */
    @Override
    public void validationTemplateData(AmazonTemplateValidationContext context) {
        AmazonTemplateBO template = context.getTemplate();
        // EAN 完整
        if (BooleanUtils.isFalse(template.getUpcExempt()) && BooleanUtils.isTrue(context.getCheckEan())) {
            checkTemplateEAN(template, context);
        }
        Boolean isCheckAttr = context.getIsCheckAttr();
        // 侵权词提示  标题 描述 关键词 五点描述 品牌 属性
        ApiResult<String> apiResult = checkAllTemplatePropertiesInfringementWords(template, isCheckAttr);
        if (!apiResult.isSuccess()) {
            String result = apiResult.getResult();
            AmazonTemplateValidationDO validation = new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.INCLUDE_INFRINGING_WORD, false);
            validation.setData(result);
            context.addValidationData(validation);
        } else {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.INCLUDE_INFRINGING_WORD, true));
        }
        // 必填属性校验
        boolean isJsonTemplate = TemplateInterfaceTypeEnums.JSON.isTrue(template.getInterfaceType())
                && StringUtils.isNotBlank(template.getProductType());
        if (isJsonTemplate) {
            validationTemplateJsonData(template, context);
        }
    }

    /**
     * 通过JSON Schema校验模板必填属性
     */
    private void validationTemplateJsonData(AmazonTemplateBO template, AmazonTemplateValidationContext context) {
        try {
            AmazonCategorySpProductType productType = amazonCategorySpProductTypeService.getSchemaUrlProductType(template.getCountry(), template.getProductType());
            if (productType == null) {
                context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_TYPE_MISS, false));
                return;
            }

            String schemaUrl = productType.getSchemaUrl();
            if (StringUtils.isBlank(schemaUrl)) {
                context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_TYPE_MISS, false));
                return;
            }
            String schemaData = amazonCategorySpProductTypeService.getProductTypeSchemaLocalCache(productType.getSchemaUrl());
            if (StringUtils.isBlank(schemaData)) {
                context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_TYPE_MISS, false));
                return;
            }
            JsonSchema schema = AmazonJsonSchemaFactory.getInstance().getSchema(schemaData);
            // 深拷贝校验
            AmazonTemplateBO validationTemplate = BeanUtil.deepCopyProperties(template);
            ListingProductData listingProductData = ListingProductData.ofValidation(validationTemplate);
            Set<ValidationMessage> validationMessages = schema.validate(JSON.toJSONString(listingProductData.getData()), InputFormat.JSON, executionContext -> {
                ExecutionConfig executionConfig = executionContext.getExecutionConfig();
                executionConfig.setFormatAssertionsEnabled(true);
            });
            if (CollectionUtils.isEmpty(validationMessages)) {
                context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_PROPERTY_MISS, true));
                return;
            }

            if (CollectionUtils.isNotEmpty(validationMessages)) {
                List<String> errorMsgList = new ArrayList<>();
                List<String> ignorePropertyList = new ArrayList<>();
                String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "publish_property_validate", "ignore_property", 10);
                if (StringUtils.isNotBlank(paramValue)) {
                    ignorePropertyList.addAll(Arrays.asList(paramValue.split(",")));
                }
                for (ValidationMessage validationMessage : validationMessages) {
                    // 忽略必填项
                    boolean anyMatch = ignorePropertyList.stream().anyMatch(ignoreProperty -> {
                        return validationMessage.getMessage().contains(ignoreProperty);
                    });
                    if (anyMatch) {
                        continue;
                    }
                    // 非必填附加属性
                    if ("additionalProperties".equals(validationMessage.getMessageKey())) {
                        context.addAdditionalProperty(validationMessage.getProperty());
                    } else {
                        errorMsgList.add(validationMessage.getMessage());
                    }
                }
                if (CollectionUtils.isNotEmpty(errorMsgList)) {
                    context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_PROPERTY_MISS, false, errorMsgList));
                }
            }
        } catch (Exception e) {
            log.error("校验模板JSON数据失败，e:{}", e.getMessage(), e);
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.PRODUCT_TYPE_MISS, false, "校验模板JSON数据失败异常：" + e.getMessage()));
        }
    }

    /**
     * 检测非豁免upc模板，ean是否为空
     *
     * @param template
     * @param context
     */
    public void checkTemplateEAN(AmazonTemplateBO template, AmazonTemplateValidationContext context) {
        List<AmazonSku> amazonSkus = template.getAmazonSkus();
        List<Integer> templateIds = new ArrayList<>();
        if ((!template.getSaleVariant() || CollectionUtils.isEmpty(amazonSkus)) && StringUtils.isEmpty(template.getStandardProdcutIdValue())) {
            AmazonTemplateValidationDO validation = new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.EAN_EMPTY, false);
            templateIds.add(template.getId());
            validation.setData(templateIds);
            validation.setErrorMsg("EAN码缺失不刊登");
            context.addValidationData(validation);
            return;
        }
        if (CollectionUtils.isNotEmpty(amazonSkus) && StringUtils.isEmpty(amazonSkus.get(amazonSkus.size() - 1).getStandardProdcutIdValue())) {
            AmazonTemplateValidationDO validation = new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.EAN_EMPTY, false);
            templateIds.add(template.getId());
            validation.setErrorMsg("EAN码缺失不刊登");
            validation.setData(templateIds);
            context.addValidationData(validation);
        }
    }


    /**
     * 校验模板所有属性是否存在侵权词
     * 标题
     * 描述
     * 关键词
     * 五点描述
     * 品牌  品牌校验不需要校验分类
     * 属性  属性校验是可选的
     *
     * @param template
     */
    public ApiResult<String> checkAllTemplatePropertiesInfringementWords(AmazonTemplateBO template, Boolean isCheckAttr) {
        InfringementErrorMsgBo infringementErrorMsgBo = new InfringementErrorMsgBo();
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("checkingServer");
            // 品牌 属性 校验特殊处理
            if (StringUtils.isNotBlank(template.getBrand())) {
                // 品牌
                ApiResult<String> brandResult = checkBrandInfringementWord(template);
                if (!brandResult.isSuccess()) {
                    if (StringUtils.isBlank(brandResult.getResult())) {
                        return brandResult;
                    }
                    InfringementErrorMsgBo brandMsgBo = JSON.parseObject(brandResult.getResult(), InfringementErrorMsgBo.class);
                    infringementErrorMsgBo.setBrandInfringement(brandMsgBo.getBrandInfringement());
                }
            }
            if (isCheckAttr) {
                // 属性
                ApiResult<String> attrResult = checkAttrInfringementWord(template);
                if (!attrResult.isSuccess()) {
                    if (StringUtils.isBlank(attrResult.getResult())) {
                        return attrResult;
                    }
                    InfringementErrorMsgBo attrMsgBo = JSON.parseObject(attrResult.getResult(), InfringementErrorMsgBo.class);
                    infringementErrorMsgBo.setAttrInfringement(attrMsgBo.getAttrInfringement());
                }
            }

            // 校验模板 标题 描述 关键词 五点描述
            TemplateInfringementWordDTO infringementWordDTO = checkInfringementWordsWithCheckingServer(template);
            infringementWordDTO.setBrand(infringementErrorMsgBo.getBrandInfringement());
            stopWatch.stop();
            log.info("all properties 侵权词校验:{}ms,templateId:{}", stopWatch.getTotalTimeMillis(), template.getId());
            if (infringementWordDTO.existInfringementWord()) {
                infringementWordDTO.setIsInfringingWordsError(true);
                ApiResult<String> apiResult = ApiResult.newError(infringementWordDTO.formatMsg());
                infringementErrorMsgBo.setTitleInfringement(infringementWordDTO.getTitle());
                infringementErrorMsgBo.setDescInfringement(infringementWordDTO.getDescription());
                infringementErrorMsgBo.setSearchDataInfringement(infringementWordDTO.getSearchData());
                infringementErrorMsgBo.setBullpointInfringement(infringementWordDTO.getBulletPoint());
                infringementErrorMsgBo.setErrorMsg(infringementWordDTO.formatMsg());
                apiResult.setResult(JSON.toJSONString(infringementErrorMsgBo));
                return apiResult;
            }

            // 属性校验结果处理
            if (infringementErrorMsgBo.getBrandInfringement() != null || infringementErrorMsgBo.getAttrInfringement() != null) {
                ApiResult<String> apiResult = ApiResult.newError(infringementWordDTO.formatMsg());
                apiResult.setResult(JSON.toJSONString(infringementErrorMsgBo));
                return apiResult;
            }
        } catch (Exception e) {
            log.error("校验失败，模板Id：{},e:{}", template.getId(), e.getMessage(), e);
            return ApiResult.newError(template.getId() + ",校验失败，" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 调用接口校验属性是否是侵权词和商标词
     *
     * @param template
     * @return
     */
    public ApiResult<String> checkAttrInfringementWord(AmazonTemplateBO template) {
        if (null == template || !template.getSaleVariant() || StringUtils.isBlank(template.getVariations())) {
            return ApiResult.newSuccess();
        }
        InfringementErrorMsgBo infringementErrorMsgBo = new InfringementErrorMsgBo();
        try {
            List<AmazonSku> amazonSkus = JSON.parseArray(template.getVariations(), AmazonSku.class);
            List<String> attrValueList = AmazonTemplateUtils.getAttrValueList(amazonSkus, template.getInterfaceType());
            if (CollectionUtils.isNotEmpty(attrValueList)) {
                String text = StringUtils.join(attrValueList, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT);
                CheckingWordContext checkingWordContext = new CheckingWordContext(template, text);
                checkingWordContext.setOrderAccountBrandReplace(false); // 属性不替换订单品牌

                ApiResult<List<WordValidateResult>> apiResult = amazonInfringementWordHelper.searchTemplateInfringementWordInfos(checkingWordContext);
                if (!apiResult.isSuccess()) {
                    return ApiResult.newError("属性校验侵权词商标词失败，" + apiResult.getErrorMsg());
                }
                List<WordValidateResult> infringementWordInfos = apiResult.getResult();
                if (CollectionUtils.isNotEmpty(infringementWordInfos)) {
                    List<String> words = infringementWordInfos.stream().map(WordValidateResult::getOriginWord).collect(Collectors.toList());
                    infringementErrorMsgBo.setErrorMsg(String.format("属性存在商标词或侵权词  %s  不允许使用，请重新填写", org.apache.commons.lang.StringUtils.join(words, ",")));
                    infringementErrorMsgBo.setAttrInfringement(org.apache.commons.lang.StringUtils.join(words, ","));
                    ApiResult<String> apiResultData = ApiResult.newError(infringementErrorMsgBo.getErrorMsg());
                    apiResultData.setResult(JSON.toJSONString(infringementErrorMsgBo));
                    return apiResultData;
                }
            }
        } catch (Exception e) {
            return ApiResult.newError("属性校验侵权词商标词失败，" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 校验品牌是否存在侵权词
     *
     * @param template 模版
     * @return ApiResult<String>
     */
    public ApiResult<String> checkBrandInfringementWord(AmazonTemplateBO template) {
        if (template == null || StringUtils.isBlank(template.getBrand())) {
            return ApiResult.newSuccess();
        }
        try {
            InfringementErrorMsgBo infringementErrorMsgBo = new InfringementErrorMsgBo();
            // 品牌不需要校验分类
            CheckingWordContext checkingWordContext = new CheckingWordContext(template, template.getBrand());
            ApiResult<List<WordValidateResult>> apiResult = amazonInfringementWordHelper.searchTemplateInfringementWordInfos(checkingWordContext);
            if (!apiResult.isSuccess()) {
                return ApiResult.newError("品牌校验侵权词商标词失败，" + apiResult.getErrorMsg());
            }
            List<WordValidateResult> infringementWordInfos = apiResult.getResult();
            if (CollectionUtils.isNotEmpty(infringementWordInfos)) {
                List<String> words = infringementWordInfos.stream().map(WordValidateResult::getOriginWord).collect(Collectors.toList());
                infringementErrorMsgBo.setErrorMsg(String.format("品牌存在商标词或侵权词  %s  不允许使用，请重新填写", StringUtils.join(words, ",")));
                infringementErrorMsgBo.setBrandInfringement(StringUtils.join(words, ","));
                ApiResult<String> apiResultData = ApiResult.newError(infringementErrorMsgBo.getErrorMsg());
                apiResultData.setResult(JSON.toJSONString(infringementErrorMsgBo));
                return apiResultData;
            }
        } catch (Exception e) {
            return ApiResult.newError("品牌校验侵权词商标词失败，" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    /**
     * 调用侵权词服务API检查侵权词
     */
    private TemplateInfringementWordDTO checkInfringementWordsWithCheckingServer(AmazonTemplateBO template) {
        String parentSku = template.getParentSku();
        TemplateInfringementWordDTO infringementWordDTO = new TemplateInfringementWordDTO();
        infringementWordDTO.setSpu(parentSku);
        infringementWordDTO.setId(template.getId());
        String userName = WebUtils.getUserName();

        // 标题
        CompletableFuture<Void> titleFuture = checkInfringementWordAsyncTask(() -> {
            DataContextHolder.setUsername(userName);
            checkInfringementWords(template, template.getTitle(), infringementWordDTO::setTitle);
        });

        // 描述
        CompletableFuture<Void> descriptionFuture = checkInfringementWordAsyncTask(() -> {
            DataContextHolder.setUsername(userName);
            checkInfringementWords(template, template.getDescription(), infringementWordDTO::setDescription);
        });

        // 五点描述
        CompletableFuture<Void> bulletPointFuture = checkInfringementWordAsyncTask(() -> {
            DataContextHolder.setUsername(userName);
            String bulletPointData = template.formatBulletPointData(template.getBulletPoint());
            if (StringUtils.isBlank(bulletPointData)) {
                return;
            }
            checkInfringementWords(template, bulletPointData, infringementWordDTO::setBulletPoint);
        });

        // 关键词
        CompletableFuture<Void> searchTxtFuture = checkInfringementWordAsyncTask(() -> {
            DataContextHolder.setUsername(userName);
            String searchTxt = Boolean.TRUE.equals(template.getIsLock()) ? template.getSearchData() : template.getSearchTerms();
            String searchData = template.formatSearchData(searchTxt, template.getSaleVariant());
            if (StringUtils.isBlank(searchData)) {
                return;
            }
            checkInfringementWords(template, searchData, infringementWordDTO::setSearchData);
        });
        // 等待任务全部执行完毕
        CompletableFuture.allOf(titleFuture, descriptionFuture, bulletPointFuture, searchTxtFuture).join();
        return infringementWordDTO;
    }

    private void checkInfringementWords(AmazonTemplateBO template, String text, Consumer<String> setter) {
        CheckingWordContext checkingWordContext = new CheckingWordContext(template, text);
        ApiResult<List<WordValidateResult>> listApiResult = amazonInfringementWordHelper.searchTemplateInfringementWordInfos(checkingWordContext);
        if (!listApiResult.isSuccess()) {
            return;
        }
        List<WordValidateResult> wordValidateResults = listApiResult.getResult();
        if (CollectionUtils.isNotEmpty(wordValidateResults)) {
            List<String> words = wordValidateResults.stream()
                    .map(WordValidateResult::getOriginWord)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            setter.accept(StringUtils.join(words, ","));
        }
    }

    /**
     * 删除掉侵权词
     */
    public ApiResult<String> delTemplateInfringementWords(AmazonTemplateBO template) {
        try {
            // 判定某些词汇不可写在标题首位
            AmazonTemplateUtils.removeTitleFirstWord(template);
            ApiResult<String> attrResult = amazonInfringementWordHelper.replaceTemplateAttrInfringementWord(template);
            if (!attrResult.isSuccess()) {
                return attrResult;
            }
            String accountNumber = template.getSellerId();

            String searchTxt = template.getIsLock() ? template.getSearchData() : template.getSearchTerms();
            Map<String, String> orderAccountBrandMap = amazonInfringementWordHelper.getOrderAccountBrandMap(accountNumber);
            List<String> txtList = Arrays.asList(
                    StringUtils.defaultString(template.getTitle(), ""),
                    StringUtils.defaultString(template.getDescription(), ""),
                    template.formatBulletPointData(template.getBulletPoint()),
                    template.formatSearchData(searchTxt, template.getSaleVariant()));

            String text = StringUtils.join(txtList, AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT)
                    .replaceAll("<br>", AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT)
                    .replaceAll("<br/>", AmazonConstant.LISTING_CHECK_BRAND_WORD_SPLIT);

            CheckingWordContext wordContext = new CheckingWordContext(template, text);
            ApiResult<List<WordValidateResult>> listApiResult = amazonInfringementWordHelper.searchTemplateInfringementWordInfos(wordContext);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError("模板移除侵权词失败,原因如下：" + listApiResult.getErrorMsg());
            }
            List<WordValidateResult> wordValidateResults = listApiResult.getResult();
            if (CollectionUtils.isEmpty(wordValidateResults)) {
                return ApiResult.newSuccess();
            }

            // 删除目标字段上的侵权词
            template.setTitle(StringUtils.capitalize(amazonInfringementWordHelper.delInfringementWord(template.getTitle(), orderAccountBrandMap, wordValidateResults)));
            template.setBulletPoint(amazonInfringementWordHelper.jsonParseArrayDelInfringementWord(template.getBulletPoint(), orderAccountBrandMap, wordValidateResults));
            template.setDescription(amazonInfringementWordHelper.delInfringementWord(template.getDescription(), orderAccountBrandMap, wordValidateResults));
            // 范本取searchData 模板取searchTerms
            if (template.getIsLock()) {
                template.setSearchData(amazonInfringementWordHelper.delInfringementWord(template.getSearchData(), orderAccountBrandMap, wordValidateResults));
            } else {
                amazonInfringementWordHelper.searchTermsDelInfringementWord(template, orderAccountBrandMap, wordValidateResults);
            }
            AmazonAutoPublishUtil.alignTemplateBrandAndManufacturer(template);
        } catch (Exception e) {
            log.error("模板移除侵权词失败,原因如下：" + e.getMessage(), e);
            return ApiResult.newError("模板移除侵权词失败,原因如下：" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * 检查Amazon刊登平台分类
     *
     * @param template 模版
     * @return apiResult
     */
    public ApiResult<String> validationAmazonPublishCategory(AmazonTemplateBO template) {
        // 店铺分类必填
        AmazonCategoryWithBLOBs category = amazonCategoryService.getAmazonCategoryWithBLOBsByBrowseNodeId(template.getCategoryId(), template.getCountry());
        if (category == null) {
            template.setReportSolutionId(777);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.CATEGORY_MISS.getName());
            return ApiResult.newError("分类：" + StringUtils.defaultString(template.getCategoryId(), "") + "站点：" + StringUtils.defaultString(template.getCountry(), "") + ",分类为空，请重新选择分类。");
        }
        return ApiResult.newSuccess();
    }


}
