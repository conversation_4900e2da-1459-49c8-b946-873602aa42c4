package com.estone.erp.publish.amazon.mq.templatestatus;

import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishRetryHandler;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Amazon发布状态更新器
 * 负责更新模版状态、处理报告状态，以及发送后置处理消息
 */
@Slf4j
@Component
public class AmazonPublishStatusUpdater {

    @Autowired
    private AmazonTemplateService amazonTemplateService;

    @Autowired
    private AmazonProcessReportService amazonProcessReportService;

    @Autowired
    private AmazonPublishRetryHandler amazonPublishRetryHandler;

    @Resource
    private RabbitMqSender rabbitMqSender;


    /**
     * 更新模版刊登成功状态
     * 
     * @param template 模版信息
     * @param reportList 处理报告列表
     */
    public void updatePublishSuccess(AmazonTemplateBO template, List<AmazonProcessReport> reportList) {
        updateTemplateStatus(template, reportList, AmaoznPublishStatusEnum.PUBLISH_SUCCESS, "刊登成功");
        
        // 推送刊登成功消息到后置处理队列
        rabbitMqSender.allPublishVHostRabbitTemplateSend(
                PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, 
                AmazonQueues.AMAZON_PUBLISH_AFTER_PROCESS_QUEUE_KEY, 
                template.getId()
        );
    }

    /**
     * 更新模版刊登失败状态
     * 
     * @param template 模版信息
     * @param reportList 处理报告列表
     * @param failureDetails Listing信息列表
     */
    public void updatePublishFail(AmazonTemplateBO template, List<AmazonProcessReport> reportList, String failureDetails) {
        updateTemplateStatus(template, reportList, AmaoznPublishStatusEnum.PUBLISH_FAIL, failureDetails);
        amazonPublishRetryHandler.retryPublishByFailureDetails(template, failureDetails);
    }

    /**
     * 更新价格库存同步成功状态
     * 
     * @param template 模版信息
     * @param reportList 处理报告列表
     */
    public void updatePriceStockSyncSuccess(AmazonTemplateBO template, List<AmazonProcessReport> reportList) {
        template.setStepTemplateStatus(true);
        updateTemplateStatus(template, reportList, AmaoznPublishStatusEnum.PUBLISHING, "刊登成功，进入价格库存同步流程");
    }

    /**
     * 更新模版超时状态
     * 
     * @param template 模版信息
     * @param reportList 处理报告列表
     * @param timeoutHours 超时小时数
     */
    public void updateTimeoutFail(AmazonTemplateBO template, List<AmazonProcessReport> reportList, int timeoutHours) {
        String errorMsg = "刊登超时（超过" + timeoutHours + "小时）";
        template.setReportSolutionType(AmazonReportSolutionTypeEnum.TEMPLATE_TIME_OUT.getName());
        updateTemplateStatus(template, reportList, AmaoznPublishStatusEnum.PUBLISH_FAIL, errorMsg);
    }

    /**
     * 批量更新处理报告状态
     * 
     * @param reports 处理报告列表
     * @param success 是否成功
     * @param resultMsg 结果消息
     */
    public void updateReportStatus(List<AmazonProcessReport> reports, boolean success, String resultMsg) {
        for (AmazonProcessReport report : reports) {
            report.setStatusCode("Complete");
            report.setStatus(success);
            report.setFinishDate(new Date());
            report.setResultMsg(resultMsg);
        }
        amazonProcessReportService.update(reports);
    }

    /**
     * 通用的模版状态和处理报告更新方法
     * 
     * @param template 模版信息
     * @param reportList 处理报告列表
     * @param status 目标状态
     * @param message 状态消息
     */
    private void updateTemplateStatus(AmazonTemplateBO template, List<AmazonProcessReport> reportList, 
                                      AmaoznPublishStatusEnum status, String message) {
        // 更新模版状态
        AmazonTemplateBO updateTemplate = new AmazonTemplateBO();
        updateTemplate.setId(template.getId());
        updateTemplate.setPublishStatus(status.getStatusCode());
        if (template.getStepTemplateStatus() != null) {
            updateTemplate.setStepTemplateStatus(template.getStepTemplateStatus());
        }
        updateTemplate.setLastUpdateDate(new Date());
        amazonTemplateService.updateAmazonTemplateFilterNullByPrimaryKey(updateTemplate);

        // 更新处理报告状态
        if (CollectionUtils.isNotEmpty(reportList)) {
            List<AmazonProcessReport> updatedReports = new ArrayList<>();

            for (AmazonProcessReport report : reportList) {
                // 检查报告状态是否为"刊登中"
                if (report.getStatusCode() != null && report.getStatusCode().equals("Processing")) {
                    // 设置报告状态为"完成"
                    report.setStatusCode("Complete");
                    // 设置成功/失败状态
                    report.setStatus(status == AmaoznPublishStatusEnum.PUBLISH_SUCCESS);
                    if (status == AmaoznPublishStatusEnum.PUBLISHING) {
                        report.setStatus(Boolean.TRUE.equals(template.getStepTemplateStatus()));
                    }
                    report.setFinishDate(new Date());
                    report.setResultMsg(message);
                    updatedReports.add(report);
                }
            }

            if (CollectionUtils.isNotEmpty(updatedReports)) {
                amazonProcessReportService.update(updatedReports);
            }
        }
    }
} 