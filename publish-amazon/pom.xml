<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.estone</groupId>
		<artifactId>publish</artifactId>
		<version>0.0.1</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	
	<artifactId>publish-amazon</artifactId>
    <name>publish-amazon web</name>
	<version>0.0.1</version>
	<packaging>jar</packaging>
	<description>amazon project for Spring Boot</description>

	<dependencies>
        <dependency>
            <groupId>com.estone</groupId>
            <artifactId>publish-tool</artifactId>
        </dependency>
        <dependency>
            <groupId>com.estone</groupId>
            <artifactId>publish-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.estone.amazon</groupId>
            <artifactId>catalog_items_api_model</artifactId>
            <version>2022-04-01-2024.12.03</version>
        </dependency>
        <dependency>
            <groupId>com.estone.amazon</groupId>
            <artifactId>reports_api_model</artifactId>
            <version>2021-06-30-2024.12.03</version>
        </dependency>
        <dependency>
            <groupId>com.estone.amazon</groupId>
            <artifactId>feeds_api_model</artifactId>
            <version>2021-06-30-2024.12.03</version>
        </dependency>
        <dependency>
            <groupId>com.estone.amazon</groupId>
            <artifactId>listings_items_api_model</artifactId>
			<version>2021-08-01-2024.12.03</version>
		</dependency>
        <dependency>
            <groupId>com.estone.amazon</groupId>
            <artifactId>product_pricing_api_model</artifactId>
            <version>2022-05-01-2024.12.03</version>
        </dependency>
		<dependency>
			<groupId>com.estone.amazon</groupId>
			<artifactId>customer_feedback_api_model</artifactId>
			<version>v2024-06-01</version>
		</dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.10.3</version>
        </dependency>
		<!--html-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<!--Flying Saucer OpenHTML2PDF-->
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf</artifactId>
			<version>9.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-core</artifactId>
			<version>9.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.24</version>
		</dependency>
		<!-- kuromoji -->
		<dependency>
			<groupId>com.atilika.kuromoji</groupId>
			<artifactId>kuromoji-jumandic</artifactId>
			<version>0.9.0</version>
		</dependency>

		<dependency>
			<groupId>com.networknt</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>1.5.6</version>
		</dependency>

		<dependency>
			<groupId>org.graalvm.js</groupId>
			<artifactId>js-scriptengine</artifactId>
			<version>22.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.graalvm.js</groupId>
			<artifactId>js</artifactId>
			<version>22.0.0</version>
		</dependency>


		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.13.2</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.13.2</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.2</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
			<version>2.13.2</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.13.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.hbase</groupId>
			<artifactId>hbase-client</artifactId>
			<version>2.2.4</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.phoenix</groupId>
			<artifactId>phoenix-queryserver-client</artifactId>
			<version>5.0.0-HBase-2.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>3.5.1</version>
		</dependency>
		
		<dependency>
			<groupId>com.estone.amazon</groupId>
			<artifactId>product_type_definitions_api_model</artifactId>
			<version>v2020-09-01-2024.12.03</version>
		</dependency>
    </dependencies>
	<build>
		<finalName>publish-amazon</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
					<encoding>UTF-8</encoding>
					<forceJavacCompilerUse>true</forceJavacCompilerUse>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	<repositories>
		<repository>
			<id>maven_central</id>
			<name>Maven Central</name>
			<url>https://repo.maven.apache.org/maven2/</url>
		</repository>
	</repositories>
</project>