# AI服务调用优化方案

## 概述

针对AI服务调用速度慢的问题，我们在现有的`AmazonNewProductCopywritingAiGenerateJob`中实现了基于信号量+线程池的并发控制优化，参考了`WalmartTaskOffLinkListingRetireJobHandler`的成功实践。

## 优化内容

### 1. 在AmazonExecutors中添加AI服务专用线程池

```java
/**
 * AI服务调用专用线程池
 */
public static final ThreadPoolExecutor AI_SERVICE_POOL = ExecutorUtils
        .newFixedThreadPool("amazon-ai-service-pool", 20);

/**
 * AI服务调用执行
 */
public static void executeAiService(Runnable runnable) {
    ExecutorUtils.execute(AI_SERVICE_POOL, runnable, "amazon-ai-service");
}
```

### 2. 在AmazonNewProductCopywritingAiGenerateJob中添加threadNum参数

```java
@Data
public static class InnerParam {
    // 其他参数...
    
    /**
     * 线程数量，默认为10
     */
    private Integer threadNum = 10;
}
```

### 3. 实现批量处理方法

```java
/**
 * 批量处理产品 - 使用信号量+线程池控制并发
 * 参考WalmartTaskOffLinkListingRetireJobHandler的实现方式
 */
private void batchProcessProducts(List<AmazonMustPublishNewProduct> productList, 
                                 String instructions, Integer threadNum) throws InterruptedException {
    
    // 使用传入的线程数量，如果为空则使用默认值10
    int actualThreadNum = (threadNum != null && threadNum > 0) ? threadNum : 10;
    
    // 控制线程池数量，创建Semaphore信号量
    final Semaphore sp = new Semaphore(actualThreadNum);
    
    // 按产品循环处理数据
    for (AmazonMustPublishNewProduct product : productList) {
        sp.acquire();
        AmazonExecutors.executeAiService(() -> {
            try {
                // 处理单个产品的AI调用
                processProduct(product, instructions);
                XxlJobLogger.log("成功处理产品 SPU: {}", product.getSpu());
            } catch (Exception e) {
                log.error("处理产品失败，SPU: {}, 错误: {}", product.getSpu(), e.getMessage(), e);
            } finally {
                sp.release();
            }
        });
    }
}
```

## 使用方法

### 任务参数示例

```json
{
    "threadNum": 15,
    "startDate": "2025-01-01 00:00:00",
    "endDate": "2025-01-31 23:59:59"
}
```

### 参数说明

- **threadNum**: 并发线程数量，默认为10
  - 建议根据AI服务的QPS限制和系统资源调整
  - 过大可能导致AI服务过载，过小影响处理速度
- **其他参数**: 保持原有功能不变

## 核心实现原理

### 信号量控制
```java
// 创建信号量，控制并发数量
final Semaphore sp = new Semaphore(threadNum);

// 获取许可
sp.acquire();
try {
    // 执行AI服务调用
    processProduct(product, instructions);
} finally {
    // 释放许可
    sp.release();
}
```

### 线程池执行
- 使用`AmazonExecutors.executeAiService()`提交任务
- 线程池大小：20个线程
- 任务队列：无界队列
- 拒绝策略：CallerRunsPolicy

## 性能提升

### 优化前
- 串行处理产品
- 单线程调用AI服务
- 处理速度慢

### 优化后
- 并行处理产品
- 信号量控制并发数量
- 显著提升处理速度
- 可配置并发线程数

## 注意事项

1. **线程数量设置**: 
   - 默认值10适合大多数场景
   - 可根据AI服务承载能力调整
   - 建议不超过20

2. **异常处理**: 
   - 单个产品处理失败不影响其他产品
   - 完善的异常日志记录
   - 确保信号量正确释放

3. **资源管理**: 
   - 线程池由AmazonExecutors统一管理
   - 信号量自动释放，避免死锁
   - 任务完成后自动清理资源

## 扩展应用

这个优化方案可以应用到其他需要批量调用AI服务的场景：

1. **其他AI文案生成任务**
2. **批量翻译任务**
3. **图片识别任务**
4. **其他耗时的外部服务调用**

只需要按照相同的模式：
1. 添加threadNum参数
2. 使用信号量控制并发
3. 使用合适的线程池执行任务

## 监控建议

1. **执行日志**: 关注任务执行时间和成功率
2. **线程池监控**: 监控AI_SERVICE_POOL的使用情况
3. **AI服务监控**: 关注AI服务的响应时间和错误率
4. **系统资源**: 监控CPU和内存使用情况
